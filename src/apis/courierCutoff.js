import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getCourierCutoffListAPI = (requestBody) => {
  const { filters, ...payload } = requestBody;
  return tokenInstance().post(config.courierCutoff.list, { ...filters }, { params: payload });
};

export const uploadCutoffCutoffAPI = (payload) =>
  tokenInstance().post(config.courierCutoff.upload, payload);
