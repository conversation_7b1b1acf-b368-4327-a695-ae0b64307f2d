import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import {
  getClosePickingLoad,
  getClosePickingReset,
  DO_SHIPMENT_LIST,
  updateDOShipmentDetailsListLoad
} from 'redux/reducers/doShipmentList';
import { CONSUL } from 'redux/reducers/consul';

import Spinner from 'components/Spinner/Spinner';
import NavigationDrawer from 'layouts/Main/components/Drawer/NavigationDrawer';
import PickingScanProductsTable from 'views/PickingScan/ScanTransferBody/PickingScanProductsTable';

let interval;

const DistrubutorOrderClosePickingModal = (props) => {
  const {
    isAllItemsPicked,
    CONTENT,
    openDrawer,
    handleCloseFun,
    classes,
    productList,
    shippingPackageID,
    createSummaryFun,
    disableGoBackToScanningBtn,
    isLoading
  } = props;
  const dispatch = useDispatch();
  const { isLoading: isClosePickingLoading, isSuccess } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].closePicking
  );
  const featureToggle = useSelector((state) => state[CONSUL].featureToggle.data);

  const { headerData } = useSelector((state) => state[DO_SHIPMENT_LIST].shipmentDetailsList);

  useEffect(() => {
    if (isSuccess) {
      handleCloseFun();
      dispatch(getClosePickingReset());
      dispatch(updateDOShipmentDetailsListLoad({ id: shippingPackageID }));
      interval = setInterval(
        () => dispatch(updateDOShipmentDetailsListLoad({ id: shippingPackageID })),
        10000
      );
    }
  }, [isSuccess]);

  useEffect(() => {
    if (headerData?.status && featureToggle?.doPicking?.printStatus.includes(headerData?.status)) {
      clearInterval(interval);
    }
  }, [headerData]);

  useEffect(() => () => clearInterval(interval), []);

  return (
    <NavigationDrawer
      openDrawer={openDrawer}
      handleClose={isClosePickingLoading ? () => null : handleCloseFun}
      modalwidth="500px"
    >
      <Box className={classes.drawerContainer}>
        <Box>
          <PickingScanProductsTable tableData={productList} isLoading={isLoading} />
        </Box>
        <Box width="100%">
          {isAllItemsPicked ? (
            <Button
              disabled={!productList.length || isClosePickingLoading}
              onClick={() => {
                dispatch(getClosePickingLoad({ shippingPackageID }));
              }}
              className="mr-b12"
              fullWidth
              color="primary"
              type="submit"
              variant="contained"
              data-cy="confirm-btn"
            >
              {isClosePickingLoading ? <Spinner size="18px" /> : CONTENT.CONFIRM}
            </Button>
          ) : (
            <Box className={classes.errorMessage}>{CONTENT.FEW_ITEMS_ARE_PENDING}</Box>
          )}
          <Button
            onClick={createSummaryFun}
            fullWidth
            color="primary"
            type="submit"
            variant="outlined"
            data-cy="go-back-to-scanning"
            disabled={disableGoBackToScanningBtn}
          >
            {CONTENT.GO_BACK_TO_SCANNING}
          </Button>
        </Box>
      </Box>
    </NavigationDrawer>
  );
};

export default DistrubutorOrderClosePickingModal;
