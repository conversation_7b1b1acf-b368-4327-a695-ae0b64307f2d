

.rdrInRange {
    color:#F2F2F2 !important;
}

.rdrInRange + .rdrDayNumber > span {
    color:black !important
}


.rdrDay:not(.rdrDayPassive) .rdrInRange ~ .rdrDayNumber span{
    color: black !important;
}




.rdrDay.rdrDayPassive .rdrInRange ~ .rdrDayNumber span{
 color: #d5dce0 !important;

}



.rdrDefinedRangesWrapper{
    display: none !important;
}

.rdrWeekDay{
    color: #000000 !important;
}

// .rdrStartEdge{
//     border-top-left-radius: 4px !important;
//     border-bottom-left-radius: 4px !important;
// }

// .rdrEndEdge{
//     border-top-right-radius: 4px !important;
//     border-bottom-right-radius: 4px !important;
// }
.rdrCalendarWrapper{
    width :500px !important;
    max-width: 500px !important;
    height: 280px !important;
}

.rdrMonth:first-child .rdrMonthName{
   position : absolute !important;
   top : 12px  !important;
   left :  80px  !important;
   font-size:  13px  !important
}
 
.rdrMonth:last-child .rdrMonthName{
    position : absolute !important;
    top : 12px  !important;
    right :  58px  !important;
    font-size:  13px  !important
 }