import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import TextField from '@mui/material/TextField';
import { keys } from 'utils/lodash';
import DateSelectFilter from 'components/common/DateSelectFilter';
import { generatePayloadForSearchAPI } from 'utils/helpers';
import {
  cycleCountWebFilterDateSet,
  cycleCountWebSummaryViewDataRequest
} from 'redux/reducers/cycleCount';

const useStyles = makeStyles()(() => ({
  container: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: 30,
    padding: '15px 30px',
    margin: '8px auto',
    background: '#FFFFFF',
    color: '#666666',
    fontSize: 20
  }
}));

const minDate = new Date(1640975400000);



const CycleCountFilter = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CYCLE_COUNT);
  
  const DATE_RANGE_OPTIONS = [
    { key: CONTENT.TODAY, value: 'TODAY' },
    { key: CONTENT.LAST_SEVEN_DAYS, value: 'LAST 7 DAYS' },
    { key: CONTENT.LAST_THIRTY_DAYS, value: 'LAST 30 DAYS' },
    { key: CONTENT.CUSTOM_RANGE, value: 'Custom Range' }
  ];
  const [selectedFilterList, setSelectedFilterList] = useState([
    { key: 'Date_Range', value: DATE_RANGE_OPTIONS[2].value }
  ]);
  
  const onChangeFilterList = (data) => {
    const payload = generatePayloadForSearchAPI(data, {}, 'Date_Range');

    const selectedDate = {
      start: keys(payload).length !== 0 ? payload.Date_Rangefrom : '',
      end: keys(payload).length !== 0 ? payload.Date_Rangeto : ''
    };

    setSelectedFilterList(data);
    dispatch(cycleCountWebFilterDateSet(selectedDate));
    dispatch(cycleCountWebSummaryViewDataRequest(selectedDate));
  };

  useEffect(() => {
    onChangeFilterList([{ key: 'Date_Range', value: DATE_RANGE_OPTIONS[2].value }]);
  }, []);

  return (
    <Box className={classes.container}>
      <Box>{CONTENT.FILTER}</Box>
      <TextField
        type="text"
        size="medium"
        label={CONTENT.SORT_DATE_BY}
        value={selectedFilterList[0].value}
        variant="outlined"
        InputProps={{
          readOnly: true,
          startAdornment: (
            <DateSelectFilter
              onSubmit={onChangeFilterList}
              columnName="Date_Range"
              selectedFilterList={selectedFilterList}
              monitorPhasefilter
              dateOptions={DATE_RANGE_OPTIONS}
              minDate={minDate}
              marginLeft="100px"
            />
          )
        }}
      />
    </Box>
  );
};

export default CycleCountFilter;
