import React from 'react';
import EmptyItem from '../BlankPage';

const heading = 'test heading';
const subHeading = 'test sub heading';

describe('<EmptyItem />', () => {
  it('renders heading and sub heading correctly', () => {
    cy.mount(<EmptyItem heading={heading} subHeading={subHeading}/>);
    cy.getByCy('heading').should('have.text', heading);
    cy.getByCy('sub-heading').should('have.text', subHeading);
  });
});
