import React from 'react'
import LkInput from './../../components/MaterialUi/LkInput';
import { Box } from '@mui/material';
import LkSelect from 'components/MaterialUi/LkSelectBox';
import MenuItem from "@mui/material/MenuItem";

const selectBoxItems = [{ key: "pid", value: "PID" },
{ key: "createdBy", value: "Created By" }
]

const selectBoxMapping = {
    pid: "PID",
    grnCode: "GRN Number",
    po: "PO Number",
    invoiceRefNumber: "Invoice Ref Number",
    "Vendor Invoice Number": "Vendor Invoice Number",
    vendor: "Vendor Name",
    createdBy: "Created By"
}
const SelectBoxes = ({ selectBox, inputBox, handleSelectBox, handleInputBox }) => {
    return (
        <Box display="flex">

            <Box width={200}>
                <LkSelect style={{ height: 40 }} label={false} fullWidth={true} value={selectBox} onChange={handleSelectBox}>
                    <MenuItem value={0}>{"Please Select"}</MenuItem>
                    {selectBoxItems.map((t) => <MenuItem key={t.key} value={t.key}>{t.value}</MenuItem>)}
                </LkSelect>
            </Box>
            <Box width={200} ml={2} >
                {selectBox !== 0 && <>               
                        <LkInput fullWidth={true}
                            label={`Search ${selectBoxMapping[selectBox]}...`}
                            onChange={(e) => handleInputBox(false, e)} disabled={selectBox === 0 ? true : false} value={inputBox} />

                </>
                }



            </Box>
        </Box>

    )
}

export default SelectBoxes