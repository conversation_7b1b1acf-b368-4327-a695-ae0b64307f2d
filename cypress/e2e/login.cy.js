describe('My First Test', () => {
  before(() => {
    cy.login();
    cy.visit('/dashboard');
  });

  after(() => {
    cy.logout();
  });

  it('Visits the homepage', () => {
    cy.getByCy('global-search').click();
    cy.get('#barcodeSearchId').type('0454qirm');
    cy.waitForApi('POST', '/nexs/api/ims/getHistory', () =>
      cy.get('.MuiSvgIcon-root.text-white').click()
    );
    cy.getByCy('table-cell-0').first().should('contain.text', 'FITTING_DONE');
  });
});
