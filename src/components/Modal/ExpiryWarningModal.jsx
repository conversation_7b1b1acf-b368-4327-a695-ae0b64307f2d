import React from 'react';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import LkModal from './Modal';

const styles = makeStyles()((theme) => ({
  modalContent: {
    padding: theme.spacing(3),
    textAlign: 'center'
  },
  warningIcon: {
    fontSize: '48px',
    color: '#f44336',
    marginBottom: theme.spacing(2)
  },
  warningText: {
    color: '#f44336',
    fontWeight: 'bold',
    marginBottom: theme.spacing(2)
  },
  descriptionText: {
    marginBottom: theme.spacing(3),
    color: '#666'
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'center',
    gap: theme.spacing(2)
  },
  failButton: {
    backgroundColor: '#f44336',
    color: 'white',
    '&:hover': {
      backgroundColor: '#d32f2f'
    }
  },
  cancelButton: {
    backgroundColor: '#757575',
    color: 'white',
    '&:hover': {
      backgroundColor: '#616161'
    }
  }
}));

const ExpiryWarningModal = ({ open, onClose, onFail, daysToExpiry }) => {
  const { classes } = styles();

  const handleFail = () => {
    onFail();
    onClose();
  };

  return (
    <LkModal open={open} handleClose={onClose} maxWidth="sm" fullWidth>
      <Box className={classes.modalContent}>
        <Typography variant="h4" className={classes.warningIcon}>
          ⚠️
        </Typography>

        <Typography variant="h6" className={classes.warningText}>
          Product is expired, please QC fail and repick
        </Typography>

        <Typography variant="body1" className={classes.descriptionText}>
          {daysToExpiry <= 0
            ? `This product expired ${Math.abs(daysToExpiry)} days ago.`
            : `This product will expire in ${daysToExpiry} days.`}
          <br />
          Please mark this item as QC FAIL to initiate repicking process.
        </Typography>

        <Box className={classes.buttonContainer}>
          <Button variant="contained" className={classes.cancelButton} onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" className={classes.failButton} onClick={handleFail}>
            Mark QC FAIL
          </Button>
        </Box>
      </Box>
    </LkModal>
  );
};

export default ExpiryWarningModal;
