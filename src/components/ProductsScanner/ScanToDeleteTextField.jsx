import React, { useRef } from 'react';
import { makeStyles } from 'tss-react/mui';
import { getBarcodeFromURL } from '../../utils/helpers';

const useStyles = makeStyles()(() => ({
  textField: {
    flexGrow: 1,
    height: 35,
    borderRadius: 6,
    outline: 'none',
    padding: 10,
    marginLeft: 26,
    marginRight: 10,
    borderColor: '#F44336'
  }
}));

const ScanToDeleteTextField = ({ deleteBarcode }) => {
  const { classes } = useStyles();
  const inputRef = useRef(null);

  const scanBarcodeFunc = (inputBarcode) => {
    const barcode = getBarcodeFromURL(inputBarcode.trim());
    inputRef.current.value = '';
    if (barcode.length > 1) {
      deleteBarcode(barcode);
    }
  };

  return (
    <input
      type="text"
      placeholder="Scan To Delete"
      ref={inputRef}
      disabled={false}
      className={classes.textField}
      onKeyDown={(e) => e.key === 'Enter' && scanBarcodeFunc(e.target.value)}
    />
  );
};

export default ScanToDeleteTextField;
