import Box from '@mui/material/Box';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import MenuItem from '@mui/material/MenuItem';
import Checkbox from '@mui/material/Checkbox';
import ListSubheader from '@mui/material/ListSubheader';
import FormControlLabel from '@mui/material/FormControlLabel';
import { RadioButtonChecked, RadioButtonUnchecked } from '@mui/icons-material';
import LkInput from 'components/MaterialUi/LkInput';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  ASRS_DISCREPANCY,
  ASRS_DISCREPANCY_REPORT,
  INVENTORY,
  INVENTORY_REPORT,
  PO,
  PO_ITEM_REPORT,
  SALE_ORDER,
  SALE_ORDER_REPORT,
  GRN_REPORT,
  GRN
} from './constant';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    padding: '20px 15px',
    flexDirection: 'column',
    justifyContent: 'space-between'
  },
  selectField: {
    marginTop: 20,
    marginLeft: 25,
    minWidth: 250,
    width: 'calc(100% - 84%)'
  },
  selectedReportInput: {
    maxHeight: 55,
    minHeight: 55,
    overflow: 'hidden'
  }
}));
const ReportType = ({ selectReportTypeFunc, selectedReport }) => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.EXPORT_REPORT);
  const typeList = [
    INVENTORY_REPORT,
    SALE_ORDER_REPORT,
    ASRS_DISCREPANCY_REPORT,
    PO_ITEM_REPORT,
    GRN_REPORT
  ];

  const handleChange = ({ target: { value } }) => {
    if (value === INVENTORY_REPORT) {
      navigate(`/reporting/${INVENTORY.toLowerCase()}`);
    } else if (value === SALE_ORDER_REPORT) {
      navigate(`/reporting/${SALE_ORDER.toLowerCase()}`);
    } else if (value === PO_ITEM_REPORT) {
      navigate(`/reporting/${PO.toLowerCase()}`);
    } else if (value === GRN_REPORT) {
      navigate(`/reporting/${GRN.toLowerCase()}`);
    } else {
      navigate(`/reporting/${ASRS_DISCREPANCY.toLowerCase()}`);
    }
    selectReportTypeFunc(value);
  };

  return (
    <Box className={classes.container}>
      <Box marginLeft={3}>{CONTENT.WHICH_REPORT_YOU_WANT_TO_EXPORT}</Box>
      <LkInput
        id="reportType"
        select
        fullWidth
        size="medium"
        value={selectedReport}
        label={CONTENT.SELECT_REPORT_TYPE}
        onChange={handleChange}
        className={classes.selectField}
        InputProps={{
          className: classes.selectedReportInput
        }}
      >
        <ListSubheader data-cy="list-heading" className={classes.selectDestinationTitle}>
          {CONTENT.SELECT_REPORT_TYPE}
        </ListSubheader>
        {typeList.map((eachItem) => {
          const isSelected = selectedReport === eachItem;

          return (
            <MenuItem data-cy={eachItem} key={eachItem} value={eachItem}>
              <FormControlLabel
                control={
                  <Checkbox
                    data-cy={`input-${eachItem}`}
                    checked={isSelected}
                    name="checkedItems"
                    color="primary"
                    icon={<RadioButtonUnchecked />}
                    checkedIcon={<RadioButtonChecked />}
                  />
                }
                label={eachItem}
              />
            </MenuItem>
          );
        })}
      </LkInput>
    </Box>
  );
};

export default ReportType;
