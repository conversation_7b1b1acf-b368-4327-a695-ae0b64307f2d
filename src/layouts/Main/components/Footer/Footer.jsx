import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { Typography, Link } from '@mui/material';

const useStyles = makeStyles()(theme => ({
  root: {
    padding: theme.spacing(4)
  }
}));

const Footer = props => {
  const { className, ...rest } = props;

  const { classes, cx } = useStyles();

  return (
    (<div
      {...rest}
      className={cx(classes.root, className)}
    >
      <Typography variant="body1">
        &copy;{' '}
        <Link
          component="a"
          href="https://www.lenskart.com/"
          target="_blank"
          underline="hover"
        >
          Lenskart.com
        </Link>
      </Typography>
      <Typography variant="caption">
        Created with love for the environment. By designers and developers who
        love to work together in offices!
      </Typography>
    </div>)
  );
};

Footer.propTypes = {
  className: PropTypes.string
};

export default Footer;
