import React, { useCallback, useState, useEffect } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { Topbar } from 'layouts/Main/components';
import TopbarRightElement from 'layouts/Main/components/Topbar/TopbarRightElement';
import { makeStyles } from 'tss-react/mui';
import { Box, Button, FormControlLabel } from '@mui/material';
import LkChip from 'components/MaterialUi/LkChip';
import Switch from '@mui/material/Switch';
import { useDispatch, useSelector } from 'react-redux';
import {
  getFacilityDetailsLoad,
  saveFacilityLoad,
  saveFacilityReset,
  getPincodeDeatilsReset,
  getTaxDeatilsLoad,
  getStateCountryListLoad,
  getEntityListReset
} from 'redux/actionCreators/facility';
import Spinner from 'components/Spinner/Spinner';
import { toastOpen } from 'redux/actionCreators/toast';
import {
  layoutSaveLoad,
  setLayoutCreateData,
  getLayoutDetailReset,
  layoutSaveReset,
  getLayoutListLoad,
  getLayoutListReset
} from 'redux/actionCreators/layout';
import {
  convertDateFormat,
  taxregexValidation,
  allContactDataFilled,
  getQueryParam
} from 'utils/helpers';
import { LOCALISATION } from 'redux/reducers/localisation';
import MiniDrawer from './FacilityDetailsDrawer';
import GeneralDetails from '../FacilityCreation/GeneralDetails';
import TaxDetails from '../FacilityCreation/TaxDetails';
import ContactDeatils from '../FacilityCreation/ContactDetails';
import FacilityEntityDetails from './EntityDetails/FacilityEntityDetails';
import LayoutSchemaDetails from './LayoutSchemaDetails';
import SaveChangesFonfirmModal from './SaveChangesConfirmModal';

const useStyles = makeStyles()(() => ({
  root: {
    height: '91vh',
    background: '#fff'
  },
  btn: {
    borderRadius: 8
  }
}));
const contactDetails = {
  primary_contact: { name: '', phone: '', mobile: '', email: '', type: 'PRIMARY' },
  secondary_contact: { name: '', phone: '', mobile: '', email: '', type: 'SECONDARY' }
};

const taxKeyMapping = {
  'GST No': 'gst_number',
  'PAN No': 'pan',
  'Registered Name': 'registered_name',
  'TIN No': 'tin'
};
const generalDetailsMock = {
  image: '',
  type: '',
  country: '',
  display_name: '',
  legal_name: '',
  legal_owner: '',
  facility_code: '',
  pin_code: '',
  city: '',
  state: '',
  sPCity: '',
  sPstate: '',
  address_line1: '',
  address_line2: '',
  sPcountry: '',
  sPpin_code: '',
  sPaddress_line1: '',
  sPaddress_line2: ''
};

let layoutIdFromSaveLayout = '';

const FacilityDetails = () => {
  const location = useLocation();
  const { facilityId } = useParams();
  const [facilityActive, setFacilityActive] = useState(false);
  const [editedTabs, setEditedTabs] = useState({
    generalDetailsData: false,
    contactDetailsData: false,
    taxData: false
  });
  const dispatch = useDispatch();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.FACILITY);

  const { layoutCreateData, layoutSaveProccessing, layoutSaveData } = useSelector(
    (state) => state.layout
  );
  const {
    facilityDetailsLoading,
    facilityDetails,
    facilityDetailsError,
    facilitySave,
    facilitySaveError,
    facilitySaveLoading,
    pincodeDetails1,
    pincodeDetails2,
    pincodeDetails1Fail,
    pincodeDetails2Fail,
    uploadLogoSuccess,
    taxDetailsSuccess
  } = useSelector((state) => state.facility);
  const { classes } = useStyles();
  const [selectedTab, setSelectedTab] = useState(0);
  const [saveChangeConfirmModal, setSaveChangesConfirmModal] = useState(false);
  const [taxData, setTaxData] = useState({ registered_name: '', gst_number: '', tin: '', pan: '' });
  const [generalDetailsData, setGeneralDetailsData] = useState(
    JSON.parse(JSON.stringify(generalDetailsMock))
  );
  const [contactDetailsData, setContactDetailsData] = useState(
    JSON.parse(JSON.stringify(contactDetails))
  );
  const [openLinkLayoutModal, setOpenLInkLayoutModal] = useState(false);
  const [inValidData, setinValidData] = useState({});
  const [layoutSchemaData, setLayoutSchemaData] = useState({ key: '', value: '' });
  const setEditedTabsFun = useCallback(
    (i) => setEditedTabs((temp) => ({ ...temp, [i]: true })),
    [setEditedTabs]
  );
  const setSelectdTabFun = (i) => {
    let noError = true;
    if (selectedTab === 1) {
      const { result, invaliddataTemp } = allContactDataFilled(contactDetailsData);
      setinValidData(invaliddataTemp);
      noError = result;
    }
    if (selectedTab === 2) {
      const { result, invaliddataTemp } = taxregexValidation(taxDetailsSuccess, taxData);
      setinValidData(invaliddataTemp);
      noError = result;
    }
    if (noError) {
      setSelectedTab(i);
    }
  };
  const setDataFun = useCallback((key, data, callBack) => {
    callBack((t) => ({ ...t, [key]: data }));
  }, []);
  const saveChange = false;

  const setContactDetailFun = useCallback(
    (key, value, contactType) => {
      setContactDetailsData((t) => ({ ...t, [contactType]: { ...t[contactType], [key]: value } }));
    },
    [setContactDetailsData]
  );

  const nameWrapperWithChildren = (name, children) => (
    <Box>
      {name && (
        <Box className="fs14 fs-bold" pb={2} pt={1}>
          {' '}
          {name}{' '}
        </Box>
      )}
      {children}
    </Box>
  );

  const setOpenLInkLayoutModalFun = useCallback(() => {
    setOpenLInkLayoutModal((t) => !t);
  }, [setOpenLInkLayoutModal]);

  const handleSwitch = () =>
    setGeneralDetailsData((t) => ({ ...t, is_shipping_address_same: !t.is_shipping_address_same }));

  const returnTabContent = () => {
    switch (selectedTab) {
      case 0:
        return nameWrapperWithChildren(
          CONTENT.GENERAL_DETAILS,
          <GeneralDetails
            isFacilityActive={facilityActive}
            switchValue={generalDetailsData.is_shipping_address_same || false}
            handleSwitch={handleSwitch}
            detailsMode
            setEditedTabsFun={setEditedTabsFun}
            data={generalDetailsData}
            isNextEntered={false}
            setData={setDataFun}
            setDataCallBack={setGeneralDetailsData}
          />
        );
      case 1:
        return nameWrapperWithChildren(
          CONTENT.CONTACT_DETAILS,
          <ContactDeatils
            error={inValidData}
            data={contactDetailsData}
            isNextEntered={false}
            setEditedTabsFun={setEditedTabsFun}
            setData={setContactDetailFun}
            fromEdit
          />
        );
      case 2:
        return nameWrapperWithChildren(
          CONTENT.TAX_DETAILS,
          <TaxDetails
            inValidData={inValidData}
            fromEdit
            data={taxData}
            isNextEntered={false}
            setEditedTabsFun={setEditedTabsFun}
            setData={setDataFun}
            setDataCallBack={setTaxData}
          />
        );
      case 3:
        return nameWrapperWithChildren(
          facilityDetails?.layout_id ? '' : CONTENT.LAYOUT_SCHEMA,
          <LayoutSchemaDetails
            data={layoutSchemaData}
            setData={setLayoutSchemaData}
            setOpenLInkLayoutModal={setOpenLInkLayoutModalFun}
            openLinkLayoutModal={openLinkLayoutModal}
            layoutIdFromSaveLayout={layoutIdFromSaveLayout}
            CONTENT={CONTENT}
          />
        );
      default:
        return nameWrapperWithChildren(
          '',
          <FacilityEntityDetails
            layout_id={facilityDetails.layout_id || layoutIdFromSaveLayout}
            layout_name={facilityDetails.layout_name}
            facility_code={
              facilityDetails.facility_code || generalDetailsData.facility_code || null
            }
          />
        );
    }
  };

  const saveChangesFonfirmModalFun = useCallback(() => {
    setSaveChangesConfirmModal((t) => !t);
  }, [setSaveChangesConfirmModal]);

  const resetData = () => {
    layoutIdFromSaveLayout = '';
    dispatch(layoutSaveReset());
    dispatch(setLayoutCreateData({ layout_schema: [] }));
    dispatch(getPincodeDeatilsReset());
    dispatch(getLayoutDetailReset());
  };

  useEffect(() => {
    const refreshCallnack = () => 'Are you really want to perform the action?';
    window.onbeforeunload = refreshCallnack;
    dispatch(getStateCountryListLoad());
    return () => {
      layoutIdFromSaveLayout = '';
      dispatch(saveFacilityReset());
      dispatch(layoutSaveReset());
      dispatch(setLayoutCreateData({ layout_schema: { children: [] } }));
      dispatch(getPincodeDeatilsReset());
      dispatch(getLayoutDetailReset());
      dispatch(getEntityListReset());
      dispatch(getLayoutListReset());
      resetData();
      window.removeEventListener('onbeforeunload', refreshCallnack);
    };
  }, [dispatch]);

  useEffect(() => {
    if (facilityId) {
      dispatch(getFacilityDetailsLoad(facilityId));
    }
    dispatch(getLayoutListLoad({ page_number: 0, is_suggestion: true }));
  }, [facilityId, dispatch]);

  useEffect(() => {
    if (taxDetailsSuccess) {
      const temp = { registered_name: '', gst_number: '', tin: '', pan: '' };
      Object.keys(taxDetailsSuccess).forEach((name) => {
        const t = taxKeyMapping[name] || name;
        temp[t] = taxData[t] || '';
      });
      setTaxData(temp);
    }
  }, [taxDetailsSuccess]);

  useEffect(() => {
    if (generalDetailsData.is_shipping_address_same) {
      const { country, pin_code, city, state, address_line1, address_line2 } = generalDetailsData;
      const temp = {
        sPcountry: country,
        sPpin_code: pin_code,
        sPaddress_line1: address_line1,
        sPaddress_line2: address_line2,
        sPCity: city,
        sPstate: state
      };
      setGeneralDetailsData({ ...generalDetailsData, ...temp });
    }
  }, [generalDetailsData.is_shipping_address_same]);

  useEffect(() => {
    if (facilityDetails) {
      const {
        facility_code = '',
        type = '',
        display_name = '',
        legal_name = '',
        logo_url = '',
        billing_address = '',
        shipping_address = '',
        is_shipping_address_same,
        tax_details = {},
        primary_contact = {},
        secondary_contact = {},
        legal_owner = ''
      } = facilityDetails;
      const {
        address_line1 = '',
        address_line2 = '',
        city = '',
        state = '',
        country = '',
        pin_code = ''
      } = billing_address;
      const generalDetailstemp = {
        legal_owner,
        image: logo_url || '',
        type: type || '',
        display_name: display_name || '',
        legal_name: legal_name || '',
        facility_code: facility_code || '',
        country: country || '',
        pin_code: pin_code ?? '',
        city: city || '',
        state: state || '',
        sPCity: shipping_address.city || '',
        sPstate: shipping_address.state || '',
        address_line1: address_line1 || '',
        address_line2: address_line2 || '',
        sPcountry: shipping_address.country || '',
        sPpin_code: shipping_address.pin_code ?? '',
        sPaddress_line1: shipping_address.address_line1 || '',
        sPaddress_line2: shipping_address.address_line2 || '',
        is_shipping_address_same
      };
      setGeneralDetailsData({ ...generalDetailstemp });
      setTaxData(tax_details);
      setContactDetailsData((t) => ({
        primary_contact: { ...t.primary_contact, ...primary_contact },
        secondary_contact: { ...t.secondary_contact, ...secondary_contact }
      }));
      if (facilityDetails.status === 'ACTIVE') {
        setFacilityActive(true);
      } else {
        setFacilityActive(false);
      }
      const openLayout = getQueryParam(location.search.replace('?', ''), 'openLayout');
      if (openLayout) {
        setSelectedTab(3);
      }
    }
  }, [facilityDetails, location.search]);

  useEffect(() => {
    if (facilitySaveError) {
      if (!saveChange) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            subHeading: facilitySaveError?.meta?.displayMessage || facilitySaveError,
            severity: 'error'
          })
        );
      }
      if (facilityDetails.status === 'ACTIVE') {
        setFacilityActive(true);
      } else {
        setFacilityActive(false);
      }
      dispatch(saveFacilityReset());
    }
    if (facilitySave) {
      if (layoutIdFromSaveLayout) {
        facilityDetails.layout_id = layoutIdFromSaveLayout;
        facilityDetails.status = 'CREATED';
      }
      if (facilityActive) {
        facilityDetails.status = 'ACTIVE';
      }
      if (!saveChange) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: facilitySave.meta.displayMessage,
            severity: 'success'
          })
        );
      }
      dispatch(saveFacilityReset());
      setEditedTabs({ generalDetailsData: false, contactDetailsData: false, taxData: false });
    }
  }, [facilitySaveError, facilitySave, saveChange, dispatch, facilityDetails, facilityActive]);

  useEffect(() => {
    if (pincodeDetails1) {
      if (pincodeDetails1.result) {
        const { city, state } = pincodeDetails1.result;
        setGeneralDetailsData((t) => ({ ...t, city, state }));
      } else {
        setGeneralDetailsData((t) => ({ ...t, city: '', state: '' }));
      }
    }
  }, [pincodeDetails1]);
  useEffect(() => {
    if (pincodeDetails2) {
      if (pincodeDetails2.result) {
        const { city, state } = pincodeDetails2.result;
        setGeneralDetailsData((t) => ({ ...t, sPCity: city, sPstate: state }));
      } else {
        setGeneralDetailsData((t) => ({ ...t, sPCity: '', sPstate: '' }));
      }
    }
  }, [pincodeDetails2]);
  useEffect(() => {
    if (pincodeDetails1Fail) {
      setGeneralDetailsData((t) => ({ ...t, city: '', state: '' }));
    }
  }, [pincodeDetails1Fail]);
  useEffect(() => {
    if (pincodeDetails2Fail) {
      setGeneralDetailsData((t) => ({ ...t, sPCity: '', sPstate: '' }));
    }
  }, [pincodeDetails2Fail]);

  useEffect(() => {
    if (generalDetailsData.country) {
      dispatch(getTaxDeatilsLoad(generalDetailsData.country));
    }
  }, [generalDetailsData.country, dispatch]);

  const generategeneralDetailsData = () => {
    const {
      sPCity,
      image,
      type,
      country,
      sPcountry,
      sPstate,
      city,
      state,
      facility_code,
      is_shipping_address_same,
      display_name,
      legal_name,
      legal_owner,
      pin_code,
      address_line1,
      address_line2,
      sPpin_code,
      sPaddress_line1,
      sPaddress_line2
    } = generalDetailsData;
    const payload = {
      billing_address: { address_line1, address_line2, city, country, pin_code, state },
      facility_code,
      display_name,
      legal_name,
      legal_owner,
      is_shipping_address_same,
      logo_url: image || uploadLogoSuccess?.logoUrl || '',
      shipping_address: {
        address_line1: sPaddress_line1,
        address_line2: sPaddress_line2,
        city: sPCity,
        country: sPcountry,
        pin_code: sPpin_code,
        state: sPstate
      }
    };
    if (type) {
      payload.type = type;
    }
    return payload;
  };

  const generatePayloadForSubmit = () => {
    let payload = {};
    if (editedTabs.contactDetailsData) {
      payload = { ...payload, ...contactDetailsData };
    }
    if (editedTabs.taxData) {
      payload = { ...payload, tax_details: { ...taxData } };
    }
    if (editedTabs.generalDetailsData) {
      payload = { ...payload, ...generategeneralDetailsData() };
    }
    return payload;
  };

  useEffect(() => {
    if (layoutSaveData?.data?.layout_id) {
      const { layout_id } = layoutSaveData.data;
      layoutIdFromSaveLayout = layout_id;
      let payload = { facility_id: facilityDetails.facility_id, layout_id };
      if (!facilityActive) {
        payload.status = 'CREATED';
      }
      payload = { ...payload, ...generatePayloadForSubmit() };
      dispatch(saveFacilityLoad(payload));
      dispatch(layoutSaveReset());
      dispatch(getLayoutDetailReset());
    }
  }, [
    layoutSaveData,
    editedTabs,
    dispatch,
    facilityActive,
    contactDetailsData,
    taxData,
    facilityDetails
  ]);

  const updateFacilityFun = (payload) => {
    dispatch(saveFacilityLoad({ facility_id: facilityDetails?.facility_id, ...payload }));
  };

  const onChangeFacilityActive = () => {
    if (!facilityActive) {
      updateFacilityFun({ status: 'ACTIVE' });
      setFacilityActive(!facilityActive);
    }
  };

  const onSubmitSave = (reset) => {
    saveChangesFonfirmModalFun();
    if (!reset) {
      saveChangesFonfirmModalFun();
    }
    let payload = {};
    if (selectedTab === 0) {
      payload = generategeneralDetailsData();
      payload = { ...payload, ...generatePayloadForSubmit() };
      updateFacilityFun(payload);
    }
    if (selectedTab === 1) {
      const { result, invaliddataTemp } = allContactDataFilled(contactDetailsData);
      setinValidData(invaliddataTemp);
      payload = { ...contactDetailsData };
      payload = { ...payload, ...generatePayloadForSubmit() };
      if (result) {
        updateFacilityFun(payload);
      }
    }
    if (selectedTab === 2) {
      const { result, invaliddataTemp } = taxregexValidation(taxDetailsSuccess, taxData);
      setinValidData(invaliddataTemp);
      payload = { tax_details: { ...taxData } };
      payload = { ...payload, ...generatePayloadForSubmit() };
      if (result) {
        updateFacilityFun(payload);
      }
    }
    if (selectedTab === 3) {
      if (facilityDetails?.layout_id) {
        layoutCreateData.layout_schema = {
          storage: false,
          children: layoutCreateData.layout_schema
        };
        layoutCreateData.facility_id = facilityDetails?.facility_id;
        dispatch(layoutSaveLoad({ ...layoutCreateData }));
        layoutCreateData.layout_schema = layoutCreateData.layout_schema.children;
      } else if (layoutCreateData?.layout_id) {
        setOpenLInkLayoutModal(true);
      }
    }
  };

  if (facilityDetailsLoading) {
    return <Spinner className="text-center" />;
  }
  return (
    <div className={classes.root}>
      {saveChangeConfirmModal && (
        <SaveChangesFonfirmModal
          onSubmit={onSubmitSave}
          open={saveChangeConfirmModal}
          handleClose={saveChangesFonfirmModalFun}
          CONTENT={CONTENT}
        />
      )}
      <Topbar pagename="Facility">
        <div>{generalDetailsData.display_name}</div>
        <TopbarRightElement />
      </Topbar>
      {facilityDetailsError ? (
        <Box textAlign="center" pt={3}>
          {' '}
          {facilityDetailsError.meta?.displayMessage}
        </Box>
      ) : (
        <Box>
          <Box display="flex" p={1} pl={3} pr={3} className="border-bottom-ee" alignItems="center">
            <Box className="fs20" flex={1}>
              {generalDetailsData?.facility_code}-{generalDetailsData.display_name}{' '}
              <LkChip
                label={facilityActive ? CONTENT.ACTIVE_UPPERCASE : facilityDetails?.status}
                type={facilityActive ? 'success' : 'warning'}
                className="mr-l8"
              />{' '}
            </Box>
            <Box display="flex" flex={0.5} justifyContent="space-around">
              <Box>
                {' '}
                <Box className="fs10 text-99 "> {CONTENT.CREATED} </Box>{' '}
                <Box className="fs14 text-66 mr-t2">
                  {' '}
                  {facilityDetails?.created_at &&
                    convertDateFormat(facilityDetails?.created_at, 'shortDate')}
                </Box>{' '}
              </Box>
              <Box>
                {' '}
                <Box className="fs10 text-99"> {CONTENT.LAST_MODIFIED} </Box>{' '}
                <Box className="fs14 text-66 mr-t2">
                  {' '}
                  {facilityDetails?.updated_at &&
                    convertDateFormat(facilityDetails?.updated_at, 'shortDate')}
                </Box>{' '}
              </Box>
            </Box>
            <Box display="flex" ml={3}>
              <FormControlLabel
                control={
                  <Switch
                    name="checkedB"
                    checked={facilityActive}
                    onChange={onChangeFacilityActive}
                    color="primary"
                  />
                }
                label={CONTENT.ACTIVATE_FACILITY}
              />
              <Box ml={1.5}>
                <Button
                  disabled={!!(layoutSaveProccessing || facilitySaveLoading)}
                  variant="contained"
                  color="primary"
                  onClick={saveChangesFonfirmModalFun}
                  className={classes.btn}
                >
                  {' '}
                  {CONTENT.SAVE}{' '}
                </Button>
              </Box>
            </Box>
          </Box>
          <Box style={{ position: 'relative' }}>
            <MiniDrawer
              selectedTab={selectedTab}
              setSelectedTab={setSelectdTabFun}
              noPadding={selectedTab === 4}
              CONTENT={CONTENT}
            >
              {returnTabContent()}
            </MiniDrawer>
          </Box>
        </Box>
      )}
    </div>
  );
};

export default FacilityDetails;
