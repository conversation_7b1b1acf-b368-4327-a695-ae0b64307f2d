import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';

import { Box, Button, Typography } from '@mui/material';

import LkInput from 'components/MaterialUi/LkInput';

import { setPageTitle } from 'redux/reducers/settings';
import { LOCALISATION } from 'redux/reducers/localisation';
import { CUSTOMER, getCustomerByIdReset } from 'redux/reducers/customer';

import { getConsulKeyValue } from 'redux/reducers/consul';
import GeneralForm from './components/GeneralForm';
import BillingForm from './components/BillingForm';
import ShippingForm from './components/ShippingForm';
import ContactForm from './components/ContactForm';
import AccoutingForm from './components/AccoutingForm';
import ConfirmationModal from './components/ConfirmationModal';

const CustomerFormTitle = styled(Typography)`
  padding-block: 1.5rem;
`;

const CustomerFieldContainer = styled(Box)`
  display: flex;
  flex-wrap: wrap;
  gap: 1.75rem;
`;

const CustomerTextField = styled(LkInput)`
  width: 312px;
`;

const CustomerFormButton = styled(Button)`
  width: 250px;
`;

const CustomerFormsNew = ({
  handleChange,
  errors,
  onSubmit,
  formState,
  setConfirmModal,
  confirmModal,
  modalSubmit
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { action } = useParams();
  const doCountryList = useSelector((state) => state.consul.doCountry.data);

  const { isLoading } = useSelector((state) => state[CUSTOMER].isCreatedOrUpdated);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);

  const createOrEditTitle = action === 'create' ? 'Create New Customer' : 'Edit Customer';

  useEffect(() => {
    dispatch(setPageTitle({ title: createOrEditTitle }));
    dispatch(getConsulKeyValue(['doCountry']));
  }, []);

  return (
    <Box
      display="flex"
      margin={1.5}
      borderRadius={5}
      flexDirection="column"
      height="95%"
      border="1px solid #ccc"
      border-radius="8px"
      overflow="hidden"
      bgcolor="#FFFFFF"
    >
      {/* Scrollable content area */}
      <form
        style={{
          flexGrow: 1,
          overflowY: 'auto',
          padding: '16px'
        }}
      >
        <GeneralForm
          CustomerFieldContainer={CustomerFieldContainer}
          CustomerFormTitle={CustomerFormTitle}
          CustomerTextField={CustomerTextField}
          handleChange={handleChange}
          errors={errors}
          formState={formState}
        />
        <BillingForm
          CustomerFieldContainer={CustomerFieldContainer}
          CustomerFormTitle={CustomerFormTitle}
          CustomerTextField={CustomerTextField}
          formState={formState}
          doCountryList={doCountryList}
          handleChange={handleChange}
          errors={errors}
        />
        <ShippingForm
          CustomerFieldContainer={CustomerFieldContainer}
          CustomerFormTitle={CustomerFormTitle}
          CustomerTextField={CustomerTextField}
          formState={formState}
          doCountryList={doCountryList}
          handleChange={handleChange}
          errors={errors}
        />
        <ContactForm
          CustomerFieldContainer={CustomerFieldContainer}
          CustomerFormTitle={CustomerFormTitle}
          CustomerTextField={CustomerTextField}
          handleChange={handleChange}
          formState={formState}
          errors={errors}
        />
        <AccoutingForm
          CustomerFieldContainer={CustomerFieldContainer}
          CustomerFormTitle={CustomerFormTitle}
          CustomerTextField={CustomerTextField}
          handleChange={handleChange}
          formState={formState}
          errors={errors}
        />
      </form>

      {/* Fixed button container */}
      <Box
        sx={{
          borderTop: '1px solid #ccc',
          padding: '16px',
          backgroundColor: '#fff',
          textAlign: 'right'
        }}
      >
        <Box display="flex" justifyContent="flex-end" alignItems="center" gap={2.5}>
          <CustomerFormButton
            type="reset"
            onClick={() => {
              navigate('/customer/list');
              dispatch(getCustomerByIdReset());
            }}
            color="primary"
            variant="outlined"
          >
            {CONTENT.CANCEL}
          </CustomerFormButton>
          <CustomerFormButton onClick={onSubmit} color="primary" variant="contained">
            {CONTENT.CONFIRM}
          </CustomerFormButton>
        </Box>
      </Box>
      <ConfirmationModal
        open={confirmModal}
        isEdit={action === 'edit'}
        handleClose={() => setConfirmModal(false)}
        handleSubmit={modalSubmit}
        name={formState.name}
        loading={isLoading}
      />
    </Box>
  );
};

export default CustomerFormsNew;
