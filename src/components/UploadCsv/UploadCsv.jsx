import React, { useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import LkModal from '../Modal/Modal';
import { SelectFile, UploadingFile, UploadCsvError } from './component';
import './uploadCsv.scss';



// States :-
// 1. Select File
// 2. Uploading File
// 3. Import Proceed Successfully
// 4. Problem with CSV(Download Error Report)
// 5. Problem with CSV(Download Sample CSV)

const UploadCsv = props => {
  const { open, onClose, sampleCSV = [], checkedItemLabel, uploadCsvPass, failDataMessage, downloadErrorReport, handleSubmit, resetState, sampleFileName, localUpload = false, importLoading, subTitle, showSampleArray = true, showSelectedFacilityText = true } = props;
  const [currState, setCurrState] = useState(1);
  const [file, setFile] = useState(null);
  // const [checkedItems, setCheckedItems] = useState(false);
  const [uploaded, setUploaded] = useState(false);

  useEffect(() => {
    if (failDataMessage)
      setCurrState(4);
  }, [failDataMessage]);
  useEffect(() => {
    setCurrState(1);
    setUploaded(false);
  }, [resetState]);

  useEffect(() => {
    if (uploadCsvPass || localUpload)
      setUploaded(true);
  }, [uploadCsvPass, localUpload]);

  const selectFiles = event => {
    const { selectFiles } = props;
    const file = event.target.files[0];
    setFile(file);
    setCurrState(2);
    selectFiles(file)
  };
  const handleCancelFile = () => {
    setFile(null);
    setCurrState(1);
  };

  const handleClose = () => {
    setCurrState(1);
    onClose(false);
  };


 
  switch (currState) {
    case 1:
      return (<SelectFile
        subTitle={subTitle || 'Please select the CSV file to import PID details'}
        handleClose={handleClose}
        open={open}
        sampleCSV={sampleCSV}
        checkedItemLabel={checkedItemLabel}
        selectFiles={selectFiles}
        sampleFileName={sampleFileName}
        showSampleArray={showSampleArray}
        showSelectedFacilityText={showSelectedFacilityText}
      />);
    case 2:
      return (<UploadingFile
        uploading={!uploaded} file={file} handleClose={() => handleClose()}
        open={open} cancelFile={() => handleCancelFile()} importFile={handleSubmit} localUpload={localUpload} importLoading={importLoading} />);
    case 3:
      return (
        <LkModal open={open} title="Import Processed Successfully" subTitle="The file has been imported successfully" handleClose={() => handleClose()}>
          <div className="text-center">
            <Button style={{ borderRadius: 8 }} variant="contained" color="primary" onClick={() => handleClose()}>OK</Button>
          </div>
        </LkModal>);
    case 4:
      return (<UploadCsvError message={failDataMessage} open={open} handleClose={() => handleClose()} downloadReport={() => downloadErrorReport()} />)
  }
};

export default UploadCsv;
