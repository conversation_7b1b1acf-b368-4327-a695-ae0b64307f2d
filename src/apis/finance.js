import { generatePayloadForSearchAPI } from 'utils/helpers';
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getDOListApi = (payload) => {
  const { page, size, sortBy, sortOrder, filters } = payload;
  let searchTerms = '';

  if (filters.status.length === 0) {
    searchTerms = 'status.in:CREATED,APPROVED,REJECTED';
  } else if (filters.status.length === 1) {
    searchTerms = `status.in:${filters.status}`;
  } else {
    const joinedFilters = filters.status.join(',');
    searchTerms = `status.in:${joinedFilters}`;
  }

  if (filters.doNumber) {
    searchTerms += `___incrementId.like:${filters.doNumber}`;
  }
  if (filters.poNumber) {
    searchTerms += `___poNumber.like:${filters.poNumber}`;
  }
  if (filters.createdAt && filters.createdAt !== 'ALL PERIOD') {
    const date = generatePayloadForSearchAPI([{ key: 'CREATED', value: filters.createdAt }]);
    const { CREATEDfrom, CREATEDto } = date;
    const startDate = CREATEDfrom.replace(' ', 'T');
    const endDate = CREATEDto.replace(' ', 'T');
    searchTerms += `___createdAt.gte:${startDate}Z___createdAt.lte:${endDate}Z`;
  }

  return tokenInstance().get(config.finance.getDOList, {
    params: { page, size, sortBy, sortOrder, searchTerms }
  });
};

export const getDODetailsApi = ({ empCode, incrementId }) =>
  tokenInstance({ userName: empCode }).post(`${config.finance.getDODetails}/${incrementId}`);

export const approveDOApi = ({ empCode, id }) =>
  tokenInstance({ userName: empCode }).post(
    `${config.finance.approveAndRejectDO}/approve/${id}`
  );

export const rejectDOApi = ({ empCode, rejectReason, id }) =>
  tokenInstance({
    userName: empCode,
    status: 'REJECTED',
    cancellationReason: rejectReason
  }).post(`${config.finance.approveAndRejectDO}/reject/${id}`);
