import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import { isEmpty } from 'utils/lodash';

import { InfoStrip } from 'components/common';
import Spinner from 'components/Spinner/Spinner';
import ReassignUserModal from 'components/GRNListing/ReassignUserModal';
import ReassignSuccessModal from 'components/GRNListing/ReassignSuccessModal';
import GrnDetailsModal from 'components/GrnDetails/GrnDetailsModal';
import TabsContainer from 'components/GrnDetails/TabsContainer';
import BeginIqcModal from 'components/InwardQC/BeginIqcModal';

import { required } from 'utils/validation';
import {
  convertDateFormat,
  roundUptoFixedDigits,
  fileDownload,
  getCurrencySumbol
} from 'utils/helpers';

import useKeyboardShortcut from 'common/useKeyboardShortcut';

import { toastOpen } from 'redux/actionCreators/toast';
import { reassignGRNReset } from 'redux/actionCreators/grnListing';
import { getGrnSummaryLoad, grnCloseLoad } from 'redux/actionCreators/grn';
import { getIQCHeaderInfoLoad } from 'redux/reducers/inwardQC';
import {
  getGrnPidsLoad,
  exportGrnBarCodeDetailsLoad,
  exportGrnBarCodeDetailsReset,
  resetGrnDetailsReducer,
  getgrnPdfDownloadLoad,
  getgrnPdfDownloadReset
} from 'redux/actionCreators/grnDetails';
import { LOCALISATION } from 'redux/reducers/localisation';
import { printMultiplePutawayLoad, printMultiplePutawayReset } from 'redux/actionCreators/putaway';
import MoreItem from 'components/common/MoreItem';
import GRNPrintConfirmModal from 'components/PrintConfirmModal/GRNPrintConfirmModal';

const styles = makeStyles()((theme) => ({
  root: {
    flexGrow: 1,
    borderBottom: '1px solid #DDDDDD'
  },
  container: {
    backgroundColor: 'white',
    minHeight: 550
  },
  iqcPadding: {
    paddingRight: 0
  },
  tabLabel: {
    textTransform: 'none',
    color: theme.palette.primary,
    fontSize: '14px',
    fontWeight: 'normal'
  },
  tab: {
    minWidth: '100px'
  },
  tabContent: {
    borderRadius: '8px',
    border: '1px solid #DDDDDD'
  },
  tabBody: {
    maxHeight: '340px',
    overflowY: 'scroll'
  },
  btn: {
    borderRadius: 8
  },
  reAssignBtn: {
    marginLeft: 8,
    borderRadius: 8
  }
}));

const defaultReqObj = {
  isReset: true,
  payload: {
    page_num: 0,
    page_size: 30,
    sort_by: 'pid',
    sort_order: 'DESC'
  }
};

let infoStripTemp = [];
const modalData = {};

const GrnDetails = () => {
  const navigate = useNavigate();
  const { grnCode, iqc } = useParams();
  const dispatch = useDispatch();

  const [printValue, setPrintValue] = useState(0);
  const [grnDetailsModalData, setGrnDetailsModalData] = useState({});
  const [grnSummaryLoading, setGrnSummaryLoading] = useState(true);
  const [openDetailsModal, setOpenDetailsModal] = useState(false);
  const [assignTo, setAssignTo] = useState('not Found');
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const [openPrintModal, setOpenPrintModal] = useState({ open: false, print: false });
  const [vendorInfoStripData, setVendorInfoStripData] = useState([]);
  const [isIqc, setIsIqc] = useState(iqc === 'iqc');
  const [beginIqcModal, setBeginIqcModal] = useState(false);

  const {
    getGrnPids,
    getGrnBlockedPidsLoading,
    getGrnPidsLoading,
    exportGrnBarCodeDetails,
    exportGrnBarCodeDetailsError,
    getGrnPidsError,
    pdfDownloadSuccess,
    pdfDownloadError,
    getGrnBlockedPidsError
  } = useSelector((state) => state.grnDetails);
  const { grnSummary, grnSummaryFail, closeGrnSuccess, closeGrnFail } = useSelector(
    (state) => state.grn
  );
  const { multiplePutaway } = useSelector((state) => state.putaway);
  const { reassignSuccess, reassignFail } = useSelector((state) => state.grnListing);
  const iqcTableData = useSelector((state) => state.inwardQC.iqcTableData);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_DETAIL);

  const GrnStatus = {
    created: { text: CONTENT.CREATED, type: 'parisGreen' },
    pending: { text: CONTENT.PENDING, type: 'brightYellow' },
    closed: { text: CONTENT.CLOSED, type: 'grey2' },
    in_progress: { text: CONTENT.PENDING, type: 'brightYellow' },
    IQC_IN_PROGRESS: { text: CONTENT.PENDING, type: 'brightYellow' }
  };

  const closeOpenPrintModal = useCallback(
    () => setOpenPrintModal((t) => ({ ...t, open: false })),
    [setOpenPrintModal]
  );

  const setValueFun = useCallback((e) => setAssignTo(e.target.value), [setAssignTo]);

  const isPutAwayCodeAvailable = !isEmpty(getGrnPids?.result?.data?.putaway_list);

  const printFun = useCallback(() => {
    if (printValue === 'GRN') {
      dispatch(getgrnPdfDownloadLoad(grnCode));
    } else if (printValue === 'PUTAWAY' && isPutAwayCodeAvailable) {
      const putawayCodes = getGrnPids.result.data.putaway_list;
      dispatch(printMultiplePutawayLoad({ putawayCodes }));
    }
  }, [dispatch, grnCode, printValue]);

  const downloadShortcutFun = useCallback(() => {
    dispatch(exportGrnBarCodeDetailsLoad(grnCode));
  }, [dispatch, grnCode]);

  const saveAndCloseFun = () => {
    dispatch(grnCloseLoad(grnCode));
    setOpenDetailsModal(false);
  };

  const shortcutFunToOpenModal = useCallback(
    () => setOpenDetailsModal(true),
    [setOpenDetailsModal]
  );

  useKeyboardShortcut([17, 69], downloadShortcutFun);

  useKeyboardShortcut([18, 68], shortcutFunToOpenModal);

  useKeyboardShortcut([17, 80], printFun);

  const goToPoNum = (po_id) => {
    window.open(`${window.location.origin}/po/detail?poNum=${po_id}&fromPo=true`, '_blank');
  };

  const goToUnicommGrn = (pid, ugrn) =>
    window.open(
      `https://lenskart.unicommerce.com/purchaseOrder/grns?orderCode=${pid}&grnCode=${ugrn}`,
      '_blank'
    );

  const goToInvoice = (invoiceRefNumber) => {
    window.open(
      `${window.location.origin}/invoice/view?invoiceRefNumber=${invoiceRefNumber}`,
      '_blank'
    );
  };

  useEffect(() => {
    if (pdfDownloadSuccess) {
      const { data } = pdfDownloadSuccess.response;
      if (data === '') {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: CONTENT.NO_DATA_FOUND,
            severity: 'error'
          })
        );
      }
      setOpenPrintModal({ open: false });
      fileDownload(data, `GRN-NEXS-${grnCode}`, 'pdf');
    }
    if (pdfDownloadError) {
      if (pdfDownloadError?.data?.meta?.displayMessage) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: `${CONTENT.USER_NOT_HAVE_PERMISSION} 
            : /nexs/api/grn/v1/master/grn-pdf/${grnCode}`,
            severity: 'error'
          })
        );
      }
      setOpenPrintModal({ open: false });
      dispatch(getgrnPdfDownloadReset());
    }
    if (multiplePutaway.isSuccess || multiplePutaway.isError) {
      setOpenPrintModal({ open: false });
      dispatch(printMultiplePutawayReset());
    }
  }, [pdfDownloadSuccess, pdfDownloadError, dispatch, grnCode, multiplePutaway]);

  useEffect(() => {
    if (grnSummaryFail) {
      setGrnSummaryLoading(false);
    }
  }, [grnSummaryFail]);

  useEffect(() => {
    if (grnSummary) {
      const {
        total_quantity,
        total_failed,
        created_at,
        total_scanned,
        pids,
        vendor,
        po_id,
        grn_status,
        created_by,
        unicom_grn_code,
        send_to_party,
        handover_party,
        vendor_invoice_num,
        b2b_invoice_date,
        bill_of_entry_amount,
        currency,
        bill_of_entry_date,
        invoice_date,
        total_received_amount,
        total_rejected_amount
      } = grnSummary;
      const invoice_ref_num = getGrnPids?.result?.data?.invoice_ref_num;
      const tempHeader = [
        {
          key: CONTENT.UNICOMM_GRN,
          value: unicom_grn_code,
          className: 'text-primary cursor-pointer',
          onClick: () => {
            goToUnicommGrn(po_id, unicom_grn_code);
          }
        },
        {
          key: CONTENT.VENDOR_INV_NUMBER,
          value: vendor_invoice_num,
          className: 'text-primary cursor-pointer',
          onClick: () => {
            goToInvoice(invoice_ref_num);
          }
        }
      ];
      const isDataPresent = infoStripTemp.some(({ key }) => key === CONTENT.UNICOMM_GRN);

      if (!isDataPresent && invoice_ref_num) {
        if (infoStripTemp.length === 4) {
          infoStripTemp = [...infoStripTemp, ...tempHeader];
        } else {
          infoStripTemp = [...tempHeader, ...infoStripTemp];
        }
      }

      setVendorInfoStripData(infoStripTemp);

      const convertedCurrency = getCurrencySumbol(currency);

      const temp = {
        vendorName: {}
      };
      temp.chipBoxData = (pids || [])
        .filter((t) => t.status === 'FAILED')
        .map((t) => ({ title: t.pid, type: 'error' }));
      temp.subtitleInfoStripData = [
        {
          title: total_scanned || 0,
          subtitle: CONTENT.TOTAL_SCANNED,
          subTitleClassName: 'text-99 mr-t5'
        },
        {
          title: total_scanned - total_failed || 0,
          subtitle: CONTENT.ACCEPTED,
          subTitleClassName: 'text-99 mr-t5'
        },
        {
          title: total_failed || 0,
          subtitle: CONTENT.REJECTED,
          titleColor: 'error.main',
          titleClassName: 'text-red',
          subTitleClassName: 'text-99 mr-t5'
        },
        {
          title: pids?.length || 0,
          subtitle: CONTENT.PIDS,
          titleColor: 'warning.main',
          titleClassName: 'text-bright-yellow',
          subTitleClassName: 'text-99 mr-t5'
        }
      ];
      temp.vendorName.name = vendor;
      temp.infoTableData = [
        {
          key: CONTENT.PO_NO,
          value: po_id,
          link: `${window.location.origin}/po/detail?poNum=${po_id}&fromPo=true`
        },
        { key: CONTENT.STATUS, value: grn_status },
        { key: CONTENT.CREATED_BY, value: created_by },
        {
          key: CONTENT.BILL_OF_ENTRY_AMOUNT,
          value: `${convertedCurrency}${roundUptoFixedDigits(bill_of_entry_amount || 0)}`
        },
        {
          key: CONTENT.INVOIVE_REF_NO,
          value: invoice_ref_num,
          link: `${window.location.origin}/invoice/view?invoiceRefNumber=${invoice_ref_num}`
        },
        {
          key: CONTENT.BILL_OF_ENTRY_DATE,
          value: bill_of_entry_date ? convertDateFormat(bill_of_entry_date, 'shortDate') : '-'
        },
        { key: CONTENT.BILL_OF_ENTRY_NO, value: created_by },
        { key: CONTENT.ASSIGNED_TO, value: getGrnPids?.result?.data?.assigned_to },
        { key: CONTENT.CREATED_ON, value: convertDateFormat(created_at, 'shortDate') },
        { key: CONTENT.RECEIVED_UNITS, value: total_quantity },
        {
          key: CONTENT.RECEIVED_AMOUNTS,
          value: `${convertedCurrency}${roundUptoFixedDigits(total_received_amount || 0)}`
        },
        { key: CONTENT.REJECTED_UNITS, value: total_failed },
        {
          key: CONTENT.REJECTED_AMOUNT,
          value: `${convertedCurrency}${roundUptoFixedDigits(total_rejected_amount || 0)}`
        }
      ];
      temp.inputs = {
        VendorInvoiceNo: vendor_invoice_num || '',
        VendorInvoiceDate: invoice_date || '',
        B2BInvoiceDate: b2b_invoice_date || '',
        HandoverBy: handover_party || '',
        SendtoParty: send_to_party || ''
      };

      setGrnDetailsModalData(temp);
      setGrnSummaryLoading(false);
    }
  }, [grnSummary, getGrnPids]);

  useEffect(() => {
    if (getGrnPids && getGrnPids.result && getGrnPids.result.data) {
      const { created_on, grn_code, vendor_name, grn_status, po_id, assigned_to, invoice_ref_num } =
        getGrnPids.result.data;
      const tempHeader = [
        {
          key: CONTENT.GRN,
          className: 'text-primary cursor-pointer',
          value: grn_code,
          status: GrnStatus[grn_status]?.text,
          type: GrnStatus[grn_status]?.type,
          onClick: () => {
            setOpenDetailsModal(true);
          }
        },
        { key: CONTENT.CREATED_ON, value: convertDateFormat(created_on, 'shortDate') },
        { key: CONTENT.VENDOR, value: vendor_name },
        {
          key: CONTENT.PO,
          value: po_id,
          className: 'text-primary cursor-pointer',
          onClick: () => {
            goToPoNum(po_id);
          }
        }
      ];
      modalData.created_by = assigned_to;
      modalData.grn_code = grn_code;
      modalData.invoice_num = invoice_ref_num;

      const infotempData = infoStripTemp.some(({ key }) => key === CONTENT.GRN);

      if (!infotempData) {
        if (infoStripTemp.length === 2) {
          infoStripTemp = [...tempHeader, ...infoStripTemp];
        } else {
          infoStripTemp = [...infoStripTemp, ...tempHeader];
        }
      }

      infoStripTemp.splice(0, 1, {
        key: CONTENT.GRN,
        className: 'text-primary cursor-pointer',
        value: grn_code,
        status: GrnStatus[grn_status]?.text,
        type: GrnStatus[grn_status]?.type,
        onClick: () => {
          setOpenDetailsModal(true);
        }
      });

      setVendorInfoStripData(infoStripTemp);
    }
  }, [getGrnPids]);

  useEffect(() => {
    if (!isEmpty(iqcTableData?.headerInfo)) {
      const { samplingCount, qcPassCount, qcFailCount, pendingQc } = iqcTableData.headerInfo;
      const temp = [
        {
          key: CONTENT.PENDING_QC,
          value:
            (pendingQc && samplingCount) || pendingQc === 0 || samplingCount === 0
              ? `${pendingQc}/${samplingCount}`
              : '-'
        },
        {
          key: CONTENT.QC_PASS,
          value:
            (qcPassCount && samplingCount) || qcPassCount === 0 || samplingCount === 0
              ? `${qcPassCount}/${samplingCount}`
              : '-'
        },
        {
          key: CONTENT.QC_FAIL,
          value:
            (qcFailCount && samplingCount) || qcFailCount === 0 || samplingCount === 0
              ? `${qcFailCount}/${samplingCount}`
              : '-'
        }
      ];
      setVendorInfoStripData([...infoStripTemp, ...temp]);
    }
  }, [iqcTableData.headerInfo]);

  const toolTipElement = () => (
    <Box className="grnActions" width={200}>
      <div
        className="display-flex justify-content-space-between 
        align-items-center pd-12 cursor-pointer"
        onClick={() => setOpenPrintModal({ open: true })}
      >
        <div className="fs14">
          {' '}
          <img
            className="mr-r8"
            width={16}
            height={16}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/print.svg`}
            alt="img"
          />
          {CONTENT.PRINT}
        </div>
        <div className="fs14 text-99">{CONTENT.CTRL_P}</div>
      </div>
      <div
        className="display-flex  justify-content-space-between pd-12 cursor-pointer"
        onClick={() => dispatch(exportGrnBarCodeDetailsLoad(grnCode))}
      >
        <div className="fs14">
          {' '}
          <img
            className="mr-r8"
            width={16}
            height={16}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/barCodeGrey.svg`}
            alt="img"
          />
          {CONTENT.BARCODE_DETAIL}
        </div>
        <div className="fs14 text-99">{CONTENT.CTRL_E}</div>
      </div>
    </Box>
  );

  const { classes, cx } = styles();
  const [openReassignModal, setOpenReassignModal] = useState(false);
  const closeSuccessModal = useCallback(() => setOpenSuccessModal(false), [setOpenSuccessModal]);
  useEffect(() => {
    if (reassignSuccess) {
      dispatch(getGrnPidsLoad({ pageNum: 0, grnCode }));
      setOpenSuccessModal(true);
      dispatch(reassignGRNReset());
    }
  }, [reassignSuccess, dispatch, grnCode]);

  const closeReassignModal = useCallback(
    (value) => setOpenReassignModal(value),
    [setOpenReassignModal]
  );

  useEffect(() => {
    dispatch(getGrnSummaryLoad({ grnNo: grnCode }));

    return () => {
      dispatch(resetGrnDetailsReducer());
      infoStripTemp = [];
    };
  }, [dispatch, grnCode]);

  useEffect(() => {
    if (getGrnPids?.result?.data?.iqc) {
      dispatch(
        getIQCHeaderInfoLoad({
          ...defaultReqObj,
          payload: { ...defaultReqObj.payload, grn_code: grnCode }
        })
      );
    }
  }, [getGrnPids]);

  useEffect(() => {
    if (closeGrnSuccess) {
      dispatch(getGrnPidsLoad({ pageNum: 0, grnCode }));
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Success',
          subHeading: closeGrnSuccess.display_message,
          severity: 'success'
        })
      );
    }
    if (closeGrnFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: closeGrnFail?.display_message || closeGrnFail?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [closeGrnSuccess, closeGrnFail, dispatch, grnCode]);

  useEffect(() => {
    if (exportGrnBarCodeDetails) {
      const { data } = exportGrnBarCodeDetails.response;
      if (data === '') {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: 'No Data Found',
            severity: 'error'
          })
        );
      } else {
        fileDownload(data, 'grn-details');
      }

      dispatch(exportGrnBarCodeDetailsReset());
    }
    if (exportGrnBarCodeDetailsError) {
      if (exportGrnBarCodeDetailsError?.response?.data?.meta?.displayMessage) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: `User Does not have permission for path 
            : /nexs/api/grn/v1/master/grn-pdf/${grnCode}`,
            severity: 'error'
          })
        );
      }
      dispatch(exportGrnBarCodeDetailsReset());
    }
  }, [exportGrnBarCodeDetails, exportGrnBarCodeDetailsError, grnCode, dispatch]);

  useEffect(() => {
    if (getGrnBlockedPidsError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: getGrnBlockedPidsError?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [getGrnBlockedPidsError]);

  const handleCloseDetailModal = useCallback(
    () => setOpenDetailsModal(false),
    [setOpenDetailsModal]
  );

  const goToGRNCreate = () => {
    if (grnSummary) {
      const { vendor_name, po_id, invoice_id, vendor, vendor_invoice_num, grn_code } = grnSummary;
      navigate(
        // eslint-disable-next-line max-len
        `/grn/create?vendorName=${vendor_name}&poNumber=${po_id}&invoiceRefNum=${invoice_id}&invoiceName=${vendor_invoice_num}&vendorId=${vendor}&grnCode=${grn_code}`
      );
    } else {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: grnSummaryFail?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  };

  const startIqcButtonDisabled = useMemo(
    () =>
      !getGrnPids?.result?.data?.products?.length ||
      iqcTableData?.headerInfo?.grnClosure ||
      iqcTableData?.headerInfo?.items?.length === 0,
    [getGrnPids, iqcTableData?.headerInfo]
  );

  const handlePrintSelect = (e) => {
    const { value } = e.target || {};
    if (value === 'GRN') {
      setPrintValue('GRN');
    } else if (value === 'PUTAWAY') {
      setPrintValue('PUTAWAY');
    }
  };

  const printOptions = [
    { key: 'GRN', value: CONTENT.GRN },
    { key: 'PUTAWAY', value: CONTENT.PUTAWAY }
  ].filter((option) => isPutAwayCodeAvailable || option.key !== 'PUTAWAY');

  return (
    <div>
      <GrnDetailsModal
        onSubmit={saveAndCloseFun}
        showModal={openDetailsModal}
        setShowModal={handleCloseDetailModal}
        titleKey={CONTENT.GRN_NO}
        invoiceText={grnCode}
        status={getGrnPids?.result?.data?.grn_status}
        infoTableData={grnDetailsModalData.infoTableData || []}
        vendorText={grnDetailsModalData.vendorName || ''}
        subtitleInfoStripData={grnDetailsModalData.subtitleInfoStripData || []}
        required={required}
        chipBoxData={grnDetailsModalData.chipBoxData || []}
        createVendorInvoiceLoading={false}
        inputsValue={grnDetailsModalData.inputs || {}}
      />
      {getGrnPidsError ? (
        <Box
          height="400px"
          className="fs20 fs-bold"
          alignItems="center"
          display="flex"
          justifyContent="center"
        >
          {' '}
          {getGrnPidsError?.display_message || getGrnPidsError?.data?.meta?.displayMessage}{' '}
        </Box>
      ) : (
        <Box>
          <GRNPrintConfirmModal
            open={openPrintModal.open}
            printFun={printFun}
            handleClose={closeOpenPrintModal}
            printOptions={printOptions}
            handleSelect={handlePrintSelect}
            printValue={printValue}
          />

          <ReassignSuccessModal
            open={openSuccessModal}
            closeModal={closeSuccessModal}
            assignTo={assignTo}
          />

          <ReassignUserModal
            reassignGRNReset={reassignGRNReset}
            reassignFail={reassignFail}
            open={openReassignModal}
            reassignSuccess={reassignSuccess}
            data={modalData}
            closeModal={closeReassignModal}
            value={assignTo}
            setValue={setValueFun}
          />
          {beginIqcModal && (
            <BeginIqcModal
              open={beginIqcModal}
              grnCode={grnCode}
              pid={
                getGrnPids?.result?.data?.products[0]?.pid ||
                (iqcTableData?.data?.items?.length && iqcTableData?.data?.items[0]?.pid)
              }
              handleClose={() => setBeginIqcModal(false)}
            />
          )}

          <Box>
            {(getGrnBlockedPidsLoading && getGrnPidsLoading) || grnSummaryLoading ? (
              <Box height="400px" alignItems="center" display="flex" justifyContent="center">
                <Spinner />{' '}
              </Box>
            ) : (
              <>
                <InfoStrip data={vendorInfoStripData} usePoStatus={false}>
                  <Box className="display-flex" alignItems="center">
                    {!isEmpty(iqcTableData?.headerInfo) && getGrnPids?.result?.data?.iqc ? (
                      <Button
                        variant="contained"
                        color="primary"
                        className={classes.reAssignBtn}
                        disabled={startIqcButtonDisabled}
                        onClick={() => setBeginIqcModal(true)}
                      >
                        {CONTENT.START_IQC}
                      </Button>
                    ) : (
                      <>
                        {getGrnPids?.result?.data?.grn_status !== 'closed' && (
                          <Button
                            variant="outlined"
                            color="primary"
                            disabled={
                              getGrnPids?.result?.data?.grn_status === 'IQC_IN_PROGRESS' ||
                              getGrnPidsLoading
                            }
                            className={classes.reAssignBtn}
                            onClick={goToGRNCreate}
                          >
                            {CONTENT.START_RECEIVING}
                          </Button>
                        )}

                        <Button
                          variant="contained"
                          color="primary"
                          className={classes.reAssignBtn}
                          // disabled={getGrnPids?.result?.data?.grn_status === "closed"}
                          // will be enabled once user authenication is done
                          disabled
                          onClick={() => setOpenReassignModal(true)}
                        >
                          {CONTENT.REASSIGN_GRN}
                        </Button>
                      </>
                    )}
                    <Box ml={3} mr={2}>
                      <MoreItem itemsToShow={toolTipElement()} />
                    </Box>
                  </Box>
                </InfoStrip>

                <div className={cx({ [classes.container]: true, [classes.iqcPadding]: isIqc })}>
                  <TabsContainer
                    grnCode={grnCode}
                    setIsIqc={setIsIqc}
                    isIqc={isIqc}
                    grnPageLoading={grnSummaryLoading || getGrnPidsLoading}
                    grnClosure={
                      iqcTableData?.headerInfo?.grnClosure || iqcTableData?.headerInfo?.iqcDone
                    }
                    showIqc={!isEmpty(iqcTableData?.headerInfo) && getGrnPids?.result?.data?.iqc}
                  />
                </div>
              </>
            )}
          </Box>
        </Box>
      )}
    </div>
  );
};
export default GrnDetails;
