import React from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import LkChip from 'components/MaterialUi/LkChip';
import { manualOverrideLoad } from '../../redux/actionCreators/grnListing';

const styles = makeStyles()((theme) => ({
  button: {
    borderRadius: theme.spacing(1),
    marginLeft: theme.spacing(2)
  }
}));

const BlockedTabActions = ({
  selectBox = '',
  inputBox = '',
  selectedFilterList = [],
  deleteSelectedFilter,
  resetFilters,
  actionValues,
  clearSelection,
  dispatch
}) => {
  const { classes } = styles();
  const confirmFun = () => {
    const payload = {
      payload: actionValues,
      filter: {
        key: selectBox,
        value: inputBox
      }
    };
    dispatch(manualOverrideLoad(payload));
  };
  const enable =
    actionValues.manual_override_allowed.length === 0 &&
    actionValues.manual_override_rejected.length === 0;
  const filterList = React.useMemo(
    () => selectedFilterList?.filter(({ key }) => !key.startsWith('GRN_')),
    [selectedFilterList]
  );

  return (
    <div className={classes.root}>
      <Box p={2} display="flex" justifyContent="space-between">
        <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
          {filterList?.map(({ key, value }) => {
            const temp = key.startsWith('BLOCKED_') ? key.slice(8) : key;
            return (
              <Box key={`${key} ${value}`} mr={2} mb={1}>
                <LkChip
                  label={`${temp}: ${value}`}
                  type="filter"
                  deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                  size="small"
                  onDelete={() => deleteSelectedFilter(key, value)}
                />
              </Box>
            );
          })}
        </Box>

        <Box>
          <Button
            variant="outlined"
            disabled={!filterList?.length}
            color="primary"
            onClick={resetFilters}
            size="medium"
            className={classes.button}
          >
            RESET
          </Button>
          <Button
            variant="outlined"
            disabled={enable}
            color="primary"
            onClick={clearSelection}
            size="medium"
            className={classes.button}
          >
            CANCEL
          </Button>
          <Button
            disabled={enable}
            variant="outlined"
            onClick={confirmFun}
            color="primary"
            size="medium"
            className={classes.button}
          >
            CONFIRM
          </Button>
        </Box>
      </Box>
    </div>
  );
};

export default BlockedTabActions;
