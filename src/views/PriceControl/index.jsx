import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { useDispatch } from 'react-redux';
import {
  getClassificationAndBrandListLoad,
  getLegalEntityListLoad,
  getPriceControlListLoad
} from 'redux/reducers/priceControl';

import useFilterHook from 'components/common/useFilterHook';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import PriceControlBody from './PriceControlBody';
import PriceControlHeader from './PriceControlHeader';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    height: 'calc(100vh - 65px)',
    background: '#FFFFFF'
  }
}));

const initialRequest = {
  pageRequest: {
    pageNumber: 0,
    pageSize: 500,
    sortKey: 'updated_at',
    sortOrder: 'DESC'
  }
};

const PriceControl = () => {
  const location = useLocation();
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const initialReq = useRef(false);
  const [requestBody, setRequestBody] = useState(initialRequest);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter } = useFilterHook();

  const dispatchListLoadFun = () => dispatch(getPriceControlListLoad(requestBody));

  useEffect(() => {
    dispatch(getClassificationAndBrandListLoad({ type: 'classification' }));
    dispatch(getClassificationAndBrandListLoad({ type: 'brand' }));
    dispatch(getLegalEntityListLoad());
  }, []);

  useEffect(() => {
    if (initialReq.current) {
      dispatchListLoadFun();
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current = true;
    const queryFilter = getAllQueryParam(window.location.search);
    const payload = generatePayloadForSearchAPI(queryFilter, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => {
      let upldatedRequest = { pageRequest: { ...prevReqBody.pageRequest }, ...payload };
      if (prevReqBody.barcode) {
        // sending barcode
        upldatedRequest = {
          ...upldatedRequest,
          barcode: prevReqBody.barcode
        };
      }
      return {
        ...upldatedRequest
      };
    });
  }, [location.search, dispatch]);

  const fetchMoreData = (sortOrder, sortKey, pageNumber) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNumber,
        sortKey,
        sortOrder
      }
    }));
  };

  return (
    <Box className={classes.root}>
      <PriceControlHeader
        selectedFilterList={selectedFilterList}
        deleteSelectedFilter={deleteSelectedFilter}
        dispatchListLoadFun={dispatchListLoadFun}
      />
      <PriceControlBody
        fetchMoreData={fetchMoreData}
        selectedFilterList={selectedFilterList}
        onChangeFilterList={onChangeFilterList}
      />
    </Box>
  );
};

export default PriceControl;
