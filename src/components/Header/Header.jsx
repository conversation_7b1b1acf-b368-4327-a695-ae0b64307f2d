import React from 'react';
import { Grid, Typography, Divider, Select, MenuItem, Button } from '@mui/material';
import './header.scss';

function Header() {
    return (
        <div className="header-nexs">
            <Grid container>
                <Grid item xs={4} container>
                    <Typography variant="h3">Nexs</Typography>
                    <Divider className="divider-header-nexs" orientation="vertical" flexItem />
                    {/* <button className="btn btn-primary">Parmod Dudeja</button>
                    <div className="material-group mr-b30 inline-block">
                        <input className="form-control" type="text" autoComplete="tel" maxLength="6"  />
                        <label>Enter OTP</label>
                    </div> */}
                    <Button className="btn btn-primary">Parmod Dudeja</Button>
                    <Select
                        variant="standard"
                        labelId="demo-simple-select-filled-label"
                        id="demo-simple-select-filled"
                        value={10}>
                        <MenuItem value={10}>Create Invoice</MenuItem>
                        <MenuItem value={20}>Create Invoice</MenuItem>
                        <MenuItem value={30}>Create Invoice</MenuItem>
                    </Select>
                </Grid>
                <Grid item xs={4} container alignItems="center">
                    <Typography className="h4">LKG123123123</Typography>
                </Grid>
                <Grid item xs={3}>Nexs23</Grid>
            </Grid>
        </div>
    );
}

export default Header;
