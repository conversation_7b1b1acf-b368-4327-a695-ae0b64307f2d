import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import TableBody from '@mui/material/TableBody';
import { makeStyles } from 'tss-react/mui';
import { get } from 'utils/lodash';
import LkTableBodyRow from './LkTableBodyRow';
import LKTableNoDataRow from '../LKTableNoDataRow';
import useLkTableInfiniteScroll from './useLkTableInfiniteScroll';

const useStyles = makeStyles()(() => ({
  tableBody: {
    height: '100%',
    width: '100%'
  }
}));

const LkNonVertualizedTableBody = ({
  headerConfig,
  isDataFetching,
  tableData,
  pageSize,
  totalRowsCount,
  setNextPage,
  selectedRows,
  warnedRows,
  tableBodyRef,
  rowSize,
  rowExtendedSize,
  renderExtenedComponent,
  columnWidth,
  plainTheme,
  noDataText,
  hasMoreItemsToload,
  rowKey = null,
  onRowClick = null
}) => {
  const { classes } = useStyles();
  const [rowsConfig, setRowsConfig] = useState({});

  const { firstLoadingRowRef } = useLkTableInfiniteScroll(
    setNextPage,
    hasMoreItemsToload,
    isDataFetching
  );

  const toggleRowSize = useCallback((index) => {
    setRowsConfig((prevState) => {
      let newRowConfig = {};
      const targerRow = prevState[index];
      if (targerRow && targerRow.expanded) {
        newRowConfig = { ...prevState[index], expanded: false, size: rowSize };
      } else {
        newRowConfig = { ...prevState[index], expanded: true, size: rowExtendedSize };
      }

      return {
        ...prevState,
        [index]: newRowConfig
      };
    });
  }, []);

  const renderTableBody = (data, loadingData) =>
    data.map((rowData, index) => {
      const key = tableData.length + 1 + index;
      const style = {
        height: 150,
        position: 'relative',
        width: '100%',
        outline: '0.5px solid rgb(221,221,221)'
      };

      return (
        <LkTableBodyRow
          key={key}
          rowIndex={index}
          id={get(rowData, rowKey, null) ?? rowKey === 'index' ? index : null}
          selectedRows={selectedRows}
          warnedRows={warnedRows}
          isLoading={loadingData}
          style={style}
          headerConfig={headerConfig}
          rowData={rowData}
          isExpanded={style.height === rowsConfig[index]?.size}
          rowSize={rowSize}
          toggleRowSize={() => toggleRowSize(index)}
          renderExtenedComponent={renderExtenedComponent}
          columnWidth={columnWidth}
          onRowClick={onRowClick}
          plainTheme={plainTheme}
          isNonVertualizedTable
          firstLoadingRowRef={firstLoadingRowRef}
        />
      );
    });

  const loadingArray = () => {
    const pendingRowsCount = totalRowsCount - tableData.length || pageSize;
    return Array(pendingRowsCount <= pageSize ? pendingRowsCount : pageSize).fill({});
  };

  return (
    <TableBody component="div" className={classes.tableBody} ref={tableBodyRef}>
      {tableData.length === 0 && !isDataFetching ? (
        <LKTableNoDataRow noDataText={noDataText} />
      ) : (
        <>
          {tableData.length > 0 && renderTableBody(tableData, false)}
          {(tableData.length === 0 || hasMoreItemsToload()) &&
            renderTableBody(loadingArray(), true)}
        </>
      )}
    </TableBody>
  );
};

export default React.memo(LkNonVertualizedTableBody);

LkNonVertualizedTableBody.propTypes = {
  headerConfig: PropTypes.arrayOf(PropTypes.instanceOf(Object).isRequired).isRequired,
  isDataFetching: PropTypes.bool.isRequired,
  tableData: PropTypes.instanceOf(Array).isRequired,
  pageSize: PropTypes.number.isRequired,
  rowKey: PropTypes.string,
  selectedRows: PropTypes.instanceOf(Array).isRequired,
  warnedRows: PropTypes.instanceOf(Array).isRequired,
  rowSize: PropTypes.number.isRequired,
  rowExtendedSize: PropTypes.number.isRequired,
  columnWidth: PropTypes.number.isRequired,
  onRowClick: PropTypes.func,
  noDataText: PropTypes.string.isRequired
};
