import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { LOCALISATION } from 'redux/reducers/localisation';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import PrintIcon from '@mui/icons-material/Print';
import IconButton from '@mui/material/IconButton';

import useWindowResize from 'common/useWindowResize';
import TotalCountsContainer from 'common/TotalCountsContainer';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import BarCodeWrapper from 'components/Grn/BarCodeWrapper';
import LkToolTip from 'components/ToolTip/ToolTip';
import Spinner from 'components/Spinner/Spinner';
import LkInput from 'components/MaterialUi/LkInput';
import LkModal from 'components/Modal/Modal';
import { useQuery } from 'components/common/fetchQueryParamHook';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

import {
  manifestDetailsLoad,
  searchAWBLoad,
  closeManifestLoad,
  printManifestLoad,
  printManifestReset,
  manifestShippingDeleteRequest,
  retryManifestLoad,
  retryManifestReset,
  closeManifestReset,
  printSGInvoicesLoad
} from 'redux/actionCreators/manifest';
import { toastOpen } from 'redux/actionCreators/toast';

import { debounce, genericDateFormatted, fileDownload, convertDateFormat } from 'utils/helpers';
// import './ManifestDetails.scss';

const useStyles = makeStyles()((theme) => ({
  root: {
    padding: '24px',
    background: '#fff',
    minHeight: '80vh'
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(2),
    marginTop: '4px'
  },
  customRow: {
    padding: 0,
    display: 'flex'
  },
  gridContainer: {
    border: '1px solid #e2e2e4',
    borderTop: 'none'
  },
  label: {
    fontWeight: '500',
    letterSpacing: '0.2px',
    fontSize: '13px',
    paddingLeft: '10px',
    paddingRight: '10px'
  },
  dtoolTip: {
    bgcolor: 'red',
    boxShadow: 1,
    borderRadius: 2,
    p: 2,
    minWidth: 300
  },
  awbSearch: {
    borderRadius: 0,
    width: 250
  },
  pagination: {
    marginTop: theme.spacing(3),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  closeShipment: {
    color: '#d43f3a'
  },
  closeManifestDisabled: {
    opacity: '0.5',
    cursor: 'not-allowed',
    pointerEvents: 'none'
  },
  input: {
    background: 'white',
    borderRadius: theme.spacing(0.5)
  },
  detailItem: {
    display: 'flex',
    borderTop: '1px solid #e2e2e4',
    alignItems: 'center',
    '& label': {
      textAlign: 'right',
      backgroundColor: '#f5f5f5',
      padding: '15px 12px',
      width: '50%',
      fontSize: 14,
      paddingRight: 15,
      fontWeight: 500,
      letterSpacing: 0.2
    },
    '& span': {
      padding: 12,
      fontSize: 14,
      paddingLeft: 15
    }
  },
  awbFilter: {
    display: 'flex',
    maxWidth: 600,
    border: '1px solid #e2e2e4',
    alignItems: 'center',
    justifyContent: 'center',
    '& input': {
      border: 'none',
      width: '100%',
      borderLeft: '1px solid #e2e2e4',
      padding: '0 10px',
      height: 40,
      '&:focus': {
        outline: 'none'
      }
    },
    '& svg': {
      fontSize: 28,
      width: 50,
      textAlign: 'center',
      lineHeight: 40,
      justifyContent: 'center'
    }
  },
  detailHeader: {
    '& .detail-header-grid': {
      margin: ' 20px 0 30px'
    }
  },
  awsSearch: {
    margin: 0,
    display: 'flex',
    '& .aws-search-label': {
      color: '#FFFFFF',
      backgroundColor: '#00acc1',
      padding: '11px 20px',
      fontSize: 16
    }
  },
  closeManifest: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 14,
    border: '1px solid #d43f3a',
    color: '#d43f3a',
    lineHeight: '36px',
    padding: '0 12px',
    cursor: 'pointer',
    '& svg': {
      marginRight: 10
    },
    '&:hover': {
      backgroundColor: '#d43f3a',
      color: '#fff'
    }
  },
  printManifest: {
    '& div': {
      border: '1px solid #00acc1',
      lineHeight: '36px',
      padding: '0 12px',
      cursor: 'pointer',
      fontSize: 14,
      color: '#00acc1',
      marginLeft: 15,
      display: 'flex',
      alignItems: 'center',
      minWidth: 160,
      '&:hover': {
        backgroundColor: '#00acc1',
        color: '#fff'
      }
    },
    '& svg': {
      marginRight: 10
    }
  }
}));

let reqFormat = null;

const ManifestDetails = () => {
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const queryPrams = useQuery();
  const bagSearch = queryPrams.get('bagSearch') === 'true';
  const bagShipment = queryPrams.get('bagShipment') === 'true';
  const bagIdVal = queryPrams.get('bagId');
  const manifestIdVal = queryPrams.get('manifestId');

  const { manifestId: manifestID } = useParams();

  const [tableHeight] = useWindowResize(window.innerHeight - 500);
  const {
    manifestLoading,
    manifestDetails,
    searchAWBLoading,
    searchAWBSuccess,
    closeManifestSuccess,
    printManifestData,
    retryManifest: { isLoading: isRetryLoading, data: discrepancyData }
  } = useSelector((state) => state.manifest);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.MANIFEST);
  const [showConfirmModal, openConfirmModal] = useState(false);
  const [searchItem, setSearchItem] = useState('');
  const [isShowModal, setIsShowModal] = useState(false);

  const awbForm = useRef();
  const {
    count,
    manifestId,
    status,
    shippingProvider,
    createdBy,
    shipmentDetails,
    createdAt,
    updatedBy,
    updatedAt,
    bagId,
    bagCount,
    bagStatus
  } = manifestDetails || {};

  const TOTAL_COUNT = [
    {
      key: 'nxsCount',
      label: CONTENT.NEXS_COUNT
    },
    {
      key: 'bagCount',
      label: CONTENT.BAG_COUNT
    }
  ];

  const countData = {
    nxsCount: count,
    bagCount
  };

  const renderDeleteShipment = (shippingPackageId, isProcessing = false) => {
    if (status === 'CLOSED') {
      return '';
    }
    if (isProcessing) {
      return <Spinner />;
    }
    return (
      <IconButton
        size="small"
        onClick={() => dispatch(manifestShippingDeleteRequest(manifestId, shippingPackageId))}
        edge="end"
        className={classes.closeShipment}
      >
        <DeleteIcon style={{ cursor: 'pointer' }} />
      </IconButton>
    );
  };

  const headerConfig = [
    {
      key: 'No',
      name: CONTENT.S_NO,
      align: 'left',
      style: { minWidth: '80px', maxWidth: '80px' },
      formatBody: useCallback((test, rowIndex) => <div>{rowIndex + 1}</div>)
    },
    {
      formatBody: useCallback(({ shippingPackageId, invoiceCode, incrementId }) => (
        <>
          <div>
            {CONTENT.CODE} :{' '}
            <span style={{ fontSize: '16px', fontWeight: '700' }}>{shippingPackageId}</span>
          </div>
          <div>
            {CONTENT.INVOICE} : <span style={{ fontSize: '14px' }}>{invoiceCode}</span>
          </div>
          <div>
            {CONTENT.ORDER} :{' '}
            <span style={{ fontSize: '16px', fontWeight: '700' }}>{incrementId}</span>
          </div>
        </>
      )),
      key: 'Package Details',
      name: CONTENT.PACKAGE_DETAILS,
      style: { minWidth: '250px', maxWidth: '250px' }
    },
    {
      key: 'trackingNumber',
      name: CONTENT.TRACKING_NUMBER,
      align: 'center',
      style: { fontWeight: 700 }
    },
    {
      key: 'shippingOrder.numberOfItems',
      name: CONTENT.NO_OF_PRODUCTS,
      style: { minWidth: '110px', maxWidth: '110px', fontWeight: 700 }
    },
    {
      formatBody: useCallback(
        ({ shippingOrder }) => (
          <div>{shippingOrder.weight === 0 ? '0.5' : shippingOrder.weight || '-'}</div>
        ),
        []
      ),
      name: CONTENT.WEIGHT_GM,
      key: 'shippingOrder.weight',
      style: { minWidth: '80px', maxWidth: '80px' }
    },
    {
      key: 'shippingOrder.totalPrice',
      name: CONTENT.AMOUNT,
      align: 'center',
      style: { minWidth: '110px', maxWidth: '110px', fontWeight: 700 }
    },
    {
      formatBody: useCallback(
        ({ shippingMethod, cashondelivery, COD }) => (
          <div>{shippingMethod === cashondelivery ? COD : shippingMethod}</div>
        ),
        []
      ),
      name: CONTENT.PAYMENT_METHOD,
      align: 'center',
      key: 'shippingMethod'
    },
    {
      key: 'shippingOrder.updatedAt',
      name: CONTENT.ADDED_AT,
      align: 'center',
      formatBody: ({ updatedAt: addedAt }) => genericDateFormatted(addedAt)
    },
    {
      key: 'Actions',
      name: CONTENT.ACTIONS,
      align: 'center',
      style: { minWidth: '110px', maxWidth: '110px', fontWeight: 700 },
      formatBody: useCallback(
        ({ shippingPackageId, isProcessing }) =>
          renderDeleteShipment(shippingPackageId, isProcessing),
        [renderDeleteShipment]
      )
    }
  ];

  const printInternationalInvoices = () => {
    dispatch(
      printSGInvoicesLoad({
        manifestId,
        type: 'sgTaxInvoice',
        shipments: shipmentDetails.map(({ shippingPackageId }) => shippingPackageId)
      })
    );
  };

  useEffect(() => {
    if (searchAWBSuccess || closeManifestSuccess) {
      debounce(dispatch(manifestDetailsLoad({ manifestId })), 500);
    }
    if (closeManifestSuccess) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: `${manifestId} ${CONTENT.CLOSED_SUCCESSFULLY}`,
          severity: 'success'
        })
      );
      dispatch(closeManifestReset());
    }
  }, [searchAWBSuccess, closeManifestSuccess]);

  useEffect(() => {
    if (printManifestData) {
      fileDownload(
        printManifestData,
        `${manifestId}-${convertDateFormat(new Date(), 'yyyy-mm-dd hh:mm:ss')}`,
        reqFormat
      );
      dispatch(printManifestReset());
    }
  }, [printManifestData, dispatch]);

  const printManifestFunc = (format) => {
    reqFormat = format;
    const data = {
      format,
      id: manifestId
    };
    dispatch(printManifestLoad(data));
  };

  useEffect(() => {
    let payLoad = {};
    if (bagSearch) {
      payLoad = { bagId: bagIdVal, manifestId: manifestIdVal, bagSearch };
    } else {
      payLoad = { manifestId: manifestID, bagSearch };
    }
    dispatch(manifestDetailsLoad(payLoad));
  }, [manifestID, bagIdVal, manifestIdVal]);

  useEffect(() => () => dispatch(retryManifestReset()), []);

  useEffect(() => {
    setIsShowModal(discrepancyData.bagDiscrepancyCount > 0);
  }, [discrepancyData.bagDiscrepancyCount]);

  const ifPackageExists = (scanParam) => {
    if (manifestDetails.channel === 'B2B') {
      return false;
    }

    if (Object.keys(manifestDetails).length > 0) {
      const packages = manifestDetails.shipmentDetails;
      let shipmentExist = false;
      if (packages.length > 0) {
        packages.some((pack) => {
          if (scanParam === pack.trackingNumber || scanParam === pack.shippingPackageId) {
            dispatch(
              toastOpen({
                isToastOpen: true,
                heading: `${scanParam} ${CONTENT.ALREADY_EXISTS_IN_MANIFEST}`,
                severity: 'error'
              })
            );
            shipmentExist = true;
            return shipmentExist;
          }
          return false;
        });
      }

      return shipmentExist;
    }
    dispatch(
      toastOpen({
        isToastOpen: true,
        heading: CONTENT.MANIFEST_NOT_FOUND,
        severity: 'error'
      })
    );
    return false;
  };

  const searchAWBFunc = (value) => {
    let scanParam = value.trim();
    if (manifestDetails.isFedexCourier) {
      scanParam = scanParam.slice(-12);
    }
    if (scanParam.length > 0) {
      if (!ifPackageExists(scanParam)) {
        const data = {
          id: manifestId,
          value: scanParam
        };
        dispatch(searchAWBLoad(data));
        awbForm.current.value = '';
      }
    } else {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PLEASE_SCAN_VALID_PKG,
          severity: 'error'
        })
      );
    }
  };

  const handleModal = (type) => {
    openConfirmModal(type);
  };

  const filterTotalCountKeys = TOTAL_COUNT.filter(
    ({ key }) => key !== 'bagCount' || status !== 'CLOSED'
  );

  const closeManifest = () => {
    dispatch(closeManifestLoad(manifestId));
    handleModal(false);
  };

  const filterShipmentDetails = useMemo(() => {
    if (searchItem === '') {
      return shipmentDetails;
    }
    return shipmentDetails.filter(
      (item) =>
        item.shippingPackageId.toLowerCase().includes(searchItem) ||
        item.trackingNumber.toLowerCase().includes(searchItem) ||
        item.incrementId.toString().includes(searchItem)
    );
  }, [shipmentDetails, searchItem]);

  const getWarnedRows = useMemo(() => {
    const result = [];
    shipmentDetails?.forEach((element) => {
      if (element.shouldbeDeleted) {
        result.push(element.shippingPackageId);
      }
    });
    return result;
  }, [shipmentDetails]);

  const onSubmitBagId = (e) => {
    let { value } = e.target;
    value = value.trim();
    if (value && e.key === 'Enter') {
      if (manifestIdVal) {
        navigate(`/manifest-detail?bagId=${value}&manifestId=${manifestIdVal}&bagSearch=true`);
      } else {
        navigate(`/manifest-detail?bagId=${value}&bagSearch=true`);
      }
    }
  };

  const discreprencyHeader = [
    {
      key: 'shipmentPackageId',
      name: 'Shipment Package Id',
      style: { minWidth: '250px', maxWidth: '250px' }
    },
    {
      key: 'status',
      name: 'Status',
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      key: 'message',
      name: 'Reason',
      style: { minWidth: '500px', maxWidth: '500px' }
    }
  ];

  const isInternationalManifest = shipmentDetails?.[0]?.lkCountry !== 'IN';
  return (
    <div className="manifest-details">
      <div className={`${classes.root} pos-rel`}>
        <div className={`display-flex align-items-center ${classes.mainBar}`}>
          {manifestID && (
            <Box className="display-flex align-items-center gap10">
              <TotalCountsContainer
                data={{ shipmentCount: count }}
                TOTAL_COUNT_KEYS={[{ key: 'shipmentCount', label: CONTENT.SHIPMENT_COUNT }]}
                noMarginLeft
              />
            </Box>
          )}
          {bagSearch ? (
            <Box className="display-flex align-items-center gap10">
              <TotalCountsContainer
                data={countData}
                TOTAL_COUNT_KEYS={filterTotalCountKeys}
                noMarginLeft
              />

              {count !== bagCount && status !== 'CLOSED' ? (
                <>
                  <Button
                    color="primary"
                    type="submit"
                    data-cy="retry"
                    variant="contained"
                    onClick={() =>
                      dispatch(retryManifestLoad({ bagId: bagIdVal, manifestId: manifestIdVal }))
                    }
                    disabled={isRetryLoading}
                  >
                    {isRetryLoading ? <Spinner /> : CONTENT.RETRY}
                  </Button>
                  {discrepancyData.bagDiscrepancyCount > 0 && (
                    <InfoOutlinedIcon
                      fontSize="small"
                      onClick={() => setIsShowModal(true)}
                      style={{ cursor: 'pointer' }}
                    />
                  )}
                </>
              ) : (
                ''
              )}
            </Box>
          ) : (
            <div />
          )}

          <Box className="display-flex align-items-center gap10">
            {bagSearch && !bagShipment && (
              <Box width={250} mr={1} data-cy="scan barcode">
                <BarCodeWrapper title={CONTENT.BAG_ID} padding={1}>
                  <LkInput
                    fullWidth
                    variant="outlined"
                    className={classes.input}
                    onKeyPress={onSubmitBagId}
                    placeholder={CONTENT.SCAN_BARCODE}
                  />
                </BarCodeWrapper>
              </Box>
            )}
            <LkToolTip placement="bottom" title={CONTENT.GO_BACK}>
              <Button
                data-cy="back"
                color="primary"
                style={{ borderRadius: 4 }}
                type="submit"
                variant="contained"
                onClick={() => navigate(-1)}
              >
                <ArrowBackIcon fontSize="small" /> &nbsp;{CONTENT.BACK}
              </Button>
            </LkToolTip>
          </Box>
        </div>
        {manifestLoading && (
          <Box display="flex" justifyContent="center" alignItems="center">
            <Spinner />
          </Box>
        )}
        {manifestDetails && (
          <Box sx={{ width: '100%' }} mb={3}>
            <Grid
              sx={{
                marginBottom: 2.5,
                width: '30%'
              }}
              container
              className={classes.gridContainer}
            >
              <Grid item xs={6}>
                <div className={classes.detailItem}>
                  <Box
                    component="label"
                    sx={{
                      height: '44px'
                    }}
                  >
                    {CONTENT.BAG_ID} :
                  </Box>
                  <span>{bagId ?? '-'}</span>
                </div>
              </Grid>
              <Grid item xs={6}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.BAG_STATUS} :</label>
                  <Box
                    data-cy="bag status"
                    color="#fff"
                    component="span"
                    sx={{
                      color: bagStatus !== 'Attached' ? 'green' : 'red',
                      fontWeight: 'bold'
                    }}
                  >
                    {bagStatus ?? ''}
                  </Box>
                </div>
              </Grid>
            </Grid>

            <Grid container className={`${classes.gridContainer}`}>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.MANIFEST_NUMBER} :</label>
                  <span>{manifestId}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.CREATED_AT} :</label>
                  <span>{convertDateFormat(createdAt, 'shortDateTime')}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.CREATED_BY}</label>
                  <span>{createdBy}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.STATUS} :</label>
                  <span>{status}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.UPDATED_AT} :</label>
                  <span>{convertDateFormat(updatedAt, 'shortDateTime')}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.UPDATED_BY}</label>
                  <span>{updatedBy}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.SHIPPING_PROVIDER} :</label>
                  <span>{shippingProvider}</span>
                </div>
              </Grid>
              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.CLOSED_AT}</label>
                  <span>
                    {status === 'CLOSED' ? convertDateFormat(updatedAt, 'shortDateTime') : '-'}
                  </span>
                </div>
              </Grid>

              <Grid item xs={4}>
                <div className={classes.detailItem}>
                  <label>{CONTENT.CLOSED_BY}</label>
                  <span>{status === 'CLOSED' ? updatedBy : '-'}</span>
                </div>
              </Grid>
            </Grid>
          </Box>
        )}

        <div className="manifest-list-table">
          <div className={classes.detailHeader}>
            {count > 0 && (
              <Box className={classes.awbFilter}>
                <SearchIcon sx={{ color: '#999999' }} fontSize="small" />
                <input
                  type="text"
                  value={searchItem}
                  onChange={(e) => setSearchItem(e.target.value.toLowerCase())}
                  placeholder={CONTENT.AWB_ORDER_SHIPMENT_PLACEHOLDER}
                />
              </Box>
            )}
            <Grid
              container
              direction="row"
              className="justify-content-space-between detail-header-grid"
            >
              <div className={classes.awsSearch}>
                {status === 'CREATED' && !bagShipment && (
                  <>
                    <div data-cy="enter awb shipment" className="aws-search-label">
                      {CONTENT.AWB_OR_SHIPMENT}
                    </div>
                    <LkInput
                      className={classes.awbSearch}
                      label={CONTENT.ENTER_AWB_OR_SHIPMENT}
                      variant="outlined"
                      inputRef={awbForm}
                      onKeyPress={(event) =>
                        event.which === 13 && searchAWBFunc(event.target.value)
                      }
                    />
                    {searchAWBLoading > 0 && <Spinner display="flex" alignItems="center" ml={1} />}
                  </>
                )}
              </div>
              {status === 'CREATED' && count > 0 && (
                <div
                  data-cy="close manifest"
                  className={cx(classes.closeManifest, {
                    [classes.closeManifestDisabled]: searchAWBLoading > 0
                  })}
                  onClick={!searchAWBLoading > 0 ? () => handleModal(true) : null}
                >
                  <CloseIcon className="cursor-pointer" />
                  {CONTENT.CLOSE_MANIFEST}
                </div>
              )}
              {status === 'CLOSED' && (
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  className={classes.printManifest}
                >
                  <div data-cy="print summary" onClick={() => printManifestFunc('print')}>
                    <PrintIcon /> {CONTENT.PRINT_SUMMARY}
                  </div>
                  <div data-cy="print pdf" onClick={() => printManifestFunc('pdf')}>
                    <PrintIcon /> {CONTENT.PRINT_PDF}
                  </div>
                  <div data-cy="print csv" onClick={() => printManifestFunc('csv')}>
                    <PrintIcon /> {CONTENT.PRINT_CSV}
                  </div>
                  {isInternationalManifest && (
                    <div data-cy="print invoices" onClick={printInternationalInvoices}>
                      <PrintIcon /> {CONTENT.PRINT_INVOICES}
                    </div>
                  )}
                </Box>
              )}
            </Grid>
          </div>
          {/* detail-header */}

          <LkTable
            tableHeight={tableHeight}
            headerConfig={headerConfig}
            isDataFetching={manifestLoading}
            tableData={filterShipmentDetails}
            rowKey="shippingPackageId"
            pageLimit={5}
            totalRowsCount={filterShipmentDetails?.length}
            dataRequestFunction={() => null}
            initialSortBy={{ id: 'createdAt', order: 'DESC' }}
            warnedRows={getWarnedRows}
            rowSize={80}
            noDataText={CONTENT.NO_DATA}
          />
        </div>
      </div>
      {/* manifest-root */}
      {showConfirmModal && (
        <LkModal open showActionButton={false} handleClose={() => handleModal(false)}>
          <div className="confirm-box pd-l20 pd-r20 mr-b20 text-center">
            <h5>{CONTENT.ARE_YOU_SURE}</h5>
            {manifestLoading && (
              <div className="create-manifest-loader">
                {' '}
                <Spinner />
              </div>
            )}
            <Box pt={4} m={4} className="display-flex justify-content-center gap10">
              <Button
                className={classes.cancelButton}
                onClick={() => handleModal(false)}
                color="primary"
                variant="outlined"
              >
                {CONTENT.CANCEL}
              </Button>
              <Button
                className={classes.cancelButton}
                onClick={closeManifest}
                color="primary"
                variant="contained"
              >
                {CONTENT.CONFIRM}
              </Button>
            </Box>
          </div>
        </LkModal>
      )}
      {isShowModal && (
        <LkModal
          modalHeight="610px"
          modalWidth="1000px"
          upperHeading="Discrepancy Detail"
          open
          handleClose={() => setIsShowModal(false)}
          showActionButton={false}
        >
          <LkTable
            headerConfig={discreprencyHeader}
            tableData={discrepancyData?.shipmentStatusDetailsList}
            rowKey="shippingPackageId"
            pageLimit={5}
            totalRowsCount={discrepancyData?.bagDiscrepancyCount}
            isNonVertualizedTable
          />
        </LkModal>
      )}
    </div>
  );
};

export default ManifestDetails;
