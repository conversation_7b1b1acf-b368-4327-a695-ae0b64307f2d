import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@mui/material';

import InfoAction from 'components/OrderDetailAction/InfoAction';

import { autoToManualLoad } from 'redux/actionCreators/orderDetails';

const useAutoToManualJIT = (email, featureToggle, getFacilityCode, shipmentItems, classes) => {
  const initialManualToApiPayload = {
    fittingId: '',
    facilityCode: '',
    shippingPackageId: ''
  };
  const dispatch = useDispatch();
  const [isAutoToManualModalOpen, setIsAutoToManualModalOpen] = useState(false);
  const [autoToManualAPIPayload, setAutoToManualAPIPayload] = useState(initialManualToApiPayload);

  const { isLoading: autoToManualLoading, isSuccess: autoToManualSuccess } = useSelector(
    (state) => state.orderDetails.autoToManual
  );

  const autoToManualConditions = featureToggle?.autoToManualConditions;

  const formatHeaderAutoToManual = useCallback(
    (rows) => {
      const autoJitAllowedStatus =
        autoToManualConditions[getFacilityCode(rows[0].shippingPackageId)]?.status;

      const autoToManualEmailAllowed = autoToManualConditions[
        getFacilityCode(rows[0].shippingPackageId)
      ]?.email?.map((user_email) => user_email.toLowerCase());

      const isEmailAllowed = autoToManualEmailAllowed?.includes(email?.toLowerCase());

      const showAuto =
        isEmailAllowed &&
        rows.some(
          (item) =>
            item.orderItemSubType?.includes('AUTO') && autoJitAllowedStatus?.includes(item.status)
        );

      return showAuto ? (
        <Button
          variant="contained"
          color="primary"
          onClick={() => {
            setAutoToManualAPIPayload({
              fittingId: rows[0].fittingId,
              facilityCode: getFacilityCode(rows[0].shippingPackageId),
              shippingPackageId: rows[0].shippingPackageId
            });
            setIsAutoToManualModalOpen(true);
          }}
        >
          A - M
        </Button>
      ) : null;
    },
    [featureToggle, shipmentItems]
  );

  const formatBodyShowType = useCallback(
    (shipmentIdData) => {
      const { customFields, orderItemType, orderItemSubType, fulfillableType } =
        shipmentIdData || {};
      const storeInventoryType = customFields?.customFields?.STORE_INVENTORY_TYPE;
      const formattedSubType =
        orderItemSubType && orderItemSubType !== 'NONE' ? orderItemSubType : null;

      const items = [orderItemType, formattedSubType, storeInventoryType, fulfillableType];
      const isDisabled = !items.some(Boolean);

      return <InfoAction classes={classes} items={items} disabled={isDisabled} />;
    },
    [shipmentItems]
  );

  useEffect(() => {
    let timeOut;
    if (autoToManualSuccess) {
      setIsAutoToManualModalOpen(false);
      setAutoToManualAPIPayload(initialManualToApiPayload);
      timeOut = setTimeout(() => {
        window.location.reload();
      }, 2000);
    }
    return () => {
      clearTimeout(timeOut);
    };
  }, [autoToManualSuccess]);

  const closeAutoToManualModal = () => setIsAutoToManualModalOpen(false);

  const triggerAutoToManualFn = () =>
    dispatch(
      autoToManualLoad({
        scannedEntity: autoToManualAPIPayload.fittingId.toString(),
        facilityCode: autoToManualAPIPayload.facilityCode.toString(),
        scannedEntityType: 'FITTING_ID',
        shippingPackageId: autoToManualAPIPayload.shippingPackageId.toString()
      })
    );

  return {
    formatHeaderAutoToManual,
    formatBodyShowType,
    isAutoToManualModalOpen,
    closeAutoToManualModal,
    triggerAutoToManualFn,
    autoToManualLoading
  };
};

export default useAutoToManualJIT;
