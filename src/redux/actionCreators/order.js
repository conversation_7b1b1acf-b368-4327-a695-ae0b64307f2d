import * as action from 'redux/actionTypes/order';

export function getOrderDetails(searchParam) {
  return {
    type: action.GET_ORDER_DETAILS_LOAD,
    searchParam
  };
}

export function getOrderDetailsSuccess(payload) {
  return {
    type: action.GET_ORDER_DETAILS_SUCCESS,
    payload
  };
}

export function getOrderDetailsReset(payload) {
  return {
    type: action.GET_ORDER_DETAILS_RESET,
    payload
  };
}

export function getOrderDetailsFail(payload) {
  return {
    type: action.GET_ORDER_DETAILS_FAIL,
    payload
  };
}

export function updateOrderDetailsStatus(payload) {
  return {
    type: action.UPDATE_ORDER_DETAILS_STATUS,
    payload
  };
}

export function getOrderByBarcode(barcode) {
  return {
    type: action.GET_ORDER_BY_BARCODE_LOAD,
    barcode
  };
}

export function getOrderByBarcodeSuccess(payload) {
  return {
    type: action.GET_ORDER_BY_BARCODE_SUCCESS,
    payload
  };
}

export function getOrderByBarcodeFail(payload) {
  return {
    type: action.GET_ORDER_BY_BARCODE_FAIL,
    payload
  };
}

export function getUnicomComments(payload) {
  return {
    type: action.GET_UNICOM_COMMENTS_LOAD,
    payload
  };
}

export function getUnicomCommentsSuccess(payload) {
  return {
    type: action.GET_UNICOM_COMMENTS_SUCCESS,
    payload
  };
}

export function getUnicomCommentsFail(payload) {
  return {
    type: action.GET_UNICOM_COMMENTS_FAIL,
    payload
  };
}

export function clearOrderData() {
  return {
    type: action.CLEAR_ORDER_DATA
  };
}

export function selectProductTab(index) {
  return {
    type: action.SELECT_PRODUCT_TAB,
    payload: { index }
  };
}

export function setShipmentData(data) {
  return {
    type: action.SET_SHIPMENT_DATA,
    payload: data
  };
}

export function setStoreValueByKey(key, value) {
  return {
    type: action.SET_VALUE_BY_KEY,
    payload: { key, value }
  };
}

export function setMultipleValues(obj) {
  return {
    type: action.SET_MULTIPLE_VALUES,
    payload: obj
  };
}

export function scanNextBarcode(payload) {
  return {
    type: action.SCAN_NEXT_BARCODE,
    payload
  };
}

export function setLensFocus(payload) {
  return {
    type: action.SET_LENS_FOCUS,
    payload
  };
}

export function setHoldUnholdVisit(payload) {
  return {
    type: action.IS_HOLD_UNHOLD_VISIT,
    payload
  };
}

export function resetInitialAPIresponse() {
  return {
    type: action.RESET_INITIAL_API_RESPONSE
  };
}
