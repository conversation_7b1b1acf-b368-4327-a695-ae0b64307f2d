import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Box } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { convertDateFormat, getQueryParam } from 'utils/helpers';
import QcFailCodeModal from 'components/Grn/QcFailCodeModal';
import EmptyItem from 'components/BlankPage/BlankPage';
import { toastOpen } from 'redux/actionCreators/toast';
import { getUserPutawayLoad } from 'redux/actionCreators/putaway';
import { saveGrnLoad } from 'redux/reducers/inwardQC';
import { LOGIN } from 'redux/reducers/login';
import { LOCALISATION } from 'redux/reducers/localisation';

import { getBarCodeSeriesLoad } from 'redux/actionCreators/barcodeSeries';
import SamplingSummaryModal from 'components/Grn/SamplingSummaryModal';
import InfoStrip from '../../components/common/InfoStrip';
import PIDList from '../../components/Grn/PIDList';
import GrnSection from '../../components/Grn/GrnSection';
import GrnSummaryModal from '../../components/Grn/GrnSummaryModal';
import {
  getPidListLoad,
  getPidDetailLoad,
  setGrnDetail,
  proceedToGreenChannel,
  searchByDescPrductsLoad,
  grnCloseReset,
  resetGrnCreate
} from '../../redux/actionCreators/grn';
import AddPidModal from '../../components/Grn/AddPidModal';
import { resetInvoiceReferenceCreation } from '../../redux/actionCreators/invoiceReferenceCreation';

import AddPidMoreModal from '../../components/Grn/AddPidMoreModal';
import localStorageHelper from '../../utils/localStorageHelper';
import Spinner from '../../components/Spinner/Spinner';
import useKeyboardShortcut from '../../common/useKeyboardShortcut';
import './GrnHome.scss';
import { CATEGORY_MAPPING } from '../../config/CategoryMapping';
import { GrnStatus } from '../../config/GrnStatus';
import GrnPutaway from './GrnPutaway';

const GrnHome = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    grnCreateSuccess,
    pidlistError,
    pidList,
    pidDetail,
    pidListLoading,
    pidDetailsFail,
    pidDetailLoad,
    updateQtyData,
    isProceedToGreenChannel,
    productSeachByDescLoading,
    meta
  } = useSelector((state) => state.grn);
  let selectedPid;
  const [openAddPidModal, setOpenAddPidModal] = useState(false);
  const [openQcFailModal, setQcFailModal] = useState(false);
  const [openAddPidMoreModal, setOpenAddPidMoreModal] = useState(false);
  const [openGrnSummaryModal, setOpenGrnSummaryModal] = useState(false);
  const [infoStripData, setInfoStripData] = useState([]);
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);

  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const checkSamplingData = useSelector((state) => state.inwardQC.checkSamplingData);
  const [showPutaway, setShowPutaway] = useState(false);
  const dispatch = useDispatch();
  const shortCuttoOpenAddPid = useCallback(() => setOpenAddPidModal(true), [setOpenAddPidModal]);
  useKeyboardShortcut([18, 68], shortCuttoOpenAddPid);
  const hasIqcSamplingPercentage =
    pidList?.result?.meta?.invoice?.items?.[0]?.iqc_sampling_percent > 0;

  useEffect(() => {
    const putawayParam = getQueryParam(location.search.replace('?', ''), 'showPutaway');
    if (putawayParam === 'false') {
      setShowPutaway(false);
      localStorageHelper.setItem('showPutaway', 'false');
    } else {
      setShowPutaway(true);
      localStorageHelper.setItem('showPutaway', 'true');
    }
  }, [location]);

  useEffect(() => {
    if (userDetail && showPutaway) {
      dispatch(getUserPutawayLoad({ type: 'GRN', userid: userDetail.empCode }));
    } // userDetail.email  "<EMAIL>"  userDetail.empCode
  }, [dispatch, userDetail, showPutaway]);

  useEffect(() => {
    const grnDetail = localStorageHelper.getItem('grn');
    if (!grnCreateSuccess && !grnDetail) {
      // if grn is not in reducer and localStorage
      navigate('/grn/create');
    }
    if (grnDetail) {
      if (!grnCreateSuccess) {
        // when grn is not in reducer
        dispatch(setGrnDetail(grnDetail));
      }
      if (grnCreateSuccess) {
        const { grn_status, invoice_reference_num, invoice_ref_num, invoice_date } =
          grnCreateSuccess;
        const updatedInfoStripData = [
          {
            key: createGrnLocal.GRN,
            className: 'text-primary cursor-pointer',
            value: grnCreateSuccess?.grn_code,
            status: GrnStatus[grn_status]?.text,
            type: GrnStatus[grn_status]?.type
          },
          { key: createGrnLocal.DATE, value: convertDateFormat(invoice_date, 'shortDate') },
          { key: createGrnLocal.INVOICE, value: invoice_reference_num || invoice_ref_num }
        ];
        setInfoStripData(updatedInfoStripData);
        // dispatch(getPidListLoad({ grnNo: grnCreateSuccess?.grn_code }));
        dispatch(getBarCodeSeriesLoad());
      }
    }
  }, [grnCreateSuccess, dispatch]);

  useEffect(() => {
    if (pidlistError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createGrnLocal.ERROR,
          subHeading: pidlistError?.response?.data?.display_message,
          severity: 'error'
        })
      );
    }
  }, [pidlistError, dispatch]);

  useEffect(
    () => () => {
      dispatch(resetGrnCreate());
      localStorageHelper.setItem('showPutaway', 'false');
      // dispatch(resetGrnDetailsReducer())
      dispatch(resetInvoiceReferenceCreation());
    },
    [dispatch]
  );

  useEffect(() => {
    if (pidDetail?.result?.data) {
      const { estimated_qty, total_scanned, status, pid, categoryId, sampling_percent } =
        pidDetail.result.data;
      const isComplete = estimated_qty <= total_scanned && estimated_qty > 0;
      if (status !== 'FAILED') {
        setOpenAddPidMoreModal(isComplete);
      }
      if (!(isProceedToGreenChannel[pid] && status === 'PENDING')) {
        if (status === 'PASSED') {
          dispatch(proceedToGreenChannel({ [pid]: true }));
        } else {
          dispatch(proceedToGreenChannel({ [pid]: false }));
        }
      }
      const tempProductType =
        categoryId === 11354 || categoryId === 11356 ? CATEGORY_MAPPING[categoryId] : 'other';

      const obj = {
        purchase_order_code: pidList?.result?.data?.po_id,
        product_type: tempProductType,
        search_query: pid,
        pid,
        isPrescriptionLens: tempProductType === 'prescription-lens'
      };
      dispatch(searchByDescPrductsLoad(obj));
      if (sampling_percent === 0) {
        setShowPutaway(true);
      } else {
        setShowPutaway(false);
      }
    }
  }, [pidDetail, grnCreateSuccess, dispatch]);

  const getPidDetail = (pid) => {
    const { grn_code, grn_pids } = pidList?.result?.data || {};
    if (grn_pids && grn_pids.length > 0) {
      selectedPid = pid || grn_pids[0]?.pid;
      dispatch(getPidDetailLoad({ grnNo: grn_code, pid: selectedPid }));
    } else {
      setOpenAddPidModal(true);
    }
  };

  useEffect(() => {
    if (pidDetailsFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: pidDetailsFail?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [pidDetailsFail]);

  useEffect(() => {
    if (pidList) {
      const { grn_pids, grn_status, vendor_invoice_number, po_id, vendor } =
        pidList?.result?.data || {};
      if (infoStripData[0]) {
        infoStripData[0].status = GrnStatus[grn_status]?.text;
        infoStripData[0].type = GrnStatus[grn_status]?.type;
      }
      // checking Vendor Invoice Number already present after adding new PID
      const test = infoStripData.findIndex((t) => t.key === 'Vendor Invoice Number');
      if (test === -1) {
        setInfoStripData([
          ...infoStripData,
          { key: createGrnLocal.PO, value: po_id },
          { key: createGrnLocal.VENDOR, value: vendor },
          { key: createGrnLocal.VENDOR_INVOICE_NUMBER, value: vendor_invoice_number }
        ]);
      } else {
        setInfoStripData([...infoStripData]);
      }
      if (!grn_pids.length) {
        getPidDetail();
      }
      const temp = {};
      grn_pids.forEach(({ pid, status }) => {
        if (status === 'PASSED') {
          temp[pid] = true;
        }
      });
      dispatch(proceedToGreenChannel(temp));
    }
    if (!pidList && grnCreateSuccess) {
      dispatch(getPidListLoad({ grnNo: grnCreateSuccess?.grn_code }));
    }
  }, [pidList, dispatch, grnCreateSuccess]);

  const toggleModal = useCallback((state, callback) => {
    callback(state);
  }, []);

  useEffect(() => {
    if (updateQtyData) {
      const { pid } = pidDetail?.result?.data || {};
      dispatch(proceedToGreenChannel({ [pid]: false }));
    }
  }, [updateQtyData, pidDetail, dispatch]);

  const handleCloseQCFailCodeModal = () => setQcFailModal(false);

  const addPidMoreModalOpen = useCallback(
    () => toggleModal('', setOpenAddPidMoreModal),
    [toggleModal]
  );

  const setOpenAddPidMoreModalFun = useCallback(
    (val) => setOpenAddPidMoreModal(val),
    [setOpenAddPidMoreModal]
  );

  const classification = useMemo(
    () => meta?.po?.po_items?.length > 0 && meta.po.po_items[0].classification,
    [meta]
  );

  return (
    <>
      {openQcFailModal && (
        <QcFailCodeModal open={openQcFailModal} handleClose={handleCloseQCFailCodeModal} />
      )}
      {infoStripData.length > 0 && (
        <InfoStrip data={infoStripData} usePoStatus={false}>
          <Box className="display-flex ">
            <Box mr={2}>
              <Button
                color="primary"
                variant="outlined"
                onClick={() => setQcFailModal(true)}
                className="infoStripButton"
                startIcon={
                  <img
                    className="image-container"
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/BarCodePrimary.svg`}
                    alt="img"
                  />
                }
              >
                {createGrnLocal.QC_FAIL_REASONS}
              </Button>
            </Box>

            <Button
              color="primary"
              className="infoStripButton"
              variant="contained"
              disabled={checkSamplingData.isLoading}
              onClick={() => {
                if (hasIqcSamplingPercentage) {
                  dispatch(saveGrnLoad({ grn_code: grnCreateSuccess?.grn_code }));
                }
                dispatch(grnCloseReset());
                toggleModal(true, setOpenGrnSummaryModal);
              }}
            >
              {checkSamplingData.isLoading ? <Spinner /> : createGrnLocal.DONE}
            </Button>
          </Box>
        </InfoStrip>
      )}
      <Box className="display-flex overflow-auto" alignItems="start">
        <PIDList
          pidList={pidList}
          changePid={getPidDetail}
          toggleModal={toggleModal}
          setOpenAddPidModal={setOpenAddPidModal}
        />
        {/* eslint-disable-next-line no-nested-ternary */}
        {pidListLoading || pidDetailLoad || productSeachByDescLoading ? (
          <Box className="display-grid-center wd-100"> {!pidDetailsFail && <Spinner />}</Box>
        ) : !pidList || !pidDetail ? (
          <EmptyItem
            heading={createGrnLocal.NO_PRODUCTS_FOUND_FOR_THIS_GRN}
            subHeading={createGrnLocal.PLEASE_ADD_PRODUCTS}
          />
        ) : (
          <div className="pd-25 pd-b0 flex1 bg-white">
            <GrnSection
              createGrnLocal={createGrnLocal}
              pidDetail={pidDetail}
              toggleModal={setOpenAddPidMoreModalFun}
              addPidModal={(value) => setOpenAddPidModal(value)}
            />
          </div>
        )}
        {!productSeachByDescLoading && !pidListLoading && pidDetail && showPutaway && (
          <Box width="40px">
            <GrnPutaway showMarkAsPending={false} />
          </Box>
        )}
      </Box>
      {openAddPidModal && (
        <AddPidModal
          open={openAddPidModal}
          showSearchBydesc={classification === 11356 || classification === 11354}
          openModal={() => toggleModal('', setOpenAddPidModal)}
          classification={classification}
          grnNo={grnCreateSuccess?.grn_code}
          grnCreateSuccess={grnCreateSuccess}
          poNum={meta?.po?.po_num}
          changePid={getPidDetail}
        />
      )}
      {openAddPidMoreModal && (
        <AddPidMoreModal
          toggleAddPidModal={shortCuttoOpenAddPid}
          open={openAddPidMoreModal}
          openModal={addPidMoreModalOpen}
          grnNo={grnCreateSuccess?.grn_code}
          grnCreateSuccess={grnCreateSuccess}
          setOpenAddPidModal={() => toggleModal('', setOpenAddPidModal)}
        />
      )}
      {openGrnSummaryModal &&
        !checkSamplingData.isLoading &&
        (hasIqcSamplingPercentage ? (
          <SamplingSummaryModal
            createGrnLocal={createGrnLocal}
            open={openGrnSummaryModal}
            openModal={() => toggleModal('', setOpenGrnSummaryModal)}
            grnNo={grnCreateSuccess?.grn_code}
            tableData={checkSamplingData?.data?.data}
          />
        ) : (
          <GrnSummaryModal
            createGrnLocal={createGrnLocal}
            open={openGrnSummaryModal}
            openModal={() => toggleModal('', setOpenGrnSummaryModal)}
            grnNo={grnCreateSuccess?.grn_code}
          />
        ))}
    </>
  );
};
export default GrnHome;
