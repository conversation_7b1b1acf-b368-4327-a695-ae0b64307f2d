// eslint-disable-next-line import/no-extraneous-dependencies
import 'cypress-file-upload';
import { genericDateFormatted, valueOrDefault } from '../../src/utils/helpers';
import config from '../../src/config';

const manifestShippingMethodUrl = `${
  config.manifestShippingMethod.getManifestShippingMethodsList}?page=0&sort=updatedAt,DESC&size=30`;
const manifestUploadUrl = config.manifestShippingMethod.uploadManifestShippingMethodFile;
const manifestTrackingUrl = config.manifestShippingMethod.trackManifestShippingIdsUploadStatus;

describe('Manifest shipping method', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
    cy.intercept('GET', manifestShippingMethodUrl).as('manifestShippingMethod');
    cy.navigate(['main-navigation', 'DC Sorting', 'Manifest Shipping Method']);
  });

  after(() => {
    cy.getByCy('close-modal').click();
    cy.logout();
  });

  it('Visits the Manifest shipping method page from app bar and table get populated', () => {
    cy.fixture('manifestShippingMethod/manifestShippingMethod').as('headers');

    // check if the url is correct
    cy.url().should('include', '/manifestShippingMethod');

    // check if we are on correct page
    cy.getByCy('title').should('contain.text', 'Manifest Shipping Method');

    cy.wait('@manifestShippingMethod').then(({ response }) => {
      const {
        courier,
        createdAt,
        createdBy,
        facilityCode,
        manifestShippingMethod,
        orderType,
        shippingMethod,
        shippingProvider,
        updatedAt,
        updatedBy
      } = response.body.data.manifestShippingMethodMappingDtoList[0];

      const firstRowData = [
        manifestShippingMethod,
        shippingProvider,
        orderType,
        shippingMethod,
        courier,
        createdAt,
        updatedAt,
        createdBy,
        updatedBy,
        facilityCode
      ];

      cy.get('[data-cy=table-cell-0-9] > p').should('have.css', 'font-weight').and('equal', '700');

      cy.get('@headers').then((headers) => {
        headers.forEach((header, index) => {
          cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
          if (index === 5 || index === 6) {
            cy.checkFirstRowTableCell(
              index,
              valueOrDefault(genericDateFormatted(firstRowData[index]))
            );
          } else {
            cy.checkFirstRowTableCell(index, valueOrDefault(firstRowData[index]));
          }
        });
      });
    });
  });

  it('Uploading file in manifest shipping method', () => {
    const manifestFile = 'manifestShippingMethod/manifestError.csv';

    // Intercept POST request for file upload and alias as 'manifestFileUpload'
    cy.intercept('POST', manifestUploadUrl).as('manifestFileUpload');

    cy.getByCy('upload').click();
    cy.get('#upload-items-file').attachFile(manifestFile);

    // Wait for the POST request to complete and get its response
    cy.wait('@manifestFileUpload').then(({ response }) => {
      const fileName = response.body.data;
      cy.intercept('GET', `${manifestTrackingUrl}/${fileName}`).as('trackFile');
    });
  });
});
