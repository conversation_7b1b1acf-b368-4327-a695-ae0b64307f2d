import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import useOrderHook from 'common/useOrderHook';

import { LOCALISATION } from 'redux/reducers/localisation';

import QcSuccess from './QcSuccess';
import ShipmentHeader from './ShipmentHeader';
import QualityCheckBarcode from './QualityCheckBarcode';

const styles = makeStyles()(() => ({
  header: {
    fontWeight: 700
  },
  seperator: {
    backgroundColor: '#fff',
    padding: '0 0 24px 0'
  },
  seperatorLine: {
    height: '1px',
    background: '#ddd',
    margin: '0 178px'
  },
  qcWrapper: {
    display: 'flex',
    justifyContent: 'center',
    background: '#fff',
    paddingBottom: '65px'
  },
  viaUnicomTitle: {
    padding: '140px 0 90px 0px',
    textAlign: 'center',
    backgroundColor: '#fff',
    fontWeight: '700',
    fontSize: '24px'
  }
}));

const QCSummary = () => {
  useOrderHook();
  const { classes } = styles();

  const QC = useSelector((state) => state[LOCALISATION].localeData.QC);
  const { qccheck } = useSelector((state) => state.order);

  const [shipmentHeaderHeight, setShipmentHeaderHeight] = useState(0);

  useEffect(() => {
    /** Used to determine height of shipmentHeader to give paddingTop to content */
    if (document.getElementById('shipment_header') && !shipmentHeaderHeight) {
      setShipmentHeaderHeight(document.getElementById('shipment_header').offsetHeight);
    }
  }, []);

  return (
    <div>
      <Box>
        <ShipmentHeader />
      </Box>
      <div style={{ paddingTop: shipmentHeaderHeight }}>
        <Box>
          {qccheck === 10 ? (
            <div className={classes.viaUnicomTitle}>
              {QC.QC_ALREADY_DONE_FOR_THIS_SHIPMENT}
              <br />
              {QC.YOU_CAN_PRINT_SHIPMENT_FROM_THIS_PAGE}
            </div>
          ) : (
            <QcSuccess />
          )}
        </Box>
        <div className={classes.seperator}>
          <div className={classes.seperatorLine} />
        </div>
        <div className={classes.qcWrapper}>
          <Box width="auto" boxShadow="rgb(0 0 0 / 30%) 0px 8px 16px" borderRadius="12px">
            <Card
              sx={{
                pt: 6,
                pb: 6
              }}
            >
              <QualityCheckBarcode />
            </Card>
          </Box>
        </div>
      </div>
    </div>
  );
};

export default QCSummary;
