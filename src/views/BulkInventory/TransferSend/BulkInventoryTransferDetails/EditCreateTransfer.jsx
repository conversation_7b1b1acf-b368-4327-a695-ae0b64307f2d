import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { isEmpty } from 'utils/lodash';
import { IconButton, Box, Button } from '@mui/material/';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { makeStyles } from 'tss-react/mui';
import AddCircleIcon from '@mui/icons-material/AddCircle';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkInput from 'components/MaterialUi/LkInput';
import LkToolTip from 'components/ToolTip/ToolTip';
import { numberOnly } from 'utils/helpers';

import {
  fetchPidDataLoad,
  editCreateTransferLoad,
  resetInventoryData
} from 'redux/actionCreators/bulkInventory/bulkInventory';

import { toastOpen } from '../../../../redux/actionCreators/toast';

const useStyles = makeStyles()(() => ({
  pidDescription: {
    width: '100%',
    fontSize: 14,
    lineHeight: '143%',
    letterSpacing: '0.15px',
    color: 'rgba(60, 60, 60, 0.38)',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxHeight: 40,
    display: '-webkit-box',
    '-webkit-line-clamp': 2,
    '-webkit-box-orient': 'vertical'
  },
  createContainer: {
    padding: '0px 20px 10px',
    background: '#ffffff'
  },
  btnContainer: {
    background: '#ffffff',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '20px'
  },
  btn: {
    borderRadius: 8,
    padding: '5px 12px'
  },
  csv: {
    marginLeft: 16
  },
  done: {
    marginLeft: 16
  },
  lightCell: {
    color: 'rgba(60, 60, 60, 0.38)'
  },
  activeRow: {
    outline: '2px solid #00BAC6',
    outlineOffset: '-3px',
    borderRadius: '8px'
  },
  readOnlyInput: {
    border: '1px solid #DDDDDD',
    padding: '8px 15px',
    minWidth: '150px',
    display: 'block',
    borderRadius: '4px'
  },
  loader: {
    position: 'absolute',
    left: '20px',
    top: '0'
  }
}));

const EditCreateTransfer = (props) => {
  const {
    CONTENT,
    setEditTransfer,
    showEditTransfer,
    editableData,
    sourceFacility,
    callTransferLoad,
    destinationFecility
  } = props;
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();
  const quantityRef = useRef(null);
  const {
    error: createTransferError,
    data: createTransferData,
    isLoading: createTransferLoader
  } = useSelector((state) => state.bulkInventory.editCreatTransfer);
  const location = useLocation();
  const transferCode = location.pathname.replace('/bulkInventory/', '');
  const [editRow, setEditRow] = useState(null);
  const [gotoQty, setGotoQty] = useState(false);
  const [finalData, setFinalData] = useState([]);
  const [activePid, setActivePid] = useState();
  const [activeRow, setActiveRow] = useState(-1);

  const fetchedPids = useSelector((state) => state.bulkInventory.fetchedPidData);

  let data;
  if (fetchedPids[activePid]) {
    data = fetchedPids[activePid].data;
  }
  // NEED TO MOVE OUTSIDE OF THE COMPONENT
  const initialPidData = {
    productId: '',
    productDesc: CONTENT.PID_DESCRIPTION,
    quantity: 0,
    avail_qty: '',
    canEditPid: true,
    newPid: true
  };

  const initialFinalData = {
    transferCode,
    sourceFacility,
    destFacility: destinationFecility,
    removePidList: [],
    items: [],
    newItems: []
  };

  const cancelEdit = () => {
    setEditTransfer(false);
    setEditRow(null);
  };

  useEffect(() => {
    if (editableData) {
      editableData.map((item) => {
        const newItem = {
          productId: item.pid,
          productDesc: item.productDesc,
          quantity: item.qty
        };
        return initialFinalData.items.push(newItem);
      });
    }
    setFinalData(initialFinalData);
  }, []);

  useEffect(() => {
    const isPidExist = finalData.newItems?.some((i) => i.productId === Number(activePid));
    if (!isEmpty(data) && gotoQty && !isPidExist) {
      setEditRow({ ...editRow, productId: data.productId, productDesc: data.value, isPid: true });
      const pidData = finalData.items.map((item, index) =>
        index === 0 ? { ...item, productDesc: data.value, productId: data.productId } : item
      );

      setFinalData((prev) => ({
        ...prev,
        items: pidData
      }));

      setTimeout(() => {
        quantityRef.current.focus();
      }, 100);
      setActivePid('');
    }
    if (createTransferData) {
      cancelEdit();
      dispatch(resetInventoryData(null));
      if (editableData) {
        editableData.map((item) => {
          const newItem = {
            productId: item.pid,
            productDesc: item.productDesc,
            quantity: item.qty
          };
          return initialFinalData.items.push(newItem);
        });
      }
      setFinalData(initialFinalData);
    }
    if (createTransferError) {
      dispatch(resetInventoryData(null));
      callTransferLoad();
      if (editableData) {
        editableData.map((item) => {
          const newItem = {
            productId: item.pid,
            productDesc: item.productDesc,
            quantity: item.qty
          };
          return initialFinalData.items.push(newItem);
        });
      }
      setFinalData(initialFinalData);
    }
  }, [dispatch, data, fetchedPids, createTransferError, createTransferData, finalData.newItems]);

  const showPidError = () => {
    dispatch(
      toastOpen({
        isToastOpen: true,
        heading: CONTENT.PLEASE_ENTER_QTY,
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  };

  const checkPid = (value) => {
    const pidAlreadyExist = finalData.items.some((i) => i.productId === Number(value));
    const isPidExist = finalData.newItems.some((i) => i.productId === Number(value));
    if (pidAlreadyExist || isPidExist) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PID_ALREADY_ADDED,
          subHeading: CONTENT.PLEASE_CHOOSE_DIFF_PID,
          severity: 'error',
          autoHideDuration: 2000
        })
      );
    } else if ((value !== '' && !fetchedPids[value]) || fetchedPids[value]?.error) {
      dispatch(fetchPidDataLoad(value));
      setGotoQty(true);
    } else if (fetchedPids[value]) {
      setGotoQty(true);
    }
  };

  const pidInput = (value) => (
    <LkInput
      fullWidth
      variant="outlined"
      placeholder={value?.toString() || CONTENT.ENTER_PID}
      onBlur={(e) => {
        checkPid(e.currentTarget.value);
        setActivePid(e.currentTarget.value);
      }}
    />
  );

  const updateQty = (qty, item, index, newItem) => {
    if (qty !== '' && Number(qty) > 0) {
      const filteredItem = finalData.items;
      if (newItem) {
        filteredItem[index].canEditPid = false;
      }
      filteredItem[index].quantity = Number(qty);
      setFinalData({ ...finalData, items: filteredItem });
    } else {
      showPidError();
    }
  };

  const qtyInput = (value, item, index, newItem, pid) => (
    <LkInput
      fullWidth
      variant="outlined"
      disabled={!pid}
      onBlur={(e) => updateQty(e.currentTarget.value, item, index, newItem)}
      onKeyPress={(e) => numberOnly(e)}
      placeholder={value?.toString()}
      inputRef={newItem ? quantityRef : null}
    />
  );

  const deleteItem = (pid) => {
    const filteredItem = finalData.items.filter((i) => i.productId !== pid);
    setFinalData({
      ...finalData,
      items: filteredItem,
      removePidList: [...finalData.removePidList, pid]
    });
  };
  const addTransferData = () => {
    const payload = { ...finalData };
    payload.items = finalData.items.filter(({ newPid }) => !newPid);
    payload.newItems = finalData.items.filter(({ newPid }) => newPid);

    dispatch(editCreateTransferLoad(payload));
  };

  const addPidFunc = () => {
    setActiveRow(-1);
    setFinalData({
      ...finalData,
      items: [{ ...initialPidData }, ...finalData.items]
    });
  };

  const headerConfig = [
    {
      name: CONTENT.PID,
      key: 'pid',
      style: { minWidth: '50px', maxWidth: '50px' },
      formatBody: useCallback(
        ({ productId, canEditPid }) =>
          canEditPid ? (
            pidInput(productId)
          ) : (
            <span className={cx(`${classes.readOnlyInput} fs14`)}>{productId}</span>
          ),
        [finalData]
      )
    },
    {
      name: CONTENT.PID_DESCRIPTION,
      key: 'productDesc',
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ productDesc }) => (
          <Box>
            <LkToolTip
              placement="bottom-end"
              title={<Box p={1}>{productDesc} </Box>}
              className="dtoolTip"
            >
              <div className="ellipsis-vertical text-99">{productDesc}</div>
            </LkToolTip>
          </Box>
        ),
        []
      )
    },
    {
      name: CONTENT.QUANTITY,
      key: 'qty',
      style: { minWidth: '40px', maxWidth: '40px' },
      formatBody: useCallback(
        (item, index) => qtyInput(item.quantity, item, index, item.canEditPid, item.productId),
        [editRow, finalData]
      )
    },
    {
      name: CONTENT.AVAILABLE_QTY,
      key: 'avail_qty',
      style: { minWidth: '40px', maxWidth: '40px', textAlign: 'center' }
    },
    {
      name: '',
      key: 'action',
      style: { minWidth: '10px', maxWidth: '10px' },
      formatBody: useCallback(
        ({ productId }) => (
          <IconButton aria-label="delete" onClick={() => deleteItem(productId)}>
            <DeleteOutlineIcon />
          </IconButton>
        ),
        [finalData]
      )
    }
  ];
  const tableheight = useMemo(
    () =>
      !finalData?.items?.length || finalData?.items?.length === 1
        ? 106
        : finalData.items.length * 59 + 49,
    [finalData]
  );

  const disableAddPid = useMemo(
    () => finalData?.items?.some(({ productId, quantity }) => !productId || !quantity),
    [finalData]
  );

  const onRowClickFun = useCallback((tempRowData, index) => setActiveRow(index), []);

  return (
    <Box className={classes.createContainer}>
      <Box className={classes.btnContainer}>
        <Box>
          <Button
            className={cx(classes.btn)}
            size="small"
            variant="outlined"
            color="primary"
            startIcon={<AddCircleIcon />}
            onClick={() => addPidFunc()}
            disabled={disableAddPid}
          >
            {CONTENT.ADD_PID}
          </Button>
          {/* <Button
            className={cx(classes.btn, classes.csv)}
            size="small"
            variant="outlined"
            color="primary"
            startIcon={<SystemUpdateAltIcon />}
            onClick={() => setOpenCsvModal(true)}
          >
            {CONTENT.IMPORT_CSV}
          </Button> */}
        </Box>
        <Box>
          <Button
            className={cx(classes.btn)}
            size="small"
            variant="outlined"
            color="primary"
            onClick={() => cancelEdit()}
          >
            {CONTENT.CANCEL}
          </Button>
          <Button
            className={cx(classes.btn, classes.done)}
            size="small"
            variant="contained"
            color="primary"
            component="span"
            disabled={disableAddPid}
            onClick={() => addTransferData()}
          >
            {CONTENT.DONE}
          </Button>
        </Box>
      </Box>
      {showEditTransfer && (
        <LkTable
          isNonVertualizedTable
          tableHeight={tableheight}
          headerConfig={headerConfig}
          isDataFetching={createTransferLoader}
          tableData={finalData.items}
          totalRowsCount={1}
          showTableCount={false}
          rowSize={60}
          selectedRows={[activeRow]}
          rowKey="index"
          onRowClick={onRowClickFun}
        />
      )}
      <Box mt={2} className="text-right">
        <Button
          className={cx(classes.btn)}
          size="small"
          variant="outlined"
          color="primary"
          startIcon={<AddCircleIcon />}
          onClick={() => addPidFunc()}
          disabled={disableAddPid}
        >
          {CONTENT.ADD_PID}
        </Button>
      </Box>
    </Box>
  );
};

export default EditCreateTransfer;
