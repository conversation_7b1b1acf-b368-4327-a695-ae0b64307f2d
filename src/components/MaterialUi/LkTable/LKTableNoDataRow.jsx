import React from 'react';
import { makeStyles } from 'tss-react/mui';
import { StyledTableCell, StyledTableRow } from './LkTableCommon';

const useStyles = makeStyles()(() => ({
  tableRow: {
    position: 'relative',
    background: '#ffffff'
  },
  tableCell: {
    position: 'absolute',
    top: '45%',
    fontSize: 20,
    left: 0,
    right: 0,
    textAlign: 'center',
  }
}));

const LKTableNoDataRow = ({ noDataText }) => {
  const { classes } = useStyles();
  return (
    <StyledTableRow component="div" className={classes.tableRow}>
      <StyledTableCell component="div" className={classes.tableCell}>
        {noDataText}
      </StyledTableCell>
    </StyledTableRow>
  );
};

export default React.memo(LKTableNoDataRow);
