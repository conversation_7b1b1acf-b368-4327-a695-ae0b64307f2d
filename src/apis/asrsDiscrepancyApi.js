import config from '../config';
import { tokenInstance } from '../utils/axiosInstance';

const createParams = ({
  pid,
  page,
  status,
  sortBy,
  facility,
  listType,
  location,
  barcode,
  pageSize,
  sortOrder,
  availability,
  totalCountRequired,
  condition
}) => ({
  pid,
  page,
  status,
  sortBy,
  facility,
  listType,
  barcode,
  location,
  sortOrder,
  availability,
  size: pageSize,
  totalCountRequired,
  condition: condition?.toUpperCase()
});

export const getAsrsDiscrepancyApi = (payload) =>
  tokenInstance().get(config.asrsDiscrepancy.discrepancyReport, { params: createParams(payload) });

export const exportBarcodesListApi = (payload) =>
  tokenInstance().get(config.asrsDiscrepancy.discrepancyReport, { params: createParams(payload) });
