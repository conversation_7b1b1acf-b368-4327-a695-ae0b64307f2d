// src/hooks/useHistory.js
import { useLocation, useNavigate } from 'react-router-dom';

const useHistory = () => {
  const navigate = useNavigate();
  const location = useLocation();

  return {
    push: (pathOrObject) => {
      if (typeof pathOrObject === 'string') {
        navigate(pathOrObject);
      } else if (typeof pathOrObject === 'object' && pathOrObject !== null) {
        const { pathname, state } = pathOrObject;
        navigate(pathname, { state });
      } else {
        // eslint-disable-next-line no-console
        console.error('Invalid argument passed to push function');
      }
    },
    replace: (path) => navigate(path, { replace: true }),
    goBack: () => navigate(-1),
    goForward: () => navigate(1),
    location
  };
};

export default useHistory;
