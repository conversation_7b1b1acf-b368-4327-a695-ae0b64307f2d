import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Tab from '@mui/material/Tab';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import './tabsContainer.scss';

import { useDispatch, useSelector } from 'react-redux';

import { StyledTabs } from 'components/common/StyledTabs';
import GRNTab from 'components/GRNListing/GRNTab';
import useFilterHook from 'components/common/useFilterHook';
import { getAllQueryParam, generatePayloadForSearchAPI } from 'utils/helpers';
import { grnSearchLoad } from 'redux/actionCreators/grnListing';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import InvoiceDashboard from 'views/Receiving/InvoiceDashboard';
import { LOCALISATION } from 'redux/reducers/localisation';
import DebitNote from 'views/DebitNote/DebitNote';
import PurchaseOrderDetailTable from './PuchaseOrderDetailTable';

const styles = makeStyles()(() => ({
  positionRelative: {
    position: 'relative'
  }
}));

const PAGE_SIZE = 25;

const defaultObj = (name = 'grn?version=v1', sortKey = 'created_at', type = 'grn') => ({
  isReset: true,
  path: name,
  payload: {
    type,
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey,
      sortOrder: 'DESC'
    }
  }
});

const sortingData = {
  key: 'created_at',
  order: 'DESC'
};

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const TabsContainer = ({
  purchaseOrderDetail,
  purchaseOrderDetailProccessing,
  currency
}) => {
  const location = useLocation();
  const { classes } = styles();
  const [objPayload, setObjPayload] = useState(defaultObj());
  const dispatch = useDispatch();
  const [value, setValue] = React.useState(0);

  const PO = useSelector((state) => state[LOCALISATION].localeData.PO);

  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const [sortKey, setSortKey] = useState('created_at');

  const tabNames = [PO.PRODUCTS, PO.INVOICE, PO.GRN, PO.DEBIT_NOTE];

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  // grn call
  useEffect(() => {
    if (value === 2) {
      const result = getAllQueryParam(window.location.search);
      objPayload.payload = defaultObj().payload;
      objPayload.isReset = true;
      let { payload } = objPayload;
      payload.pageRequest.sortKey = sortingData.key;
      payload.pageRequest.sortOrder = sortingData.order;
      payload = generatePayloadForSearchAPI(
        result,
        payload,
        { ...mapSearchFilterKey, poNum: 'po_num' },
        'GRN_'
      );
      objPayload.payload = payload;
      setObjPayload({ ...objPayload });
      dispatch(grnSearchLoad(objPayload));
    }
  }, [location.search, dispatch, value]);

  return (
    <div className={classes.root}>
      <Box width="100%" margin="0 auto" className="poTabs">
        <StyledTabs
          value={value}
          onChange={handleChange}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          {tabNames.map((name, index) => (
            <Tab
              key={name}
              label={name}
              style={{ position: 'relative' }}
              className={`${value === index ? 'tabselected' : ''} potab`}
            />
          ))}
        </StyledTabs>
        <Box className={`border-grey5-radiusbase ${value === 0 && 'border-left-no-radius'}`}>
          <TabPanel value={value} index={0} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab1Absolute}>
              {' '}
            </Box>
            <Box>
              <PurchaseOrderDetailTable
                purchaseOrderDetail={purchaseOrderDetail}
                purchaseOrderDetailProccessing={purchaseOrderDetailProccessing}
                currency={currency}
              />
            </Box>
          </TabPanel>
          <TabPanel value={value} index={2} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab2Absolute}>
              {' '}
            </Box>
            <GRNTab
              mainGrnSearch=""
              handleChange={handleChange}
              sortKey={sortKey}
              setSortkey={setSortKey}
              selectedFilterList={selectedFilterList}
              onChangeFilterList={onChangeFilterList}
              deleteSelectedFilter={deleteSelectedFilter}
              resetFilters={resetFilters}
              sortingData={sortingData}
              objPayload={objPayload}
            />
          </TabPanel>

          <TabPanel value={value} index={1} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab2Absolute}>
              {' '}
            </Box>
            <InvoiceDashboard />
          </TabPanel>
          <TabPanel value={value} index={3} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab2Absolute}>
              {' '}
            </Box>
            <DebitNote />
          </TabPanel>
        </Box>
      </Box>
    </div>
  );
};

export default TabsContainer;
