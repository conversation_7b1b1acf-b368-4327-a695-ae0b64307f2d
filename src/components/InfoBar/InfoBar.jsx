import Box from '@mui/material/Box';
import React from 'react';
import PropTypes from 'prop-types';

import './infoBar.scss';

/**
 * @typedef {import('@mui/material').BoxProps} BoxProps
 */

/**
 *
 * @typedef {Object} InfoBarProps
 * @property {string} [className]
 *  @property {Array<{ key: string, value: string | Number }>} [data]
 */

/**
 * @param {InfoBarProps & BoxProps} rest
 */
const InfoBar = ({ className, data, ...rest }) => (
  <Box
    className={`${className} border-radius-6 bg-f5 display-flex info-bar`}
    style={{ justifyContent: 'space-evenly' }}
    {...rest}
  >
    {data?.map((item, index) => {
      const { key, value } = item;
      return (
        <div className="text-center pd-l25 pd-r25 pd-t5 pd-b10" key={key}>
          <div data-cy={`${key}-${index}`} className="fs12 text-66">
            {key}
          </div>
          <div data-cy={`${value}-${index}`} className="fs12 fw-bold text-33 mr-t5">
            {value}
          </div>
        </div>
      );
    })}
  </Box>
);

export default InfoBar;

InfoBar.propTypes = {
  className: PropTypes.string,
  data: PropTypes.arrayOf(
    PropTypes.exact({
      key: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
    })
  ).isRequired
};
