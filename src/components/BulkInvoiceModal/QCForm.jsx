import React from 'react';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { FieldArray } from 'react-final-form-arrays';
import { Form, FormSpy } from 'react-final-form';
import arrayMutators from 'final-form-arrays';

import { required, integer } from 'utils/validation';
import { composeValidators } from 'utils/helpers';
import { StyledTableCell, StyledTableRow } from 'theme/table.theme';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const QCForm = ({
  bulkQCData,
  onSubmit,
  classes,
  createTextField,
  maxGrnNumber,
  confirmBulkQc,
  confirm
}) => {
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);

  const headerConfig = [
    {
      name: INVOICE.ITEM_DESCRIPTION,
      headerStyle: { width: '200px' }
    },
    {
      name: INVOICE.QUANTITY,
      headerStyle: { width: '105px' }
    },
    {
      name: INVOICE.UNIT_PRICE,
      headerStyle: { width: '105px' }
    },
    {
      name: INVOICE.SHORT_QTY,
      headerStyle: { width: '105px' }
    },
    {
      name: INVOICE.QC_FAIL_BULK_QTY,
      headerStyle: { width: '105px' }
    },
    {
      name: INVOICE.QC_FAIL_REASON,
      headerStyle: { width: '300px' }
    }
  ];

  return (
    <Form
      initialValues={{ invoiceData: bulkQCData }}
      onSubmit={onSubmit}
      mutators={{
        ...arrayMutators
      }}
      render={({ handleSubmit, valid, values }) => (
        <form onSubmit={handleSubmit}>
          <div className={classes.tableBulkqc}>
            <FieldArray name="invoiceData">
              {({ fields }) => (
                <TableContainer component={Paper} className={classes.tableContainer}>
                  <Table stickyHeader className={classes.table} aria-label="sticky table">
                    <TableHead>
                      <TableRow>
                        <StyledTableCell align="left">PID</StyledTableCell>
                        {headerConfig.map(({ name, headerStyle }) => (
                          <StyledTableCell key={name} style={headerStyle} align="left">
                            {name}
                          </StyledTableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {fields.map((row, i) => (
                        <StyledTableRow key={fields.value[i].name}>
                          <StyledTableCell>{fields.value[i].product_id}</StyledTableCell>
                          <StyledTableCell style={{ width: '200px' }}>
                            <div className="ellipsis-vertical-one">{fields.value[i].name}</div>
                          </StyledTableCell>
                          <StyledTableCell style={{ width: '105px' }}>
                            {fields.value[i].quantity_original}
                          </StyledTableCell>
                          <StyledTableCell style={{ width: '105px' }}>
                            {fields.value[i].vendor_unit_cost_price}
                          </StyledTableCell>
                          <StyledTableCell style={{ width: '105px' }}>
                            {fields.value[i].short_qty}
                          </StyledTableCell>
                          <StyledTableCell style={{ width: '105px' }}>
                            {createTextField(
                              row,
                              'bulk_qc_failed_qty',
                              composeValidators(
                                required,
                                integer,
                                maxGrnNumber(fields.value[i].short_qty)
                              ),
                              'bulkqcqty'
                            )}
                          </StyledTableCell>
                          <StyledTableCell style={{ width: '300px' }}>
                            {createTextField(
                              row,
                              'reason',
                              composeValidators(required),
                              'bulkqcreason'
                            )}
                          </StyledTableCell>
                        </StyledTableRow>
                      ))}
                    </TableBody>
                  </Table>
                  <FormSpy
                    subscription={{ valid: true }}
                    onChange={(props) => {
                      if (!props.valid) {
                        confirmBulkQc([]);
                      }
                    }}
                  />
                </TableContainer>
              )}
            </FieldArray>
          </div>
          <div className="display-flex justify-content-center mr-b40">
            <div className="mr-l5">
              <Button
                onClick={() => confirm(values)}
                disabled={!valid}
                style={{ borderRadius: 8 }}
                variant="contained"
                color="primary"
              >
                {INVOICE.CONFIRM}
              </Button>
            </div>
          </div>
        </form>
      )}
    />
  );
};

export default QCForm;
