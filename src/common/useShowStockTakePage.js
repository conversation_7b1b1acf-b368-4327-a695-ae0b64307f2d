import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { CONSUL } from 'redux/reducers/consul';

const useShowStockTakePage = () => {
  const [showPage, setShowPage] = useState(false);
  const [showButton, setShowButton] = useState(false);

  const selectedFacility = useSelector((state) => state.settings.selectedFacility);
  const { isLoading, data } = useSelector((state) => state[CONSUL].stockTakeFacility);

  useEffect(() => {
    const shouldShowPage = data?.some(({ facility }) => selectedFacility === facility);
    const shouldShowButton = data?.some(
      ({ showCloseButon, facility }) => selectedFacility === facility && showCloseButon
    );
    setShowPage(shouldShowPage);
    setShowButton(shouldShowButton);
  }, [selectedFacility, data.length]);

  return { showPage, showButton, isLoading };
};

export default useShowStockTakePage;
