/* eslint-disable no-use-before-define */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-param-reassign */
import React, { useEffect, useState, useCallback, useRef, createRef } from 'react'; // useRef
import { TextField, Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

import {
  getTwoYearDateFromNow,
  formatDateDashedSeparated,
  regextest,
  debounce
} from 'utils/helpers';
import { getBarcodeSeriesSelector } from 'selectors/barcodeSeries';
import BarCodeWrapper from './BarCodeWrapper';
import { toastOpen } from '../../redux/actionCreators/toast';
import {
  createBoxCode,
  createQcDetail,
  qcDataUpdate,
  getExpiryDetailLoad,
  getQcDataLoad,
  proceedToGreenChannel
} from '../../redux/actionCreators/grn';
import BarCodeModals from './BarCodeModals';
import useTotalCountOfItems from './useTotalCountOfItems';
import QcFailCodeData from '../../__mocks__/QcFailCodeData';
import { CATEGORY_MAPPING } from '../../config/CategoryMapping';
import { getBarcodeFromURL } from '../../utils/helpers';

const styles = makeStyles()((theme) => ({
  root: {
    background: 'white',
    borderRadius: theme.spacing(0.5)
  },
  inputField: {
    width: '100%',
    background: 'white',
    outline: 0,
    border: '1px solid #bdbdbd',
    padding: 10,
    borderRadius: theme.spacing(0.5),
    '&::placeholder': {
      color: '#a4a9ab'
    }
  }
}));

let barcodeValue = '';

const obj = {
  proceedTogreenChaneelPopupCount: 0
};

const BarCodeInput = ({
  clearSearch,
  pidStatus,
  expiryDetails,
  is_box_required = false,
  prefilled_invoice = false,
  toggleAddPidModal,
  toggleAddMoreItemModal
}) => {
  const [modalOpen, setModalOpen] = useState({
    greenChannel: false,
    redChannel: false,
    pidMisMatch: false
  });
  const dispatch = useDispatch();
  const [previousExpiryDate, setPreviousExpiryDate] = useState('');
  const elRef = useRef([createRef(), createRef(), createRef()]);
  const {
    boxCodeSuccess,
    isProceedToGreenChannel,
    ASNApiSuccess,
    ASNApiError,
    boxCodeError,
    qcDetailsSuccess,
    qcDetailsError,
    QCData,
    meta,
    pidDetail,
    updateQtyData,
    pidList
  } = useSelector((state) => state.grn);
  const { boxCodeRegex, itemCodeRegex } = useSelector(getBarcodeSeriesSelector);
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);

  // const getIndexFn = useCallback(item => getIndex(item), []);
  const {
    pid,
    status,
    grn_code,
    estimated_qty,
    total_scanned,
    expiry_format,
    expiry_offset,

    expiry_date_threshold,
    lot_offset,
    lot_length
  } = pidDetail.result.data;
  const countInfo = useTotalCountOfItems(QCData);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [lotNumValue, setLotNumValue] = useState('');
  const [barCode, setBarCode] = useState({
    value: '',
    disable: estimated_qty <= total_scanned || status === 'FAILED'
  });
  const [expiryDate, setExpiryDate] = useState({ value: '', disable: true });
  const [qcCode, setQcCode] = useState({ value: '', disable: true });
  const length = expiryDetails ? 3 : 2;
  const width = 98 / length;
  const { classes } = styles();
  const itemRegexFun = (barcode) => {
    const itemRegex = itemCodeRegex.some((t) => regextest(barcode, t));
    if (!itemRegex) {
      const tempElement = document.getElementById('itemBox');
      if (tempElement) {
        tempElement.value = '';
      }
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: 'Please Scan valid Bar Code',
          severity: 'error'
        })
      );
    }
    return itemRegex;
  };

  const focusAndUpdate = (elem, updateAction) => {
    updateAction((t) => ({ ...t, disable: false }));
    const tempElement = document.getElementById(elem);
    if (tempElement) {
      tempElement.focus();
    }
  };

  const boxItemScan = () => {
    const test = boxCodeRegex.some((t) => regextest(barcodeValue, t));
    if (test) {
      const tempElement = document.getElementById('itemBox');
      if (tempElement) {
        tempElement.value = '';
      }
      // is_box_required call an API
      if (is_box_required) {
        return dispatch(
          createBoxCode({
            barcode: barcodeValue,
            grn_code,
            pid
          })
        );
      }
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: 'Please Enter Item Code',
          severity: 'error'
        })
      );
    } else {
      const boxKey = QCData?.qc_passed ? Object.keys(QCData.qc_passed)[0] : null;
      if (is_box_required && (boxKey === 'NO_BOX' || !boxKey)) {
        const tempElement = document.getElementById('itemBox');
        if (tempElement) {
          tempElement.value = '';
        }
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: 'Please Scan Boxcode',
            severity: 'error'
          })
        );
      } else {
        const itemRegex = itemRegexFun(barcodeValue);
        if (itemRegex) {
          if (prefilled_invoice) {
            setExpiryDate({ ...qcCode, disable: false });
            const { vendor_invoice_number, po_num } = meta.invoice;
            // mock data
            // dispatch(getExpiryDetailLoad({ barcode: barCode.value,
            // "po_number": "POB/2021/002797", vendor_invoice_number: "Inovice_ASN2" }))

            dispatch(
              getExpiryDetailLoad({
                barcode: barcodeValue,
                po_number: po_num,
                vendor_invoice_number
              })
            );
          }
          if (expiryDetails) {
            if (previousExpiryDate) {
              // if date is already scanned previously
              // setExpiryDate(() => ({ value: previousExpiryDate, disable: false }));
              qcDetailsScan(true);
            } else {
              focusAndUpdate('expiryDate', setExpiryDate);
            }
          } else {
            qcDetailsScan(true);
          }
        }
      }
    }
    return 0;
  };

  const clearBarcodeValue = () => {
    const barcode = document.getElementById('itemBox');
    barcode.value = '';
  };

  const qcDetailsScan = (isgreenChannelTrue = false, dateFromText, lotNumTemp) => {
    const categoryId = pidDetail?.result?.data?.categoryId;

    if (!isgreenChannelTrue) {
      const QcFailCodeDataTemp =
        QcFailCodeData[CATEGORY_MAPPING[categoryId]] || QcFailCodeData.Eyeframe;
      const test = QcFailCodeDataTemp.content.some((t) => t.textCode === qcCode.value);
      if (qcCode.value === barcodeValue || test) { /* empty */ } else {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: 'Please Scan valid Bar Code',
            severity: 'error'
          })
        );
      }
    }
    const productIndex = meta?.invoice?.items?.findIndex((t) => t.product_id === pid) ?? -1;
    const poQuantityIndex = meta?.po?.po_items.findIndex((t) => t.product_id === pid) ?? -1;
    const legal_owner = pidList?.result?.data?.legal_owner || '';
    const payload = {
      barcode: barcodeValue,
      grn_code,
      pid,
      lot_no: ASNApiSuccess?.data?.lot || lotNumTemp || lotNumValue,
      qc_status: isgreenChannelTrue || barcodeValue === qcCode.value ? 'pass' : 'fail',
      expiry_date_threshold: expiry_date_threshold || 0,
      estimated_quantity: estimated_qty,
      // "meta": meta
      po_id: meta.invoice.po_num,
      invoice_id: meta.invoice.vendor_invoice_number,
      product: productIndex === -1 ? {} : meta.invoice.items[productIndex],
      vendor_code: meta.invoice.vendor_code,
      unicom_grn_code: meta.unicom_grn_code,
      invoice_ref_number: meta.invoice.invoice_ref_number,
      poQuantity: poQuantityIndex === -1 ? 0 : meta?.po?.po_items[poQuantityIndex].quantity,
      legal_owner
    };
    if (expiryDetails) {
      if (dateFromText || expiryDate.value) {
        setPreviousExpiryDate(dateFromText || expiryDate.value);
      }
      payload.expiry_date = dateFromText || expiryDate.value || previousExpiryDate;
    }
    if (is_box_required) {
      payload.qc_pass_box_barcode = Object.keys(QCData.qc_passed)[0];
    }
    if (payload.qc_status === 'fail') {
      const QcFailCodeDataTemp =
        QcFailCodeData[CATEGORY_MAPPING[categoryId]] || QcFailCodeData.Eyeframe;
      const findQcReason = QcFailCodeDataTemp.content.filter((t) => t.textCode === qcCode.value);

      payload.qc_fail_code = qcCode.value;
      payload.qc_reason = findQcReason[0]?.['QC Fail Reason'];
      delete payload.qc_pass_box_barcode;
    }
    // setLotNumValue("");
    clearBarcodeValue();
    barcodeValue = '';
    setBarCode((t) => ({ ...t, value: '' }));
    setQcCode(() => ({ value: '', disable: true }));
    // setExpiryDate({ value: '', disable: true });
    const itemBox = document.getElementById('itemBox');
    itemBox.focus();
    if (pidList?.result?.data?.type) {
      payload.type = pidList.result.data.type;
    }
    if (pidList?.result?.data?.invoice_level) {
      payload.invoice_level = pidList.result.data.invoice_level;
    }
    payload.sampling_percent = pidDetail.result?.data?.sampling_percent;
    return dispatch(createQcDetail(payload));
  };

  const keyPress = (e) => {
    // if (e.keyCode === 9) {
    // setExpiryDate({ ...expiryDate, value: '' });
    // setShowDatePicker(false);
    // }
    if (e.keyCode === 13) {
      barcodeValue = getBarcodeFromURL(e.target.value);
      // setShowDatePicker(false);
      boxItemScan();
    }
  };
  useEffect(() => {
    const itemBox = document.getElementById('itemBox');
    itemBox.focus();
    return () => {
      barcodeValue = '';
    };
  }, []);

  useEffect(() => {
    if (countInfo) {
      const isCompleted = estimated_qty <= countInfo[0]?.Scanned || pidStatus === 'FAILED';
      if (isCompleted) {
        setBarCode((t) => ({ ...t, disable: true }));
        const itemStatus = qcDetailsSuccess?.result?.data?.status || '';
        if (pidStatus !== 'FAILED' && itemStatus !== 'FAILED') {
          toggleAddMoreItemModal(true);
        }
      } else {
        toggleAddMoreItemModal(false);
        setBarCode((t) => ({ ...t, disable: false }));
      }
      setBarCode((t) => ({ ...t, disable: false }));
      if (pidStatus === 'FAILED') {
        setBarCode((t) => ({ ...t, disable: true }));
      }
    }
  }, [countInfo, estimated_qty, toggleAddMoreItemModal, qcDetailsSuccess, status, pidStatus]);

  useEffect(() => {
    if (pidDetail) {
      const itemBox = document.getElementById('itemBox');
      itemBox.focus();
      const { status  : statusTemp} = pidDetail.result.data;
      obj.proceedTogreenChaneelPopupCount = 0;
      if (statusTemp === 'FAILED') {
        clearBarcodeValue();
        barcodeValue = '';
        setBarCode({ value: '', disable: true });
      }
    }
  }, [pidDetail]);

  useEffect(() => {
    if (updateQtyData) {
      obj.proceedTogreenChaneelPopupCount = 0;
    }
  }, [updateQtyData]);

  useEffect(() => {
    const qcDetails = document.getElementById('QCdetails');
    if (qcDetails) {
      qcDetails.focus();
    }
  }, [qcCode.disable]);

  useEffect(() => {
    if (!previousExpiryDate) {
      const expiryId = document.getElementById('expiryDate');
      if (expiryId) {
        expiryId.focus();
      }
    }
  }, [expiryDate.disable]);

  const keyPressQc = (e) => {
    if (e.keyCode === 13) {
      setQcCode({ ...qcCode, value: getBarcodeFromURL(e.target.value) });
      // barcode NXA000000067
      qcDetailsScan();
    }
  };

  const keyPressExpiry = (e, fromDate = true, valueFromText, lotNumTemp) => {
    if (e.keyCode === 27) {
      setLotNumValue('');
      setShowDatePicker(false);
      // setExpiryDate({ ...expiryDate, value: '' });
    }
    if (e.keyCode === 13) {
      if (expiryDate.value === '' && fromDate) {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: 'Please Enter valid Date',
            severity: 'error'
          })
        );
      }
      const dateFirst = new Date(valueFromText || expiryDate.value);
      const dateSecond = new Date();
      const timeDiff = Math.abs(dateSecond.getTime() - dateFirst.getTime());
      const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
      const { expiry_date_threshold  : tempExpiryDateThreshold} = pidDetail.result.data;

      if (tempExpiryDateThreshold && diffDays < tempExpiryDateThreshold) {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: 'Expiry date is below Threshold',
            severity: 'error'
          })
        );
      }
      qcDetailsScan(true, valueFromText, lotNumTemp);
      // removed this flow because no sampling percentage considered now
      // if (isProceedToGreenChannel[pid]) {
      //   qcDetailsScan(true, valueFromText, lotNumTemp);
      // } else {
      //   const QCdetails = document.getElementById('QCdetails');
      //   QCdetails.focus();
      //   setQcCode({ ...qcCode, disable: false });
      // }
    }
    return 0
  };

  useEffect(() => {
    if (boxCodeError) {
      setQcCode((t) => ({ ...t, disable: true }));

      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: boxCodeError.display_message,
          severity: 'error'
        })
      );
    }
  }, [boxCodeError, dispatch]);

  // ASN api response

  useEffect(() => {
    if (ASNApiSuccess) {
      setShowDatePicker(true);
      setExpiryDate((t) => ({ ...t, value: ASNApiSuccess.data.expiry }));
      setQcCode((t) => ({ ...t, disable: false }));
    }
    if (ASNApiError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: ASNApiError?.response?.data?.display_message,
          severity: 'error'
        })
      );
    }
  }, [ASNApiSuccess, ASNApiError, dispatch]);

  // qc failure open snackbar

  useEffect(() => {
    if (qcDetailsError) {
      const { data, status : statusTemp } = qcDetailsError;
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: data.display_message,
          severity: 'error'
        })
      );

      if (statusTemp === 406 && data.error_code === 'LK-GRN-008') {
        clearBarcodeValue();
        barcodeValue = '';
        setBarCode({ value: '', disable: true });
        setQcCode({ value: '', disable: true });
        // setExpiryDate({ value: '', disable: true });
        setModalOpen({ greenChannel: false, redChannel: true });
      } else {
        // elRef.current[0].current.focus();
        // const itemBox = document.getElementById('itemBox')
        // itemBox.focus()
        // setQcCode({ disable: true, value: "" })
        // setExpiryDate({ disable: true, value: "" })
        // setBarCode(t => { return { ...t, value: "" } })
      }

      const qcdataPayload = pidDetail.result.data;
      dispatch(getQcDataLoad(qcdataPayload));
    }
  }, [qcDetailsError, dispatch, pidDetail.result.data]);

  const delayedQuery = useCallback(
    debounce((payload) => {
      dispatch(getQcDataLoad(payload));
    }, 500),
    []
  );

  // QC success open snackbar

  useEffect(() => {
    if (qcDetailsSuccess) {
      const {
        result: { data }
      } = qcDetailsSuccess;
      const {
        barcode,
        status: statusTemp,
        qc_status,
        qc_reason,
        qc_fail_code,
        grn_code: grnCode
      } = data;
      delayedQuery({ pid, grn_code: grnCode });
      dispatch(
        qcDataUpdate({
          QCData,
          qc_reason,
          qc_fail_code,
          qc_status,
          barcode,
          is_box_required,
          status: statusTemp
        })
      );
      if (data.green_flag && isProceedToGreenChannel[pid] === false) {
        if (obj.proceedTogreenChaneelPopupCount === 0) {
          obj.proceedTogreenChaneelPopupCount = 1;
          setModalOpen({ greenChannel: true, redChannel: false });
        } else {
          dispatch(proceedToGreenChannel({ [pid]: true }));
        }
      }
    }
  }, [qcDetailsSuccess, dispatch, isProceedToGreenChannel, QCData, is_box_required, pid]);

  // Barcode success open snackbar

  useEffect(() => {
    if (boxCodeSuccess) {
      dispatch(
        qcDataUpdate({ createBox: true, QCData, barcode: boxCodeSuccess.barcode, is_box_required })
      );
      clearBarcodeValue();
      barcodeValue = '';
      setBarCode((t) => ({ ...t, value: '' }));
      const itemBox = document.getElementById('itemBox');
      itemBox.focus();
    }
  }, [boxCodeSuccess, dispatch, is_box_required, QCData]);

  const primaryBtn = () => {
    dispatch(proceedToGreenChannel({ [pid]: true }));
    // const { data } = pidDetail.result
    // dispatch(getQcDataLoad(data))
    setModalOpen({ ...modalOpen, greenChannel: false });
  };

  const handleModal = useCallback(
    (key, modalValue) => setModalOpen((t) => ({ ...t, [key]: modalValue })),
    [setModalOpen]
  );

  const extractDateFromBarCode = (barcodeScanned) => {
    barcodeScanned = barcodeScanned.slice(expiry_offset);
    const formatArray = expiry_format.split('-');
    let newDate = '';
    const resultArray = [];
    formatArray.forEach((t) => {
      let temp = barcodeScanned.slice(0, t.length);
      barcodeScanned = barcodeScanned.slice(t.length);
      if (t[0] === 'y' || t[0] === 'Y') {
        temp = temp.length === 2 ? `20${temp}` : temp;
        resultArray[0] = temp;
      } else if (t[0] === 'M' || t[0] === 'm') {
        resultArray[1] = temp;
      } else {
        resultArray[2] = temp;
      }
    });
    if (formatArray.length === 2) {
      resultArray[2] = '01';
    }
    newDate = resultArray.join('-');
    return newDate;
  };

  const onFocusFunction = () => {
    clearSearch('');
  };

  const onChangeExpiryDateText = (e) => {
    const { value } = e.target;
    setExpiryDate({ ...expiryDate, value });
  };

  const extractLotNum = (barCodeScanned) =>
    barCodeScanned.slice(lot_offset, lot_offset + lot_length);

  const keyPressExpiryDateText = (e) => {
    if (e.keyCode === 27) {
      setLotNumValue('');
      setShowDatePicker(true);
      setExpiryDate({ ...expiryDate, value: '' });
    }
    if (e.keyCode === 13) {
      const { value } = e.target;
      const lotNumTemp = extractLotNum(value);
      const tempValue = extractDateFromBarCode(value);
      setLotNumValue(lotNumTemp);
      setExpiryDate({ ...expiryDate, value: tempValue });
      keyPressExpiry({ keyCode: 13 }, false, tempValue, lotNumTemp);
    }
  };

  return (
    <>
      <BarCodeModals
        handleModal={handleModal}
        modalOpen={modalOpen}
        toggleAddPidModal={toggleAddPidModal}
        pidDetail={pidDetail}
        primaryBtn={primaryBtn}
        barCode={barcodeValue}
      />
      <Box display="flex" justifyContent="space-between" marginTop={2}>
        <BarCodeWrapper
          width={width}
          disable={!pidDetail ? true : barCode.disable}
          title={createGrnLocal.ENTER_DETAILS}
        >
          <input
            id="itemBox"
            ref={elRef.current[0]}
            disabled={!pidDetail ? true : barCode.disable}
            className={classes.inputField}
            // value={barCode.value}
            placeholder={
              is_box_required ? createGrnLocal.SCAN_ITEM_BOX_CODE : createGrnLocal.SCAN_ITEM
            }
            onFocus={() => onFocusFunction()}
            size="small"
            onKeyDown={keyPress}
            // onChange={(e) => { setBarCode({ ...barCode, value: e.target.value }) }}
          />
        </BarCodeWrapper>
        {expiryDetails && (
          <BarCodeWrapper
            width={width}
            disable={expiryDate.disable}
            title={createGrnLocal.EXPIRY_DETAILS}
          >
            {showDatePicker ? (
              <TextField
                id="expiryDate"
                type="date"
                ref={elRef.current[1]}
                onClick={() => {
                  if (ASNApiError && expiryDate.value === '') {
                    setExpiryDate({ ...expiryDate, value: getTwoYearDateFromNow() });
                  }
                }}
                fullWidth
                onChange={(e) => {
                  setExpiryDate({ ...expiryDate, value: e.target.value });
                }}
                disabled={!pidDetail ? true : expiryDate.disable}
                value={expiryDate.value}
                onKeyDown={keyPressExpiry}
                className={classes.root}
                variant="outlined"
                size="small"
                inputProps={{
                  min: formatDateDashedSeparated(Date.now())
                }}
                InputLabelProps={{
                  shrink: true
                }}
              />
            ) : (
              <TextField
                id="expiryDate"
                ref={elRef.current[1]}
                inputProps={{
                  autoComplete: 'off'
                }}
                autoFocus
                disabled={!pidDetail ? true : expiryDate.disable}
                className={classes.root}
                value={expiryDate.value}
                variant="outlined"
                placeholder={createGrnLocal.SCAN_QR_CODE}
                fullWidth
                size="small"
                onKeyDown={(e) => keyPressExpiryDateText(e)}
                onChange={onChangeExpiryDateText}
              />
            )}
          </BarCodeWrapper>
        )}
        <BarCodeWrapper width={width} disable={qcCode.disable} title={createGrnLocal.QC_DETAILS}>
          <TextField
            id="QCdetails"
            ref={elRef.current[2]}
            disabled={!pidDetail ? true : qcCode.disable}
            value={qcCode.value}
            className={classes.root}
            variant="outlined"
            placeholder="Scan QC Barcode"
            fullWidth
            size="small"
            onKeyDown={keyPressQc}
            onChange={(e) => setQcCode({ ...qcCode, value: e.target.value })}
          />
        </BarCodeWrapper>
      </Box>
    </>
  );
};
export default React.memo(BarCodeInput);
