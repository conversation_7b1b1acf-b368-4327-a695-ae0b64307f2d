import { useState } from 'react';
import FormValidator from '../components/Validations/FormValidator';

function useForms(ValidationRules, initialFormData) {
  const [form, setForm] = useState(initialFormData);
  const [errors, setErrors] = useState(false);

  const validator = new FormValidator(ValidationRules);

  const validateEachInput = (field, value) => {
    const validationStatus = validator.validateInput(field, value, { form, errors });
    const newErrors = { ...errors, [field]: '' };
    if (!validationStatus.isValid) {
      newErrors[field] = validationStatus.message;
    }
    setErrors(newErrors);
  };

  const onChangeForm = (event, name = '', newValue = '') => {
    const field = event?.target?.name || name;
    const value =
      (event?.target?.type === 'checkbox' ? event?.target?.checked : event?.target?.value) ||
      newValue;
    const newFormData = { ...form, [field]: value };
    setForm(newFormData);
    validateEachInput(field, newFormData[field]);
  };

  return { form, errors, onChangeForm, setForm, setErrors };
}

export default useForms;
