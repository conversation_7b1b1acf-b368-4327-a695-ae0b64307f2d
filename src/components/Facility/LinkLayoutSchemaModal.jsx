import React from 'react';
import LkModal from 'components/Modal/Modal';
import './linkLayoutSchemaModal.scss';
import { Field, Form } from 'react-final-form';
import { composeValidators } from 'utils/helpers';
import { Button } from '@mui/material';
import { required } from 'utils/validation';
import LkInput from 'components/MaterialUi/LkInput';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const LinkLayoutSchemaModal = ({
  facilityName,
  layoutName,
  openModal,
  setOpenModal,
  submitLinkLayoutData
}) => {
  const submitInfo = (values) => {
    submitLinkLayoutData(values);
  };

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.FACILITY);

  return (
    <LkModal
      title={CONTENT.LINK_LAYOUT_SCHEMA_TEMPLATE}
      subTitle={CONTENT.LAYOUT_SCHEMA_EDITED_WARNING}
      open={openModal}
      handleClose={setOpenModal}
    >
      <div className="linklayoutschema">
        <div className="mr-t40 mr-b30 facility-info text-center">
          <div className="fs10 text-60 mr-b5 mr-t25">{CONTENT.FACILITY}</div>
          <div className="fs16 text-3C">{facilityName}</div>
          <div className="fs10 text-60 mr-t25 mr-b15">{CONTENT.LINKED_TO}</div>
          <div className="pos-rel mr-b25">
            <div className="link-image-container">
              <div className="link-image">
                <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/Link.svg`} alt="Link" />
              </div>
            </div>
            <div className="line border-de pos-abs" />
          </div>
          <div className="fs10 text-60 mr-b5">{CONTENT.LAYOUT_SCHEMA_TEMPLATE}</div>
          <div className="fs16 text-3C mr-b25">{layoutName}</div>
        </div>
        <Form
          onSubmit={submitInfo}
          render={({ handleSubmit, invalid }) => (
            <form
              onSubmit={handleSubmit}
              name="reason-po-form"
              className="refrence-detail-form"
              noValidate
            >
              <Field name="name" validate={composeValidators(required)}>
                {({ input, meta }) => (
                  <div className="material-group mr-t40">
                    <LkInput {...input} fullWidth label={CONTENT.NAME_YOUR_LAYOUT_SCHEMA} />
                    <span className="input-error">
                      {meta.error && meta.touched ? meta.error : ''}
                    </span>
                  </div>
                )}
              </Field>
              <div className="display-flex justify-content-center mr-b40 mr-t40">
                <div>
                  <Button
                    type="submit"
                    disabled={invalid}
                    style={{ borderRadius: 8 }}
                    variant="contained"
                    color="primary"
                  >
                    {CONTENT.LINK_AND_PROCEED}
                  </Button>
                </div>
              </div>
            </form>
          )}
        />
      </div>
    </LkModal>
  );
};

export default LinkLayoutSchemaModal;
