/* eslint-disable max-len */
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const searchListApi = ({ path, payload }) => {
  const url = `${config.filters.searchList}${path}`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const searchSuggestionsApi = ({ data, type, url }) => {
  if (type === 'POST') {
    return tokenInstance()
      .post(url, data)
      .then((response) => ({ response }))
      .catch((error) => {
        throw error;
      });
  }
  const { key, value, recommendations = 25, apiName, putawayCode = '' } = data;
  let apiUrl = '';
  if (apiName === 'facilities') {
    apiUrl = `${config.facility.facilitySuggestion}?key=${key}&value=${value}&recommendations=${recommendations}`;
  } else if (apiName === 'putAwayList') {
    apiUrl = `${config.putAwayList.suggestion}?key=${key}&value=${value}&recommendations=${recommendations}&putawayCode=${putawayCode}`;
  } else if (apiName === 'barcode') {
    apiUrl = `${config.barcodeHistory.suggestionAPi}?key=${key}&value=${value}&recommendations=${recommendations}`;
  } else if (apiName === 'doOrders') {
    apiUrl = `${config.distributorOrders.orderList}?searchTerms=${data?.name}.like:${value}`;
  } else {
    apiUrl = `${config.filters.searchSuggestion}${apiName}?key=${key}&value=${value}&recommendations=${recommendations}`;
  }
  return tokenInstance()
    .get(apiUrl)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const exportDownloadApi = ({ path, payload }) => {
  const url = `${config.filters.exportDownload}${path}`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};
