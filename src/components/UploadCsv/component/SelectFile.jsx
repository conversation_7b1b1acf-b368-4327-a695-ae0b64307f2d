import React, { useState } from 'react';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Button from '@mui/material/Button';
import { fileDownload } from 'utils/helpers';
import { useSelector } from 'react-redux';
import LkModal from '../../Modal/Modal';

const SelectFiles = (props) => {
  const {
    subTitle,
    handleClose,
    sampleCSV = [],
    checkedItemLabel,
    selectFiles,
    open,
    sampleFileName,
    showSampleArray,
    showSelectedFacilityText = true
  } = props;
  const { selectedFacility } = useSelector((state) => state.settings);
  const [checkedItems, setCheckedItems] = useState(false);
  let csvData = '';
  sampleCSV.forEach((value, index) => {
    if (index === 0) {
      csvData = `${value.join()}\n`;
    } else if (index !== sampleCSV.length - 1) {
      csvData = `${csvData + value.join()}\n`;
    } else {
      csvData += value.join();
    }
  });

  const file = new Blob([csvData], { type: 'application/csv' });
  return (
    <LkModal
      open={open}
      title="CSV File Import"
      subTitle={subTitle}
      handleClose={() => handleClose()}
    >
      <div className="upload-csv mr-40 text-center">
        <div className="table-conatiner mr-t40 border-grey5-radiusbase">
          <div className="display-flex fs14 justify-content-space-between pd-12">
            <div>Sample CSV file</div>
            <div onClick={() => fileDownload(file, sampleFileName)}>
              <div className="text-turquioise_surf" style={{ cursor: 'pointer' }}>
                <img
                  className="mr-r8"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/download.svg`}
                  alt="img"
                />
                Download
              </div>
            </div>
          </div>
          {showSampleArray &&
            sampleCSV.map((sc, i) => {
              if (i === 0) {
                return (
                  <div key={i} className="display-flex pd-12 fs12 fw-bold bg-f5">
                    {sc.map((s) => (
                      <div key={s} className="text-left" style={{ width: '120px' }}>
                        {s}
                      </div>
                    ))}
                  </div>
                );
              }
              return (
                <div key={i} className="csv-row display-flex pd-12 fs12">
                  {sc.map((s) => (
                    <div key={s} className="text-left" style={{ width: '120px' }}>
                      {s}
                    </div>
                  ))}
                </div>
              );
            })}
        </div>
        {checkedItemLabel && (
          <div className="mr-t40 mr-b40">
            <FormControlLabel
              control={
                <Checkbox
                  data-cy="checkedItemLabel"
                  checked={checkedItems}
                  name="checkedItems"
                  onChange={() => setCheckedItems(!checkedItems)}
                  color="primary"
                />
              }
              label={checkedItemLabel}
            />
          </div>
        )}
        <div className="fs15 mr-b10 mr-t30">
          {showSelectedFacilityText && (
            <>
              <span>Selected facility is</span>{' '}
              <span className="fw-bold">{`"${selectedFacility}"`}</span>{' '}
              <span>Please confirm before uploading price.</span>
            </>
          )}
        </div>
        <div className="display-flex justify-content-center mr-b40 mr-t15">
          <div className="mr-r5">
            <Button
              style={{ borderRadius: 8 }}
              variant="outlined"
              color="primary"
              data-cy="secondary-btn"
              onClick={() => handleClose()}
            >
              CANCEL
            </Button>
          </div>
          <div className="mr-l5">
            <div className="file-upload-container">
              <input
                id="upload-items-file"
                type="file"
                onChange={(event) => selectFiles(event)}
                className="display-none"
                accept=".csv, application/vnd.openxmlformats-officedocument.
                spreadsheetml.sheet, application/vnd.ms-excel"
              />
              <label
                className={`bg-primary border-radius-base pd-t8 pd-b8 
                pd-l20 pd-r20 cursor-pointer fs18 text-white ${
    !checkedItems && checkedItemLabel ? 'op-5 pointer-events' : null
    }`}
                htmlFor="upload-items-file"
                data-cy="primary-btn"
              >
                UPLOAD FILE
              </label>
            </div>
          </div>
        </div>
      </div>
    </LkModal>
  );
};

export default SelectFiles;
