import Box from '@mui/material/Box';
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { keys, mapValues } from 'utils/lodash';
import { makeStyles } from 'tss-react/mui';
import {
  generateReportDataRequest,
  generateReportDataReset,
  getReportMetaDataRequest
} from 'redux/reducers/reportingTool';
import Spinner from 'components/Spinner/Spinner';
import { monitorFiltersUpdate } from 'redux/actionCreators/monitor';
import { LOGIN } from 'redux/reducers/login';
import ReportType from './Common/ReportType';
import ReportingFooter from './Common/ReportingFooter';
import SenderEmailInput from './Common/SenderEmailInput';
import DynamicDataContainer from './DynamicDataContainer';
import {
  INVENTORY,
  SALE_ORDER,
  INVENTORY_REPORT,
  SALE_ORDER_REPORT,
  ASRS_DISCREPANCY,
  ASRS_DISCREPANCY_REPORT,
  PO,
  PO_ITEM_REPORT,
  GRN_REPORT,
  GRN
} from './Common/constant';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    height: '100%',
    borderRadius: 4,
    flexDirection: 'column',
    backgroundColor: '#ffffffff',
    justifyContent: 'space-between'
  },
  dynamicContainer: {
    display: 'flex',
    height: '100%',
    flexDirection: 'column',
    justifyContent: 'space-between'
  },
  spinnerContainer: {
    display: 'flex',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center'
  }
}));

const convertArrayToObject = (selectedFilterColumns) => {
  let payloadFilterColumns = {};
  keys(selectedFilterColumns).forEach((key) => {
    payloadFilterColumns = {
      ...payloadFilterColumns,
      [key]: selectedFilterColumns[key].enabled ? selectedFilterColumns[key].inputValue : null
    };
  });
  return payloadFilterColumns;
};

const ReportingTool = () => {
  const { classes } = useStyles();
  const location = useLocation();
  const dispatch = useDispatch();

  const getPath = () => {
    if (location.pathname.includes(`/${INVENTORY.toLowerCase()}`)) {
      return INVENTORY_REPORT;
    }
    if (location.pathname.includes(`/${SALE_ORDER.toLowerCase()}`)) {
      return SALE_ORDER_REPORT;
    }
    if (location.pathname.includes(`/${ASRS_DISCREPANCY.toLowerCase()}`)) {
      return ASRS_DISCREPANCY_REPORT;
    }
    if (location.pathname.includes(`/${PO.toLowerCase()}`)) {
      return PO_ITEM_REPORT;
    }
    if (location.pathname.includes(`/${GRN.toLowerCase()}`)) {
      return GRN_REPORT;
    }
    return '';
  };

  const [error, setError] = useState(false);
  const [addedEmails, setAddedEmails] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState({});
  const [selectedFilterColumns, setSelectedFilterColumns] = useState({});
  const [selectedReport, setSelectedReport] = useState(getPath());

  const facilities = useSelector((state) => state.settings.facilitiesObj.facilities);
  const { DATE_RANGE: dateRange } = useSelector((state) => state.monitor.filters.selected.data);

  convertArrayToObject(selectedFilterColumns);

  const {
    data: { data: reportData },
    isLoading,
    reportMetaDataFail
  } = useSelector((state) => state.reportingTool.reportMetaData);

  const {
    isLoading: generatedReportLoading,
    data: generatedReport,
    isSuccessful: reportReuqestSent
  } = useSelector((state) => state.reportingTool.generatedReport);

  useEffect(() => {
    if ((dateRange.from || dateRange.to) && reportReuqestSent) {
      dispatch(
        monitorFiltersUpdate({
          filter: 'DATE_RANGE',
          updatedFilters: { from: '', to: '' }
        })
      );
    }
  }, [generatedReport, reportReuqestSent]);

  const { empCode } = useSelector((state) => state[LOGIN].user.userDetail);

  const selectReportTypeFunc = (value) => setSelectedReport(value);

  const getSelectedReportTypeFunc = (report) => {
    if (report === INVENTORY_REPORT) {
      return INVENTORY;
    }
    if (report === SALE_ORDER_REPORT) {
      return SALE_ORDER;
    }
    if (report === PO_ITEM_REPORT) {
      return PO;
    }
    if (report === GRN_REPORT) {
      return GRN;
    }
    return ASRS_DISCREPANCY;
  };

  const handleAction = () => {
    const payload = {
      filterColumns: {
        ...convertArrayToObject(selectedFilterColumns),
        dateRange: { startDate: dateRange.from || null, endDate: dateRange.to || null }
      },
      projectionColumns: reportData.projectionColumns.filter(
        (item) => selectedColumns[item] && item !== 'ALL'
      ),
      emails: addedEmails.join(','),
      reportType: getSelectedReportTypeFunc(selectedReport),
      createdBy: empCode
    };
    dispatch(generateReportDataRequest(payload));
  };

  useEffect(() => {
    if (selectedReport) {
      dispatch(
        getReportMetaDataRequest({
          reportType: getSelectedReportTypeFunc(selectedReport)
        })
      );
    }
  }, [selectedReport]);

  useEffect(() => {
    if (reportData) {
      let columns_filters = {};
      reportData.projectionColumns.forEach((eachColumn) => {
        columns_filters = { ...columns_filters, [eachColumn]: false };
      });
      setSelectedColumns(columns_filters);

      columns_filters = {};
      reportData.filterColumns.forEach((eachFilter) => {
        let optionValues = eachFilter.values;
        if (eachFilter.key === 'facilities') {
          optionValues = facilities.map((facility) => facility.code);
        }
        columns_filters = {
          ...columns_filters,
          [eachFilter.key]: {
            ...eachFilter,
            enabled: !!eachFilter.mandatory,
            mandatory: !!eachFilter.mandatory,
            inputValue: eachFilter.multiselect ? [] : '',
            values: optionValues
          }
        };
      });
      setSelectedFilterColumns(columns_filters);
    }
  }, [reportData, reportReuqestSent]);

  useEffect(() => {
    if (reportReuqestSent) {
      setAddedEmails([]);
      dispatch(generateReportDataReset());
    }
  }, [reportReuqestSent]);

  const handleColumnChange = (ev) => {
    setSelectedColumns((prevColumns) => {
      if (ev.target.name === 'ALL') {
        return mapValues(prevColumns, () => ev.target.checked);
      }
      const tempSelectedColumn = {
        ...prevColumns,
        [ev.target.name]: ev.target.checked
      };
      if ('ALL' in selectedColumns) {
        tempSelectedColumn.ALL = false;
      }
      return tempSelectedColumn;
    });
  };
  const handleFilterChange = (ev) => {
    setSelectedFilterColumns((prevFilters) => ({
      ...prevFilters,
      [ev.target.name]: { ...prevFilters[ev.target.name], enabled: ev.target.checked }
    }));
  };

  const handleInputChange = (ev, filter) => {
    const targetValue = ev.target.value;
    setSelectedFilterColumns((prevFilters) => ({
      ...prevFilters,
      [filter.key]: {
        ...filter,
        inputValue:
          targetValue.split(',').length > 20
            ? targetValue.split(',').slice(0, 20).join(',')
            : targetValue
      }
    }));
  };

  const handleMultiSelectChange = (ev, filter) => {
    const values = ev.target.value;
    if (values[values.length - 1] === 'all') {
      setSelectedFilterColumns((prevFilters) => ({
        ...prevFilters,
        [filter.key]: { ...filter, inputValue: filter.inputValue.length ? [] : filter.values }
      }));
    } else {
      setSelectedFilterColumns((prevFilters) => ({
        ...prevFilters,
        [filter.key]: { ...filter, inputValue: ev.target.value }
      }));
    }
  };

  const mandatoryFiltersEmpty = keys(selectedFilterColumns).some((filterKey) => {
    if (selectedFilterColumns[filterKey].dataType === 'date_range' && dateRange.from) {
      return false;
    }
    return (
      selectedFilterColumns[filterKey].mandatory && !selectedFilterColumns[filterKey].inputValue
    );
  });

  const disableGenerateButtonFunc =
    error ||
    isLoading ||
    !reportData ||
    !selectedReport ||
    reportMetaDataFail ||
    generatedReportLoading ||
    !addedEmails.length ||
    (!keys(selectedColumns).some((column) => selectedColumns[column]) &&
      selectedReport !== ASRS_DISCREPANCY_REPORT) ||
    mandatoryFiltersEmpty;

  const renderFiltersAndColumns = () => {
    if (isLoading) {
      return (
        <Box className={classes.spinnerContainer}>
          <Spinner />
        </Box>
      );
    }
    if (selectedReport && reportData && !reportMetaDataFail) {
      return (
        <>
          {selectedReport !== ASRS_DISCREPANCY_REPORT ? (
            <DynamicDataContainer
              selectedColumns={selectedColumns}
              handleFilterChange={handleFilterChange}
              handleInputChange={handleInputChange}
              handleMultiSelectChange={handleMultiSelectChange}
              handleColumnChange={handleColumnChange}
              selectedFilterColumns={selectedFilterColumns}
            />
          ) : null}
          <SenderEmailInput
            error={error}
            setError={setError}
            addedEmails={addedEmails}
            setAddedEmails={setAddedEmails}
            helperValue={Math.max(0, 5 - addedEmails.length)}
            reportMetaDataFail={reportMetaDataFail}
            clearData={reportReuqestSent}
          />
        </>
      );
    }
    return null;
  };

  return (
    <Box className={classes.container}>
      <Box className={classes.dynamicContainer}>
        <ReportType selectedReport={selectedReport} selectReportTypeFunc={selectReportTypeFunc} />
        {renderFiltersAndColumns()}
      </Box>
      <ReportingFooter action={handleAction} disableButton={disableGenerateButtonFunc} />
    </Box>
  );
};

export default ReportingTool;
