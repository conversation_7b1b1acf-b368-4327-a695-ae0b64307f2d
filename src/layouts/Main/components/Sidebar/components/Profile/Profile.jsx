import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { Typography } from '@mui/material';

const useStyles = makeStyles()(theme => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    minHeight: 'fit-content'
  },
  avatar: {
    width: 60,
    height: 60
  },
  name: {
    marginTop: theme.spacing(1)
  }
}));

const Profile = props => {
  const { className, ...rest } = props;

  const { classes, cx } = useStyles();

  const user = {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>'
  };

  return (
    (<div
      {...rest}
      className={cx(classes.root, className)}
    >
      <Typography
        className={classes.name}
        variant="h4"
      >
        {user.name}
      </Typography>
      <Typography variant="body2">{user.email}</Typography>
    </div>)
  );
};

Profile.propTypes = {
  className: PropTypes.string
};

export default Profile;
