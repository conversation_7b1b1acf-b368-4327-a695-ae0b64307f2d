import React, { useEffect, useCallback, useState } from 'react';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import { useSelector, useDispatch } from 'react-redux';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import ProductToolTip from 'components/common/ProductToolTip';
import LkToolTip from 'components/ToolTip/ToolTip';
import { getCurrencySumbol, roundUptoFixedDigits } from 'utils/helpers';
import { LOCALISATION } from 'redux/reducers/localisation';
import useWindowResize from '../../common/useWindowResize';
import { getGrnPidsLoad } from '../../redux/actionCreators/grnDetails';
import LkChip from '../MaterialUi/LkChip';

const initialSortBy = {
  id: 'pid',
  order: 'ASC'
};
const obj = {
  page: 0
};
const statusNameMapping = {
  PASSED: 'QC Completed',
  FAILED: 'QC Rejected',
  PENDING: 'QC Pending'
};
const statusMapping = {
  PASSED: 'success',
  FAILED: 'error',
  PENDING: 'warning'
};

const GRNTable = ({ grnCode }) => {
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 260);
  const { getGrnPids, getGrnPidsLoading } = useSelector((state) => state.grnDetails);
  const [tableData, setTableData] = useState([]);

  const convertedCurrency = getCurrencySumbol(getGrnPids?.result?.data.currency);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_DETAIL);

  useEffect(() => {
    if (getGrnPids) {
      const { page_size, total_count } = getGrnPids.result;
      obj.page_size = page_size;
      obj.total_count = total_count;
      obj.pageCount = Math.floor(total_count / page_size);
      setTableData(getGrnPids?.result?.data?.products);
    }
  }, [getGrnPids]);

  const fetchMoreListItems = (newSortType, sortKey, nextPagePage) => {
    const payload = {
      pageNum: nextPagePage,
      noReset: !!nextPagePage,
      grnCode
    };
    dispatch(getGrnPidsLoad(payload));
  };

  useEffect(() => {
    dispatch(
      getGrnPidsLoad({
        pageNum: 0,
        noReset: false,
        grnCode
      })
    );
  }, [grnCode]);

  const headerConfig = [
    {
      name: CONTENT.PID,
      key: 'pid',
      align: 'left',
      supportSort: true,
      formatBody: useCallback(
        (data) => (
          <LkToolTip
            title={
              <ProductToolTip
                poId={getGrnPids?.result?.data?.po_id}
                pid={data.pid}
                classification={data.category_id}
              />
            }
            width={data.category_id === 11356 ? '300px' : '756px'}
          >
            <Link target="_blank" to={`/inventory/${data.pid}`}>
              {data.pid}
            </Link>
          </LkToolTip>
        ),
        []
      )
    },

    {
      name: CONTENT.ITEM_DETAILS,
      key: 'description',
      style: { minWidth: '300px', maxWidth: '300px' },
      formatBody: useCallback(
        ({ description }) => (
          <Box>
            {description ? (
              <LkToolTip
                placement="bottom-end"
                title={<Box p={1}>{description} </Box>}
                className="dtoolTip"
              >
                <div className="ellipsis-vertical">{description}</div>
              </LkToolTip>
            ) : (
              '-'
            )}
          </Box>
        ),
        []
      )
    },
    {
      name: CONTENT.PID_STATUS,
      key: 'PID Status',
      style: { minWidth: '160px', maxWidth: '160px' },
      formatBody: useCallback(
        (data) =>
          data.pid_status ? (
            <LkChip
              label={statusNameMapping[data.pid_status]}
              type={statusMapping[data.pid_status]}
            />
          ) : (
            '-'
          ),
        []
      )
    },
    {
      name: CONTENT.VENDOR_SKU,
      key: 'vendor_sku',
      style: { minWidth: '130px', maxWidth: '130px' }
    },
    {
      name: CONTENT.BATH_CODE,
      key: 'batch_code'
    },
    {
      name: CONTENT.RECEIVED,
      key: 'received',
      align: 'right',
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: CONTENT.PENDING,
      key: 'pending',
      align: 'right',
      style: { minWidth: '160px', maxWidth: '160px' }
    },
    {
      name: CONTENT.PRICE,
      key: 'Price',
      align: 'right',
      style: { minWidth: '130px', maxWidth: '130px' },
      formatBody: useCallback(
        (data) => (
          <div className="">
            {convertedCurrency}
            {roundUptoFixedDigits(data.price)}
          </div>
        ),
        [convertedCurrency]
      )
    },
    {
      name: CONTENT.ADDITIONAL_COST,
      key: 'Additional Cost',
      align: 'right',
      style: { minWidth: '160px', maxWidth: '160px' },
      formatBody: useCallback(
        (data) => (
          <div className="">
            {convertedCurrency}
            {roundUptoFixedDigits(data.cgst + data.igst + data.sgst)}
          </div>
        ),
        [convertedCurrency]
      )
    }
  ];
  return (
    <div>
      <Box>
        <LkTable
          tableHeight={tableHeight}
          headerConfig={headerConfig}
          isDataFetching={getGrnPidsLoading}
          tableData={tableData || []}
          totalRowsCount={getGrnPids?.result?.total_count}
          dataRequestFunction={fetchMoreListItems}
          initialSortBy={initialSortBy}
          pageLimit={getGrnPids?.result?.page_size || 10}
          rowSize={60}
          isNonVertualizedTable
          enableLocalSort
        />
      </Box>
    </div>
  );
};

export default React.memo(GRNTable);
