import * as action from '../actionTypes/grn';

export function PIDSelection(data) {
  return {
    type: action.PID_SELECT_LOAD,
    payload: data
  };
}

export function PIDAdd(data) {
  return {
    type: action.PID_ADD_LOAD,
    payload: data
  };
}

export function createBoxCode(data) {
  return {
    type: action.BOX_CODE_LOAD,
    payload: data
  };
}

export function createBoxCodeStatusSuccess(data) {
  return {
    type: action.BOX_CODE_SUCCESS,
    payload: data
  };
}
export function createBoxCodeStatusFail(data) {
  return {
    type: action.BOX_CODE_FAIL,
    payload: data
  };
}

export function createQcDetail(data) {
  return {
    type: action.QC_DETAIL_LOAD,
    payload: data
  };
}

export function createQcDetailStatusSuccess(data) {
  return {
    type: action.QC_DETAIL_SUCCESS,
    payload: data
  };
}
export function createQcDetailStatusFail(data) {
  return {
    type: action.QC_DETAIL_FAIL,
    payload: data
  };
}

export function createQcDetailReset(data) {
  return {
    type: action.QC_DETAIL_RESET,
    payload: data
  };
}

export function grnListLoad(data) {
  return { type: action.GRN_LIST_LOAD, payload: data };
}

export function grnListSuccess(data) {
  return { type: action.GRN_LIST_SUCCESS, payload: data };
}

export function grnListFail(data) {
  return { type: action.GRN_LIST_FAIL, payload: data };
}

export function grnCreateLoad(data) {
  return { type: action.GRN_CREATE_LOAD, payload: data };
}

export function grnCreateSuccess(data) {
  return { type: action.GRN_CREATE_SUCCESS, payload: data };
}

export function grnCreateFail(data) {
  return { type: action.GRN_CREATE_FAIL, payload: data };
}

export function grnCreateReset(data) {
  return { type: action.GRN_CREATE_RESET, payload: data };
}

export function getInvoicesLoad(data) {
  return { type: action.GET_INVOICES_BY_VENDOR_ID_LOAD, payload: data };
}

export function getInvoicesSuccess(data) {
  return { type: action.GET_INVOICES_BY_VENDOR_ID_SUCCESS, payload: data };
}

export function getInvoicesFail(data) {
  return { type: action.GET_INVOICES_BY_VENDOR_ID_FAIL, payload: data };
}

export function getInvoicesRESET(data) {
  return { type: action.GET_INVOICES_BY_VENDOR_ID_RESET, payload: data };
}

export function getInvoiceDetailLoad(data) {
  return { type: action.GET_INVOICE_DETAIL_LOAD, payload: data };
}

export function getInvoiceDetailSuccess(data) {
  return { type: action.GET_INVOICE_DETAIL_SUCCESS, payload: data };
}

export function getInvoiceDetailFail(data) {
  return { type: action.GET_INVOICE_DETAIL_FAIL, payload: data };
}

export function getPidListLoad(data) {
  return { type: action.GET_PID_LIST_LOAD, payload: data };
}

export function getPidListSuccess(data) {
  return { type: action.GET_PID_LIST_SUCCESS, payload: data };
}

export function getPidListFail(data) {
  return { type: action.GET_PID_LIST_FAIL, payload: data };
}

export function getPidDetailLoad(data) {
  return { type: action.GET_PID_DETAIL_LOAD, payload: data };
}

export function getPidDetailSuccess(data) {
  return { type: action.GET_PID_DETAIL_SUCCESS, payload: data };
}

export function getPidDetailFail(data) {
  return { type: action.GET_PID_DETAIL_FAIL, payload: data };
}

export function addPidLoad(data) {
  return { type: action.ADD_PID_LOAD, payload: data };
}

export function addPidReset(data) {
  return { type: action.ADD_PID_RESET, payload: data };
}

export function addPidSuccess(data) {
  return { type: action.ADD_PID_SUCCESS, payload: data };
}

export function addPidFail(data) {
  return { type: action.ADD_PID_FAIL, payload: data };
}

export function convertPidLoad(data) {
  return { type: action.CONVERT_PID_LOAD, payload: data };
}

export function convertPidSuccess(data) {
  return { type: action.CONVERT_PID_SUCCESS, payload: data };
}

export function convertPidFail(data) {
  return { type: action.CONVERT_PID_FAIL, payload: data };
}

export function getSamplingQtyLoad(data) {
  return { type: action.GET_SAMPLING_QTY_LOAD, payload: data };
}

export function getSamplingQtySuccess(data) {
  return { type: action.GET_SAMPLING_QTY_SUCCESS, payload: data };
}

export function getSamplingQtyFail(data) {
  return { type: action.GET_SAMPLING_QTY_FAIL, payload: data };
}

export function resetPidList(data) {
  return { type: action.RESET_PID_LIST, payload: data };
}
// QC Sampling
export function getPidQcSampling(data) {
  return { type: action.GET_PID_QC_SAMPLING_LOAD, payload: data };
}

export function getPidQcSamplingSuccess(data) {
  return { type: action.GET_PID_QC_SAMPLING_SUCCESS, payload: data };
}
export function getPidQcSamplingFail(data) {
  return { type: action.GET_PID_QC_SAMPLING_FAIL, payload: data };
}

// Edit Item
export function getEditItemLoad(data) {
  return { type: action.GET_ITEM_EDIT_LOAD, payload: data };
}
export function getEditItemSuccess(data) {
  return { type: action.GET_ITEM_EDIT_SUCCESS, payload: data };
}
export function getEditItemFail(data) {
  return { type: action.GET_ITEM_EDIT_FAIL, payload: data };
}

// Delete Item
export function deleteItemLoad(data) {
  return { type: action.GET_ITEM_DELETE_LOAD, payload: data };
}
export function deleteItemSuccess(data) {
  return { type: action.GET_ITEM_DELETE_SUCCESS, payload: data };
}
export function deleteItemFail(data) {
  return { type: action.GET_ITEM_DELETE_FAIL, payload: data };
}
export function qcDataUpdate({
  createBox = false,
  qc_reason,
  qc_fail_code,
  QCData,
  barcode,
  is_box_required = true,
  qc_status,
  status = 'PASSED'
}) {
  const itemValue = status === 'PASSED' ? 1 : 0;
  if (createBox) {
    delete QCData.qc_passed.NO_BOX;
    QCData.qc_passed = { [barcode]: [], ...QCData.qc_passed };
  } else {
    const newItem = {
      channel_status: itemValue,
      barcode: barcode
    };

    if (qc_status === 'pass') {
      if (is_box_required) {
        const boxKey = Object.keys(QCData.qc_passed)[0];
        if (QCData.qc_passed[boxKey]) {
          QCData.qc_passed[boxKey].unshift(newItem);
        }
      } else {
        if (!QCData.qc_passed.NO_BOX) {
          QCData.qc_passed.NO_BOX = [];
        }

        QCData.qc_passed.NO_BOX.unshift(newItem);
      }
    } else if (qc_status === 'fail') {
      newItem.qc_reason = qc_reason;
      newItem.qc_fail_code = qc_fail_code;
      QCData.qc_failed.unshift(newItem);
    } else {
      delete newItem.channel_status;
      newItem.qc_reason = qc_reason;
      QCData.others.unshift(newItem);
    }
  }

  return { type: action.QC_DATA_UPDATE, payload: QCData };
}

// GET BOX_ITEMS
export function getQcDataLoad(data) {
  return { type: action.GET_QC_DATA_LOAD, payload: data };
}
export function getQcDataSuccess({ data }) {
  return { type: action.GET_QC_DATA_SUCCESS, payload: data };
}

export function getQcDataFail(data) {
  return { type: action.GET_QC_DATA_FAIL, payload: data };
}

// update qty

export function updateQtyReset(data) {
  return { type: action.UPDATE_QTY_RESET, payload: data };
}

export function updateQtyLoad(data) {
  return { type: action.UPDATE_QTY_LOAD, payload: data };
}

export function updateQtySuccess(data) {
  return { type: action.UPDATE_QTY_SUCCESS, payload: data };
}

export function updateQtyFail(data) {
  return { type: action.UPDATE_QTY_FAIL, payload: data };
}

// update grn summary
export function getGrnSummaryLoad(data) {
  return { type: action.GET_GRN_SUMMARY_LOAD, payload: data };
}

export function getGrnSummarySuccess(data) {
  return { type: action.GET_GRN_SUMMARY_SUCCESS, payload: data };
}

export function getGrnSummaryFail(data) {
  return { type: action.GET_GRN_SUMMARY_FAIL, payload: data };
}

export function setGrnMeta(data) {
  return { type: action.SET_GRN_META, payload: data };
}

export function setGrnDetail(data) {
  return { type: action.SET_GRN_DATA, payload: data };
}

export function resetAddPidData(data) {
  return { type: action.RESET_ADD_PID_DATA, payload: data };
}

// get Expiry Details
export function getExpiryDetailLoad(data) {
  return { type: action.GET_EXPIRY_DETAILS_LOAD, payload: data };
}

export function getExpiryDetailSuccess(data) {
  return { type: action.GET_EXPIRY_DETAILS_SUCCESS, payload: data };
}

export function getExpiryDetailFail(data) {
  return { type: action.GET_EXPIRY_DETAILS_FAIL, payload: data };
}

// Proceed to green channel
export function proceedToGreenChannel(data) {
  return { type: action.PROCEED_TO_GREEN_CHANNEL, payload: data };
}

// Product mismatch
export function productMismatchLoad(data) {
  return { type: action.PRODUCT_MISMATCH_LOAD, payload: data };
}

export function productMismatchSuccess(data) {
  return { type: action.PRODUCT_MISMATCH_SUCCESS, payload: data };
}

export function productMismatchFail(data) {
  return { type: action.PRODUCT_MISMATCH_FAIL, payload: data };
}

// Update Grn Item
export function updateGrnItemLoad(data) {
  return { type: action.UPDATE_GRN_ITEM_LOAD, payload: data };
}

export function updateGrnItemSuccess(data) {
  return { type: action.UPDATE_GRN_ITEM_SUCCESS, payload: data };
}

export function updateGrnItemFail(data) {
  return { type: action.UPDATE_GRN_ITEM_FAIL, payload: data };
}
export function updateGrnItemReset(data) {
  return { type: action.UPDATE_GRN_ITEM_RESET, payload: data };
}

// GRN close
export function grnCloseReset(data) {
  return { type: action.GRN_CLOSE_RESET, payload: data };
}
export function grnCloseLoad(data) {
  return { type: action.GRN_CLOSE_LOAD, payload: data };
}

export function grnCloseSuccess(data) {
  return { type: action.GRN_CLOSE_SUCCESS, payload: data };
}

export function grnCloseFail(data) {
  return { type: action.GRN_CLOSE_FAIL, payload: data };
}
// Get all grn with its details for an Invoice

export function getAllGrnForAnInvoice(data) {
  return { type: action.GET_GRN_DETAILS_FOR_AN_INVOICE_LOAD, payload: data };
}

export function getAllGrnForAnInvoiceSuccess(data) {
  return { type: action.GET_GRN_DETAILS_FOR_AN_INVOICE_SUCCESS, payload: data };
}

export function getAllGrnForAnInvoiceFail(data) {
  return { type: action.GET_GRN_DETAILS_FOR_AN_INVOICE_FAIL, payload: data };
}

export function getAllGrnForAnInvoiceReset(data) {
  return { type: action.GET_GRN_DETAILS_FOR_AN_INVOICE_RESET, payload: data };
}

// Seach by description
export function searchByDescLoad(data) {
  return { type: action.SEARCH_BY_DESC_LOAD, payload: data };
}

export function searchByDescSuccess(data) {
  return { type: action.SEARCH_BY_DESC_SUCCESS, payload: data };
}

export function searchByDescFail(data) {
  return { type: action.SEARCH_BY_DESC_FAIL, payload: data };
}

export function searchByDescReset(data) {
  return { type: action.SEARCH_BY_DESC_RESET, payload: data };
}

// Seach by description Products
export function searchByDescPrductsLoad(data) {
  return { type: action.SEARCH_BY_DESC_PRODUCTS_LOAD, payload: data };
}

export function searchByDescPrductsSuccess(data) {
  return { type: action.SEARCH_BY_DESC_PRODUCTS_SUCCESS, payload: data };
}

export function searchByDescPrductsFail(data) {
  return { type: action.SEARCH_BY_DESC_PRODUCTS_FAIL, payload: data };
}

// Reset Grn Create

export function resetGrnCreate(data) {
  return { type: action.RESET_GRN_CREATE, payload: data };
}

// is grn open

export function isGrnOpenLoad(data) {
  return { type: action.IS_GRN_OPEN_LOAD, payload: data };
}

export function isGrnOpenSuccess(data) {
  return { type: action.IS_GRN_OPEN_SUCCESS, payload: data };
}

export function isGrnOpenFail(data) {
  return { type: action.IS_GRN_OPEN_FAIL, payload: data };
}

export function isGrnOpenReset(data) {
  return { type: action.IS_GRN_OPEN_RESET, payload: data };
}

// Close all Open Grn

export function closeAllOpenGRNLoad(data) {
  return { type: action.CLOSE_ALL_OPEN_GRN_LOAD, payload: data };
}

export function closeAllOpenGRNReset(data) {
  return { type: action.CLOSE_ALL_OPEN_GRN_RESET, payload: data };
}
