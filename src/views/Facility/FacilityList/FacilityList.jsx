import React, { useCallback, useState, useEffect, useRef } from 'react';
import { makeStyles } from 'tss-react/mui';
import './facilityList.scss';
import { useDispatch, useSelector } from 'react-redux';
import LkToolTip from 'components/ToolTip/ToolTip';
import { Box } from '@mui/material';
import { getFacilityListLoad } from 'redux/actionCreators/facility';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { genericDateFormatted } from 'utils/helpers';
import LkChip from 'components/MaterialUi/LkChip';
import { isArray, keys } from 'utils/lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import useWindowResize from '../../../common/useWindowResize';

const useStyles = makeStyles()(() => ({
  root: {
    background: '#fff'
  },
  table: {
    minWidth: 700,
    paddingBottom: 4
  },
  container: {
    minHeight: 450
  },
  outlinedPrimary: {
    marginLeft: '16px',
    height: 35
  },
  label: {
    fontSize: '10px'
  }
}));

const PAGE_SIZE = 20;
const active = 'ACTIVE';

const defaultObj = (globalSearch) => ({
  isReset: true,
  name: active,
  payload: {
    type: 'facilities',
    facility_status: active,
    search_keyword: globalSearch || null,
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey: 'updated_at',
      sortOrder: 'DESC'
    }
  }
});

const initialSortByData = { id: 'updated_at', order: 'DESC' };

const FacilityListing = ({
  onChangeFilterList,
  globalSearch,
  data = [],
  selectedFilterList,
  totalRecordsCount,
  facilityDraftSuccess = null,
  facilityListLoading,
  isDraftMode = false,
  openCreateModal,
  CONTENT
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [requestBody, setRequestBody] = useState(defaultObj(globalSearch));
  const [tableHeight] = useWindowResize(window.innerHeight - 290);
  const isInitialReq = useRef(true);
  const location = useLocation();
  const navigate = useNavigate();

  const {
    autoSuggestionListActive_FacilityCode = null,
    autoSuggestionListDraft_FacilityCode = null,
    autoSuggestionListActive_FacilityName,
    autoSuggestionListActive_FacilityType,
    autoSuggestionListActive_FacilityStatus,
    autoSuggestionListActive_PartyName,
    autoSuggestionListActive_BillingAddress,
    autoSuggestionListActive_DraftName,
    autoSuggestionListDraft_FacilityName,
    autoSuggestionListDraft_FacilityType,
    autoSuggestionListDraft_FacilityStatus,
    autoSuggestionListDraft_PartyName,
    autoSuggestionListDraft_BillingAddress,
    autoSuggestionListDraft_DraftName
  } = useSelector((state) => state.filters);

  useEffect(() => {
    if (!openCreateModal && isDraftMode && facilityDraftSuccess && !isInitialReq.current) {
      dispatch(getFacilityListLoad(requestBody));
    }
  }, [isDraftMode, facilityDraftSuccess, openCreateModal]);

  useEffect(() => {
    if (!isInitialReq.current) {
      dispatch(getFacilityListLoad(requestBody));
    }
  }, [requestBody]);

  useEffect(() => {
    if (!isInitialReq.current) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        isReset: true,
        payload: {
          ...prevReqBody.payload,
          facility_status: isDraftMode ? null : active,
          search_keyword: globalSearch || null,
          pageRequest: {
            ...prevReqBody.payload.pageRequest,
            pageNumber: 0
          }
        }
      }));
    }
  }, [isDraftMode, globalSearch]);

  const updateRequestBody = useCallback((sortType, sortKey, page) => {
    if (!isInitialReq.current) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        isReset: !page,
        payload: {
          ...prevReqBody.payload,
          pageRequest: {
            ...prevReqBody.payload.pageRequest,
            pageNumber: page,
            sortOrder: sortType,
            sortKey
          }
        }
      }));
    }
  }, []);

  useEffect(() => {
    const appliedFilters = {};
    selectedFilterList.map((item) => {
      if (appliedFilters[mapSearchFilterKey[item.key]]) {
        const filtersValue =
          isArray(appliedFilters[mapSearchFilterKey[item.key]]) &&
          appliedFilters[mapSearchFilterKey[item.key]];

        appliedFilters[mapSearchFilterKey[item.key]] = filtersValue
          ? [...filtersValue, item.value]
          : [appliedFilters[mapSearchFilterKey[item.key]], item.value];
      } else {
        appliedFilters[mapSearchFilterKey[item.key]] = item.value;
      }
      return null;
    });

    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      payload: keys(appliedFilters).length
        ? {
          ...defaultObj().payload,
          ...appliedFilters,
          pageRequest: { ...prevReqBody.payload.pageRequest }
        }
        : {
          ...defaultObj().payload,
          facility_status: isDraftMode ? null : active,
          pageRequest: { ...prevReqBody.payload.pageRequest }
        }
    }));
    isInitialReq.current = false;
  }, [location, isDraftMode]);

  const returnAddressTooltip = (address) => (
    <LkToolTip placement="bottom-end" title={<Box p={1}>{address} </Box>} className="dtoolTip">
      <div className="ellipsis-vertical-one">{address}</div>
    </LkToolTip>
  );

  const headerConfig = [
    {
      name: CONTENT.CODE,
      key: 'facility_code',
      columnName: 'facility_code',
      align: 'center',
      supportSort: true,
      supportFilter: true,
      style: { minWidth: 100, maxWidth: 100 },
      filterData: {
        type: 'autoSelect',
        listData: isDraftMode
          ? autoSuggestionListDraft_FacilityCode
          : autoSuggestionListActive_FacilityCode,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_FacilityCode' : 'Active_FacilityCode'
      }
    },
    {
      name: CONTENT.NAME,
      key: 'display_name',
      columnName: 'display_name',
      align: 'center',
      supportSort: true,
      supportFilter: true,
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ display_name }) => <div className="text-primary">{display_name}</div>,
        []
      ),
      filterData: {
        type: 'autoSelect',
        listData: isDraftMode
          ? autoSuggestionListDraft_FacilityName
          : autoSuggestionListActive_FacilityName,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_FacilityName' : 'Active_FacilityName'
      }
    },
    {
      name: CONTENT.TYPE,
      key: 'facility_type',
      columnName: 'facility_type',
      align: 'center',
      supportSort: true,
      supportFilter: true,
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ facility_type }) => <div className="type">{facility_type}</div>,
        []
      ),
      filterData: {
        type: 'status',
        listData: isDraftMode
          ? autoSuggestionListDraft_FacilityType
          : autoSuggestionListActive_FacilityType,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_FacilityType' : 'Active_FacilityType'
      }
    },
    {
      name: CONTENT.STATUS,
      key: 'facility_status',
      columnName: 'facility_status',
      supportSort: true,
      supportFilter: true,
      align: 'center',
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ facility_status }) => (
          <LkChip
            label={facility_status}
            className="fw-bold fs10"
            variant="default"
            type={facility_status}
          />
        ),
        []
      ),
      filterData: {
        type: 'status',
        listData: isDraftMode
          ? autoSuggestionListDraft_FacilityStatus
          : autoSuggestionListActive_FacilityStatus,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_FacilityStatus' : 'Active_FacilityStatus'
      }
    },
    {
      name: CONTENT.PARTY_NAME,
      key: 'legal_name',
      columnName: 'legal_name',
      supportSort: true,
      supportFilter: true,
      align: 'center',
      style: { minWidth: 100, maxWidth: 100 },
      filterData: {
        type: 'autoSelect',
        listData: isDraftMode
          ? autoSuggestionListDraft_PartyName
          : autoSuggestionListActive_PartyName,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_PartyName' : 'Active_PartyName'
      }
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updated_at',
      columnName: 'updated_at',
      align: 'center',
      supportSort: true,
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(({ updated_at }) => genericDateFormatted(updated_at), [])
    },
    {
      name: CONTENT.CREATED_AT,
      key: 'created_at',
      columnName: 'created_at',
      align: 'center',
      supportSort: true,
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(({ created_at }) => genericDateFormatted(created_at), [])
    },
    isDraftMode && {
      name: CONTENT.DRAFT_NAME,
      key: 'draft_name',
      columnName: 'draft_name',
      supportSort: true,
      supportFilter: true,
      align: 'center',
      style: { minWidth: 100, maxWidth: 100 },
      filterData: {
        type: 'autoSelect',
        listData: isDraftMode
          ? autoSuggestionListDraft_DraftName
          : autoSuggestionListActive_DraftName,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_DraftName' : 'Active_DraftName'
      }
    },
    {
      name: CONTENT.BILLING_ADDRESS,
      key: 'city',
      columnName: 'city',
      align: 'left',
      supportSort: true,
      supportFilter: true,
      style: { minWidth: 210, maxWidth: 210, textIndent: 30 },
      formatBody: useCallback((obj) => {
        // const { state, address_line1, address_line2, city, pin_code, country } =
        //   obj?.billing_address;
        const state = obj?.billing_address?.state;
        const address_line1 = obj?.billing_address?.address_line1;
        const address_line2 = obj?.billing_address?.address_line2;
        const city = obj?.billing_address?.city;
        const pin_code = obj?.billing_address?.pin_code;
        const country = obj?.billing_address?.country;
        const stateTemp = `${state ? ' , ' : ''}`;
        const cityTemp = `${city ? ' , ' : ''}`;
        const pinCodeTemp = `${pin_code ? ' , ' : ''}`;
        const addressLine3 = `${state}${stateTemp}${city}${cityTemp} ${pin_code} ${pinCodeTemp}${
          country || obj.country
        }`;
        return (
          <div style={{ fontSize: 12, textIndent: 30 }}>
            <div> {address_line1 && returnAddressTooltip(address_line1)}</div>
            <div>{address_line2 && returnAddressTooltip(address_line2)} </div>
            {returnAddressTooltip(addressLine3)}
          </div>
        );
      }, []),
      filterData: {
        type: 'autoSelect',
        listData: isDraftMode
          ? autoSuggestionListDraft_BillingAddress
          : autoSuggestionListActive_BillingAddress,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'facilities',
        columnName: isDraftMode ? 'Draft_BillingAddress' : 'Active_BillingAddress'
      }
    }
  ];

  return (
    <div className={`${classes.root} facility-list  pos-rel`}>
      <div className="overflow-hidden">
        <LkTable
          tableHeight={tableHeight}
          isDataFetching={facilityListLoading}
          totalRowsCount={totalRecordsCount}
          headerConfig={headerConfig.filter((item) => item)}
          tableData={data}
          dataRequestFunction={updateRequestBody}
          initialSortBy={initialSortByData}
          pageNumber={requestBody.payload.pageRequest.pageNumber}
          pageLimit={PAGE_SIZE}
          rowSize={80}
          onRowClick={({ id }) => navigate(`/facilitys/details/${id}`)}
        />
      </div>
    </div>
  );
};

export default FacilityListing;
