import React, { useMemo } from 'react';
import { makeStyles } from 'tss-react/mui';

import Box from '@mui/material/Box';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import ProductBarcode from './ProductBarcode';
import BoxBarcodes from './BoxBarcodes';
import ScanToDeleteTextField from './ScanToDeleteTextField';

const useStyles = makeStyles()((theme, useCompleWidth) => ({
  container: {
    border: '1px solid #DDDDDD',
    boxSizing: 'border-box',
    borderRadius: 8,
    width: useCompleWidth ? '100%' : 975,
    height: useCompleWidth ? '100%' : 400,
    display: 'flex',
    flexDirection: 'column'
  },
  headerContainer: {
    width: '100%',
    minHeight: 45,
    display: 'flex',
    justifyContent: 'space-between',
    background: '#F5F5F5'
  },
  headerName: {
    display: 'flex',
    alignItems: 'center',
    paddingLeft: 16,
    flex: 1,
    fontWeight: 500,
    fontSize: 16,
    lineHeight: '20px',
    color: '#000000',
    gap: 10
  },
  scannedHeaderSeparator: {
    color: 'rgba(60, 60, 60, 0.54)',
    width: 6,
    height: 20,
    fontWeight: 500,
    fontSize: 16,
    lineHeight: '20px',
    margin: '0px 8px'
  },
  scannedHeaderSuccess: {
    fontWeight: 400,
    fontSize: 14,
    lineHeight: '143%',
    letterSpacing: ' 0.15px',
    color: '#3B873E'
  },
  bodyContainer: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    flexGrow: 1,
    overflow: 'auto'
  },
  successContainer: {
    flex: 1,
    height: '100%',
    borderRight: '1px solid #DDDDDD',
    padding: '8px 15px',
    overflow: 'auto'
  },
  failedContainer: {
    flex: 1,
    height: '100%',
    padding: '8px 15px',
    overflow: 'auto'
  },

  // move it to separate component
  divider: {
    width: '100%',
    height: '1px',
    background: '#EEEEEE'
  }
}));

const ScannedItems = ({
  ScannedBarcodeDetails,
  deleteBarcode,
  activeBoxBarcode,
  deleteActiveBox,
  showDeleteItemForFailed,
  inProgressBoxes = [],
  disableItemDelete = true,
  disableBoxDelete = true,
  useCompleWidth = false,
  hideToDeleteTextField = false
}) => {
  const { classes } = useStyles(useCompleWidth, {
    props: useCompleWidth
  });
  const {
    totalScanned,
    successCount,
    failedItems,
    items,
    boxes,
    invalidBoxes = []
  } = ScannedBarcodeDetails;
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.SCANNED_ITEMS);

  const renderBoxes = useMemo(() => {
    let boxKeys = Object.keys(boxes);
    let updatedBoxes = boxes;
    if (activeBoxBarcode) {
      boxKeys = [activeBoxBarcode, ...boxKeys.filter((eachBox) => eachBox !== activeBoxBarcode)];
    }
    if (!updatedBoxes[activeBoxBarcode]) {
      updatedBoxes = { ...updatedBoxes, [activeBoxBarcode]: [] };
    }
    return boxKeys.map((boxBarcode) => (
      <BoxBarcodes
        activeBoxBarcode={activeBoxBarcode}
        key={boxBarcode}
        boxBarcode={boxBarcode}
        items={updatedBoxes[boxBarcode]}
        deleteActiveBox={deleteActiveBox}
        deleteBarcode={deleteBarcode}
        disableItemDelete={disableItemDelete}
        inProgress={inProgressBoxes.includes(boxBarcode)}
        disableBoxDelete={disableBoxDelete}
        inValidBox={invalidBoxes.includes(boxBarcode)}
      />
    ));
  }, [activeBoxBarcode, boxes, disableItemDelete]);

  const returnProductBarcode = (barcode, status, index) => (
    <ProductBarcode
      key={`${barcode}-${index}`}
      barcode={barcode}
      status={status}
      deleteBarcode={deleteBarcode}
      disableDelete={disableItemDelete}
      showDeleteItemForFailed={showDeleteItemForFailed}
    />
  );

  const renderItems = useMemo(
    () => items.map(({ barcode, status }, index) => returnProductBarcode(barcode, status, index)),
    [items]
  );

  const renderFailed = useMemo(
    () =>
      failedItems.map(({ barcode, status }) => (
        <ProductBarcode
          key={barcode}
          barcode={barcode}
          status={status}
          disableDelete={disableItemDelete}
          deleteBarcode={deleteBarcode}
          showDeleteItemForFailed={showDeleteItemForFailed}
        />
      )),
    [failedItems]
  );

  return (
    <Box className={classes.container}>
      <Box className={classes.headerContainer}>
        <Box className={classes.headerName}>
          <p>
            {CONTENT.SCANNED} ({totalScanned})
          </p>
          <Box className={classes.scannedHeaderSeparator}>•</Box>
          <p className={classes.scannedHeaderSuccess}>
            {CONTENT.SUCCESS} ({successCount})
          </p>
          {successCount > 0 && !disableItemDelete && !hideToDeleteTextField && (
            <ScanToDeleteTextField deleteBarcode={deleteBarcode} />
          )}
        </Box>
        <Box className={classes.headerName}>
          <p>
            {CONTENT.FAILED} ({failedItems.length})
          </p>
        </Box>
      </Box>
      <Box className={classes.bodyContainer}>
        <Box className={classes.successContainer}>
          {renderBoxes}
          {renderItems}
        </Box>
        <Box className={classes.failedContainer}>{renderFailed}</Box>
      </Box>
    </Box>
  );
};

export default ScannedItems;
