import React, { useState, useMemo, useEffect, useCallback } from 'react';
import './qcStatus.scss';
import { makeStyles } from 'tss-react/mui';
import { convertDateFormat } from 'utils/helpers';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { Accordion, Box } from '@mui/material';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { useDispatch, useSelector } from 'react-redux';
import EmptyItem from 'components/BlankPage/BlankPage';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import IconButton from '@mui/material/IconButton';
import LkInput from '../MaterialUi/LkInput';
import { getEditItemLoad, getQcDataLoad, deleteItemLoad } from '../../redux/actionCreators/grn';
import Spinner from '../Spinner/Spinner';
import { toastOpen } from '../../redux/actionCreators/toast';
import NoMatchBarCode from './NoMatchingBarCode';
import EditExpiryModal from './EditExpiryModal';

const useStyles = makeStyles()((theme) => ({
  root: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  detail: {
    flexDirection: 'column'
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  pointerSpacing: {
    cursor: 'pointer',
    marginRight: '5px',
    fontSize: 15
  },
  accordion: {
    boxShadow: 'none',
    minHeight: '40px'
  },
  accordionSummary: {
    height: '40px',
    minHeight: '40px'
  },
  rootAccordian: {
    '.MuiAccordion-root.Mui-expanded': {
      margin: 0
    },
    '.MuiAccordionSummary-root.Mui-expanded': {
      minHeight: '40px'
    },
    '.MuiAccordionDetails-root': {
      padding: '2px 16px 1px'
    },
    '.boxItempadding': {
      paddingBottom: '12px'
    }
  },
  icon: {
    fontSize: 15
  }
}));

function returnElem(data, index, deleteItem, classes, editExpiryFun, isBox = false) {
  const { barcode, qc_reason, expiry_date } = data;

  return (
    <div
      key={`${barcode}_${index}`}
      className={`display-flex justify-content-space-between align-items-center wd-100 ${
        isBox ? 'pd-l0 pd-b16' : 'pd-8 mr-b4'
      }`}
    >
      <div className="fs12">{barcode}</div>
      <div
        className="display-flex 
         align-items-center "
      >
        {expiry_date && (
          <Box
            onClick={() => editExpiryFun(false, barcode, expiry_date)}
            className="display-flex 
         align-items-center cursor-pointer"
          >
            <AccessTimeIcon className={classes.icon} color="primary" />
            <Box pr={1.5} pl={0.5} className="fs12 fw-bold text-primary">
              {convertDateFormat(expiry_date, 'shortDate')}
            </Box>
          </Box>
        )}

        {qc_reason ? (
          <div className="fs12 text-47">{qc_reason}</div>
        ) : (
          <DeleteOutlineIcon
            onClick={() => deleteItem(barcode)}
            className={classes.pointerSpacing}
          />
        )}
      </div>
    </div>
  );
}

const QcStatus = ({ searchedValue, searchFun, boxCodeRegex, createGrnLocal }) => {
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();
  const {
    QCData,
    grnItemSearchSuccess,
    pidDetail,
    itemDeleteSuccess,
    itemDeleteFail,
    QCDataLoading,
    QCDataFail,
    pidList
  } = useSelector((state) => state.grn);
  const [isBoxSearch, setIsBoxSearch] = useState(false);
  const [editExpiryModal, setEditExpiryModal] = useState({
    open: false,
    barcode: '',
    date: null
  });
  const isBoxRequired = pidDetail?.result.data?.is_box_required;

  const toggleExpiryDateModal = useCallback(
    () => setEditExpiryModal((oldState) => ({ ...oldState, open: !oldState.open })),
    []
  );

  const editExpiryFun = (open, barcode, date) => {
    setEditExpiryModal({ open, barcode, date });
    dispatch(getEditItemLoad({ barCode: barcode }));
  };

  const isQcDataEmpty = useMemo(() => {
    if (QCData) {
      const result = Object.keys(QCData).some((t) => {
        if (t === 'qc_passed') {
          return Object.keys(QCData.qc_passed).some(
            (temp) => temp !== 'NO_BOX' || QCData[t][temp].length !== 0
          );
        }
        return QCData[t].length !== 0;
      });
      return !result;
    }
    return false;
  }, [QCData]);
  const [searchedQcData, setSearchedQcData] = useState(QCData);

  useEffect(() => {
    const searchedTemp = { ...QCData };
    if (searchedValue === '') {
      setSearchedQcData({ ...QCData });
    } else {
      // let test = boxCodeRegex.some(t => regextest(searchedValue, t))
      const test = boxCodeRegex.some((t) => searchedValue.startsWith(t));
      if (test) {
        setIsBoxSearch(true);
      } else {
        setIsBoxSearch(false);
      }
      Object.keys(QCData).forEach((element) => {
        const temp = {};
        if (element === 'qc_passed') {
          if (test) {
            const result = {};
            Object.keys(QCData[element]).forEach((t) => {
              if (t.includes(searchedValue)) {
                result[t] = QCData[element][t];
              }
            });
            searchedTemp[element] = result;
          } else {
            Object.keys(QCData[element]).forEach((t) => {
              const result = QCData.qc_passed[t].filter((item) =>
                item.barcode.includes(searchedValue)
              );
              temp[t] = result;
            });
            searchedTemp[element] = { ...temp };
          }
        } else if (test) {
          searchedTemp[element] = [];
        } else {
          const result = QCData[element].filter((temp1) => temp1.barcode.includes(searchedValue));
          searchedTemp[element] = result;
        }
      });
      setSearchedQcData({ ...searchedTemp });
    }
  }, [searchedValue, QCData, boxCodeRegex]);

  useEffect(() => {
    setSearchedQcData({ ...QCData });
  }, [QCData]);

  useEffect(() => {
    if (grnItemSearchSuccess) {
      setEditExpiryModal((oldState) => ({ ...oldState, open: true }));
    }
  }, [grnItemSearchSuccess]);

  useEffect(() => {
    if (itemDeleteSuccess) {
      const { display_message } = itemDeleteSuccess.data;
      const { data } = pidDetail.result;
      dispatch(getQcDataLoad(data));
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Success',
          subHeading: display_message,
          severity: 'success'
        })
      );
    }
  }, [itemDeleteSuccess, pidDetail, dispatch]);

  useEffect(() => {
    if (itemDeleteFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: itemDeleteFail.display_message,
          severity: 'error'
        })
      );
    }
  }, [itemDeleteFail, dispatch]);

  useEffect(() => {
    if (QCDataFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: QCDataFail.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [QCDataFail]);

  const deleteItem = (barCode) => {
    const { type, po_id, invoice_level } = pidList.result.data;
    dispatch(deleteItemLoad({ barCode, type, po_id, invoice_level }));
  };

  const searchItems = (e) => {
    const { value } = e.target;
    searchFun(value);
  };

  const clearQcDataSearch = () => searchFun('');
  if (QCDataLoading) {
    return <Spinner className="display-grid-center wd-100" p={2} />;
  }
  if (QCDataFail || !QCData) {
    return null;
  }
  return (
    <>
      {isQcDataEmpty ? (
        <EmptyItem
          height="300px"
          heading={
            isBoxRequired
              ? createGrnLocal.SCAN_BOX_CODE_TO_GET_STARTED
              : createGrnLocal.createGrnLocal
          }
          subHeading={createGrnLocal.SCANNED_ITEMS_WILL_COME_HERE}
        />
      ) : (
        <>
          <Box pb={2} width="49%">
            <LkInput
              variant="outlined"
              fullWidth
              value={searchedValue}
              placeholder={createGrnLocal.SEARCH_BARCODE_HERE}
              margin="none"
              onChange={(e) => searchItems(e)}
              size="small"
              InputProps={{
                endAdornment: (
                  <IconButton
                    size="small"
                    aria-label="toggle password visibility"
                    onClick={clearQcDataSearch}
                    edge="end"
                  >
                    {searchedValue !== '' && <HighlightOffIcon fontSize="small" />}
                  </IconButton>
                )
              }}
            />
          </Box>
          <Box
            className="display-flex border-grey5-radiusbase 
          qc-container border-radius-base  border-top-dd "
          >
            {Object.keys(searchedQcData).map((item) => {
              const header = createGrnLocal[item];
              const itemData = searchedQcData[item];

              let isItemArray = Array.isArray(itemData);
              let boxesAreEmpty = false;
              if (item === 'qc_passed' && searchedValue !== '') {
                if (isBoxSearch) {
                  boxesAreEmpty = Object.keys(itemData).length === 0;
                } else {
                  boxesAreEmpty = Object.keys(itemData).every((t) => itemData[t].length === 0);
                }
              }
              return (
                <div className="qc-status flex1" key={item}>
                  {/* {isQcDataEmpty then show scan item warning */}

                  <div
                    className="pd-t12 titleBorderRadius pd-b12 pd-r16 pd-l16 fs16
                   fw-bold bg-f5 display-flex justify-content-space-between align-items-center"
                  >
                    {header}
                  </div>
                  {item === 'qc_passed' ? (
                    <>
                      <div className="boxOverflow pd-t8">
                        {Object.keys(itemData).length === 0 && searchedValue !== '' && (
                          <NoMatchBarCode />
                        )}
                        {Object.keys(itemData).map((box, index) => {
                          const isBox = !!(box && box !== 'NO_BOX');
                          isItemArray = Array.isArray(itemData[box]);
                          if (boxesAreEmpty && searchedValue !== '') {
                            return (
                              Object.keys(itemData).length === index + 1 && (
                                <NoMatchBarCode key={box} />
                              )
                            );
                          }

                          return (
                            <Accordion
                              key={box}
                              className={cx(classes.rootAccordian, classes.accordion)}
                            >
                              {isBox && (
                                <AccordionSummary
                                  className={classes.accordionSummary}
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1bh-content"
                                  id="panel1bh-header"
                                >
                                  <div className="display-flex ">
                                    <div className="fs12 fw-bold">{box}</div>
                                    <div className="tags mr-l8">{itemData[box].length}</div>
                                  </div>
                                </AccordionSummary>
                              )}
                              <AccordionDetails className={classes.detail}>
                                {isItemArray
                                  ? itemData[box].map((tempItem) =>
                                    returnElem(
                                      tempItem,
                                      index,
                                      deleteItem,
                                      classes,
                                      editExpiryFun,
                                      isBox
                                    )
                                  )
                                  : searchedValue !== '' && <NoMatchBarCode />}
                              </AccordionDetails>
                            </Accordion>
                          );
                        })}
                      </div>{' '}
                    </>
                  ) : (
                    <div className="boxOverflow pd-l16 pd-r16 pd-t8 pd-b8">
                      {isItemArray && itemData.length > 0
                        ? itemData.map((tempItem, index) =>
                          returnElem(tempItem, index, deleteItem, classes, editExpiryFun, false)
                        )
                        : searchedValue !== '' && <NoMatchBarCode />}
                    </div>
                  )}
                </div>
              );
            })}
          </Box>
        </>
      )}
      {editExpiryModal.open && (
        <EditExpiryModal handleClose={toggleExpiryDateModal} data={editExpiryModal} />
      )}
    </>
  );
};

export default QcStatus;
