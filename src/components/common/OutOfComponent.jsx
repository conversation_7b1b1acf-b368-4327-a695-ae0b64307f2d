import React from 'react';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';

const useStyles = makeStyles()(() => ({
  container: {
    color: 'rgba(60, 60, 60, 0.54)',
    background: '#FFFFFF',
    border: '1px solid #DDDDDD',
    borderRadius: 8,
    padding: '8px 8px 10px 8px'
  },
  title: {
    fontSize: 12,
    marginBottom: 10
  },
  number: {
    fontSize: 20,
    fontWeight: 400,
    lineHeight: '123.5%',
    alignItems: 'center',
    letterSpacing: '0.25px'
  },
  dividend: {
    fontSize: 34
  }
}));

const OutOfComponent = ({ title, dividend, divisor }) => {
  const { classes } = useStyles();
  return (
    <Box className={classes.container}>
      <p className={classes.title}>{title}</p>
      <Box className={classes.number}>
        <span className={classes.dividend}>{dividend}</span>
        <span>/</span>
        <span>{divisor}</span>
      </Box>
    </Box>
  );
};

export default OutOfComponent;
