import React, { forwardRef } from 'react';
import Barcode from 'react-barcode';

const ShipmentLabelFadex = forwardRef((props, ref) => {
  const { shipmentLabelData, shipmentReturnData, shipmentInvoiceData, shipmentUnitPriceData } =
    props;

  const taxDetailsTemp = {};
  taxDetailsTemp.integratedGst = 0;
  taxDetailsTemp.centralGst = 0;
  taxDetailsTemp.stateGst = 0;
  taxDetailsTemp.totalqty = 0;
  taxDetailsTemp.prodDesc = null;
  taxDetailsTemp.unitPrice = 0;
  if (shipmentInvoiceData && shipmentInvoiceData.taxDetails) {
    shipmentInvoiceData.taxDetails.forEach((item) => {
      taxDetailsTemp.integratedGst += item.integratedGst;
      taxDetailsTemp.centralGst += item.centralGst;
      taxDetailsTemp.stateGst += item.stateGst;
      taxDetailsTemp.totalqty += 1;

      if (!taxDetailsTemp.prodDesc) {
        taxDetailsTemp.prodDesc = item.prodDesc;
      }
    });
  }
  if (shipmentUnitPriceData && shipmentUnitPriceData[0] && shipmentUnitPriceData[0].itemPrice) {
    Object.keys(shipmentUnitPriceData[0].itemPrice).forEach((item) => {
      taxDetailsTemp.unitPrice += shipmentUnitPriceData[0].itemPrice[item];
    });
  }

  const tableStyleTaxDetail = {};
  tableStyleTaxDetail.table = {
    border: '1px solid black',
    fontWeight: '500',
    fontSize: '15px',
    display: 'inline-block',
    position: 'relative',
    marginTop: '0px',
    borderCollapse: 'collapse'
  };
  tableStyleTaxDetail.tableHeadTd = {
    border: '1px solid black',
    textAlign: 'center',
    fontWeight: '700',
    padding: '.5em'
  };
  tableStyleTaxDetail.tableTd = {
    border: '1px solid black',
    textAlign: 'center',
    padding: '.5em'
  };

  return (
    <div
      ref={ref}
      className="shipmentLabelContainer"
      style={{ display: 'inline-block', width: '100%' }}
    >
      <div
        className="leftContainer"
        style={{ float: 'left', padding: '20px', width: '45%', wordWrap: 'break-word' }}
      >
        <div
          className="shipperAddress"
          style={{
            textAlign: 'left',
            marginTop: '5px',
            marginBottom: '2px'
          }}
        >
          <div style={{ textAlign: 'left' }}>
            <span className="shipTolabel" style={{ fontWeight: '800', fontSize: '15px' }}>
              Shipper
            </span>
          </div>
          <div className="add_name" style={{ fontSize: '15px' }}>
            Dealskart Online Services Pvt Ltd, Manesar
          </div>
          <div className="add_line1" style={{ fontSize: '15px' }}>
            Property No 29/24/2, 25/2/1, 30/4/1, 5/1, 6/1/1, 6/1/2,
          </div>
          <div className="add_line2" style={{ fontSize: '15px' }}>
            Revenue Estate Of Village Begumpur, Khatola, Gurugram
          </div>
          <div className="add_line3" style={{ fontSize: '15px' }}>
            <span className="add_pincode">HR IN 122004</span>
          </div>
          <div className="add_line4" style={{ fontSize: '15px' }}>
            <span className="add_telephone">Tel no - 9999899998</span>
          </div>
          <div className="remark" style={{ textAlign: 'left', fontSize: '15px' }}>
            (If Undelivered Return To This Address)
          </div>
        </div>
        <div className="gstin" style={{ fontWeight: '800', fontSize: '16px' }}>
          GSTIN: {shipmentLabelData.gstin}
        </div>
        <div className="shippingAddress" style={{ marginBottom: '30px', marginTop: '20px' }}>
          <div>
            <span className="shipTolabel" style={{ fontWeight: '800', fontSize: '18px' }}>
              {` Ship To : ${shipmentLabelData.storeCode ? shipmentLabelData.storeCode : ''}`}
            </span>
          </div>
          <div className="add_name" style={{ fontSize: '18px' }}>
            {shipmentLabelData.addressDTO.firstName} {shipmentLabelData.addressDTO.lastName}
          </div>
          <div className="add_line1" style={{ fontSize: '18px' }}>
            {shipmentLabelData.addressDTO.street}
          </div>
          <div className="add_line2" style={{ fontSize: '18px' }}>
            <span className="add_city">{shipmentLabelData.addressDTO.city}, </span>
            <span className="add_state">{shipmentLabelData.addressDTO.region} - </span>
            <span className="add_pincode">{shipmentLabelData.addressDTO.postcode}</span>
          </div>
          <div className="add_line3" style={{ fontSize: '18px' }}>
            <span className="add_country">India, </span>
            <span className="add_telephone">Tel No: {shipmentLabelData.addressDTO.telephone}</span>
          </div>
          {shipmentLabelData.addressDTO.fax && (
            <div className="alternatePhone" style={{ fontSize: '15px' }}>
              Alternate Telephone: {shipmentLabelData.addressDTO.fax}
            </div>
          )}
        </div>
        <div
          className="FedexLogo"
          style={{
            fontFamily: 'Berlin Sans',
            fontSize: '35px',
            fontWeight: 2500,
            marginBottom: '20px'
          }}
        >
          <strong>FedEx</strong>
        </div>
        <div
          className="shipmentCode"
          style={{ fontSize: '25px', fontWeight: 1000, marginBottom: '20px' }}
        >
          <strong>
            {shipmentLabelData.shippingProviderCode} - {shipmentLabelData.paymentMethod}
          </strong>
        </div>
        {shipmentLabelData.paymentMethod === 'COD' && (
          <div
            className=" collectableAmount"
            style={{
              fontWeight: '900',
              fontSize: '30px',
              marginBottom: '5px',
              marginTop: '25px'
            }}
          >
            COD Cash
            <br />
            {shipmentLabelData.collectibleAmount} INR
          </div>
        )}
      </div>

      <div
        className="rightContainer"
        style={{ float: 'right', padding: '20px', width: '55%', wordWrap: 'break-word' }}
      >
        <div className="shipmentBarcode" style={{ textAlign: 'center', width: '80%' }}>
          <div style={{ fontWeight: '800', fontSize: '15px' }} className="barcodeAwb">
            {shipmentLabelData.shippingPackageId}
          </div>
          <Barcode height={50} value={shipmentLabelData.shippingPackageId} />
        </div>
        <div className="orderDetails">
          <div
            className="orderCode"
            style={{ fontWeight: '800', fontSize: '15px', marginBottom: '10px' }}
          >
            Order Number: {shipmentLabelData.incrementId}
          </div>
        </div>
        <div className="date" style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}>
          Shipment Date: {shipmentLabelData.invoiceDate}
        </div>
        <div
          className="weight"
          style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
        >
          Weight: 0.5 kg
        </div>
        <div style={{ marginBottom: '2px' }}>
          <span style={{ fontSize: '10px' }} className="billtc">
            Bill T/C:Sender
          </span>
          <span style={{ fontSize: '10px', paddingLeft: '10px' }} className="billdt">
            Bill D/T:Sender
          </span>
          <span style={{ fontSize: '10px', paddingLeft: '10px' }} className="fedexmeter">
            Fedex Meter:100423999
          </span>
        </div>
        <div className="standard" style={{ marginBottom: '5px', marginTop: '2px' }}>
          <div
            className="tracking"
            style={{ fontWeight: '800', fontSize: '20px', marginBottom: '2px' }}
          >
            TRK: {shipmentReturnData.awbNo}
            <div style={{ fontSize: '10px', marginBottom: '2px', fontWeight: 'normal' }}>
              {shipmentReturnData.formId}
            </div>
          </div>
          <div style={{ textAlign: 'right' }}>
            <span
              className="aa"
              style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
            >
              {shipmentReturnData.serviceLevel}
            </span>
            <div
              className="economy"
              style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
            >
              {shipmentReturnData.serviceType}
            </div>
            {shipmentLabelData.paymentMethod === 'COD' && (
              <div
                style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
                className="aavalue"
              >
                {shipmentLabelData.paymentMethod}
              </div>
            )}
            <div
              className="form"
              style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
            >
              {shipmentReturnData.destinationPostalCode}
            </div>
            <div
              className="bom"
              style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
            >
              {shipmentReturnData.airportId}
            </div>
          </div>
        </div>
        <div className="shippingPackageIdBarcode" style={{ marginBottom: '2px' }}>
          <div>
            <span style={{ fontSize: '30px', marginBottom: '50px' }} className="delhr">
              <b>{shipmentReturnData.routingCode}</b>
            </span>
            <span
              style={{ fontSize: '15px', marginBottom: '50px', paddingLeft: '260px' }}
              className="mhin"
            >
              <b>{shipmentReturnData.stateCountryCode}</b>
            </span>
          </div>
          <div style={{ textAlign: 'center', width: '80%' }}>
            <Barcode height={50} value={shipmentReturnData.awbBarcode} />
          </div>
          <div style={{ fontSize: '10px' }}>UVSDK version # : {shipmentReturnData.version}</div>
        </div>
        {shipmentLabelData.paymentMethod === 'COD' && (
          <div className="shippingPackageIdBarcode">
            <div
              className="tracking"
              style={{ fontWeight: '800', fontSize: '20px', marginTop: '30px' }}
            >
              <span className="trackingreturn">TRK: {shipmentReturnData.returnTrackingNumber}</span>
            </div>
            <div style={{ fontSize: '10px' }}>0325</div>
            <div style={{ textAlign: 'right' }}>
              <div
                className="priority"
                style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
              >
                PRIORITY OVERNIGHT
              </div>
              <div
                className="cod return"
                style={{ fontWeight: 700, fontSize: '11px', paddingLeft: '156px' }}
              >
                COD RETURN
              </div>
            </div>
            <div style={{ fontSize: '20px', marginBottom: '10px' }} className="delhr">
              <b>COD AMOUNT {shipmentLabelData.collectibleAmount} INR</b>
            </div>
            <div>
              <span style={{ fontSize: '20px', marginBottom: '50px' }} className="delhr">
                {' '}
                <b>UNSECURED</b>
              </span>
              <span
                style={{ fontSize: '15px', marginBottom: '50px', paddingLeft: '280px' }}
                className="mhin"
              >
                <b>{shipmentReturnData.stateCountryCode}</b>
              </span>
            </div>
            <div style={{ textAlign: 'center', width: '80%', marginBottom: '2px' }}>
              <Barcode height={50} value={shipmentReturnData.returnBarcode} />
            </div>
          </div>
        )}
      </div>

      <div style={{ width: '100%', textAlign: 'center' }}>
        <table style={tableStyleTaxDetail.table}>
          <tr>
            <td style={tableStyleTaxDetail.tableHeadTd}>Description of Goods</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>Unit Price</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>DIS</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>IGST</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>SGST</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>CGST</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>QTY</td>
            <td style={tableStyleTaxDetail.tableHeadTd}>ORDER TOTAL</td>
          </tr>
          <tr>
            <td style={tableStyleTaxDetail.tableTd}> {taxDetailsTemp.prodDesc} </td>
            <td style={tableStyleTaxDetail.tableTd}>
              {' '}
              {Number(taxDetailsTemp.unitPrice).toFixed(2)}{' '}
            </td>
            <td style={tableStyleTaxDetail.tableTd}>
              {' '}
              {Number(shipmentUnitPriceData[0].shipmentTotalDiscount).toFixed(2)}{' '}
            </td>
            <td style={tableStyleTaxDetail.tableTd}>
              {' '}
              {Number(taxDetailsTemp.integratedGst).toFixed(2)}{' '}
            </td>
            <td style={tableStyleTaxDetail.tableTd}>
              {' '}
              {Number(taxDetailsTemp.stateGst).toFixed(2)}{' '}
            </td>
            <td style={tableStyleTaxDetail.tableTd}>
              {' '}
              {Number(taxDetailsTemp.centralGst).toFixed(2)}{' '}
            </td>
            <td style={tableStyleTaxDetail.tableTd}> {taxDetailsTemp.totalqty} </td>
            <td style={tableStyleTaxDetail.tableTd}>
              {' '}
              {Number(shipmentUnitPriceData[0].orderTotal).toFixed(2)}{' '}
            </td>
          </tr>
        </table>
      </div>

      <div style={{ position: 'absolute', bottom: '20px' }}>
        <div style={{ textAlign: 'center', fontSize: '10px', marginTop: '1px' }}>
          *All Prices are inclusive of taxes
        </div>
        <div style={{ textAlign: 'center', fontSize: '10px' }}>
          Subject to FedEx Conditions of Carriage, which limit the liability of FedEx for loss,
          delay or damage to consignments. Visit www.fedex.com.In to view the FedEx Conditions of
          Carriage.
        </div>
      </div>
    </div>
  );
});

export default ShipmentLabelFadex;
