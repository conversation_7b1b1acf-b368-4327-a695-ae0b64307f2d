import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { InfoStrip } from 'components/common';
import Spinner from 'components/Spinner/Spinner';
import { isEmpty } from 'utils/lodash';
import { convertDateFormat, getAllQueryParam } from 'utils/helpers';
import { getBarCodeSeriesLoad } from 'redux/actionCreators/barcodeSeries';
import {
  getIqcBarcodeDetailsLoad,
  getIqcBarcodeDetailsReset,
  getPidDetailsLoad,
  markIqcCompleteLoad,
  markIqcCompleteReset
} from 'redux/reducers/inwardQC';
import IQCScanner from './components/IQCScanner';
import IQCInfo from './components/IQCInfo';
import QCContainer from './components/QCContainer';

const styles = makeStyles()((theme) => ({
  root: {
    background: '#fff',
    height: 'calc(100vh - 65px)'
  },
  input: {
    width: '100%',
    height: '40',
    background: '#FFFFFF',
    outline: 0,
    border: '1px solid #bdbdbd',
    padding: 10,
    borderRadius: theme.spacing(0.5),
    '&::placeholder': {
      color: '#a4a9ab'
    }
  },
  qcfailBtn: {
    background: '#F88078',
    color: '#FFFFFF',
    marginLeft: 15,
    '&:hover': {
      backgroundColor: '#F88078'
    }
  }
}));

const InwardQC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { classes } = styles();
  const [infoStripData, setInfoStripData] = useState([]);
  const { data, isLoading } = useSelector((state) => state.inwardQC.iqcBarcodeDetails);
  const { isLoading: iQcCompleteLoading, data: iQcCompleteSuccess } = useSelector(
    (state) => state.inwardQC.iQCComplete
  );
  const { scanedBarcodeResponse } = useSelector((state) => state.inwardQC.qcData);
  const { data: barcodeSeries } = useSelector((state) => state.barcodeSeries);
  const [barcode, grncode] = getAllQueryParam(window.location.search);

  useEffect(() => {
    if (isEmpty(data)) {
      dispatch(
        getIqcBarcodeDetailsLoad({
          barcode: barcode?.value,
          grnCode: grncode?.value
        })
      );
    }
    return () => {
      dispatch(getIqcBarcodeDetailsReset());
    };
  }, [dispatch]);

  useEffect(() => {
    if (!barcodeSeries.length) {
      dispatch(getBarCodeSeriesLoad());
    }
  }, [barcodeSeries]);

  useEffect(() => {
    if (!isEmpty(data)) {
      const { grnCode, grnDate, pid, qcType, vendorId, samplingQty, qcPassQty, qcFailQty } = data;
      const pendindQc = samplingQty - qcPassQty - qcFailQty;
      const infoStripTemp = [
        {
          key: 'GRN',
          className: 'text-primary cursor-pointer',
          value: grnCode
        },
        {
          key: 'GRN Date',
          value: convertDateFormat(grnDate, 'shortDate')
        },
        { key: 'QC Type', value: qcType },
        { key: 'Vendor ID', value: vendorId },
        { key: 'Pending QC', value: `${pendindQc}/${samplingQty}` },
        { key: 'QC Pass', value: `${qcPassQty}/${samplingQty}` },
        { key: 'QC Fail', value: `${qcFailQty}/${samplingQty}` }
      ];
      setInfoStripData([...infoStripTemp]);
      dispatch(getPidDetailsLoad(pid));
    }
  }, [data]);

  useEffect(() => {
    if (!isEmpty(scanedBarcodeResponse)) {
      const { totalSamplingQty, totalGRNQcPassQty, totalGRNQcFailQty } = scanedBarcodeResponse;
      const pendindQc = totalSamplingQty - totalGRNQcPassQty - totalGRNQcFailQty;

      const newHeaders = [
        { key: 'Pending QC', value: `${pendindQc}/${totalSamplingQty}` },
        { key: 'QC Pass', value: `${totalGRNQcPassQty}/${totalSamplingQty}` },
        { key: 'QC Fail', value: `${totalGRNQcFailQty}/${totalSamplingQty}` }
      ];

      const tempInfostripData = [...infoStripData];
      tempInfostripData.splice(4, 3, ...newHeaders);
      setInfoStripData([...tempInfostripData]);
    }
  }, [scanedBarcodeResponse]);

  const completeFun = () => {
    const { grnCode, boxCode, pid } = data;
    dispatch(markIqcCompleteLoad({ grnCode, boxCode, pid }));
  };

  useEffect(() => {
    if (iQcCompleteSuccess) {
      dispatch(markIqcCompleteReset());
      setTimeout(() => {
        navigate(`/grn/details/${data?.grnCode}/iqc`);
      }, 500);
    }
  }, [dispatch, iQcCompleteSuccess]);

  if (isLoading) {
    return (
      <Box textAlign="center">
        {' '}
        <Spinner />
      </Box>
    );
  }

  return (
    <Box className={classes.root}>
      <InfoStrip data={infoStripData}>
        <Button
          variant="contained"
          color="primary"
          onClick={completeFun}
          disabled={iQcCompleteLoading || isEmpty(data)}
        >
          Done
        </Button>
      </InfoStrip>
      <Box p={2}>
        <Box display="flex" justifyContent="space-between">
          <IQCScanner classes={classes} data={data} />
          <IQCInfo data={data} />
        </Box>
        <QCContainer grn_code={data?.grnCode} />
      </Box>
    </Box>
  );
};

export default InwardQC;
