import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import { useDispatch, useSelector } from 'react-redux'; // useSelector,
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { searchListLoad, searchListReset } from 'redux/actionCreators/filters';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import useFilterHook from 'components/common/useFilterHook';
import LkToolTip from 'components/ToolTip/ToolTip';
import CloseIcon from '@mui/icons-material/Close';
import useWindowResize from 'common/useWindowResize';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkChip from 'components/MaterialUi/LkChip';
import LkInput from 'components/MaterialUi/LkInput';
import { LOCALISATION } from 'redux/reducers/localisation';
import { debitNoteListCsvLoad } from '../../redux/actionCreators/debitNote';
import Spinner from '../../components/Spinner/Spinner';
import {
  convertDateFormat,
  roundUptoFixedDigits,
  generatePayloadForSearchAPI,
  getQueryParam,
  getAllQueryParam,
  genericDateFormatted,
  getCurrencySumbol
} from '../../utils/helpers';
import { toastOpen } from '../../redux/actionCreators/toast';
import useKeyboardShortcut from '../../common/useKeyboardShortcut';

const useStyles = makeStyles()(() => ({
  root: {
    background: '#fff'
  },
  table: {
    minWidth: 700
  },
  container: {
    maxHeight: 700,
    minHeight: 450
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },

  outlinedPrimary: {
    marginLeft: '16px',
    height: 35
  },
  tableContainer: {
    padding: 20
  }
}));
const PAGE_SIZE = 40;

const obj = {
  isDataFetched: false
};

const defaultObj = {
  isReset: true,
  path: 'debitNote?version=v1',
  payload: {
    type: 'debitNote',
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey: 'created_at',
      sortOrder: 'DESC'
    }
  },
};
const sortingData = {
  key: 'created_at',
  order: 'DESC'
};

const initialSortData = { id: 'created_at', order: 'DESC' }

let objPayload = { ...defaultObj };
let fromPo = null;

const DebitNote = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [globalDebitNoteSearch, setGlobalDebitNoteSearch] = useState('');

  const {
    searchList,
    searchListLoading,
    seachListFail,
    totalRecordsCount,
    autoSuggestionListDebitNote_PO,
    autoSuggestionListDebitNote_Vendor,
    autoSuggestionListDebitNote_Created_By,
    autoSuggestionListDebitNote_Vendor_Invoice_Number,
    autoSuggestionListDebitNote_DebitNote,
    autoSuggestionListDebitNote_NexS_Invoice_Number
  } = useSelector((state) => state.filters);
  const { dnListExportError, dnListExportLoading } = useSelector((state) => state.debitNote);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.DEBIT_NOTE);

  const [tableHeight] = useWindowResize(window.innerHeight - 290);

  const { classes } = useStyles();
  const dispatch = useDispatch();
  const isInitialRequest = useRef(true);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();

  useEffect(() => {
    fromPo = null;
    fromPo = getQueryParam(location.search.replace('?', ''), 'fromPo');
    // if (fromPo) {
    //   setTableHeightValue(tableHeight - 110)
    // }
    // else setTableHeightValue(tableHeight)
  }, [location]);

  useEffect(
    () => () => {
      dispatch(searchListReset());
      obj.isDataFetched = false;
      sortingData.order = 'DESC';
      sortingData.key = 'created_at';
    },
    // dispatch(debitNoteListLoad(obj));
    [dispatch]
  );

  useEffect(() => {
    if (dnListExportError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: dnListExportError?.message,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [dnListExportError, dispatch]);

  useEffect(() => {
    if (seachListFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: seachListFail?.message,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [seachListFail, dispatch]);

  const goToDebitNote = (dn) => {
    navigate(`/debitNote/detail?dnNum=${dn}`);
  };

  const sortTable = (type, key, page) => {
    if (!isInitialRequest.current) {
      let pageNumber = 0;
      let reset = true;
      if (type) {
        if (
          objPayload.payload.pageRequest.sortKey === key &&
          objPayload.payload.pageRequest.sortOrder === type
        ) {
          pageNumber = page;
          reset = false;
        }
        objPayload.payload.pageRequest.sortKey = key;
        objPayload.payload.pageRequest.sortOrder = type;
      } else {
        objPayload.payload.pageRequest.sortKey = 'created_at';
        objPayload.payload.pageRequest.sortOrder = 'DESC';
      }
      objPayload.payload.pageRequest.pageNumber = pageNumber;
      objPayload.isReset = reset;
      dispatch(searchListLoad(objPayload));
    }
  };

  const filterSearchOption = (value, key) => {
    if (value) {
      objPayload.payload[key] = value;
    } else {
      delete objPayload.payload[key];
    }
    objPayload.payload.pageRequest.pageNumber = 0;
    objPayload.isReset = true;

    dispatch(searchListLoad(objPayload));
  };

  const exportDebitNote = useCallback(() => {
    const tempData = JSON.parse(JSON.stringify(objPayload));
    tempData.payload.pageRequest.pageSize = totalRecordsCount;
    dispatch(debitNoteListCsvLoad(tempData));
  }, [totalRecordsCount, dispatch]);
  // Alt + E -> Export CSV
  useKeyboardShortcut([18, 69], exportDebitNote);

  const HEADER_CONFIG = [
    {
      key: 'debit_note_no',
      name: CONTENT.DEBIT_NOTE_NO,
      supportFilter: true,
      supportSort: true,
      columnName: 'debit_note_no',
      align: 'left',
      style: { minWidth: '180px', maxWidth: '180px' },
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListDebitNote_DebitNote,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'DebitNote_DebitNote',
        apiName: 'debitNote'
      },
      formatBody: useCallback(
        ({ debit_note_no }) => (
          <Typography
            variant="h6"
            color="primary"
            onClick={() => goToDebitNote(debit_note_no)}
            onKeyPress={() => goToDebitNote(debit_note_no)}
          >
            {debit_note_no}
          </Typography>
        ),
        []
      )
    },
    {
      key: 'created_at',
      name: CONTENT.CREATED_ON,
      supportFilter: true,
      supportSort: true,
      align: 'left',
      columnName: 'created_at',
      filterData: {
        type: 'dateRange',
        submitFilter: onChangeFilterList,
        columnName: 'DebitNote_CREATED',
        selectedFilterList
      },
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(({ created_at }) => <Box>{genericDateFormatted(created_at)}</Box>, [])
    },
    {
      name: CONTENT.NEXS_INV_NO,
      key: 'invoice_number',
      supportFilter: true,
      supportSort: true,
      columnName: 'invoice_number',
      align: 'left',
      style: { minWidth: '180px', maxWidth: '180px' },
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListDebitNote_NexS_Invoice_Number,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'debitNote',
        columnName: 'DebitNote_NexS_Invoice_Number'
      },
      formatBody: useCallback(({ invoice_number }) => (
        <Typography
          variant="h6"
          color="primary"
          onClick={() =>
            window.open(
              `${window.location.origin}/invoice/view?invoiceNumber=${invoice_number}`,
              '_blank'
            )
          }
        >
          {invoice_number}
        </Typography>
      ))
    },
    {
      name: CONTENT.VENDOR_INV_NO,
      key: 'vendor_invoice_number',
      supportFilter: true,
      supportSort: true,
      columnName: 'vendor_invoice_number',
      align: 'left',
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListDebitNote_Vendor_Invoice_Number,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'debitNote',
        columnName: 'DebitNote_Vendor_Invoice_Number'
      },
      style: { minWidth: '170px', maxWidth: '170px' }
    },
    {
      name: CONTENT.INVOICE_DATE,
      key: 'invoice_date',
      align: 'left',
      columnName: 'invoice_date',
      supportFilter: true,
      supportSort: true,
      filterData: {
        type: 'dateRange',
        submitFilter: onChangeFilterList,
        columnName: 'DebitNote_INVOICE_DATE',
        selectedFilterList
      },
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ invoice_date }) => <Box>{convertDateFormat(invoice_date, 'utcTOlocalDate')}</Box>,
        []
      )
    },
    {
      name: CONTENT.PO_NO,
      key: 'po_num',
      supportFilter: true,
      supportSort: true,
      columnName: 'po_num',
      align: 'left',
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListDebitNote_PO,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        apiName: 'debitNote',
        columnName: 'DebitNote_PO'
      },
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ po_num }) => (
          <a
            href={`${window.location.origin}/po/detail?poNum=${po_num}&fromPo=true`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary fw-bold fs12"
          >
            {po_num}
          </a>
        ),
        []
      )
    },
    {
      name: CONTENT.VENDOR_NAME,
      key: 'vendor_name',
      supportFilter: true,
      supportSort: true,
      columnName: 'vendor_name',
      // marginLeft: '40px',
      align: 'left',
      formatBody: useCallback(
        ({ vendor_name }) => (
          <Box>
            {vendor_name.length > 30 ? (
              <LkToolTip
                placement="bottom-end"
                title={<Box p={1}>{vendor_name} </Box>}
                className="dtoolTip"
              >
                <div className="ellipsis-vertical">{vendor_name}</div>
              </LkToolTip>
            ) : (
              vendor_name || '-'
            )}
          </Box>
        ),
        []
      ),
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListDebitNote_Vendor,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'DebitNote_Vendor',
        apiName: 'debitNote'
      },
      style: { minWidth: '180px', maxWidth: '180px' }
    },
    {
      name: CONTENT.CREATED_BY,
      key: 'created_by',
      supportFilter: true,
      supportSort: true,
      columnName: 'created_by',
      align: 'left',
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListDebitNote_Created_By,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'DebitNote_Created_By',
        apiName: 'debitNote'
      },
      formatBody: useCallback(({ created_by }) => <Box>{created_by || 'NA'}</Box>, [])
    },
    {
      name: CONTENT.AMOUNT,
      key: 'tot_debit_note_value',
      align: 'right',
      columnName: 'tot_debit_note_value',
      style: { minWidth: '100px', maxWidth: '100px' },
      formatBody: useCallback(({ tot_debit_note_value, currency }) => (
        <Box>{`${getCurrencySumbol(currency)}${roundUptoFixedDigits(tot_debit_note_value)}`}</Box>
      ))
    }
  ];

  useEffect(() => {
    isInitialRequest.current = false;
    obj.isDataFetched = true;
    const result = getAllQueryParam(window.location.search);
    objPayload = JSON.parse(JSON.stringify(defaultObj));
    let { payload } = objPayload;
    payload.pageRequest.sortKey = sortingData.key;
    payload.pageRequest.sortOrder = sortingData.order;
    const globalSeachInvoiceValue = document.getElementById('searchInvoiceValue');
    if (globalSeachInvoiceValue && globalSeachInvoiceValue.value) {
      payload.global_debit_note_no = globalSeachInvoiceValue.value;
    }
    payload = generatePayloadForSearchAPI(result, payload, mapSearchFilterKey, 'DebitNote_');
    dispatch(searchListLoad(objPayload));
  }, [location.search, dispatch]);

  const filterList = React.useMemo(
    () =>
      // let fromPo = getQueryParam(window.location.search.replace('?', ''), 'fromPo');
      selectedFilterList.filter(({ key }) => key.startsWith('DebitNote_') || key === 'poNum'),
    [selectedFilterList]
  );
  const isDisabledFun = React.useMemo(() => {
    const temp = filterList.filter(({ key }) => key !== 'fromPo' && key !== 'poNum');
    return !temp.length;
  }, [filterList]);

  return (
    <Box className={`${classes.root} ${!fromPo && 'pd-24 po-dashboard'}   pos-rel`}>
      {!fromPo && (
        <Box className={classes.mainBar}>
          <Box width="320px">
            <LkInput
              fullWidth
              label={CONTENT.SEARCH_DEBIT_NOTES}
              variant="outlined"
              id="searchInvoiceValue"
              value={globalDebitNoteSearch}
              onChange={(e) => setGlobalDebitNoteSearch(e.target.value)}
              // onBlur={e => handleInputChange(props.location.pathname,queryParams,e)}
              onKeyPress={(e) =>
                e.which === 13 && filterSearchOption(e.target.value, 'global_debit_note_no')
              }
            />
          </Box>
        </Box>
      )}
      <Box
        className={`${
          !fromPo && 'border-grey5-radiusbase'
        }  invoice-dashboard-main  overflow-hidden`}
      >
        <Box className="invoice-dashboard-action pd-16 display-flex justify-content-space-between">
          <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
            {filterList.map(({ key, value }) => {
              if (key === 'poNum' || key === 'fromPo') {
                return null;
              }
              const temp = key.startsWith('DebitNote_') ? key.slice(10) : key;
              return (
                <Box key={`${key}-${value}`} mr={2} mb={1}>
                  <LkChip
                    label={`${temp}: ${value}`}
                    type="filter"
                    deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                    size="small"
                    onDelete={() => deleteSelectedFilter(key, value)}
                  />
                </Box>
              );
            })}
          </Box>
          <Box className="display-flex justify-content-space-between">
            <Button
              style={{ borderRadius: 8 }}
              disabled={isDisabledFun}
              className={classes.outlinedPrimary}
              onClick={() => {
                setGlobalDebitNoteSearch('');
                const tempData = selectedFilterList.filter(
                  ({ key }) => !key.startsWith('DebitNote_')
                );
                resetFilters(tempData);
                sortingData.key = 'created_at';
                sortingData.order = 'DESC';
              }}
              variant="outlined"
              color="primary"
            >
              {CONTENT.RESET}
            </Button>

            {dnListExportLoading ? (
              <Button
                style={{ borderRadius: 8, width: '105px' }}
                disabled
                className={classes.outlinedPrimary}
                variant="contained"
                color="primary"
              >
                <Spinner />
              </Button>
            ) : (
              <Button
                style={{ borderRadius: 8 }}
                onClick={() => exportDebitNote()}
                className={classes.outlinedPrimary}
                variant="outlined"
                color="primary"
              >
                {CONTENT.EXPORT}
              </Button>
            )}
          </Box>
        </Box>
        <Box className={classes.tableContainer}>
          <LkTable
            tableHeight={tableHeight}
            headerConfig={HEADER_CONFIG}
            isDataFetching={searchListLoading}
            tableData={searchList}
            totalRowsCount={totalRecordsCount}
            dataRequestFunction={sortTable}
            pageLimit={PAGE_SIZE}
            rowKey="id"
            initialSortBy={initialSortData}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default DebitNote;
