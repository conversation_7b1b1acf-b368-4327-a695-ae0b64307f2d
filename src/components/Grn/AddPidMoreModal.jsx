import React, { useEffect } from 'react'; // useState,
import { Form, Field } from 'react-final-form';
import { useDispatch, useSelector } from 'react-redux';
import { required } from 'utils/validation';
import { LOCALISATION } from 'redux/reducers/localisation';

import './addPidModal.scss';
import { Button } from '@mui/material';
import LkModal from '../Modal/Modal';
import LkInput from '../MaterialUi/LkInput';
import { updateQtyLoad, getSamplingQtyLoad } from '../../redux/actionCreators/grn'; // resetPidList
import { toastOpen } from '../../redux/actionCreators/toast';
import Spinner from '../Spinner/Spinner';

const mutatorsObj = {
  setEstimatedQuantity: (args, state, utils) =>
    utils.changeValue(state, 'estimatedQuantity', () => args[0]),
  setMinQunatityScan: (args, state, utils) =>
    utils.changeValue(state, 'minQunatityScan', () => args[0])
};

const AddPidModalMore = (props) => {
  const { open, openModal, toggleAddPidModal, grnNo } = props;
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);

  const dispatch = useDispatch();
  const {
    samplingQtyData,
    updateQtyProccessing,
    updateQtyData,
    updateQtyDataFail,
    pidDetail,
    meta
  } = useSelector((state) => state.grn);
  useEffect(() => {
    if (updateQtyData) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createGrnLocal.QUANTITY_UPDATED_SUCCESSFULLY_ADDED,
          severity: 'success'
        })
      );
      openModal(false);
      //  openModal(false);
      //  dispatch(resetPidList());
    }
  }, [updateQtyData, dispatch, openModal]);

  useEffect(() => {
    if (updateQtyDataFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: updateQtyDataFail.response.data.display_message,
          severity: 'error'
        })
      );
      openModal(false);
    }
  }, [updateQtyDataFail, dispatch, openModal]);

  const submitAddPid = (values) => {
    const { estimatedQuantity } = values;
    if (estimatedQuantity <= 0) {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createGrnLocal.PLEASE_PROVIDE_POSITIVE_NUMBER,
          severity: 'error'
        })
      );
    }
    const obj = {
      grn_code: grnNo,
      pid: pidDetail?.result?.data?.pid,
      meta,
      estimated_qty: estimatedQuantity
    };
    dispatch(updateQtyLoad(obj));
  };

  const apiObj = (values) => {
    const { estimatedQuantity } = values;

    return {
      grnCode: grnNo,
      pid: {
        pid: pidDetail?.result?.data?.pid,
        estimatedQuantity
      },
      meta
    };
  };

  const getSamplingQty = (values) => {
    const obj = apiObj(values);
    dispatch(getSamplingQtyLoad(obj));
  };

  const openAddPidModal = () => {
    openModal(false);
    toggleAddPidModal(true);
  };

  return (
    <LkModal
      open={open}
      title={createGrnLocal.FINISHED_SCAN_MORE}
      subTitle={createGrnLocal.IF_YOU_WISH_TO_SCAN_MORE_ITEMS_OF_SAME_PID}
      showActionButton={false}
      handleClose={() => openModal(false)}
    >
      <Form
        onSubmit={submitAddPid}
        mutators={mutatorsObj}
        initialValues={{ scanned_id: pidDetail?.result?.data?.pid }}
        render={({ handleSubmit, values, form }) => (
          // mutators = form.mutators;
          <form onSubmit={handleSubmit} name="otp" className="otp-form" noValidate>
            <div className="add-pid-modal mr-t25">
              <Field name="scanned_id" validate={required}>
                {({ input, meta }) => (
                  <div className="material-group add-scan-input">
                    <LkInput disabled {...input} fullWidth placeholder="Scan PID / SKU / GTIN" />
                    <span className="input-error">
                      {meta.error && meta.touched ? meta.error : ''}
                    </span>
                  </div>
                )}
              </Field>
              <div className="display-flex justify-content-space-between add-scan-input mr-t15">
                <Field name="estimatedQuantity" validate={required}>
                  {({ input, meta }) => (
                    <div className="material-group add-scan-input">
                      <LkInput
                        fullWidth
                        disabled={!values.scanned_id}
                        {...input}
                        onBlur={() => getSamplingQty(values)}
                        placeholder={createGrnLocal.ENTER_ADDITIONAL_QUANTITY}
                      />
                      <span className="input-error">
                        {meta.error && meta.touched ? meta.error : ''}
                      </span>
                    </div>
                  )}
                </Field>
                <div className="min-scan pd-10 mr-l20 display-flex align-items-center">
                  <div className="text-66 fs14">
                    {createGrnLocal.MIN_QUANTITY_TO_SCAN}:
                    <span className="text-33 fs16 fw-bold">
                      {' '}
                      {samplingQtyData?.result?.data?.sampling_quantity || 0}
                    </span>
                  </div>
                </div>
              </div>
              <div className=" display-flex justify-content-space-around mr-b40 mr-t40">
                <div className="mr-r10">
                  <Button onClick={() => openAddPidModal()} variant="outlined" color="primary">
                    {createGrnLocal.ADD_NEW_PID}
                  </Button>
                </div>
                <div className="mr-l10">
                  {!updateQtyProccessing && (
                    <Button type="submit" variant="contained" color="primary">
                      {createGrnLocal.ADD_QUANTITY}
                    </Button>
                  )}
                  {updateQtyProccessing && (
                    <Button disabled variant="contained" color="primary">
                      <Spinner />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </form>
        )}
      />
    </LkModal>
  );
};
export default React.memo(AddPidModalMore);
