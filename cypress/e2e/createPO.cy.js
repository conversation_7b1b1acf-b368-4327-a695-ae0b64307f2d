describe('PO Creation', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
  });

  afterEach(() => {
    cy.logout();
  });

  it('Visits the homepage', () => {
    cy.title().should('eq','NexS Web Portal');
    cy.get('[data-test=submitIsVisible]', { timeout: 10000 })
    cy.getByCy('leftDrawer-open-icon').click();
    cy.getByCy('Procurement').click();
    cy.getByCy('Purchase Order').click();
    cy.getByCy('Create Purchase Order').click();
    cy.get('[data-test=submitIsVisible]', { timeout: 10000 }).should('be.visible');




    // cy.get('[data-test=submitIsVisible]', { timeout: 10000 }).should('be.visible');

    // cy.visit('/po/list');


    // cy.getByCy('global-search').click();
    // cy.get('#barcodeSearchId').type('8486qjgf');
    // cy.get('.MuiSvgIcon-root.text-white').click();
  });
});
