import React, { useState } from 'react';
import { makeStyles } from 'tss-react/mui';

import LkChip from 'components/MaterialUi/LkChip';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import CancelIcon from '@mui/icons-material/Cancel';
import { convertDatesToString } from 'utils/helpers';

const useStyles = makeStyles()(() => ({
  container: {
    marginLeft: '20px',
    display: 'flex',
    alignItems: 'center',
    flexWrap: 'wrap'
  },
  chip: {
    fontWeight: 400,
    fontSize: 13,
    lineHeight: '18px',
    letterSpacing: '0.16px',
    color: '#3C3C3C',
    marginRight: 10,
    marginTop: 5,
    minHeight: 32
  },
  showMoreButton: {
    fontWeight: 400,
    fontSize: 11,
    lineHeight: '120%',
    letterSpacing: '0.4px',
    color: 'rgba(60, 60, 60, 0.54)'
  }
}));

const FilterChipsV2 = ({ filters, deleteData, className }) => {
  const { classes, cx } = useStyles();
  const [showAll, setShowAll] = useState(false);

  const renderCard = ({ key, keyValue, values, type, disableDelete = false, value: tempValue }) => {
    let value;
    let dataCy = `${keyValue}-${values}`;
    switch (type) {
      case 'DATE_RANGE': {
        if (values.from !== '') {
          if (values.from.includes(':')) {
            value = `${keyValue} (${values.from} - ${values.to})`;
          } else {
            const date_1 = new Date(values.from.split(' ')[0]);
            const date_2 = new Date(values.to.split(' ')[0]);
            value = `${keyValue} (${convertDatesToString(date_1, date_2)})`;
          }
        } else {
          value = `${keyValue} (ALL_PERIOD)`;
        }
        break;
      }

      case 'RANGE': {
        if (values.startValue !== '' && values.endValue !== '') {
          value = `${values.startValue} <= ${keyValue} <= ${values.endValue}`;
        }
        break;
      }

      default: {
        if (!values && !keyValue) {
          value = `${key}: ${tempValue}`;
          dataCy = `${key}:${tempValue}`;
        } else if (typeof values === 'string') {
          value = `${keyValue} (${values})`;
        } else {
          value = `${keyValue} (${values.join(', ')})`;
        }
        break;
      }
    }

    return (
      <LkChip
        data-cy={dataCy}
        key={value}
        label={value}
        type="filter"
        id="deleteFilterBtn"
        deleteIcon={
          disableDelete ? null : (
            <CancelIcon
              data-cy={`${keyValue || key}-filter-delete-icon`}
              style={{ color: 'rgba(60, 60, 60, 0.23)' }}
            />
          )
        }
        onDelete={disableDelete ? null : () => deleteData(key, values || tempValue)}
        className={classes.chip}
        size="medium"
      />
    );
  };

  const initialChildren = filters?.slice(0, 4);
  const moreChildren = filters?.slice(4, filters.length);
  const renderShowAll = () => (
    <Button onClick={() => setShowAll(true)} className={classes.showMoreButton}>
      +{moreChildren.length} More
    </Button>
  );

  return (
    <Box className={cx(classes.container, className)}>
      {initialChildren.map(renderCard)}
      {moreChildren.length > 0 && (showAll ? moreChildren.map(renderCard) : renderShowAll())}
    </Box>
  );
};

export default FilterChipsV2;
