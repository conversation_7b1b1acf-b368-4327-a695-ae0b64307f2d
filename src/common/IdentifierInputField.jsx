import React from 'react';
import Box from '@mui/material/Box';
import LkInput from 'components/MaterialUi/LkInput';
import Phases from './Phases';

const classes = {
  topLeft: {
    fontSize: '14px',
    marginLeft: '20px'
  },
  topInput: {
    display: 'block',
    '& > div': {
      display: 'block'
    }
  }
};

const IdentifierInputField = ({ value, labels }) => (
  <Box sx={{ ...classes.topLeft }}>
    <Phases labels={labels} />
    <LkInput variant="outlined" sx={{ ...classes.topInput }} value={value} disabled />
  </Box>
);

export default IdentifierInputField;
