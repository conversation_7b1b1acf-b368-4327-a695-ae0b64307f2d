import React, { memo, useCallback } from 'react';
import { Box, Tab, Tabs } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import { useNavigate, useParams } from 'react-router-dom';
import UserList from '../UserList/UserList';
import UserRole from '../UserRole/UserRole';

const useStyles = makeStyles()(() => ({
  container: {
    position: 'relative',
    width: '100%'
  },
  customTabRoot: {
    color: '#00B9C6'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  },
  divider: {
    borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
  },
  userContainer: {
    height: '100%',
    backgroundColor: '#fff',
    marginBottom: 12,
    borderRadius: 8,
    padding: '16px'
  }
}));

const TabPanel = ({ children, value, index }) => value === index && children;

const UserTabs = () => {
  const { classes } = useStyles();
  const params = useParams();
  const navigate = useNavigate();
  const roles = params?.roles;

  const handleChange = useCallback((_event, userRoleOrList) => {
    navigate(`/usermanagement/${userRoleOrList}`);
  }, []);

  return (
    <Box className={classes.container}>
      <Box className={classes.divider}>
        <Tabs
          value={roles}
          aria-label="User Tabs"
          onChange={handleChange}
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          <Tab value="roles" label="Roles" />
          <Tab value="lists" label="User List" />
        </Tabs>
      </Box>
      <TabPanel value={roles} index="roles">
        <UserRole classes={classes.userContainer} />
      </TabPanel>
      <TabPanel value={roles} index="lists">
        <UserList classes={classes.userContainer} />
      </TabPanel>
    </Box>
  );
};

export default memo(UserTabs);
