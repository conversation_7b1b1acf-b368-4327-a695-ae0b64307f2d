import React, { useState, useEffect } from 'react';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Button from '@mui/material/Button';
import Spinner from 'components/Spinner/Spinner';
import LkModal from '../Modal/Modal';
import { roundUptoDigits } from '../../utils/helpers';

const CloseInvoiceModal = (props) => {
  const { open, close, closeInvoiceSubmit, closeInvoiceLoading, currency } = props;
  const [checkedItems, setCheckedItems] = useState(false);
  const [data, setData] = useState({ totalValue: 0, totalQuantity: 0, totalValue_wt: 0 });

  const handleChangeCheckedItems = () => {
    setCheckedItems(!checkedItems);
  };

  useEffect(() => {
    let totalQuantity = 0;
    let totalValue = 0;
    let totalValue_wt = 0;
    const { invoiceData } = props;
    invoiceData.invoiceData.forEach(
      ({ quantity, price_with_taxes, vendor_unit_cost_price, active }) => {
        if (active) {
          totalQuantity += Number(quantity) || 0;
          totalValue += Number(price_with_taxes) || 0;
          totalValue_wt += Number(quantity) * Number(vendor_unit_cost_price) || 0;
        }
      }
    );
    setData({ totalQuantity, totalValue, totalValue_wt });
  }, []);

  return (
    <LkModal
      handleClose={() => close()}
      modalWidth={520}
      title="Close Invoice?"
      subTitle="Please check the following things. 
      No changes will be allowed in Vendor Invoice after completion. Are you sure?"
      open={open}
      showActionButton={false}
    >
      <div className="display-flex align-items-center justify-content-center">
        <div className="mr-t25 mr-b25 bg-f5 pd-l30 pd-r30 border-radius-6 display-flex">
          <div className="fs12 text-center pd-t5 pd-r30 pd-b8 border-right-ee">
            <div className="text-66">Total Quantity</div>
            <div className="fw-bold text-33 mr-t10">{data.totalQuantity}</div>
          </div>
          <div className="fs12 text-center pd-l30 pd-t5 pd-b8">
            <div className="text-66">Total Invoice value</div>
            <div className="fw-bold text-33 mr-t10">
              {currency}
              {roundUptoDigits(data.totalValue_wt, 2)}
            </div>
          </div>
          <div className="fs12 text-center pd-l30 pd-t5 pd-b8">
            <div className="text-66">Total Invoice value(with tax)</div>
            <div className="fw-bold text-33 mr-t10">
              {currency}
              {roundUptoDigits(data.totalValue, 2)}
            </div>
          </div>
        </div>
      </div>
      <div className="mr-b40 text-center">
        <FormControlLabel
          control={
            <Checkbox
              checked={checkedItems}
              name="checkedItems"
              onChange={handleChangeCheckedItems}
              color="primary"
            />
          }
          label="Warning, this action cannot be undone"
        />
      </div>
      <div className="display-flex justify-content-center mr-b40">
        <div className="mr-r5">
          <Button
            onClick={() => close()}
            style={{ borderRadius: 8 }}
            variant="outlined"
            color="primary"
          >
            CANCEL
          </Button>
        </div>
        <div className="mr-l5">
          {closeInvoiceLoading && (
            <Button
              disabled={closeInvoiceLoading}
              style={{ borderRadius: 8 }}
              variant="contained"
              color="primary"
            >
              <Spinner />
            </Button>
          )}
          {!closeInvoiceLoading && (
            <Button
              onClick={() => closeInvoiceSubmit()}
              disabled={!checkedItems}
              style={{ borderRadius: 8 }}
              variant="contained"
              color="primary"
            >
              YES, CLOSE
            </Button>
          )}
        </div>
      </div>
    </LkModal>
  );
};

export default CloseInvoiceModal;
