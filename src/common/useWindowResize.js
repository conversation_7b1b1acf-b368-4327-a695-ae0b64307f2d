import { useEffect, useState } from 'react';

const useWindowResize = (initialHeight, bottomMargin = 250) => {
  const [height, setHeight] = useState(initialHeight);

  const handleScroll = () => {
    setHeight(window.innerHeight - bottomMargin);
  };

  useEffect(() => {
    window.addEventListener('resize', handleScroll);
    return () => window.removeEventListener('resize', handleScroll);
  }, [handleScroll]);

  return [height];
};

export default useWindowResize;
