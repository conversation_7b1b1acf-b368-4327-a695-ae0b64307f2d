import React from 'react'
import { makeStyles } from 'tss-react/mui';
import GrnTable from './GrnTable'


const styles = makeStyles()((theme) => ({
  button: {
    borderRadius: theme.spacing(1),
    marginLeft: theme.spacing(2)
  }
}));

const GRNTab = ({ handleChange, grnCode, mainGrnSearch }) => {
  const { classes } = styles();

  return (
    <div className={classes.root}>
      <GrnTable grnCode={grnCode} mainGrnSearch={mainGrnSearch} handleChange={handleChange} />
    </div>
  );
};

export default React.memo(GRNTab);
