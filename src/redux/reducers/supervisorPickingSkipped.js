import * as action from '../actionTypes/supervisorPickingSkipped';

export const TASK_STATUS = {
  IN_PROGRESS: 'IN PROGRESS',
  PROCESSED: 'PROCESSED'
};

const errorFormat = (error) => ({
  heading: 'Error!',
  subHeading: error?.response?.data?.meta?.displayMessage
    ? error.response.data.meta.displayMessage
    : 'Something went wrong',
  severity: 'error'
});

const successFormat = (message, secondaryMessage = '') => ({
  heading: message,
  subHeading: secondaryMessage,
  severity: 'success'
});

const updateListData = (listData, ids, status = null) =>
  listData.map((eachData) => {
    if (
      (Array.isArray(ids) && ids.includes(eachData.pickingDetail.id)) ||
      ids === eachData.pickingDetail.id
    ) {
      return {
        ...eachData,
        taskStatus: status
      };
    }
    return eachData;
  });

const initialState = {
  categories: {
    isLoading: false,
    data: []
  },
  users: {
    isLoading: false,
    data: []
  },
  skipped: {
    isLoading: false,
    data: [],
    totalRecords: 0
  },
  hold: {
    isLoading: false,
    data: [],
    totalRecords: 0
  },
  stockOut: 0,
  notification: null,
  exportSkippedData: {
    isLoading: false,
    data: []
  },
  uploadFastZonePid: {
    isLoading: false,
    isSuccess: false
  },
  asrsToManual: {
    isLoading: false,
    isSuccess: false
  },
  bulkUploadDiscardedShipment: {
    isLoading: false,
    isSuccess: false,
    isError: false,
    failedRecords: []
  }
};

const handleLoad = (state, key, reset, initialStateValues) => ({
  ...state,
  [key]: {
    ...(reset ? initialStateValues[key] : state[key]),
    isLoading: true
  }
});

const supervisorPickingSkipped = (state = initialState, { type, payload }) => {
  switch (type) {
    case action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_LOAD:
      return handleLoad(state, 'skipped', payload.reset, initialState);
    case action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_SUCCESS:
      return {
        ...state,
        skipped: {
          ...state.skipped,
          isLoading: false,
          data: [...state.skipped.data, ...payload.data.supervisorPanelDetail],
          totalRecords: payload.data.total
        }
      };
    case action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_RESET:
    case action.GET_SUPERVISOR_PICKING_HOLD_DATA_RESET:
    case action.GET_EXPORT_SKIPPED_DATA__FAIL:
    case action.GET_EXPORT_SKIPPED_DATA__RESET:
    case action.GET_UPLOAD_PID_DATA_RESET:
    case action.GET_UPLOAD_PID_DATA_FAIL:
    case action.MOVE_FROM_ASRS_TO_MANUAL_RESET:
    case action.BULK_UPLOAD_DISCARDED_SHIPMENT_RESET: {
      let key;
      switch (type) {
        case action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_RESET:
          key = 'skipped';
          break;
        case action.GET_SUPERVISOR_PICKING_HOLD_DATA_RESET:
          key = 'hold';
          break;
        case action.GET_EXPORT_SKIPPED_DATA__FAIL:
        case action.GET_EXPORT_SKIPPED_DATA__RESET:
          key = 'exportSkippedData';
          break;
        case action.GET_UPLOAD_PID_DATA_FAIL:
        case action.GET_UPLOAD_PID_DATA_RESET:
          key = 'uploadFastZonePid';
          break;
        case action.MOVE_FROM_ASRS_TO_MANUAL_RESET:
          key = 'asrsToManual';
          break;
        case action.BULK_UPLOAD_DISCARDED_SHIPMENT_RESET:
          key = 'bulkUploadDiscardedShipment';
          break;
        default:
          break;
      }
      return {
        ...state,
        [key]: initialState[key]
      };
    }

    case action.GET_SUPERVISOR_PICKING_HOLD_DATA_LOAD:
      return handleLoad(state, 'hold', payload.reset, initialState);
    case action.GET_SUPERVISOR_PICKING_HOLD_DATA_SUCCESS:
      return {
        ...state,
        hold: {
          ...state.hold,
          isLoading: false,
          data: payload.data,
          totalRecords: payload.totalRecords
        }
      };

    case action.GET_SUPERVISOR_PICKING_USERS_LOAD:
    case action.GET_SUPERVISOR_PICKING_CATAGORIES_LOAD:
      return {
        ...state,
        [type === action.GET_SUPERVISOR_PICKING_USERS_LOAD ? 'users' : 'categories']: {
          ...state[type === action.GET_SUPERVISOR_PICKING_USERS_LOAD ? 'users' : 'categories'],
          isLoading: true
        }
      };

    case action.GET_SUPERVISOR_PICKING_USERS_SUCCESS:
    case action.GET_SUPERVISOR_PICKING_CATAGORIES_SUCCESS:
      return {
        ...state,
        [type === action.GET_SUPERVISOR_PICKING_USERS_SUCCESS ? 'users' : 'categories']: {
          ...state[type === action.GET_SUPERVISOR_PICKING_USERS_SUCCESS ? 'users' : 'categories'],
          isLoading: false,
          data: payload.data
        }
      };

    case action.SUPERVISOR_PICKING_HOLD_REQUEST:
      return {
        ...state,
        skipped: {
          ...state.skipped,
          data: updateListData(state.skipped.data, payload.selectedIds, TASK_STATUS.IN_PROGRESS)
        }
      };

    case action.SUPERVISOR_PICKING_HOLD_SUCCESS:
      return {
        ...state,
        notification: successFormat('Success! Refresh to update', 'Moved to hold')
      };
    case action.SUPERVISOR_PICKING_HOLD_FAIL:
      return {
        ...state,
        notification: errorFormat(payload.error),
        skipped: {
          ...state.skipped,
          data: updateListData(state.skipped.data, payload.selectedIds, null)
        }
      };
    case action.SUPERVISOR_PICKING_UN_HOLD_REQUEST:
      return {
        ...state,
        hold: {
          ...state.hold,
          data: updateListData(state.hold.data, payload.selectedIds, TASK_STATUS.IN_PROGRESS)
        }
      };
    case action.SUPERVISOR_PICKING_UN_HOLD_SUCCESS:
      return {
        ...state,
        notification: successFormat('Success! Refresh to update', 'Moved to un hold')
      };
    case action.SUPERVISOR_PICKING_UN_HOLD_FAIL:
      return {
        ...state,
        notification: errorFormat(payload.error),
        hold: {
          ...state.hold,
          data: updateListData(state.hold.data, payload.selectedIds, null)
        }
      };
    case action.SUPERVISOR_PICKING_NOT_FOUND_REQUEST:
    case action.SUPERVISOR_PICKING_ASSIGN_REQUEST:
    case action.SUPERVISOR_PICKING_SCAN_REQUEST: {
      const ids =
        type === action.SUPERVISOR_PICKING_SCAN_REQUEST ? payload.id : payload.selectedIds;
      if (payload.onHold) {
        return {
          ...state,
          hold: {
            ...state.hold,
            data: updateListData(state.hold.data, ids, TASK_STATUS.IN_PROGRESS)
          }
        };
      }

      return {
        ...state,
        skipped: {
          ...state.skipped,
          data: updateListData(state.skipped.data, ids, TASK_STATUS.IN_PROGRESS)
        }
      };
    }
    case action.SUPERVISOR_PICKING_NOT_FOUND_SUCCESS:
      if (payload.onHold) {
        return {
          ...state,
          hold: {
            ...state.hold,
            data: updateListData(state.hold.data, payload.selectedIds, TASK_STATUS.PROCESSED)
          },
          notification: successFormat('Success! Refresh to update', payload.data.data)
        };
      }

      return {
        ...state,
        skipped: {
          ...state.skipped,
          data: updateListData(state.skipped.data, payload.selectedIds, TASK_STATUS.PROCESSED)
        },
        notification: successFormat(
          'Success! Refresh to update',
          'Item Successfully marked as not found '
        )
      };
    case action.SUPERVISOR_PICKING_NOT_FOUND_FAIL:
    case action.SUPERVISOR_PICKING_ASSIGN_FAIL:
    case action.SUPERVISOR_PICKING_SCAN_FAIL: {
      const ids = type === action.SUPERVISOR_PICKING_SCAN_FAIL ? payload.id : payload.selectedIds;
      const dataKey = payload.onHold ? 'hold' : 'skipped';
      return {
        ...state,
        notification: errorFormat(payload),
        [dataKey]: {
          ...state[dataKey],
          data: updateListData(state[dataKey].data, ids, null)
        }
      };
    }
    case action.SUPERVISOR_PICKING_ASSIGN_SUCCESS:
      if (payload.onHold) {
        return {
          ...state,
          hold: {
            ...state.hold,
            data: updateListData(state.hold.data, payload.selectedIds, TASK_STATUS.PROCESSED)
          },
          notification: successFormat(
            'Success! Refresh to update',
            'Skipped item summery created successfully'
          )
        };
      }

      return {
        ...state,
        skipped: {
          ...state.skipped,
          data: updateListData(state.skipped.data, payload.selectedIds, TASK_STATUS.PROCESSED)
        },
        notification: successFormat(
          'Success! Refresh to update',
          'Skipped item summery created successfully'
        )
      };
    case action.SUPERVISOR_PICKING_SCAN_SUCCESS: {
      if (payload.onHold) {
        return {
          ...state,
          hold: {
            ...state.hold,
            data: updateListData(state.hold.data, payload.id, TASK_STATUS.PROCESSED)
          },
          stockOut: state.stockOut + 1,
          notification: successFormat(
            'Success! Refresh to update',
            'Skipped Item successfully stocked out'
          )
        };
      }

      return {
        ...state,
        skipped: {
          ...state.skipped,
          data: updateListData(state.skipped.data, payload.id, TASK_STATUS.PROCESSED)
        },
        stockOut: state.stockOut + 1,
        notification: successFormat(
          'Success! Refresh to update',
          'Skipped Item successfully stocked out'
        )
      };
    }

    case action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_FAIL:
    case action.GET_SUPERVISOR_PICKING_HOLD_DATA_FAIL: {
      const key = type === action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_FAIL ? 'skipped' : 'hold';
      return {
        ...state,
        [key]: {
          ...initialState[key],
          isLoading: false
        },
        notification: errorFormat(payload)
      };
    }
    case action.GET_SUPERVISOR_PICKING_USERS_FAIL:
    case action.GET_SUPERVISOR_PICKING_CATAGORIES_FAIL:
      return {
        ...state,
        notification: errorFormat(payload)
      };
    case action.GET_EXPORT_SKIPPED_DATA_LOAD:
      return {
        ...state,
        exportSkippedData: {
          ...state.exportSkippedData,
          isLoading: true,
          data: []
        }
      };
    case action.GET_EXPORT_SKIPPED_DATA__SUCCESS:
      return {
        ...state,
        exportSkippedData: {
          ...state.exportSkippedData,
          isLoading: false,
          data: payload
        }
      };
    case action.GET_UPLOAD_PID_DATA_LOAD:
      return {
        ...state,
        uploadFastZonePid: {
          ...state.uploadFastZonePid,
          isLoading: true,
          isSuccess: false
        }
      };
    case action.GET_UPLOAD_PID_DATA_SUCCESS:
      return {
        ...state,
        uploadFastZonePid: {
          ...state.uploadFastZonePid,
          isLoading: false,
          isSuccess: payload
        }
      };
    case action.MOVE_FROM_ASRS_TO_MANUAL_LOAD:
      return {
        ...state,
        asrsToManual: {
          ...state.asrsToManual,
          isLoading: true,
          isSuccess: false
        }
      };
    case action.MOVE_FROM_ASRS_TO_MANUAL_SUCCESS:
      return {
        ...state,
        asrsToManual: {
          ...state.asrsToManual,
          isLoading: false,
          isSuccess: true
        }
      };
    case action.MOVE_FROM_ASRS_TO_MANUAL_FAIL:
      return {
        ...state,
        asrsToManual: {
          ...state.asrsToManual,
          isLoading: false,
          isSuccess: false
        }
      };
    case action.BULK_UPLOAD_DISCARDED_SHIPMENT_LOAD:
      return {
        ...state,
        bulkUploadDiscardedShipment: {
          ...state.bulkUploadDiscardedShipment,
          isLoading: true,
          isSuccess: false
        }
      };
    case action.BULK_UPLOAD_DISCARDED_SHIPMENT_SUCCESS:
      return {
        ...state,
        bulkUploadDiscardedShipment: {
          ...state.bulkUploadDiscardedShipment,
          isLoading: false,
          isSuccess: true,
          isError: false,
          data: payload
        }
      };
    case action.BULK_UPLOAD_DISCARDED_SHIPMENT_FAIL:
      return {
        ...state,
        bulkUploadDiscardedShipment: {
          ...state.bulkUploadDiscardedShipment,
          isLoading: false,
          isSuccess: false,
          isError: true,
          failedRecords: payload || []
        }
      };
    default:
      return state;
  }
};

export default supervisorPickingSkipped;
