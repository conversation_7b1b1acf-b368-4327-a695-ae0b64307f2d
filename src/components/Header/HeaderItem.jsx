import React from 'react';
import { Box } from '@mui/material';

import { makeStyles } from 'tss-react/mui';
import Skeleton from '@mui/material/Skeleton';

const useStyles = makeStyles()(() => ({
  title: {
    fontSize: 12,
    lineHeight: '14px',
    color: '#333333',
    marginBottom: 8
  },
  value: {
    fontWeight: 'bold',
    fontSize: 16,
    lineHeight: '19px'
  },
  valueImage: {
    height: 'auto',
    width: 24
  }
}));

const HeaderItem = ({ title, value, isLoading = false, image = null, defaultValue = '-' }) => {
  const { classes } = useStyles();

  const renderContent = () => {
    if (isLoading) {
      return <Skeleton variant="rectangular" animation="wave" />;
    }
    return (
      <Box
        className={classes.value}
        display="flex"
        justifyContent="space-around"
        alignItems="center"
      >
        <Skeleton variant="rectangular" animation="wave" />
        {image && <img className={classes.valueImage} src={image} alt={value} />}
        {value || defaultValue}
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column">
      <Box className={classes.title}>{title}</Box>
      {renderContent()}
    </Box>
  );
};

export default React.memo(HeaderItem);
