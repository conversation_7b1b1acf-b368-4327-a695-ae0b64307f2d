// eslint-disable-next-line import/no-extraneous-dependencies
import config from '../../src/config';

describe('Reactive Transfer', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
    cy.navigate(['main-navigation', 'orderProcessing', 'reactiveTransfer']);
  });

  afterEach(() => {
    cy.logout();
  });

  it('Visits the Reactive Transfer Page from app bar and table is populated', () => {
    cy.fixture('reactiveTransfer/reactiveTransferHeader').as('headers');

    cy.intercept('POST', config.reactiveTransfer.list).as('getreactiveTransferList');

    cy.get('@headers').then((headers) => {
      headers.forEach((header, index) => {
        cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
      });
    });

    cy.wait('@getreactiveTransferList').its('response.statusCode').should('eq', 200);

    cy.wait('@getreactiveTransferList').then(({ response }) => {
      const apiData = response?.body?.data?.transferList; // Capturing the API response body
      if (apiData.length) {
        cy.getByCy(`table-cell-${0}-0`).should('exist');

        // Your assertions for this item's data
        cy.getByCy(`table-cell-${0}-0`).should('contain.text', apiData[0].incrementId);
      }
    });
  });
});
