import React from 'react';
import { useParams } from 'react-router-dom';

import { Topbar } from 'layouts/Main/components';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

import TopbarRightElement from '../../layouts/Main/components/Topbar/TopbarRightElement';
import ScannerToPrintShipment from './ScannerToPrintShipment';


const ShipmentPrinting = () => {
  const { type = 'autobag' } = useParams();
  const shipmentPrintingLocal = useSelector(
    (state) => state[LOCALISATION].localeData.SHIPMENTPRINTING
  );
  
  const TITLE = {
    carton: shipmentPrintingLocal.MONOCARTON,
    autobag: shipmentPrintingLocal.AUTOBAGPRINTING,
    packslip: shipmentPrintingLocal.PACK_SLIP
  }
  
  return (
    <div>
      <Topbar>
        <div className="fw-500">{TITLE[type]} </div>
        <TopbarRightElement />
      </Topbar>
      <ScannerToPrintShipment type={type} />
    </div>
  );
};

export default ShipmentPrinting;
