.linklayoutschema {
    .facility-info {
        background: #fafafa;
        border: 1px solid rgba(60, 60, 60, 0.23);
        border-radius: 8px;
    }
    .text-60 {
        color: #606060;
    }
    .text-3C {
        color: #3c3c3c;
    }
    .border-de {
        border-bottom: 1px solid #dedede;
    }
    .link-image-container {
        display: grid;
        place-items: center;
        .link-image {
            background: #ffffff;
            border: 1px solid #f0f0f0;
            border-radius: 50%;
            width: 31px;
        }        
    }
    .line {
        top: 10px;
        left: 0px;
        right: 0px;
    }
}
