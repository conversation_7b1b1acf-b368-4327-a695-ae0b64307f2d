import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import IconButton from '@mui/material/IconButton';

import { LOCALISATION } from 'redux/reducers/localisation';
import Phases from 'common/Phases';
import { RTV_BARCODE_SCAN } from 'redux/reducers/RTVbarcodeScan.slice';
import OutOfComponent from './OutOfComponent';

const classes = {
  container: {
    padding: '10px 20px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    height: '90px',
    background: '#FFFFFF',
    boxSizing: 'border-box'
  },
  button: {
    width: '40px',
    height: '40px'
  },

  left: {
    display: 'flex',
    flexDirection: 'row',
    gap: '30px',
    alignItems: 'center',
  },
  right: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: '16px',
    textAlign: 'center'
  },
  scanCompleteBtn: {
    borderRadius: '4px',
    width: '234px',
    height: '42px'
  }
};

const RTVScanHeader = ({ transferCode }) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.RTV_GATEPASS);
  const {
    pidCount,
    barcodeScan: { savedScannedBarcodes }
  } = useSelector((state) => state[RTV_BARCODE_SCAN]);
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(`/RTVgatepass/details/${transferCode}`);
  };

  const scannedItemsCount = useMemo(
    () => savedScannedBarcodes.filter(({ status }) => status === 'success')?.length,
    [savedScannedBarcodes]
  );

  return (
    <Box sx={{ ...classes.container }} data-cy="rtv-scan-header">
      <Box sx={{ ...classes.left }}>
        <IconButton
          sx={{ ...classes.button }}
          onClick={handleBack}
          size="large"
          data-cy="rtv-scan-handle-back"
        >
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ ...classes.meta }}>
          <Phases labels={[CONTENT.SEND, CONTENT.GATEPASS_ID, CONTENT.SCANNING]} />
          <TextField
            value={transferCode}
            onChange={null}
            fullWidth
            variant="outlined"
            size="small"
            disabled
          />
        </Box>
      </Box>
      <Box sx={{ ...classes.right }}>
        <OutOfComponent title={CONTENT.PID_SCANNED} count={pidCount?.length || 0} />
        <OutOfComponent title={CONTENT.TOTAL_ITEMS_ADDED} count={scannedItemsCount || 0} />

        <Button
          startIcon={<CheckCircleIcon />}
          sx={{ ...classes.scanCompleteBtn }}
          color="primary"
          variant="contained"
          onClick={handleBack}
        >
          {CONTENT.SCANNING_COMPLETE}
        </Button>
      </Box>
    </Box>
  );
};

export default RTVScanHeader;
