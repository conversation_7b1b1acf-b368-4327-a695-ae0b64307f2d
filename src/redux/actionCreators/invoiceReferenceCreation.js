import * as action from '../actionTypes/invoiceReferenceCreation';

export function searchVendorList(data) {
  return {
    type: action.SEARCH_VENDOR_LIST,
    payload: data
  };
}

export function searchVendorListSuccess(data) {
  return {
    type: action.SEARCH_VENDOR_LIST_SUCCESS,
    payload: data
  };
}

export function searchVendorListFail(data) {
  return {
    type: action.SEARCH_VENDOR_LIST_FAIL,
    payload: data
  };
}

export function searchVendorListReset(data) {
  return {
    type: action.SEARCH_VENDOR_LIST_RESET,
    payload: data
  };
}

export function getApprovedPoList(data) {
  return {
    type: action.GET_APPROVED_PO_LIST,
    payload: data
  };
}

export function getApprovedPoListLoad() {
  return {
    type: action.GET_APPROVED_PO_LIST_LOAD
  };
}

export function getApprovedPoListFail(data) {
  return {
    type: action.GET_APPROVED_PO_LIST_FAIL,
    payload: data
  };
}

export function getApprovedPoListSuccess(data) {
  return {
    type: action.GET_APPROVED_PO_LIST_SUCCESS,
    payload: data
  };
}

export function searchVendorInvoiceNumber(data) {
  return {
    type: action.SEARCH_VENDOR_INVOICE_NUMBER_LIST,
    payload: data
  };
}

export function searchVendorInvoiceNumberSuccess(data) {
  return {
    type: action.SEARCH_VENDOR_INVOICE_NUMBER_LIST_SUCCESS,
    payload: data
  };
}

export function searchVendorInvoiceNumberFail(data) {
  return {
    type: action.SEARCH_VENDOR_INVOICE_NUMBER_LIST_FAIL,
    payload: data
  };
}

export function getItemsVendorInvoiceCreation(data) {
  return {
    type: action.GET_ITEMS_VENDOR_INVOICE_CREATION,
    payload: data
  };
}

export function getItemsVendorInvoiceCreationLoad() {
  return {
    type: action.GET_ITEMS_VENDOR_INVOICE_CREATION_LOAD
  };
}

export function getItemsVendorInvoiceCreationFail(data) {
  return {
    type: action.GET_ITEMS_VENDOR_INVOICE_CREATION_FAIL,
    payload: data
  };
}

export function getItemsVendorInvoiceCreationSuccess(data) {
  return {
    type: action.GET_ITEMS_VENDOR_INVOICE_CREATION_SUCCESS,
    payload: data
  };
}

export function getItemsVendorInvoiceCreationReset(data) {
  return {
    type: action.GET_ITEMS_VENDOR_INVOICE_CREATION_RESET,
    payload: data
  };
}

export function createVendorInvoice(data) {
  return {
    type: action.CREATE_VENDOR_INVOICE,
    payload: data
  };
}

export function createVendorInvoiceLoad(data) {
  return {
    type: action.CREATE_VENDOR_INVOICE_LOAD,
    payload: data
  };
}

export function createVendorInvoiceFail(data) {
  return {
    type: action.CREATE_VENDOR_INVOICE_FAIL,
    payload: data
  };
}

export function createVendorInvoiceSuccess(data) {
  return {
    type: action.CREATE_VENDOR_INVOICE_SUCCESS,
    payload: data
  };
}

export function getVendorInvoiceCreationStatus(data) {
  return {
    type: action.GET_VENDOR_INVOICE_CREATION_STATUS,
    payload: data
  };
}

export function getVendorInvoiceCreationStatusLoad(data) {
  return {
    type: action.GET_VENDOR_INVOICE_CREATION_STATUS_LOAD,
    payload: data
  };
}

export function getVendorInvoiceCreationStatusFail(data) {
  return {
    type: action.GET_ITEMS_VENDOR_INVOICE_CREATION_FAIL,
    payload: data
  };
}

export function getVendorInvoiceCreationStatusSuccess(data) {
  return {
    type: action.GET_VENDOR_INVOICE_CREATION_STATUS_SUCCESS,
    payload: data
  };
}

export function getVendorInvoiceCreationStatusReset(data) {
  return {
    type: action.GET_VENDOR_INVOICE_CREATION_STATUS_RESET,
    payload: data
  };
}

export function resetInvoiceReferenceNonRepeatableData() {
  return {
    type: action.RESET_INVOICE_REFERENCE_NON_REPEATABLE_DATA
  };
}

export function resetVendorInvoiceCreationData() {
  return {
    type: action.RESET_VENDOR_INVOICE_CREATION_DATA
  };
}

// close invoice
export function closeInvoiceLoad(data) {
  return { type: action.CLOSE_INVOICE_LOAD, payload: data };
}

export function closeInvoiceSuccess(data) {
  return { type: action.CLOSE_INVOICE_SUCCESS, payload: data };
}

export function closeInvoiceFail(data) {
  return { type: action.CLOSE_INVOICE_FAIL, payload: data };
}

export function closeInvoiceReset(data) {
  return { type: action.CLOSE_INVOICE_RESET, payload: data };
}

// import invoice
export function importInvoiceLoad(data) {
  return { type: action.IMPORT_INVOICE_LOAD, payload: data };
}

export function importInvoiceSuccess(data) {
  return { type: action.IMPORT_INVOICE_SUCCESS, payload: data };
}

export function importInvoiceFail(data) {
  return { type: action.IMPORT_INVOICE_FAIL, payload: data };
}

export function importInvoiceReset(data) {
  return { type: action.IMPORT_INVOICE_RESET, payload: data };
}

// export invoice
export function exportInvoiceLoad(data) {
  return { type: action.EXPORT_INVOICE_LOAD, payload: data };
}

export function exportInvoiceSuccess(data) {
  return { type: action.EXPORT_INVOICE_SUCCESS, payload: data };
}

export function exportInvoiceFail(data) {
  return { type: action.EXPORT_INVOICE_FAIL, payload: data };
}

export function exportInvoiceReset(data) {
  return { type: action.EXPORT_INVOICE_RESET, payload: data };
}

// download error report(import invoice)
export function downloadErrorInvoiceLoad(data) {
  return { type: action.DOWNLOAD_INVOICE_ERROR_REPORT_LOAD, payload: data };
}

export function downloadErrorInvoiceSuccess(data) {
  return { type: action.DOWNLOAD_INVOICE_ERROR_REPORT_SUCCESS, payload: data };
}

export function downloadErrorInvoiceFail(data) {
  return { type: action.DOWNLOAD_INVOICE_ERROR_REPORT_FAIL, payload: data };
}

export function downloadErrorInvoiceReset(data) {
  return { type: action.DOWNLOAD_INVOICE_ERROR_REPORT_RESET, payload: data };
}

// Reset invoiceReferenceCreation

export function resetInvoiceReferenceCreation(data) {
  return { type: action.RESET_INVOICE_REFERENCE_CREATION, payload: data };
}

// get Pid Info
export function getPidInfoLoad(data) {
  return { type: action.GET_PID_INFO_LOAD, payload: data };
}

export function getPidInfoSuccess(data) {
  return { type: action.GET_PID_INFO_SUCCESS, payload: data };
}

export function getPidInfoFail(data) {
  return { type: action.GET_PID_INFO_FAIL, payload: data };
}

export function getPidInfoReset(data) {
  return { type: action.GET_PID_INFO_RESET, payload: data };
}
