/* eslint-disable import/no-extraneous-dependencies */
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import basicSsl from '@vitejs/plugin-basic-ssl';

export default ({ mode }) => {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = dirname(__filename);

  loadEnv(mode, process.cwd(), '');

  let proxy = null;
  if (process.env.NODE_ENV === 'development') {
    proxy = {
      '/local-ui': {
        target: 'https://ui.nexs.preprod.lenskart.com',
        changeOrigin: true,
        rewrite: (urlPath) => urlPath.replace(/^\/local-ui/, '')
      }
    };
  }

  return defineConfig({
    base: process.env.PUBLIC_URL,
    plugins: [react(), basicSsl()],
    resolve: {
      alias: {
        components: path.resolve(__dirname, 'src/components'),
        layouts: path.resolve(__dirname, 'src/layouts'),
        theme: path.resolve(__dirname, 'src/theme'),
        apis: path.resolve(__dirname, 'src/apis'),
        common: path.resolve(__dirname, 'src/common'),
        utils: path.resolve(__dirname, 'src/utils'),
        config: path.resolve(__dirname, 'src/config'),
        views: path.resolve(__dirname, 'src/views'),
        constant: path.resolve(__dirname, 'src/constant'),
        i18n: path.resolve(__dirname, 'src/i18n'),
        helpers: path.resolve(__dirname, 'src/helpers'),
        selectors: path.resolve(__dirname, 'src/selectors'),
        'redux/reducers': path.resolve(__dirname, 'src/redux/reducers'),
        'redux/sagas': path.resolve(__dirname, 'src/redux/sagas'),
        'redux/actionCreators': path.resolve(__dirname, 'src/redux/actionCreators'),
        'redux/actionTypes': path.resolve(__dirname, 'src/redux/actionTypes'),
        model: path.resolve(__dirname, 'src/model'),
        test: path.resolve(__dirname, 'src/test'),
        'redux/redux-store': path.resolve(__dirname, 'src/redux/redux-store')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'] // tried diffrent option ut it's not working
          // please refer :  https://vite.dev/config/shared-options.html#css-preprocessoroptions
        }
      }
    },
    server: {
      open: true,
      strictPort: 3000,
      port: 3000,
      https: true,
      proxy,
      hmr: {
        port: 3000,
        host: 'ui.nexs.local.lenskart.com',
        protocol: 'wss'
      }
    },
    build: {
      outDir: 'build',
      sourcemap: 'hidden'
    },
    esbuild: {
      loader: 'jsx',
      include: /\.*\.jsx?$/,
      exclude: [/node_modules/]
    },
    optimizeDeps: {
      include: ['@mui/material/Tooltip', '@emotion/styled', '@mui/x-tree-view']
    }
  });
};
