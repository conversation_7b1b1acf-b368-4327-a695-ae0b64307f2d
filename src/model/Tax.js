export default function Tax({ cgst_per = 0, igst_per = 0, sgst_per = 0, ugst_per = 0, taxable_value = 0 }) {
  this.taxable_value = taxable_value;
  this.cgst =  {
    percent: cgst_per,
    rate: Math.round(cgst_per * taxable_value)/100,
  };
  this.igst = {
    percent: igst_per,
    rate: Math.round(igst_per * taxable_value)/100
  };
  this.sgst = {
    percent: sgst_per,
    rate: Math.round(sgst_per * taxable_value)/100
  };
  this.ugst = {
    percent: ugst_per,
    rate: Math.round(ugst_per * taxable_value)/100
  };
  this.total_tax = this.cgst.rate + this.igst.rate + this.sgst.rate + this.ugst.rate;
}

Tax.prototype.setCgst = function(percent) {
  let currRate = this.cgst.rate;
  let newRate = Math.round(percent * this.taxable_value)/100;
  this.cgst = {
    percent: percent,
    rate: newRate
  };
  this.total_tax += newRate - currRate;
};
Tax.prototype.setIgst = function(percent) {
  let currRate = this.igst.rate;
  let newRate = Math.round(percent * this.taxable_value)/100;
  this.igst = {
    percent: percent,
    rate: newRate
  };
  this.total_tax += newRate - currRate;
};
Tax.prototype.setUgst = function(percent) {
  let currRate = this.ugst.rate;
  let newRate = Math.round(percent * this.taxable_value)/100;
  this.ugst = {
    percent: percent,
    rate: newRate
  };
  this.total_tax += newRate - currRate;
};
Tax.prototype.setSgst = function(percent) {
  let currRate = this.sgst.rate;
  let newRate = Math.round(percent * this.taxable_value)/100;
  this.sgst = {
    percent: percent,
    rate: newRate
  };
  this.total_tax += newRate - currRate;
};
Tax.prototype.setTaxableValue = function(taxable_value) {
  this.taxable_value = taxable_value;
  this.cgst.rate = Math.round(this.cgst.percent * taxable_value)/100;
  this.igst.rate = Math.round(this.igst.percent * taxable_value)/100;
  this.sgst.rate = Math.round(this.sgst.percent * taxable_value)/100;
  this.ugst.rate = Math.round(this.ugst.percent * taxable_value)/100;
  this.total_tax = this.cgst.rate + this.igst.rate + this.sgst.rate + this.ugst.rate;
};
