import React, { Fragment } from 'react';
import { makeStyles } from 'tss-react/mui';

import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import Box from '@mui/material/Box';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 8
  },
  label: {
    fontWeight: 400,
    fontSize: 12,
    lineHeight: '166%',
    letterSpacing: 0.4,
    height: 'fit-content',
    color: 'rgba(60, 60, 60, 0.54)'
  },
  activeLabel: {
    color: '#3C3C3C'
  },
  icon: {
    color: '#3C3C3C8A',
    fontSize: 10,
    margin: '0px 10px'
  }
}));

const Phases = ({ labels }) => {
  const { classes, cx } = useStyles();

  return (
    <Box className={classes.container} data-cy="phases">
      {labels.map((eachLabel, index) => (
        <Fragment key={eachLabel}>
          <span
            data-cy={eachLabel}
            className={cx(classes.label, {
              [classes.activeLabel]: index === labels.length - 1
            })}
          >
            {eachLabel}
          </span>
          {index !== labels.length - 1 && <ArrowForwardIosIcon className={classes.icon} />}
        </Fragment>
      ))}
    </Box>
  );
};

export default Phases;
