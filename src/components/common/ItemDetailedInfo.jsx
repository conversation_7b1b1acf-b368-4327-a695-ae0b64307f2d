import React, { useMemo } from 'react';
import { keys } from 'utils/lodash';
import Spinner from '../Spinner/Spinner';
import './ItemDetailedInfo.scss';

function applyItemColor(key, pidStatus) {
  if (
    (pidStatus === 'FAILED' && key === 'QC Fail') ||
    (pidStatus === 'PASSED' && key === 'QC Pass')
  ) {
    return ' bg-white text-33';
  }
  return ' ';
}

const ItemDetailedInfo = (props) => {
  const { infoData, pidStatus } = props; // isComplete
  const pidStatusMemo = useMemo(() => {
    let temp = ' bg-f2';
    if (pidStatus === 'PASSED') {
      temp = 'bg-76 text-white';
    }
    if (pidStatus === 'FAILED') {
      temp = 'bg-red text-white';
    }

    return temp;
  }, [pidStatus]);

  if (!infoData) {
    return <Spinner margin="0 auto" p={2} />;
  }

  return (
    <div
      data-cy="item-detailed-info"
      className={`pd-8 pd-r40 item-detail-info border-dd display-flex 
        justify-content-space-between border-radius-base align-items-center ${pidStatusMemo}`}
    >
      {infoData.map((item) => (
        <div
          key={keys(item)}
          data-cy={`${keys(item)}-info`}
          className={`display-flex flex-direction-column pd-l16 pd-r16 
              border-radius-base pd-t4 pd-b4 ${applyItemColor(keys(item), pidStatus)}`}
        >
          <div className={`fs24${keys(item) === 'Scanned' ? ' fw700' : ''}`}>
            {item[keys(item)]}
          </div>
          <div className="fs14">{keys(item)}</div>
        </div>
      ))}
    </div>
  );
};
export default ItemDetailedInfo;
