import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

const TYPE = 5;

export const getStorePackingListApi = (payload) =>
  tokenInstance().post(config.storePacking.packingList, {...payload, type: TYPE});

export const flushStorePackingApi = (payload) =>
  tokenInstance().put(config.storePacking.flushStorePacking, {...payload, type: TYPE});

export const printShipmentLabelApi = ({ shippingPackageId }) =>
  tokenInstance().get(
    `${config.storePacking.printShipment}?shippingPackageId=${shippingPackageId}`
  );

export const removeStoreShimpentApi = ({ shipmentId }) =>
  tokenInstance().delete(`${config.storePacking.removeStoreShipment}/${shipmentId}`);

export const getStoreCourierListApi = ({ payload: { country, pincode, shipmentId } }) => {
  const url = config.storePacking.storeCourierList;
  return tokenInstance().get(
    `${url}/${shipmentId}/serviceability?country=${country}&pincode=${pincode}`
  );
};

export const changeStoreCourierShipmentApi = ({ payload }) =>
  tokenInstance().post(config.storePacking.changeStoreCourier, payload);
