import React, { useCallback, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Button } from '@mui/material';
import Box from '@mui/material/Box';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';
import { genericDateWithoutTimeFormat } from 'utils/helpers';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';

const initailSortingData = {
  id: 'created_at',
  order: 'DESC'
};

const VendorShipmentListBody = ({
  pageLimit,
  fetchMoreListItems,
  pageNumber,
  CONTENT,
  data,
  isLoading,
  totalCount,
  onChangeFilterList,
  disableDownloadFun,
  onDownloadFun,
  deleteSelectedFilter,
  selectedFilterList = [],
  resetAppliedFilters,
  searchVal
}) => {
  const [tableHeight] = useWindowResize(window.innerHeight - 240);

  const facility = useSelector((state) => state.settings?.facilitiesObj?.facilities);
  const facilityCode = facility?.map((item) => item.code);

  const headerConfig = [
    {
      name: CONTENT.VENDOR_SHIPMENT,
      key: 'shippingPackageId',
      formatBody: useCallback(
        ({ shipmentId }) => <Link to={`/vendorShipment/details/${shipmentId}`}>{shipmentId}</Link>,
        []
      ),
      style: { minWidth: 180, maxWidth: 180 },
      leftSticky: 'true'
    },
    {
      name: CONTENT.QUANTITY,
      key: 'shipmentQuantity',
      align: 'right',
      style: { minWidth: 80, maxWidth: 80 }
    },
    {
      name: CONTENT.PO,
      key: 'poNum',
      formatBody: useCallback(
        ({ poNum }) =>
          poNum ? (
            <Link target="_blank" to={`/po/detail?poNum=${poNum}&fromPo=true`}>
              {poNum}
            </Link>
          ) : (
            '-'
          ),
        []
      ),
      style: { minWidth: 160, maxWidth: 160 },
      filterData: {
        type: 'input',
        columnName: 'po_num'
      }
    },
    {
      name: CONTENT.VENDOR_INV_NO,
      key: 'vendorInvoiceNumber'
    },
    {
      name: CONTENT.FACILITY_CODE,
      key: 'facilityCode',
      filterData: {
        type: 'autoSelect',
        columnName: 'facility_code',
        localSearch: true,
        listData: facilityCode,
        selectedFilterList
      }
    },
    {
      name: CONTENT.ETD,
      key: 'expectedDispatchDate',
      formatBody: useCallback(
        ({ expectedDispatchDate }) => (
          <div className="fw-bold">
            {genericDateWithoutTimeFormat(expectedDispatchDate, 'utcTOlocalDate')}
          </div>
        ),
        []
      ),
      style: { minWidth: 180, maxWidth: 180 }
    },
    {
      name: CONTENT.BOE_DATE,
      key: 'billOfEntryDate',
      supportSort: true,
      formatBody: useCallback(
        ({ billOfEntryDate }) => (
          <div className="fw-bold">
            {genericDateWithoutTimeFormat(billOfEntryDate, 'utcTOlocalDate')}
          </div>
        ),
        []
      )
    },
    {
      name: CONTENT.WH_LANDED_DATE,
      key: 'whLandingDate',
      formatBody: useCallback(
        ({ whLandingDate }) => (
          <div className="fw-bold">
            {genericDateWithoutTimeFormat(whLandingDate, 'utcTOlocalDate')}
          </div>
        ),
        []
      )
    },
    {
      name: CONTENT.EAD,
      key: 'expectedArrivalDate',
      formatBody: useCallback(
        ({ expectedArrivalDate }) => (
          <div className="fw-bold">
            {genericDateWithoutTimeFormat(expectedArrivalDate, 'utcTOlocalDate')}
          </div>
        ),
        []
      ),
      style: { minWidth: 180, maxWidth: 180 }
    },
    {
      name: CONTENT.AD_DATE,
      key: 'actualDispatchDate',
      formatBody: useCallback(
        ({ actualDispatchDate }) => (
          <div className="fw-bold">
            {genericDateWithoutTimeFormat(actualDispatchDate, 'utcTOlocalDate')}
          </div>
        ),
        []
      ),
      style: { minWidth: 180, maxWidth: 180 }
    },
    {
      name: CONTENT.CC_DATE,
      key: 'customClearanceDate',
      formatBody: useCallback(
        ({ customClearanceDate }) => (
          <div className="fw-bold">
            {genericDateWithoutTimeFormat(customClearanceDate, 'utcTOlocalDate')}
          </div>
        ),
        []
      ),
      style: { minWidth: 190, maxWidth: 190 }
    },
    {
      name: CONTENT.CREATED_AT,
      key: 'createdAt',
      columnName: 'created_at',
      formatBody: useCallback(
        ({ createdAt }) => (
          <div className="fw-bold">{genericDateWithoutTimeFormat(createdAt, 'utcTOlocalDate')}</div>
        ),
        []
      )
    },
    {
      name: CONTENT.CREATED_BY,
      key: 'createdBy'
    }
  ];

  const isResetDisabled = useMemo(
    () => !(searchVal || selectedFilterList.length > 0),
    [searchVal, selectedFilterList]
  );

  return (
    <Box className="border-radius-8 border-dd">
      <Box className="display-flex justify-content-space-between pd-t10 pd-b10 pd-r16">
        <Box>
          {selectedFilterList.length ? (
            <Box>
              <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />
            </Box>
          ) : (
            ''
          )}
        </Box>
        <Box>
          <Button
            variant="outlined"
            className="mr-r16"
            onClick={resetAppliedFilters}
            disabled={isResetDisabled}
            data-cy="reset-filter-btn"
          >
            {CONTENT.RESET}
          </Button>
          <Button
            variant="outlined"
            data-cy="export-list-data"
            disabled={disableDownloadFun}
            onClick={onDownloadFun}
          >
            {CONTENT.EXPORT}
          </Button>
        </Box>
      </Box>
      <LkTable
        pageLimit={pageLimit}
        tableData={data}
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        totalRowsCount={totalCount}
        headerConfig={headerConfig}
        pageNumber={pageNumber}
        dataRequestFunction={fetchMoreListItems}
        initialSortBy={initailSortingData}
        setFilters={onChangeFilterList}
        isNonVertualizedTable
      />
    </Box>
  );
};

export default VendorShipmentListBody;
