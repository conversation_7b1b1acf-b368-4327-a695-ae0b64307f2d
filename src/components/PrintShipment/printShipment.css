.barcodeBoldheading > svg > g > text {
  background-color: red !important;
  font-weight: bold !important;
}

.warehouseManagementLabel {
  writing-mode: tb-rl;
  transform: rotate(-360deg);
  margin-top: 40px;
  font-size: 12px;
}

.dateLabel {
  writing-mode: tb-rl;
  transform: rotate(-360deg);
  font-size: 12px;
}
.rightElements {
  margin-top: 20px;
  margin-left: 15px;
  width: 15px;
}

.bulkOrderContainer {
  display: flex;
  gap: 20;
  flex-wrap: wrap;
}

.bulkOrderTable {
  flex: 1;
  border: 1px solid black;
  border-collapse: collapse;
  margin-top: 20px;
  height: max-content;
}

.bulkOrderBorder {
  border: 1px solid black;
  padding: 3px;
  text-align: start;
}

.boxCount {
  font-size: 16;
  font-weight: 800;
  position: absolute;
}
.bulk-order-container-with-total {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.direction {
  font-weight: 800;
  position: absolute;
  right: 0;
  top: 0;
}
