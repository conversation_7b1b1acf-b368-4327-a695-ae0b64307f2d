import React from 'react';
import { makeStyles } from 'tss-react/mui';
import Tooltip from '@mui/material/Tooltip';

const LkToolTip = (props) => {
  const { title, width, children, placement, maxContent, isarrow = true, boxshadow = 0 } = props;
  const useStyles = makeStyles()((theme) => ({
    arrow: {
      fontSize: 16,
      width: 17,
      '&::before': {
        border: '1px solid #DDDDDD',
        backgroundColor: theme.palette.common.white,
        boxSizing: 'border-box'
      }
    },
    tooltip: {
      backgroundColor: theme.palette.common.white,
      color: '#333333',
      fontWeight: 'normal',
      border: '1px solid #DDDDDD',
      borderRadius: '8px',
      padding: '0px',
      overflow: 'hidden',
      boxShadow: theme.shadows[boxshadow],
      marginRight: 20
    },
    customWidth: {
      width,
      maxWidth: width
    },
    maxContent: {
      width: 'max-content',
      maxWidth: 'max-content'
    }
  }));

  const { classes } = useStyles();

  return (
    <div data-cy="tooltip-container">
      <Tooltip
        {...props}
        arrow={isarrow}
        placement={placement}
        title={title}
        classes={{
          arrow: isarrow ? classes.arrow : null,
          tooltip: `${classes.tooltip} ${maxContent ? classes.customWidth : classes.maxContent}`
        }}
      >
        {children}
      </Tooltip>
    </div>
  );
};

export default LkToolTip;
