@use 'sass:map';
@use './variables' as *;

.tag {
    font-size: $font-size-caption;
    padding: 2px 8px;
    border-radius: $border-radius-caption;
    font-weight: bold;
    &-turquioise_surf {
        background: $color-primary;
        color: $color-white;
    }
}

.tag-status {
    padding: 2px 6px;    
    border-radius: 4px;
    width: max-content;
    font-size: 12px;
    text-align: center;
    display: flex;
    align-items: center;
    &-bright_yellow {
        color: $color-yellow;
        border: 1px solid $color-yellow;
    }
    &-paris_green {
        color: map.get($map: $colors-secondary, $key: paris_green);
        border: 1px solid map.get($map: $colors-secondary, $key: paris_green);
    }
}
