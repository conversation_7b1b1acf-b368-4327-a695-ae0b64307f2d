import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import Barcode from 'react-barcode';
import { convertDateFormat } from 'utils/helpers';
import './printShipment.css';

const ShipmentPrintTemplate = forwardRef((props, ref) => {
  const { shipmentLabelData } = props;
  const { boxCount, direction } = shipmentLabelData || {};
  const AEFacilities = ['UAE1', 'DXB1'];
  const hideLogosForFacilities = ['PBR01', 'BR01'];

  const [routingCode, route = null] = shipmentLabelData?.routingCode?.split('$') || [];

  const renderTemplate = (index, totalCount) => (
    <div
      ref={ref}
      style={{ padding: '0px 0px 0px 20px', display: 'flex' }}
      className="container"
      key={index}
    >
      <div style={{ width: '100%', paddingTop: '20px' }}>
        <div
          className="shipmentLabelContainer"
          style={{ border: '2px solid #000', display: 'inline-block', width: '100%' }}
        >
          <div
            className="leftContainer"
            style={{ float: 'left', padding: '10px', width: '45%', wordWrap: 'break-word' }}
          >
            <div
              className="shipmentCode"
              style={{ fontSize: '20px', fontWeight: '800', marginBottom: '10px' }}
            >
              {shipmentLabelData.shippingProviderCode} - {shipmentLabelData.paymentMethod}
            </div>
            <div
              className="shipmentBarcode barcodeBoldheading"
              style={{ textAlign: 'left', marginLeft: '-10px', minHeight: '90px' }}
            >
              {shipmentLabelData.awbNumber && (
                <Barcode
                  textAlign="center"
                  fontSize={16}
                  fontWeight="800"
                  height={50}
                  value={shipmentLabelData.awbNumber}
                />
              )}
            </div>
            <div className="shippingAddress" style={{ marginBottom: '10px', marginTop: '10px' }}>
              <div>
                <span className="shipTolabel" style={{ fontWeight: '800', fontSize: '16px' }}>
                  {` Ship To : ${
                    shipmentLabelData.storeCode && shipmentLabelData.storeCode !== '0'
                      ? shipmentLabelData.storeCode
                      : ''
                  }`}
                </span>
                <div className="add_name" style={{ fontWeight: '800', fontSize: '16px' }}>
                  {shipmentLabelData.addressDTO.firstName} {shipmentLabelData.addressDTO.lastName}
                </div>
              </div>
              <div className="add_line1" style={{ fontWeight: '800', fontSize: '16px' }}>
                {shipmentLabelData.addressDTO.street}
              </div>
              <div className="add_line2" style={{ fontSize: '10px' }}>
                <span className="add_city" style={{ fontWeight: '800', fontSize: '16px' }}>
                  {shipmentLabelData.addressDTO.city},{' '}
                </span>
                <span className="add_state" style={{ fontWeight: '800', fontSize: '16px' }}>
                  {shipmentLabelData.addressDTO.region} -{' '}
                </span>
                <span className="add_pincode" style={{ fontWeight: '800', fontSize: '16px' }}>
                  {shipmentLabelData.addressDTO.postcode}
                </span>
              </div>
              <div className="add_line3" style={{ fontWeight: '800', fontSize: '16px' }}>
                <span className="add_country">
                  {AEFacilities.includes(shipmentLabelData.facility) ? 'AE' : 'India'},{' '}
                </span>
                <span className="add_telephone">
                  Tel No: {shipmentLabelData.addressDTO.telephone}
                </span>
              </div>
              {shipmentLabelData.addressDTO.fax && (
                <div className="alternatePhone" style={{ fontWeight: '800', fontSize: '16px' }}>
                  Alternate Telephone: {shipmentLabelData.addressDTO.fax}
                </div>
              )}
            </div>
            <div className="orderDetails">
              <div
                className="orderCode"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                Order Code: {shipmentLabelData.incrementId}
              </div>
              <div
                className="routingCode"
                style={{ fontWeight: '800', fontSize: '18px', marginBottom: '5px' }}
              >
                <p>Routing Code: {routingCode}</p>
              </div>
              <div
                className="gstin"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                {AEFacilities.includes(shipmentLabelData.facility)
                  ? 'VATTRN: '
                  : `GSTIN:  ${shipmentLabelData.collectibleAmount}`}
              </div>
              <div
                className="spn"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                SPN No: {shipmentLabelData.spnNo}{' '}
                {shipmentLabelData.tinNo && <span>TIN No: {shipmentLabelData.tinNo}</span>}
              </div>
              {shipmentLabelData.channel === 'BULKTOVENDOR' && (
                <>
                  <div
                    className="spn"
                    style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
                  >
                    Box Count: {boxCount}{' '}
                  </div>
                  <div
                    className="spn"
                    style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
                  >
                    PO No: {shipmentLabelData.poNumber}{' '}
                  </div>
                </>
              )}
              {shipmentLabelData.paymentMethod === 'COD' && (
                <div
                  className="collectableAmount"
                  style={{
                    fontWeight: 800,
                    fontSize: '16px',
                    marginBottom: '5px',
                    marginTop: '25px'
                  }}
                >
                  Collectable Amount:{' '}
                  {AEFacilities.includes(shipmentLabelData.facility) ? 'AED' : 'INR'}{' '}
                  {shipmentLabelData.collectibleAmount}
                </div>
              )}
            </div>
          </div>
          <div
            className="rightContainer"
            style={{ float: 'right', padding: '5px', width: '55%', wordWrap: 'break-word' }}
          >
            {shipmentLabelData.addressDTO.region === 'Maharashtra' && (
              <div>
                <img
                  src={`${import.meta.env.VITE_STATIC_SERVER}/media/desktop/nexs/star.png`}
                  alt="star img"
                />
              </div>
            )}
            <div
              className="orderCode"
              style={{
                fontWeight: '800',
                fontSize: '16px',
                marginBottom: '5px',
                textAlign: 'center',
                width: '100%',
                display: 'flex',
                justifyContent: 'space-around'
              }}
            >
              {route && <p>R10: {route}</p>}
              {shipmentLabelData.incrementId % 1000}
            </div>
            <div className="companyLogo" style={{ textAlign: 'center' }}>
              {(shipmentLabelData.merchantId === 'LENSKART' ||
                shipmentLabelData.merchantId === 'DEALSKART') &&
                shipmentLabelData?.channel !== 'ODONLINE' &&
                !hideLogosForFacilities.includes(shipmentLabelData.facility) && (
                <span>
                  <img
                    src={`${
                      import.meta.env.VITE_STATIC_SERVER
                    }/images/lenskartLogoForShipment.png`}
                    alt="lenskart logo"
                    width="140px"
                  />
                </span>
              )}
              {shipmentLabelData?.channel === 'ODONLINE' && (
                <span>
                  <img
                    width="140px"
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/jj_logo.webp`}
                    alt="Own Day Logo"
                  />
                </span>
              )}
              {boxCount > 0 && (
                <span className="boxCount" style={{ marginLeft: '60px', marginTop: '30px' }}>
                  {`${index} / ${totalCount}`}
                </span>
              )}
              {direction && (
                <span
                  className="direction"
                  style={{ marginRight: '50px', marginTop: '30px', fontSize: '24px' }}
                >
                  {direction}
                </span>
              )}
            </div>
            <div
              className="shippingPackageIdBarcode"
              style={{ marginBottom: '10px', textAlign: 'left' }}
            >
              <div
                className="barcodePayment"
                style={{
                  fontWeight: '800',
                  marginTop: '10px'
                }}
              >
                {shipmentLabelData.paymentMethod === 'COD' ? 'CASH ON DELIVERY' : 'PREPAID'}
              </div>
              <div
                style={{ textAlign: 'left', marginRight: '40px', marginLeft: '-10px' }}
                className="barcodeBoldheading"
              >
                <Barcode
                  textAlign="center"
                  fontSize={16}
                  fontWeight="800"
                  height={50}
                  value={shipmentLabelData.shippingPackageId || shipmentLabelData.transferCode}
                  width={shipmentLabelData.shippingPackageId ? 2 : 1.5}
                />
              </div>
            </div>
            <div className="invoiceDetails">
              <div
                className="channel"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                Channel: {shipmentLabelData.channel}
              </div>
              <div
                className="invoiceNo"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                Invoice No.: {shipmentLabelData.invoiceNo}
              </div>
              <div
                className="date"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                Dated : {convertDateFormat(shipmentLabelData.invoiceDate, 'date')}
              </div>
              <div
                className="weight"
                style={{ fontWeight: '800', fontSize: '16px', marginBottom: '5px' }}
              >
                Weight: {shipmentLabelData.weight.toFixed(2)}
              </div>
              {shipmentLabelData?.binCode && (
                <div className="alternatePhone" style={{ fontWeight: '800', fontSize: '56px' }}>
                  BIN: {shipmentLabelData?.binCode}
                </div>
              )}
            </div>
          </div>
        </div>
        {shipmentLabelData.productToCountMap && (
          <table
            style={{
              fontWeight: '500',
              fontSize: '18px',
              display: 'inline-block',
              marginLeft: '15%',
              position: 'relative',
              marginTop: '50px'
            }}
          >
            <tbody>
              <tr>
                <td style={{ fontWeight: '800', paddingTop: '.5em', paddingBottom: '.5em' }}>
                  Product Description
                </td>
                <td style={{ fontWeight: '800', paddingTop: '.5em', paddingBottom: '.5em' }}>
                  Qty
                </td>
              </tr>
              {Object.keys(shipmentLabelData.productToCountMap).map((key) => (
                <tr key={key}>
                  <td style={{ fontWeight: '800', paddingTop: '.5em', paddingBottom: '.5em' }}>
                    {key}
                  </td>
                  <td style={{ fontWeight: '800', paddingTop: '.5em', paddingBottom: '.5em' }}>
                    {shipmentLabelData.productToCountMap[key]}
                  </td>
                </tr>
              ))}
              <tr>
                <td
                  style={{
                    float: 'right',
                    fontWeight: 800,
                    paddingTop: '.5em',
                    paddingBottom: '.5em'
                  }}
                >
                  Total=
                </td>
                <td style={{ fontWeight: '800', paddingTop: '.5em', paddingBottom: '.5em' }}>
                  {shipmentLabelData.totalItemsCount}
                </td>
              </tr>
            </tbody>
          </table>
        )}
        <div className="bulkOrderContainer">
          {shipmentLabelData?.bulkCategoryResponseList?.map(({ category, bulkItemList }) => {
            const totalQtyCount = bulkItemList?.reduce(
              (acc, curr) => acc + (curr.quantity ?? 0),
              0
            );
            const rows = bulkItemList?.map((item) => (
              <tr key={item.sno}>
                <td className="bulkOrderBorder">{item.sno}</td>
                <td className="bulkOrderBorder">{item.brand}</td>
                <td className="bulkOrderBorder">{item.quantity}</td>
              </tr>
            ));
            return (
              <div key={category} className="bulk-order-container-with-total">
                <table className="bulkOrderTable">
                  <thead>
                    <tr>
                      <th className="bulkOrderBorder" style={{ textAlign: 'center' }} colSpan="3">
                        {category}
                      </th>
                    </tr>
                    <tr>
                      <th className="bulkOrderBorder" style={{ width: '30px' }}>
                        S.no
                      </th>
                      <th className="bulkOrderBorder" style={{ width: '150px' }}>
                        Brand
                      </th>
                      <th className="bulkOrderBorder" style={{ width: '45px' }}>
                        Qty
                      </th>
                    </tr>
                  </thead>
                  <tbody>{rows}</tbody>
                </table>
                <table className="bulkOrderTable">
                  <tbody>
                    <tr>
                      <td
                        className="bulkOrderBorder"
                        style={{ textAlign: 'center', width: '180px' }}
                      >
                        Total
                      </td>
                      <td className="bulkOrderBorder" style={{ width: '22px' }}>
                        {totalQtyCount}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            );
          })}
        </div>
      </div>

      <div className="rightElements">
        <div className="dateLabel">{convertDateFormat(new Date(), 'date')}</div>
        <div className="warehouseManagementLabel">
          Lenskart Warehouse Management - Lenskart Warehouse Management
        </div>
      </div>
    </div>
  );

  if (boxCount) {
    const boxes = Array.from({ length: boxCount }, (v, i) => i + 1);
    return <div>{boxes.map((_, index) => renderTemplate(index + 1, boxCount))}</div>;
  }
  return renderTemplate();
});

export default ShipmentPrintTemplate;

ShipmentPrintTemplate.propTypes = {
  shipmentLabelData: PropTypes.shape({
    incrementId: PropTypes.number.isRequired,
    channel: PropTypes.string.isRequired,
    facility: PropTypes.string.isRequired,
    shippingPackageId: PropTypes.string.isRequired,
    transferCode: PropTypes.string,
    collectibleAmount: PropTypes.number.isRequired,
    paymentMethod: PropTypes.string.isRequired,
    gstin: PropTypes.string.isRequired,
    addressDTO: PropTypes.shape({
      lastName: PropTypes.string,
      firstName: PropTypes.string.isRequired,
      region: PropTypes.string.isRequired,
      postcode: PropTypes.string.isRequired,
      street: PropTypes.string.isRequired,
      city: PropTypes.string.isRequired,
      telephone: PropTypes.string.isRequired,
      fax: PropTypes.string.isRequired
    }).isRequired,
    shippingProviderCode: PropTypes.string.isRequired,
    awbNumber: PropTypes.string.isRequired,
    invoiceNo: PropTypes.string.isRequired,
    weight: PropTypes.number.isRequired,
    spnNo: PropTypes.string,
    tinNo: PropTypes.string,
    invoiceDate: PropTypes.string.isRequired,
    routingCode: PropTypes.string.isRequired,
    storeId: PropTypes.number.isRequired,
    errorMessage: PropTypes.string,
    merchantId: PropTypes.string.isRequired,
    productToCountMap: PropTypes.object,
    totalItemsCount: PropTypes.number.isRequired,
    itemDescription: PropTypes.string.isRequired,
    storeCode: PropTypes.string.isRequired,
    shipmentCount: PropTypes.number.isRequired,
    binCode: PropTypes.string,
    shipmentPdfAvailable: PropTypes.bool,
    bulkCategoryResponseList: PropTypes.arrayOf(
      PropTypes.shape({
        category: PropTypes.string.isRequired,
        bulkItemList: PropTypes.arrayOf(
          PropTypes.shape({
            sno: PropTypes.number.isRequired,
            brand: PropTypes.string.isRequired,
            quantity: PropTypes.number.isRequired
          })
        ).isRequired
      })
    )
  }).isRequired
};
