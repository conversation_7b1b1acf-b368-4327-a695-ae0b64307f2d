import React from 'react';
import { Box } from '@mui/material';
import { useTheme } from '@emotion/react';
import { makeStyles } from 'tss-react/mui';
import Barcode from 'react-barcode';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import LkModal from '../Modal/Modal';
import QcFailCodeData from '../../__mocks__/QcFailCodeData';
import './QcFailCodeModal.scss';

const styles = makeStyles()((theme) => ({
  root: {
    flexGrow: 1,
    borderBottom: '1px solid #DDDDDD'
  },
  tabLabel: {
    textTransform: 'none',
    color: theme.palette.primary,
    fontSize: '14px',
    fontWeight: 'normal'
  },
  tab: {
    minWidth: '100px'
  },
  tabContent: {
    borderRadius: '8px',
    border: '1px solid #DDDDDD'
  },
  tabBody: {
    maxHeight: '340px',
    overflowY: 'scroll'
  }
}));

const getTabData = (dataKeys, value, classes, theme) =>
  dataKeys.map((temp, index) => (
    <TabPanel key={temp} value={value} index={index} dir={theme.direction}>
      <Box height="398px" className="contentParent">
        <Box className={classes.tabContent} mt={4} ml={10} mr={10}>
          <Box className="pd-t12 pd-b12 pd-r16 pd-l16 fs12 fw-bold bg-f5 display-flex">
            {QcFailCodeData[dataKeys[value]].header.map((t, i) => {
              let flex = '60%';
              if (i === 1) {
                flex = '23%';
              } else if (i === 0) {
                flex = '17%';
              }
              return (
                <div key={t} style={{ flex }}>
                  {' '}
                  {t}
                </div>
              );
            })}
          </Box>
          <Box className={`${classes.tabBody} tabBody`}>
            {QcFailCodeData[dataKeys[value]].content.map((t, i) => {
              const indexTemp = i % 2;
              return (
                <Box
                  key={t.textCode}
                  alignItems="center"
                  className={`pd-r16 pd-l16 fs12 ${indexTemp && 'bg-f5'} ali  display-flex`}
                >
                  <div style={{ flex: '15%' }}> {t.textCode}</div>
                  <div style={{ flex: '25%' }}>
                    <Barcode
                      value={t.textCode}
                      width={1.5}
                      displayValue={false}
                      height={35}
                      background=""
                    />
                  </div>
                  <div style={{ flex: '60%' }}>{t['QC Fail Reason']}</div>
                </Box>
              );
            })}
          </Box>
        </Box>
      </Box>
    </TabPanel>
  ));

const QcFailCodeModal = ({ open, handleClose }) => {
  const theme = useTheme();
  const dataKeys = Object.keys(QcFailCodeData);
  const { classes } = styles();
  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <LkModal
      allowPaddingForChildren={false}
      modalWidth="800px"
      modalHeight="682px"
      open={open}
      upperHeading="QC Fail Codes Summary"
      handleClose={handleClose}
      primaryBtn={() => window.print()}
      primaryBtnText="print"
      primaryBtnClass="printButton"
      className="QCFailCodesModal"
    >
      <div className={classes.root}>
        <Box width="80%" margin="0 auto">
          <Tabs
            value={value}
            onChange={handleChange}
            indicatorColor="primary"
            textColor="primary"
            centered
          >
            {dataKeys.map((tab, index) => (
              <Tab
                key={tab}
                className={classes.tab}
                label={
                  <span
                    className={classes.tabLabel}
                    style={{ fontWeight: value === index ? 'bold' : '' }}
                  >
                    {tab}
                  </span>
                }
              />
            ))}
          </Tabs>
        </Box>
      </div>
      {getTabData(dataKeys, value, classes, theme)}
    </LkModal>
  );
};

export default QcFailCodeModal;

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {value === index && <Box mb={3}>{children}</Box>}
    </div>
  );
};
