import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import './layoutList.scss';
import useWindowResize from 'common/useWindowResize';
import { getLayoutListLoad } from 'redux/actionCreators/layout';
import { genericDateFormatted } from 'utils/helpers';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import { useNavigate } from 'react-router-dom';

const useStyles = makeStyles()(() => ({
  root: {
    background: '#fff'
  },
  table: {
    minWidth: 700,
    paddingBottom: 4
  },
  container: {
    maxHeight: 700
  },
  outlinedPrimary: {
    marginLeft: '16px',
    height: 35
  },
  label: {
    fontSize: '10px'
  }
}));

const PAGE_SIZE = 20;
const initialSortByData = { id: 'updatedDate', order: 'DESC' };

const defaultRequest = (status, keyword) => ({
  status,
  keyword,
  page_number: 0,
  sort_order: 'DESC',
  sort_key: 'updatedDate',
  page_size: PAGE_SIZE
});

const LayoutList = ({ status, globalSearch, CONTENT }) => {
  const navigate = useNavigate();
  const { classes } = useStyles();
  const initialReq = useRef(true);
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 222);
  const [requestBody, setRequestBody] = useState(defaultRequest(status, globalSearch));

  const { layoutListData, layoutListProccessing } = useSelector((state) => state.layout);

  useEffect(() => {
    if (requestBody.keyword !== globalSearch) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        page_number: 0,
        keyword: globalSearch
      }));
    }
  }, [globalSearch]);

  useEffect(() => {
    dispatch(getLayoutListLoad(requestBody));
  }, [requestBody]);

  const goToLayoutDetail = (ev, layout, isClone) => {
    const { layout_id, facility_id } = layout;
    if (isClone) {
      ev.stopPropagation();
    }
    if (layout.status === 'LINKED') {
      navigate(`/facilitys/details/${facility_id}?openLayout=true`);
    } else if (!isClone) {
      navigate(`/layoutpage/create?layoutId=${layout_id}`);
    } else {
      navigate(`/layoutpage/create?layoutId=${layout_id}&clone=true`);
    }
  };

  const updateRequestBody = (sortType, sortKey, page) => {
    if (!initialReq.current) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        page_number: page,
        sort_key: sortKey,
        sort_order: sortType
      }));
    }
    initialReq.current = false;
  };

  const headerConfig = [
    {
      name: CONTENT.NAME,
      key: 'name',
      columnName: 'name',
      align: 'left',
      supportSort: true,
      style: { minWidth: '250px', maxWidth: '250px', paddingLeft: 20 },
      formatBody: useCallback((layout) => (
        <div onClick={(ev) => goToLayoutDetail(ev, layout, false)} className="text-primary">
          {layout.name}
        </div>
      ))
    },
    {
      name: CONTENT.LAST_MODIFIED,
      key: 'updatedDate',
      columnName: 'updatedDate',
      align: 'left',
      supportSort: true,
      style: { minWidth: '250px', maxWidth: '250px' },
      formatBody: useCallback(({ updatedDate }) => genericDateFormatted(updatedDate))
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      columnName: 'status',
      align: 'left',
      supportSort: true,
      style: { minWidth: '200px', maxWidth: '200px' },
      formatBody: useCallback(({ status: stats }) => (
        <div className={`status ${stats}`}>{stats}</div>
      ))
    },
    {
      name: CONTENT.DESCRIPTION,
      key: 'description',
      columnName: 'description',
      align: 'left',
      supportSort: true,
      style: { minWidth: '250px', maxWidth: '250px' }
    },
    {
      name: '',
      key: '',
      columnName: '',
      align: 'center',
      style: { minWidth: '100px', maxWidth: '100px' },
      formatBody: useCallback((layout) => (
        <div onClick={(ev) => goToLayoutDetail(ev, layout, true)} className="action-box">
          <img
            className="img-action"
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/clone.svg`}
            alt="clone"
          />
        </div>
      ))
    }
  ];

  return (
    <div className={`${classes.root} layout-list po-dashboard pos-rel`}>
      <LkTable
        pageLimit={PAGE_SIZE}
        tableHeight={tableHeight}
        headerConfig={headerConfig}
        initialSortBy={initialSortByData}
        isDataFetching={layoutListProccessing}
        tableData={layoutListData?.data?.layouts || []}
        totalRowsCount={layoutListData?.data?.count}
        dataRequestFunction={updateRequestBody}
        pageNumber={requestBody.page_number}
      />
    </div>
  );
};

export default LayoutList;
