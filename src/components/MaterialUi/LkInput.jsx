import React from 'react';
import TextField from '@mui/material/TextField';
import { makeStyles, withStyles } from 'tss-react/mui';
import styled from 'styled-components';
import spacing from 'theme/spacing';
import { getBarcodeFromURL } from '../../utils/helpers';

const useStyles = makeStyles()(() => ({
  root: {
    borderColor: '#DDDDDD'
  }
}));

const HoverTextField = withStyles(TextField, {
  root: {
    '& label': {
      color: '#9e9e9e'
    },
    '& .MuiSelect-icon': {
      color: '#9e9e9e'
    },
    '& .MuiIconButton-label': {
      color: '#9e9e9e'
    },
    '& .MuiInput-underline:after': {
      borderBottomColor: 'white'
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: 'white'
      },
      '&:hover fieldset': {
        borderColor: '#DDDDDD'
      }
    }
  }
});

const CustomTextField = styled(TextField)`
  ${spacing}
`;

const LkInput = (props) => {
  const {
    className,
    onChange,
    onBlur,
    margin,
    error,
    label,
    onKeyDown,
    onKeyPress,
    onKeyUp,
    onHoverTextFiled = false,
    size = 'small',
    variant = 'outlined',
    ...rest
  } = props;

  const { classes, cx } = useStyles();

  const wrapOnKeyPress = (originalOnKeyPress) => (event) => {
    const eventClone = event;
    if (event.key === 'Enter') {
      const { value: inputValue } = event.target;
      eventClone.target.value = getBarcodeFromURL(inputValue);
    }
    originalOnKeyPress(eventClone);
  };

  if (!onHoverTextFiled) {
    return (
      <CustomTextField
        {...rest}
        size={size}
        className={cx(classes.root, className)} /* , classes.notchedOutline */
        margin={margin}
        onChange={onChange}
        variant={variant}
        label={label}
        onBlur={onBlur}
        error={error}
        onKeyDown={onKeyDown ? wrapOnKeyPress(onKeyDown) : null}
        onKeyPress={onKeyPress ? wrapOnKeyPress(onKeyPress) : null}
        onKeyUp={onKeyUp ? wrapOnKeyPress(onKeyUp) : null}
      />
    );
  }
  return (
    <HoverTextField
      {...rest}
      size={size}
      className={cx(classes.root, className)} /* , classes.notchedOutline */
      margin={margin}
      onChange={onChange}
      variant={variant}
      label={label}
      onBlur={onBlur}
      error={error}
    />
  );
};

// @TODO add proptypes for this components

export default LkInput;
