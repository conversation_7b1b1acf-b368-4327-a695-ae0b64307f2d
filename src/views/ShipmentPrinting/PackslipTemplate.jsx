/* eslint-disable class-methods-use-this */
import React, { forwardRef, useEffect } from 'react';
import './packslipTemplate.css';
import Barcode from 'react-barcode';
import { numberToWords } from 'utils/helpers';

const PackslipTemplate = forwardRef((props, ref) => {
  //  @page css property will apply for all other templates, if we add in css file so added here.
  const appendPrintStyles = () => {
    const style = document.createElement('style');
    style.textContent = `
        @page {
          size: 623.62px 264.56px;
        }
        @media print {
          body {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
    `;
    style.setAttribute('data-print-styles', 'true');
    document.head.appendChild(style);
  };

  const removePrintStyles = () => {
    const style = document.querySelector('style[data-print-styles]');
    if (style) {
      document.head.removeChild(style);
    }
  };

  useEffect(() => {
    appendPrintStyles();

    return () => {
      removePrintStyles();
    };
  }, []);

  const { packslipData } = props;
  const { invoiceSummaryPdfResponse, printShipmentResponse } = packslipData;
  const [routingCode, route = null] = printShipmentResponse?.routingCode?.split('$') || [];

  const {
    addressLine1 = '',
    addressLine2 = '',
    city = '',
    pincode = '',
    state = '',
    phone = '',
    email = ''
  } = invoiceSummaryPdfResponse.invoiceSummaryResponse.billingAddress;

  const getBillToAddressFontSize = () => {
    const totalAddressLength = (addressLine1?.length || 0) + (addressLine2?.length || 0);
    return totalAddressLength > 160 ? '6.7px' : '8px';
  };

  const getShipToAddressFontSize = () => {
    const shippingAddress1 =
      invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.addressLine1;
    const shippingAddress2 =
      invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.addressLine2;
    const totalAddressLength = (shippingAddress1?.length || 0) + (shippingAddress2?.length || 0);
    return totalAddressLength > 220 ? '8px' : '9.5px';
  };

  const decodeHtmlEntities = (htmlString) => {
    const parser = new DOMParser();
    const decodedString = parser.parseFromString(htmlString, 'text/html').documentElement
      .textContent;
    return decodedString;
  };

  return (
    <div
      ref={ref}
      style={{
        border: '1px solid black',
        width: '616.06px',
        height: '253.22px'
      }}
      className="container"
    >
      <div
        style={{
          width: '226.7px',
          height: '219.21px',
          borderRight: '1px solid black',
          borderBottom: '1px solid black'
        }}
        className="first-container"
      >
        <div style={{ width: '100%', height: '143px' }}>
          <table style={{ borderSpacing: 0, paddingTop: '4px' }}>
            <tbody>
              <tr>
                <td
                  style={{
                    width: '25mm',
                    height: '141px',
                    verticalAlign: 'baseline',
                    textAlign: 'center',
                    padding: 0
                  }}
                >
                  <div>
                    Channel -{' '}
                    <span style={{ fontSize: '9px' }}>{printShipmentResponse.channel}</span>
                  </div>
                  <div style={{ padding: '7px 0px' }}>
                    <img
                      src={invoiceSummaryPdfResponse.shipmentBarcode}
                      style={{ width: '68px', height: '68px' }}
                      alt=""
                    />
                  </div>
                  <div
                    style={{
                      paddingTop: '10px',
                      paddingLeft: '5px',
                      fontSize: '9.5',
                      fontWeight: '600',
                      fontFamily: 'Bahnschrift Condensed, sans-serif'
                    }}
                  >
                    <span>{printShipmentResponse.shippingPackageId}</span>
                  </div>
                </td>
                <td
                  style={{
                    verticalAlign: 'baseline',
                    fontSize: getShipToAddressFontSize(),
                    paddingLeft: '6px',
                    lineBreak: 'anywhere'
                  }}
                >
                  <div style={{ fontWeight: '700' }}>Ship To:</div>
                  <span>
                    {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.name}
                  </span>
                  <br />
                  {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress
                    .addressLine1 && (
                    <div
                      style={{
                        width: '30.5mm',
                        display: 'inline-block',
                        wordBreak: 'break-word'
                      }}
                      // eslint-disable-next-line react/no-danger
                      dangerouslySetInnerHTML={{
                        __html: decodeHtmlEntities(
                          invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress
                            .addressLine1
                        )
                      }}
                    />
                  )}
                  {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress
                    .addressLine2 && (
                    <div
                      style={{
                        width: '30.5mm',
                        display: 'inline-block',
                        wordBreak: 'break-word'
                      }}
                      // eslint-disable-next-line react/no-danger
                      dangerouslySetInnerHTML={{
                        __html: decodeHtmlEntities(
                          invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress
                            .addressLine2
                        )
                      }}
                    />
                  )}
                  <br />
                  <span>
                    {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.city}
                  </span>{' '}
                  -
                  <span>
                    {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.pincode}
                  </span>{' '}
                  <span>
                    {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.state}
                  </span>
                  <br />
                  Phone:{' '}
                  <span>
                    {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.phone}
                  </span>
                  <br />
                  <div
                    style={{ width: '30.5mm', display: 'inline-block', wordBreak: 'break-word' }}
                  >
                    {invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingAddress.email}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div style={{ height: '72px', borderTop: '0.3px solid black' }}>
          <table style={{ borderSpacing: 0 }}>
            <tbody>
              <tr>
                <td
                  style={{
                    width: '150px',
                    verticalAlign: 'baseline',
                    paddingLeft: '3px',
                    paddingTop: '3px',
                    borderRight: '0.3px solid black',
                    height: '75px',
                    lineBreak: 'anywhere',
                    fontSize: getBillToAddressFontSize()
                  }}
                >
                  <div style={{ fontWeight: '700' }}>Bill To:</div>
                  <span
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: decodeHtmlEntities(addressLine1)
                    }}
                  />
                  <span
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: decodeHtmlEntities(addressLine2)
                    }}
                  />
                  <br />
                  {city} - {pincode} {state} <br />
                  Phone: {phone}
                  <br />
                  <span>{email}</span>
                </td>
                <td
                  style={{
                    width: '82px',
                    verticalAlign: 'baseline',
                    textAlign: 'center',
                    fontSize: '7px',
                    paddingTop: '2px'
                  }}
                >
                  <div>Scan for Invoice</div>
                  <div style={{ paddingBottom: '3px' }}>
                    <img
                      src={invoiceSummaryPdfResponse.qrCode}
                      style={{ width: '36.5px', height: '36.5px' }}
                      alt=""
                    />
                  </div>
                  <div>Order code: {printShipmentResponse.incrementId}</div>
                  <div>Weight: {printShipmentResponse.weight}gm</div>
                  <div>
                    Order Date: {invoiceSummaryPdfResponse.invoiceSummaryResponse.orderDate}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div
        style={{
          height: '219.21px',
          width: '294.8px',
          borderBottom: '1px solid black'
        }}
        className="second-container"
      >
        <div style={{ paddingLeft: '2px', paddingTop: '2px' }}>
          <div style={{ display: 'inline-block', width: '50%', fontWeight: '600' }}>
            TAX INVOICE - {invoiceSummaryPdfResponse.invoiceSummaryResponse.invoiceNo}
          </div>
          <div
            style={{ display: 'inline-block', width: '49%', textAlign: 'end', fontWeight: '600' }}
          >
            Invoice Date – {invoiceSummaryPdfResponse.invoiceSummaryResponse.invoiceDate}
          </div>
        </div>
        <div
          style={{
            paddingTop: '10px',
            height:
              numberToWords(invoiceSummaryPdfResponse.invoiceSummaryResponse.grandTotal)?.length >
              35
                ? '166px'
                : '173px'
          }}
        >
          <table>
            <thead>
              <tr>
                <th style={{ fontSize: '9px' }}>
                  <b>Products</b>
                </th>
                <th>Unit Price</th>
                <th>After Discount</th>
                <th>IGST</th>
                <th>SGST</th>
                <th>CGST</th>
                <th>
                  Qty
                  <br />
                  (Uom)
                </th>
                <th>Total amount (Inc. GST)</th>
              </tr>
            </thead>
            <tbody>
              {invoiceSummaryPdfResponse.invoiceSummaryResponse.itemSummaryResponseList.map(
                (item) => (
                  <tr key={item.productId}>
                    <td style={{ width: '104px', wordWrap: 'break-word' }}>{item.productDesc}</td>
                    <td>{item.unitCostPrice}</td>
                    <td>{item.discount}</td>
                    <td>{item.igst}</td>
                    <td>{item.sgst}</td>
                    <td>{item.cgst}</td>
                    <td>{item.quantity}</td>
                    <td>{item.unitPrice}</td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
        <div style={{ textAlign: 'end', paddingTop: '4px', paddingBottom: '7px' }}>
          <table style={{ fontSize: '7px', width: '98%' }}>
            <tbody>
              {/* need this code in future */}
              {/* <tr>
                  <td>
                    Shipping and Handling <br />
                    charges (inc. of taxes)
                  </td>
                  <td>{invoiceSummaryPdfResponse.invoiceSummaryResponse.shippingCharges}</td>
                </tr> */}
              <tr>
                <td>Grand Total</td>
                <td>{invoiceSummaryPdfResponse.invoiceSummaryResponse.grandTotal}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div
          style={{
            fontWeight: '600',
            borderTop: '0.5px solid black',
            paddingTop: '3px',
            paddingLeft: '2px'
          }}
        >
          {printShipmentResponse.paymentMethod} | Collectable value - Rs.{' '}
          {invoiceSummaryPdfResponse.invoiceSummaryResponse.grandTotal} (
          {numberToWords(invoiceSummaryPdfResponse.invoiceSummaryResponse.grandTotal)})
        </div>
      </div>
      <div
        style={{
          width: '94.48px',
          height: '253.22px',
          borderLeft: '1px solid black',
          paddingTop: '1px'
        }}
        className="third-container"
      >
        <div style={{ textAlign: 'center', height: '54px', borderBottom: '1px solid black' }}>
          <div>
            {printShipmentResponse.incrementId
              .toString()
              .substring(printShipmentResponse.incrementId.toString().length - 3)}
          </div>
          <div className="logo">
            <img
              style={{ width: '56px', height: '39px' }}
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/lenskartLogoForShipment.png`}
              alt="Lenskart"
            />
          </div>
        </div>
        <div
          id="rotatedCanvas"
          style={{ position: 'relative', top: '20px', left: '20.5px', height: '49px' }}
        >
          {printShipmentResponse.awbNumber && (
            <>
              <div
                style={{
                  width: '195px',
                  height: '48.5px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <Barcode
                  margin={1}
                  height={32}
                  width={1.5}
                  fontSize={8.5}
                  value={printShipmentResponse.awbNumber}
                />
              </div>
              <div
                style={{
                  width: '190px',
                  fontSize: '13px',
                  textAlign: 'center'
                }}
              >
                <span
                  style={{
                    display: 'inline-block',
                    fontWeight: '600',
                    paddingRight: '5px',
                    paddingBottom: '2px'
                  }}
                >
                  {printShipmentResponse.shippingProviderCode}
                </span>{' '}
                <br />
                <span style={{ fontSize: '9px' }}>Routing Code: </span>
                <span
                  style={{
                    fontSize: printShipmentResponse.routingCode?.length <= 14 ? '15px' : '13px',
                    fontWeight: 'bold'
                  }}
                >
                  {routingCode}
                </span>
                {route && (
                  <span style={{ fontSize: '11px' }}>
                    R10 : <span style={{ fontWeight: 'bold', fontSize: '16px' }}>{route}</span>
                  </span>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      <div
        style={{
          position: 'absolute',
          top: '220px',
          fontSize: '6.3px',
          verticalAlign: 'baseline',
          height: '34px'
        }}
        className="footer"
      >
        <div
          style={{
            width: '300px',
            display: 'inline-block',
            fontSize: '8px',
            paddingLeft: '3px'
          }}
        >
          <b>Lenskart Solutions Limited (formerly known as Lenskart Solutions Private Limited)</b>|
          Plot No. 151, Okhla Industrial Estate, Phase III, New Delhi, 110020 India <br />
          <a
            style={{
              color: 'black',
              fontSize: '8px',
              fontWeight: '400'
            }}
            href="mailto:<EMAIL>"
          >
            <EMAIL>
          </a>{' '}
          | GSTIN: 08AACCV7324B1ZK | CIN: U33100DL2008PLC178355
        </div>
        <div
          style={{
            display: 'inline-block',
            fontSize: '8px'
          }}
        >
          <div>Disclaimer: </div>
          <div>Tax is payable on reverse charge basis: No</div>
          <div>Lenskart recovers insurance component as a pure agent</div>
        </div>
        <div
          style={{
            display: 'inline-block',
            textAlign: 'center',
            position: 'relative',
            top: '-5px',
            left: '4px'
          }}
        >
          <img
            style={{ width: '38px' }}
            src="https://static-nexs.lenskart.com/images/authorisedSign.jpeg"
            alt=""
          />
        </div>
      </div>
    </div>
  );
});

export default PackslipTemplate;
