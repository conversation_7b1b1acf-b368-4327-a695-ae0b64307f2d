import React, { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { Box, Button, IconButton } from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import QrCodeScannerIcon from '@mui/icons-material/QrCodeScanner';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import FileDownloadIcon from '@mui/icons-material/FileDownload';

import { LOGIN } from 'redux/reducers/login';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  closeStockTakeCycleLoad,
  createStockTakeCycleLoad,
  getStockTakeCycleDetailLoad,
  STOCK_TAKE_CYCLE,
  stockTakeIdDownloadLoad,
  uploadStockTakeCycleLoad
} from 'redux/reducers/stockTake';

import Spinner from 'components/Spinner/Spinner';
import LkTable from 'components/MaterialUi/LkTable/LkTable';

import useWindowResize from 'common/useWindowResize';
import { fileDownload, genericDateFormatted } from 'utils/helpers';

import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import CreateStockTakeModal from './CreateStockTakeModal';
import StockTakeDetails from './StockTakeDetails';
import CloseCycleModal from './CloseCycleModal';

const initialSortBy = {
  id: 'createdAt',
  order: 'DESC'
};

const csvMimeTypes = [
  '.csv',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel'
].join(', ');

const StockTakeListBody = ({
  fetchMoreListItems,
  requestBody,
  selectedFilterList,
  onChangeFilterList,
  deleteSelectedFilter,
  showButton
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [tableHeight] = useWindowResize(window.innerHeight - 160);

  const [stockTakeModal, setStockTakeModal] = useState(false);
  const [stockTakeDetailsModal, setStockTakeDetailsModal] = useState(false);
  const [uploadId, setUploadId] = useState('');
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [cycleDetails, setCycleDetails] = useState({ id: '', name: '' });
  const {
    isLoading: createStockTakeLoading,
    isSuccess: createStockTakeSuccess,
    data: createStockTakeData
  } = useSelector((state) => state[STOCK_TAKE_CYCLE].createStockTakeCycle);
  const { isLoading: isDownloading, stockTakeId: clickedStockTakeId } = useSelector(
    (state) => state[STOCK_TAKE_CYCLE].idDownload
  );
  const { isLoading, data, totalCount } = useSelector(
    (state) => state[STOCK_TAKE_CYCLE].stockTakeCycleList
  );
  const { isLoading: isUploading } = useSelector(
    (state) => state[STOCK_TAKE_CYCLE].uploadStockTakeCycle
  );
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.STOCK_TAKE);
  const userName = useSelector((state) => state[LOGIN].user.userDetail.userName);
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);

  const closeModal = () => setStockTakeModal(false);

  const closeStockDetailsModal = () => setStockTakeDetailsModal(false);

  const closeCycleCloseModal = () => setShowCloseModal(false);

  const createStockTakeFn = (e) => {
    const value = e?.target?.value;
    if (e.keyCode === 13) {
      if (value?.trim() === '') {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.CYCLE_NAME_MUST_NOT_BE_BLANK,
            severity: 'error'
          })
        );
      }
      dispatch(
        createStockTakeCycleLoad({
          cycleName: value?.trim(),
          facilityCode: selectedFacility,
          createdBy: userName,
          requestBody
        })
      );
    }
    return null;
  };

  const handleCloseCycle = () => {
    const payload = {
      stockTakeId: cycleDetails.id,
      status: 'CLOSED',
      updatedBy: userName,
      requestBody
    };
    dispatch(closeStockTakeCycleLoad(payload));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.set('file', file);
      formData.set('facilityCode', selectedFacility);
      formData.set('createdBy', userName);
      dispatch(uploadStockTakeCycleLoad({ id: uploadId, formData, requestBody }));
      // This is added to upload the same file again
      e.target.value = '';
    }
  };

  const handleDownload = (stockTakeId) => {
    dispatch(stockTakeIdDownloadLoad({ stockTakeId }));
  };

  const donwloadFile = () => {
    const csvData = `
Barcode,ProductId,Qty,NotFound
BARCODE00001,92074,16,2
BARCODE00002,24934,8,0
BARCODE00003,24934,8,0
BARCODE00004,92074,16,0
BARCODE00005,68759,10,3
BARCODE00006,68759,10,0
`;
    const file = new Blob([csvData], { type: 'application/csv' });
    fileDownload(file, 'sample');
  };

  const headerConfig = [
    {
      name: CONTENT.CYCLE_ID,
      key: 'id',
      formatBody: useCallback(
        ({ id, status }) => (
          <Button
            disabled={!(status === 'UPLOADED' || status === 'CLOSED')}
            onClick={() => {
              setStockTakeDetailsModal(true);
              dispatch(getStockTakeCycleDetailLoad(id));
            }}
          >
            {id}
          </Button>
        ),
        []
      ),
      style: { minWidth: 100, maxWidth: 100 },
      filterData: { type: 'input', columnName: 'Cycle Id', selectedFilterList }
    },
    {
      name: CONTENT.CYCLE_NAME,
      key: 'name',
      filterData: {
        type: 'input',
        columnName: 'name',
        selectedFilterList
      }
    },
    {
      name: CONTENT.FACILITY_CODE,
      key: 'facilityCode'
    },
    {
      name: CONTENT.CREATED_AT,
      key: 'createdAt',
      formatBody: useCallback(({ createdAt }) => genericDateFormatted(createdAt), []),
      supportSort: true
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updatedAt',
      formatBody: useCallback(({ updatedAt }) => genericDateFormatted(updatedAt), []),
      supportSort: true
    },
    { name: CONTENT.CREATED_BY, key: 'createdBy' },
    { name: CONTENT.UPDATED_BY, key: 'updatedBy' },
    {
      name: CONTENT.ACTIONS,
      key: 'action',
      formatBody: useCallback(
        ({ id, status, name }, index) => {
          const disableActionButton =
            isUploading || status !== 'UPLOADED' || status === 'IN_PROGRESS' || status === 'CLOSED';
          const showSpinner = uploadId === id && isUploading;
          const isUploadedOrUploading =
            status === 'UPLOADED' || isUploading || status === 'CLOSED' || status === 'IN_PROGRESS';

          return (
            <Box className="display-flex" data-cy="actions">
              <input
                id={`upload-items-file-${index}`}
                onChange={handleFileChange}
                type="file"
                className="display-none"
                accept={csvMimeTypes}
              />

              {isUploadedOrUploading ? (
                <Box className="mr-r16 mr-t8">
                  {showSpinner ? <Spinner /> : <UploadFileIcon color="disabled" />}
                </Box>
              ) : (
                <Box
                  component="label"
                  data-cy={`upload-items-file-${index}`}
                  htmlFor={`upload-items-file-${index}`}
                  className="cursor-pointer mr-r16 mr-t8"
                  onClick={() => setUploadId(id)}
                  title={CONTENT.UPLOAD_ITEMS}
                >
                  <UploadFileIcon color="primary" />
                </Box>
              )}
              <Box className="mr-r16">
                <IconButton
                  disabled={disableActionButton}
                  color="secondary"
                  onClick={() => navigate(`/stockTake/scan/${id}`)}
                >
                  <QrCodeScannerIcon />
                </IconButton>
              </Box>
              {showButton && (
                <IconButton
                  onClick={() => {
                    setCycleDetails((prevReqBody) => ({ ...prevReqBody, id, name }));
                    setShowCloseModal(true);
                  }}
                  disabled={disableActionButton}
                  color="success"
                >
                  <CheckCircleOutlineIcon />
                </IconButton>
              )}
              <IconButton
                disabled={isDownloading && clickedStockTakeId === id}
                color="primary"
                onClick={() => handleDownload(id)}
              >
                {isDownloading && clickedStockTakeId === id ? <Spinner /> : <FileDownloadIcon />}
              </IconButton>
            </Box>
          );
        },
        [uploadId, isUploading, handleFileChange, isDownloading, clickedStockTakeId]
      ),
      style: { minWidth: 200, maxWidth: 200 }
    }
  ];

  return (
    <Box className="border-radius-8 border-dd pd-12">
      <Box className="display-flex justify-content-space-between  pd-16">
        <Box>
          {selectedFilterList.length ? (
            <Box>
              <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />
            </Box>
          ) : null}
        </Box>
        <Box>
          <Button
            className="mr-r12"
            variant="contained"
            color="primary"
            onClick={() => setStockTakeModal(true)}
          >
            {CONTENT.CREATE_CYCLE_TAKE}
          </Button>
          <Button variant="outlined" color="primary" onClick={donwloadFile}>
            {CONTENT.DOWNLOAD_SAMPLE_CSV}
          </Button>
        </Box>
      </Box>
      <LkTable
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        headerConfig={headerConfig}
        dataRequestFunction={fetchMoreListItems}
        tableData={data}
        totalRowsCount={totalCount}
        initialSortBy={initialSortBy}
        pageNumber={requestBody.page}
        pageLimit={requestBody.pageSize}
        setFilters={onChangeFilterList}
        isNonVertualizedTable
      />
      {stockTakeModal && (
        <CreateStockTakeModal
          open={stockTakeModal}
          handleClose={closeModal}
          handleSubmit={createStockTakeFn}
          createStockTakeLoading={createStockTakeLoading}
          createStockTakeSuccess={createStockTakeSuccess}
          createStockTakeData={createStockTakeData}
        />
      )}
      {stockTakeDetailsModal && (
        <StockTakeDetails open={stockTakeDetailsModal} handleClose={closeStockDetailsModal} />
      )}
      {showCloseModal && (
        <CloseCycleModal
          open={showCloseModal}
          cycleName={cycleDetails.name}
          close={closeCycleCloseModal}
          handleCloseCycle={handleCloseCycle}
        />
      )}
    </Box>
  );
};

export default StockTakeListBody;
