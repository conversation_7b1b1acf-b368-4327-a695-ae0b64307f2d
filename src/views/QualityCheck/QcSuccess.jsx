import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import uuid from 'uuid/v4';
import { LOCALISATION } from 'redux/reducers/localisation';

import { Style_QcSuccess } from './Style';

const styles = makeStyles()(() => ({ ...Style_QcSuccess }));

const AFTER_QC_PASS_STATUS = [
  'QC_PASS',
  'QCPass',
  'AWB_CREATED',
  'DISPATCHED',
  'COMPLETE',
  'INVOICED',
  'QC_DONE'
];

const renderItemBlock = (label, count, classes, className = '') => (
  <div className={`${classes.itemsWrapper} ${classes.marginLeft20}`}>
    <div className={`${classes.itemsNumber} ${className}`}>{count}</div>
    <div className={classes.itemsDesc}>{label}</div>
  </div>
);

const renderAccessoryTable = (accessories, delayCard, classes, QC) => (
  <Box className={`${classes.window1} ${classes.window2}`}>
    <div className={classes.rowHead}>
      <div className={classes.firstColumn}>{QC.S_NO}</div>
      <div className={classes.secondColumn}>{QC.ACCESSORY_NAME}</div>
      <div className={classes.thirdColumn}>{QC.IMAGE}</div>
    </div>

    {accessories.map((item, index) => {
      const image = item?.order?.product?.productImages?.[0] || '';
      return (
        <div
          key={uuid()}
          className={`${classes.rowWrapper} ${classes.fw400} ${
            index % 2 === 0 ? classes.backgroundWhite : ''
          } ${index + 1 === accessories.length && !delayCard ? classes.lastWrapper : ''}`}
        >
          <div className={classes.firstColumn}>{index + 1}.</div>
          <div className={classes.secondColumn}>{item.order.product.value}</div>
          <div className={classes.thirdColumn}>
            <img className={classes.accessoriesImg} alt="accessory-img" src={image} />
          </div>
        </div>
      );
    })}

    {delayCard && (
      <div
        className={`${classes.rowWrapper} ${classes.fw400} ${
          accessories.length % 2 === 0 ? classes.backgroundWhite : ''
        } ${classes.lastWrapper}`}
      >
        <div className={classes.firstColumn}>{accessories.length + 1}.</div>
        <div className={classes.secondColumn}>Delay Card</div>
        <div className={classes.thirdColumn}>
          <img
            className={classes.accessoriesImg}
            alt="accessory-img"
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/Delay_card.svg`}
          />
        </div>
      </div>
    )}
  </Box>
);

const QcSuccess = () => {
  const { classes } = styles();
  const navigate = useNavigate();

  const QC = useSelector((state) => state[LOCALISATION].localeData.QC);
  const {
    accessories = {},
    delayOrderCard,
    products = {},
    lenses = {}
  } = useSelector((state) => state.order);
  const { qcData = [] } = useSelector((state) => state.qc);

  if (!qcData || qcData.length === 0) {
    navigate('/qc');
  }

  const delayCard = delayOrderCard;
  const holded = [];
  let isQCPass = true;
  let holdCount = 0;
  let failedCount = 0;
  let unHoldCount = 0;

  // changing from for loop to some due to sonarqube issue
  qcData.some(({ status, uwItemId }) => {
    if (status === 'QCHold' || status === 'QC_HOLD') {
      holdCount += 1;
    } else if (status === 'QCFailed' || status === 'QC_FAILED') {
      failedCount += 1;
    } else if (status === 'QCUnhold' || status === 'QC_UNHOLD') {
      unHoldCount += 1;
    }

    if (!AFTER_QC_PASS_STATUS.includes(status)) {
      isQCPass = false;

      if (holded.includes(uwItemId)) {
        return true; // exit .some()
      }

      const found = lenses.some((lens) => {
        if (lens.parentUw === uwItemId || lens.uwItemId === uwItemId) {
          if (holded.includes(lens.parentUw)) {
            return true; // exit inner .some()
          }

          holded.push(lens.uwItemId);
          holded.push(lens.parentUw);
        }
        return false;
      });

      if (found) {
        return true;
      } // exit outer .some()
    }

    return false; // continue to next qcData
  });

  const passed = qcData.length - holdCount - failedCount - unHoldCount;

  const renderTitle = () => {
    if (passed === qcData.length) {
      return QC.QC_COMPLETE;
    }
    if (failedCount > 0) {
      return QC.QC_FAIL;
    }
    if (holdCount > 0) {
      return QC.QC_ON_HOLD;
    }
    if (unHoldCount > 0) {
      return QC.QC_PENDING;
    }
    return '';
  };

  return (
    <div className={classes.container}>
      <Box className={classes.window1}>
        {failedCount > 0 ? (
          <div className={classes.window3}>
            <img
              alt="order-status"
              className={classes.failImg}
              src={`${import.meta.env.VITE_STATIC_SERVER}/media/nexus/failIcon.svg`}
            />
          </div>
        ) : (
          <img
            className={classes.img}
            alt="order-status"
            src={
              holdCount
                ? `${import.meta.env.VITE_STATIC_SERVER}/media/nexus/hold.svg`
                : `${import.meta.env.VITE_STATIC_SERVER}/media/nexus/tickmark.svg`
            }
          />
        )}

        <div className={classes.orderStatus}>{renderTitle()}</div>
        <div className={classes.para}>{QC.QC_COMPLETE_OF_ALL_ITEMS_TEXT}</div>
        <div className={classes.itemsContainer}>
          {renderItemBlock(QC.TOTAL_PRODUCTS, products.length, classes)}
          {renderItemBlock(QC.QC_PASSED, passed, classes, classes.color_blue)}

          {isQCPass &&
            renderItemBlock(
              QC.ACCESSORIES,
              delayCard ? accessories.length + 1 : accessories.length,
              classes,
              classes.color_bright_yellow
            )}

          {holdCount > 0 &&
            renderItemBlock(QC.ON_HOLD, holdCount, classes, classes.color_bright_yellow)}

          {unHoldCount > 0 &&
            renderItemBlock(QC.ON_UNHOLD, unHoldCount, classes, classes.color_bright_yellow)}

          {failedCount > 0 && renderItemBlock(QC.FAIL_LOWERCASE, failedCount, classes, 'text-red')}
        </div>
      </Box>

      {(delayCard || accessories.length > 0) &&
        renderAccessoryTable(accessories, delayCard, classes, QC)}
    </div>
  );
};

export default QcSuccess;
