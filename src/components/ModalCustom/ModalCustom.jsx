import React from 'react';
import CloseIcon from '@mui/icons-material/Close';
import './modalCustom.scss';

const ModalCustom = (props) => {
  const { open, close, heading, children } = props;

  return open ? (
    <div id="myModal" className="modal-custom">
      <div className="modal-custom-content">
        <div className="mr-16 display-flex align-items-center justify-content-space-between">
          <div className="fs16 fw-bold text-center flex1">{heading}</div>
          <div>
            <CloseIcon style={{ cursor: 'pointer' }} onClick={close} />
          </div>
        </div>
        <div>{children}</div>
      </div>
    </div>
  ) : (
    <>
    </>
  );
};

export default ModalCustom;
