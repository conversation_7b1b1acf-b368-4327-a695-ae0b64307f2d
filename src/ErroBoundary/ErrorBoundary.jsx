import React from 'react';

import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

import { Topbar } from 'layouts/Main/components';

const ErrorBoundary = () => {
  const hideTopBar =
    window.location.pathname === '/changePassword' ||
    window.location.pathname === '/login' ||
    window.location.pathname === 'register';
  return (
    <>
      {!hideTopBar ? <Topbar /> : null}
      <Box mt={20} className="display-flex flex-direction-column align-items-center">
        <InfoOutlinedIcon color="disabled" fontSize="large" />
        <Typography m={1} variant="h3">
          Something went wrong.
        </Typography>
        <Typography variant="body2">Please try after sometime</Typography>
      </Box>
    </>
  );
};

export default ErrorBoundary;
