import React, { useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';

import LkInput from 'components/MaterialUi/LkInput';
import { toastOpen } from 'redux/actionCreators/toast';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import { LOCALISATION } from 'redux/reducers/localisation';

const CustomerHeader = ({ searchData, selectedFilters, deleteFilter }) => {
  const navigate = useNavigate();
  const searchCustomerNameRef = useRef(null);
  const dispatch = useDispatch();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);

  const handleCustomerCreation = () => {
    navigate('/customer/create');
  };
  const handleSearch = (e) => {
    const inputValue = searchCustomerNameRef.current.value?.trim();

    if (e.which === 13) {
      if (inputValue === '') {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.SEARCH_BY_CUSTOMER_NAME_CANNOT_BE_EMPTY,
            severity: 'error'
          })
        );
      }
      searchData([{ key: 'name', value: inputValue }]);
      searchCustomerNameRef.current.value = '';
    }
    return null;
  };

  return (
    <Box display="flex" justifyContent="space-between" padding={2} bgcolor="#FFFFFF">
      <Box display="flex">
        <Box width={300} display="flex" alignItems="center">
          <LkInput
            variant="outlined"
            id="globalSearchId"
            fullWidth
            inputRef={searchCustomerNameRef}
            onKeyPress={handleSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                </InputAdornment>
              )
            }}
            placeholder={CONTENT.SEARCH_BY_CUSTOMER_NAME}
          />
        </Box>
        {selectedFilters?.length > 0 ? (
          <FilterChipsV2 deleteData={deleteFilter} filters={selectedFilters} />
        ) : null}
      </Box>
      <Box>
        <Button onClick={handleCustomerCreation} variant="contained">
          {CONTENT.CREATE_NEW_CUSTOMER}
        </Button>
      </Box>
    </Box>
  );
};

export default CustomerHeader;
