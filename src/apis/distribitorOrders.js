import { tokenInstance } from 'utils/axiosInstance';
import { generatePayloadForSearchAPI } from 'utils/helpers';
import config from '../config';

export const validateCSVApi = (payload) =>
  tokenInstance().post(config.distributorOrders.validateCSV, payload);

export const createOrderApi = (payload) => {
  const header = {
    userName: payload?.empCode
  };
  return tokenInstance(header).post(
    `${config.distributorOrders.createOrder}/${payload?.referenceOrderId}`
  );
};

export const cancelOrderApi = ({ userName, id, status, cancellationReason }) => {
  const header = {
    userName,
    status,
    cancellationReason
  };
  return tokenInstance(header).post(`${config.distributorOrders.cancelOrder}/${id}`);
};

export const orderListApi = (payload) => {
  const { page, size, sortBy, sortOrder, searchTerms: filters } = payload;
  let searchTerms = '';

  const appendSearchTerm = (term) => {
    if (searchTerms) {
      searchTerms += `___${term}`;
    } else {
      searchTerms = term;
    }
  };

  if (filters.incrementId.length) {
    appendSearchTerm(`incrementId.in:${filters.incrementId}`);
  }
  if (filters.poNumber.length) {
    appendSearchTerm(`poNumber.in:${filters.poNumber}`);
  }
  if (filters.createdAt && filters.createdAt !== 'ALL PERIOD') {
    const date = generatePayloadForSearchAPI([{ key: 'CREATED', value: filters.createdAt }]);
    const { CREATEDfrom, CREATEDto } = date;
    const startDate = CREATEDfrom.replace(' ', 'T');
    const endDate = CREATEDto.replace(' ', 'T');
    appendSearchTerm(`createdAt.gte:${startDate}Z___createdAt.lte:${endDate}Z`);
  }
  if (filters.status.length) {
    appendSearchTerm(`status.in:${filters.status}`);
  }
  if (filters.facility) {
    appendSearchTerm(`facility.eq:${filters.facility}`);
  }

  return tokenInstance().get(config.distributorOrders.orderList, {
    params: { page, size, sortBy, sortOrder, searchTerms }
  });
};

export const customerDropdownListApi = (payload) =>
  tokenInstance().get(config.distributorOrders.customerDropdownList, {
    params: payload
  });

export const doNumberDetailsApi = (payload) => {
  const { userName, incrementId } = payload;
  return tokenInstance({ userName }).post(
    `${config.distributorOrders.doNumberDetails}/${incrementId}`
  );
};

export const returnApi = (payload) =>
  tokenInstance().post(config.distributorOrders.return, payload);
