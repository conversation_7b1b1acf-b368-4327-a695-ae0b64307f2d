worker_processes 1;

events { worker_connections 1024; }

http {
    include    mime.types;
    sendfile on;

    server {
        root /usr/share/nginx/html/;
        index index.html;
        server_name localhost;
        listen 8080;

        location ~ /v2/utility/ {
                proxy_pass http://api-gateway.juno.preprod-eks.internal;
        }

        location / {
          index index.html;
          try_files $uri /index.html;
        }
    }
}
