import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import { Box, Button, Divider, MenuItem, TextField } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import { Field, Form } from 'react-final-form';
import Spinner from 'components/Spinner/Spinner';
import Autocomplete from '@mui/material/Autocomplete';
import LkInput from 'components/MaterialUi/LkInput';
import FlexBox from '../../components/core/FlexBox';
import LkModal from '../../components/Modal/Modal';
import {
  fetchChannelLoad,
  getProviderLoad,
  saveManifestLoad
} from '../../redux/actionCreators/manifest';
import localStorageHelper from '../../utils/localStorageHelper';
import './CreateManifest.scss';

const useStyles = makeStyles()((theme) => ({
  smallField: {
    width: theme.spacing(22.5)
  },
  bigField: {
    width: '90%'
  },
  bottomBox: {
    width: 282,
    height: 122
  },
  buttonBox: {
    textAlign: 'center'
  },
  button: {
    width: 146
  },
  cancelButton: {
    width: 140,
    margin: '16px'
  }
}));

let readyToGo = false;
const paymentMethods = [
  {
    label: 'Standard-COD',
    value: 'Standard-COD',
    enable: 0
  },
  {
    label: 'Standard-Prepaid',
    value: 'Standard-Prepaid',
    enable: 0
  },
  {
    label: 'Any',
    value: 'Any',
    enable: 0
  }
];
let filterPaymentMethods = [];
let selectedFacility = '';
let selectProviderCode = '';

const CreateManifest = (props) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const navigate = useNavigate();

  const {
    handleModal
  } = props;

  const { manifestLoading, manifestChannels, providerList, saveManifestData } = useSelector(
    (state) => state.manifest
  );
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.MANIFEST);

  const [selectedShippingProvider, setSelectedShippingProvider] = useState('');
  const [selectedShippingMethod, setSelectedShippingMethod] = useState('');
  const [selectedOrderType, setSelectedOrderType] = useState('');

  useEffect(() => {
    dispatch(fetchChannelLoad());
    selectedFacility = localStorageHelper.getItem('facility-code');
  }, [dispatch]);

  useEffect(() => {
    if (saveManifestData) {
      navigate(`/manifest-detail/${saveManifestData.manifestId}`);
    }
  }, [saveManifestData]);

  const createManifestFunc = () => {
    const payload = {
      channel: selectedOrderType,
      facilityCode: selectedFacility,
      shippingMethod: selectedShippingMethod,
      shippingProvider: selectedShippingProvider,
      shippingProviderCode: selectProviderCode
    };
    dispatch(saveManifestLoad(payload));
  };

  const setDropDownVal = (e) => {
    const val = e.target.value || null;
    const { name } = e.target;
    if (name === 'orderType') {
      setSelectedOrderType(val);
      const payload = {
        orderType: val,
        page: 500
      };
      dispatch(getProviderLoad(payload));
    }
    if (name === 'shippingMethod') {
      setSelectedShippingMethod(val);
    }
    return val;
  };

  if (selectedShippingProvider && selectedShippingMethod && selectedOrderType) {
    readyToGo = true;
  }

  const getshippingMethodList = (item, code) => {
    selectProviderCode = code;
    const any = item?.any;
    const cod = item?.cod;
    const prepaid = item?.prepaid;
    paymentMethods[0].enable = cod;
    paymentMethods[1].enable = prepaid;
    paymentMethods[2].enable = any;
    filterPaymentMethods = paymentMethods.filter((type) => type.enable === 1);
  };

  return (
    <LkModal open showActionButton={false} handleClose={() => handleModal(false)}>
      <div className="create-modal-container pd-l20 pd-r20 mr-b20">
        <h4>{CONTENT.CREATE_MANIFEST}</h4>
        <Divider mb={4.5} />
        {manifestLoading && (
          <div className="create-manifest-loader">
            {' '}
            <Spinner />
          </div>
        )}
        <Box mt={3}>
          <Form
            onSubmit={createManifestFunc}
            initialValues=""
            render={({ handleSubmit }) => (
              <form onSubmit={handleSubmit}>
                <FlexBox justifyContent="center" mb={4.5}>
                  <Field name="orderType">
                    {({ input }) => (
                      <LkInput
                        className={classes.bigField}
                        label={CONTENT.ORDER_TYPE}
                        variant="outlined"
                        {...input}
                        select
                        onChange={(e) => setDropDownVal(e)}
                        value={selectedOrderType}
                      >
                        {manifestChannels.map((item) => (
                          <MenuItem value={item} key={item}>
                            {item}
                          </MenuItem>
                        ))}
                      </LkInput>
                    )}
                  </Field>
                </FlexBox>
                <FlexBox justifyContent="center" mb={4.5}>
                  <Field name="shippingProvider">
                    {() => (
                      <Autocomplete
                        className={classes.bigField}
                        variant="outlined"
                        size="small"
                        name="Shipping Provider"
                        options={providerList}
                        getOptionLabel={(option) => option?.shippingProvider}
                        isOptionEqualToValue={(option, value) =>
                          option?.shippingProvider === value?.shippingProvider
                        }
                        onChange={(e, value) => {
                          setSelectedShippingProvider(value?.shippingProvider);
                          getshippingMethodList(value, value?.shippingProviderCode);
                        }}
                        disabled={!providerList.length}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label={CONTENT.SHIPPING_PROVIDER}
                            variant="outlined"
                          />
                        )}
                        renderOption={(optionProps, option) => (
                          <li {...optionProps} key={option.id}>
                            {option.shippingProvider}
                          </li>
                        )}
                      />
                    )}
                  </Field>
                </FlexBox>

                <FlexBox justifyContent="center" mb={4.5}>
                  <Field name="shippingMethod">
                    {({ input }) => (
                      <LkInput
                        className={classes.bigField}
                        label={CONTENT.SHIPPING_METHOD}
                        variant="outlined"
                        select
                        {...input}
                        onChange={(e) => setDropDownVal(e)}
                        value={selectedShippingMethod}
                        disabled={!filterPaymentMethods.length}
                      >
                        {filterPaymentMethods.length > 0 &&
                          filterPaymentMethods.map((item) => {
                            const { label, value } = item;
                            return (
                              <MenuItem value={label} key={value}>
                                {label}
                              </MenuItem>
                            );
                          })}
                        {!filterPaymentMethods.length && <MenuItem value="">Select</MenuItem>}
                      </LkInput>
                    )}
                  </Field>
                </FlexBox>

                <Box m="auto" width="300" className={classes.buttonBox}>
                  <Button
                    className={classes.button}
                    type="submit"
                    color="primary"
                    variant="contained"
                    disabled={!readyToGo}
                  >
                    {CONTENT.CREATE}
                  </Button>
                  <Box component="span" m={2} width={140}>
                    <Button
                      className={classes.cancelButton}
                      onClick={() => handleModal(false)}
                      color="primary"
                      variant="outlined"
                    >
                      {CONTENT.CANCEL}
                    </Button>
                  </Box>
                </Box>
              </form>
            )}
          />
        </Box>
      </div>
    </LkModal>
  );
};

export default CreateManifest;
