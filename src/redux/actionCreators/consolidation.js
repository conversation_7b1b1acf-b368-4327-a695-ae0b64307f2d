import * as action from '../actionTypes/consolidation';
// API CALL FOR DROP DOWN CATEGORY OPTIONS
export const consolidationDataRequest = () => ({
  type: action.CONSOLIDATE_OPTIONS_LOAD
});

export const consolidationDataSuccess = (data) => ({
  type: action.CONSOLIDATE_OPTIONS_SUCCESS,
  payload: data
});

export const consolidationDataFail = () => ({
  type: action.CONSOLIDATE_OPTIONS_FAIL
});

// CONSOLIDATION PIGEONHOLE LIST
export const pigeonHoleDataListRequested = (payload) => ({
  type: action.CONSOLIDATION_DATA_LIST_LOAD,
  payload
});
export const pigeonHoleDataListSuccess = (data) => ({
  type: action.CONSOLIDATION_DATA_LIST_SUCCESS,
  payload: data
});
export const pigeonHoleDataListFail = (error) => ({
  type: action.CONSOLIDATION_DATA_LIST_FAIL,
  payload: error
});

// PIGEONHOLE CHILD DATA LIST
export const pigeonHoleChildListRequested = (boxCode) => ({
  type: action.CONSOLIDATION_CHILD_DATA_LIST_LOAD,
  payload: boxCode
});
export const pigeonHoleChildListSuccess = (payload) => ({
  type: action.CONSOLIDATION_CHILD_DATA_LIST_SUCCESS,
  payload
});
export const pigeonHoleChildListFail = (payload) => ({
  type: action.CONSOLIDATION_CHILD_DATA_LIST_FAIL,
  payload
});

// CONSOLIDATION SCAN LIST
export const consolidationScanLoad = (boxCode) => ({
  type: action.CONSOLIDATION_SCAN_LOAD,
  payload: boxCode
});

export const consolidationScanSuccess = (payload) => ({
  type: action.CONSOLIDATION_SCAN_SUCCESS,
  payload
});

export const consolidationScanFail = (payload) => ({
  type: action.CONSOLIDATION_SCAN_FAIL,
  payload
});

export const consolidationScanReset = (payload) => ({
  type: action.CONSOLIDATION_SCAN_RESET,
  payload
});
