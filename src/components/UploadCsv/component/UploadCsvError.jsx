import React from 'react';
import ButtonBase from '@mui/material/ButtonBase';
import Button from '@mui/material/Button';
import LkModal from '../../Modal/Modal';


const DownloadIcon = () => {
  return <img src={import.meta.env.VITE_STATIC_SERVER + '/images/download.svg'} alt="img" />
}

const UploadCsvError = props => {
  const { open, handleClose, downloadReport, message } = props;
  return (
    <LkModal open={open} title="Problems with CSV File" subTitle="The import was unsuccessful. Please download the error report below:" handleClose={() => handleClose()}>
      <div className="mr-40 display-flex pd-12 border-grey5-radiusbase justify-content-space-between">
        <div className="fs12">{message}</div>
        <ButtonBase color="primary" alt="img" >
        <div className=" display-flex fs14 text-turquioise_surf align-items-center" onClick={() => downloadReport()}>
          {DownloadIcon()}
          <div className="mr-l8">Download Error Report</div>
        </div>
        </ButtonBase>
      </div>
      <div className="text-center">
        <Button style={{ borderRadius: 8 }} variant="contained" color="primary" onClick={() => handleClose()}>OK</Button>
      </div>
    </LkModal>
  );
};

export default UploadCsvError;
