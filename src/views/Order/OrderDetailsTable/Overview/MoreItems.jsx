import React, { useState } from 'react';
import clsx from 'clsx';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import MoreVertIcon from '@mui/icons-material/MoreVert';

const MoreItems = ({
  CONTENT,
  shipmentId,
  classes,
  handlePrintInvoice,
  handleShipmentLabel,
  headerData,
  disabled,
  isAllowedToMoveToJIT,
  handleMoveToJitFn
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const closeMenu = () => setAnchorEl(null);

  const openMenu = Boolean(anchorEl);
  const shouldShowLastChildClass = !isAllowedToMoveToJIT && headerData?.navChannel !== 'MPDTC';
  return (
    <Box position="relative">
      <IconButton
        id="actions"
        aria-label="actions"
        aria-controls={openMenu ? 'actions' : undefined}
        aria-haspopup="true"
        aria-expanded={openMenu ? 'true' : undefined}
        onClick={(e) => setAnchorEl(e.currentTarget)}
        disabled={disabled}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="actions-menu"
        anchorEl={anchorEl}
        open={openMenu}
        anchorOrigin={{
          horizontal: 'right',
          vertical: 'top'
        }}
        MenuListProps={{
          'aria-labelledby': 'actions'
        }}
        onClose={closeMenu}
        style={{ top: '40px' }}
      >
        <MenuItem className={classes.menuItem} onClick={() => handleShipmentLabel(shipmentId)}>
          {CONTENT.PRINT_SHIPPING_LABEL}
        </MenuItem>
        {headerData?.navChannel !== 'MPDTC' && (
          <MenuItem
            className={clsx(
              classes.menuItem,
              shouldShowLastChildClass && classes.menuItemLastChild
            )}
            onClick={() => handlePrintInvoice(shipmentId)}
          >
            {CONTENT.PRINT_INVOICE}
          </MenuItem>
        )}
        {isAllowedToMoveToJIT && (
          <MenuItem
            className={`${classes.menuItem} ${classes.menuItemLastChild}`}
            onClick={() => handleMoveToJitFn(shipmentId)}
          >
            {CONTENT.MOVE_TO_JIT}
          </MenuItem>
        )}
      </Menu>
    </Box>
  );
};

export default MoreItems;
