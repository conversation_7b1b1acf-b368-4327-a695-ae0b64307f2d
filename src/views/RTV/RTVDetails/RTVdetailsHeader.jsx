import Box from '@mui/material/Box';
import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@mui/material/Button';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import IconButton from '@mui/material/IconButton';

import {
  RTV_LIST,
  approveRejectGatepassLoad,
  submitGatepassForArrovalLoad
} from 'redux/reducers/RTVlist.slice';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { pdfDownloadLoad, pdfDownloadReset } from 'redux/actionCreators/pdfDownload';

import Spinner from 'components/Spinner/Spinner';
import MoreItem from 'components/common/MoreItem';
import LkChip from 'components/MaterialUi/LkChip';
import PrintConfirmModal from 'components/PrintConfirmModal/PrintConfirmModal';

import { pdfNameMapping } from 'config/DebitNoteStatus';
import IdentifierInputField from 'common/IdentifierInputField';
import { convertDateFormat, fileDownload } from 'utils/helpers';

import RTVsubmitModal from './RTVsubmitModal';

import 'views/BulkInventory/TransferSend/BulkInventoryTransferDetails/TransferDetailsTop.scss';

const classes = {
  headerRoot: {
    background: 'white',
    display: 'flex',
    padding: '15px 20px',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  backArrow: {
    cursor: 'pointer',
    fontSize: '28px'
  },
  inputBox: {
    padding: '10px'
  },
  topRightbtn: {
    borderRadius: 2,
    padding: '5px 10px',
    fontWeight: '500',
    fontSize: '12px',
    letterSpacing: '0.4px'
  },
  topCenter: {
    gap: '24px',
    alignItems: 'center',
    flex: 1,
    padding: '0 35px',
    fontSize: '14px',
    position: 'relative'
  },
  label: {
    textTransform: 'uppercase',
    color: 'rgba(60, 60, 60, 0.54)',
    marginBottom: '10px',
    display: 'block'
  },
  statusLabel: {
    textTransform: 'capitalize'
  },

  closeTransferLoading: {
    display: 'flex'
  },
  statusStyle: {
    color: '#f44336',
    border: '1px solid rgba(244, 67, 54, 0.6)',
    fontWeight: 500
  }
};

const defaultModalData = {
  open: false,
  status: ''
};

const RTVdetailsHeader = ({ data, gatepassNum }) => {
  const {
    createdAt,
    facility_code,
    vendor_name,
    status,
    pidCounts,
    updatedBy,
    debitNoteNo,
    gatepassBarcodeDetailResponsesList
  } = data;

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [openReasonModal, setopenReasonModal] = useState({ ...defaultModalData });
  const [reason, setReason] = useState('');
  const [selectedPrintOption, setSelectedPrintOption] = useState(0);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const { pdfDownloadSuccess, pdfDownloadError } = useSelector((state) => state.pdfDownload);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.RTV_GATEPASS);
  const submitFOrApprovalLoading = useSelector(
    (state) => state[RTV_LIST].submitForApproval.isLoading
  );

  const showHeaderLabel = (label, value) => (
    <div className="text-center">
      <Box sx={{ ...classes.label }}>{label}</Box>
      <div className="fw500">{value || '-'}</div>
    </div>
  );

  const submitForApproval = () => {
    if (openReasonModal.status === 'DISCARDED') {
      dispatch(
        approveRejectGatepassLoad({
          status: openReasonModal.status,
          gatepass_num: gatepassNum,
          reason
        })
      );
    } else {
      dispatch(submitGatepassForArrovalLoad({ gatepass_num: gatepassNum, reason }));
    }
    setopenReasonModal({ ...defaultModalData });
  };

  const changePrintOptions = useCallback((e) => setSelectedPrintOption(e.target.value), []);

  const printOptions = [
    { value: CONTENT.DEBIT_NOTE, key: 'debitnote' },
    { value: CONTENT.DELIVERY_CHALLAN, key: 'deliverychallan' }
  ];
  const printFunCall = () => {
    setShowPrintModal(false);
    dispatch(pdfDownloadLoad({ type: selectedPrintOption, id: debitNoteNo }));
  };

  const toolTipElement = () => (
    <Box
      className="display-flex justify-content-space-between mr-10 cursor-pointer"
      width="100px"
      onClick={() => setShowPrintModal(true)}
      data-cy="open-print-modal"
    >
      <Box className="fs14">
        <img
          className="mr-r8"
          src={`${import.meta.env.VITE_STATIC_SERVER}/images/print.svg`}
          alt="img"
          width="16px"
          height="16px"
        />
        {CONTENT.PRINT}
      </Box>
      <Box className="fs14 text-99">{CONTENT.CTRL_P}</Box>
    </Box>
  );

  useEffect(() => {
    if (pdfDownloadSuccess) {
      fileDownload(
        pdfDownloadSuccess,
        `${pdfNameMapping[selectedPrintOption]}-NEXS-${debitNoteNo}`,
        'pdf'
      );
      dispatch(pdfDownloadReset());
    }
    if (pdfDownloadError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: pdfDownloadError?.meta?.displayMessage,
          severity: 'error'
        })
      );

      dispatch(pdfDownloadReset());
    }
  }, [pdfDownloadError, pdfDownloadSuccess]);

  const primaryBtnTxtValue = {
    SUBMIT: CONTENT.YES_SUBMIT,
    DISCARDED: CONTENT.YES_DISCARDED
  };

  const titleMapping = {
    SUBMIT: CONTENT.SUBMIT_GATEPASS_FOR_APPROVAL,
    DISCARDED: CONTENT.REJECT_GATEPASS
  };

  return (
    <Box sx={{ ...classes.headerRoot }} data-cy="rtv-details-header-container">
      <Box display="flex" alignItems="center">
        <IconButton onClick={() => navigate('/RTVgatepass/list')}>
          <ArrowBackIcon sx={{ ...classes.backArrow }} fontSize="medium" data-cy="arrow-back" />
        </IconButton>
        <IdentifierInputField labels={[CONTENT.SEND, CONTENT.GATEPASS_ID]} value={gatepassNum} />
      </Box>
      <Box display="flex" sx={{ ...classes.topCenter }}>
        {showHeaderLabel(CONTENT.SOURCE_LOWER, facility_code)}
        {showHeaderLabel(
          CONTENT.CREATED_AT,
          createdAt && convertDateFormat(createdAt, 'shortDateTime')
        )}
        {showHeaderLabel(CONTENT.UPDATED_BY, updatedBy)}
        {showHeaderLabel(CONTENT.VENDOR, vendor_name)}
        {showHeaderLabel(CONTENT.NO_OF_PIDS, pidCounts?.length)}
        {showHeaderLabel(CONTENT.NO_OF_ITEMS_DETAILS, gatepassBarcodeDetailResponsesList?.length)}
        {showHeaderLabel(
          CONTENT.STATUS_DETAILS,
          status && <LkChip label={status} type="success" />
        )}
      </Box>
      <Box className="text-right display-flex">
        {(data.status === 'CREATED' || data.status === 'REJECTED') && (
          <Button
            sx={{ ...classes.topRightbtn, mr: 2 }}
            size="small"
            variant="outlined"
            data-cy="submit-for-approval-btn"
            startIcon={
              submitFOrApprovalLoading ? (
                <Spinner size="20px" progressClassname={classes.closeTransferLoading} />
              ) : (
                <CheckCircleIcon style={{ fontSize: 24 }} />
              )
            }
            onClick={() => setopenReasonModal({ open: true, status: 'SUBMIT' })}
          >
            {CONTENT.SUBMIT_FOR_APPROVAL}
          </Button>
        )}

        {(data.status === 'CREATED' || data.status === 'REJECTED') && (
          <Button
            sx={{ ...classes.topRightbtn }}
            size="small"
            color="error"
            variant="outlined"
            data-cy="discard-gatepass-btn"
            onClick={() => setopenReasonModal({ open: true, status: 'DISCARDED' })}
          >
            {CONTENT.DISCARD}
          </Button>
        )}
      </Box>
      {status !== 'APPROVED' || !status ? (
        <MoreVertIcon color="disabled" data-cy="action-icon" />
      ) : (
        <MoreItem itemsToShow={toolTipElement()} />
      )}

      <RTVsubmitModal
        submitForApproval={submitForApproval}
        hideReasonInput={openReasonModal.status === 'SUBMIT'}
        openReasonModal={openReasonModal.open}
        setopenReasonModal={() => setopenReasonModal({ open: false, reason: '' })}
        CONTENT={CONTENT}
        reason={reason}
        setReason={setReason}
        title={titleMapping[openReasonModal.status]}
        primaryBtnTxt={primaryBtnTxtValue[openReasonModal.status]}
      />
      {showPrintModal && (
        <PrintConfirmModal
          changePrintfun={changePrintOptions}
          printValueTemp={selectedPrintOption}
          open={showPrintModal}
          printFun={printFunCall}
          handleClose={() => setShowPrintModal(false)}
          printOptions={printOptions}
        />
      )}
    </Box>
  );
};

export default RTVdetailsHeader;
