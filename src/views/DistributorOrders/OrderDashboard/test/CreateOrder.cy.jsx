import React from 'react';
import {
  customerList,
  validateCsvSuccess,
  validateCsvError,
  orderPlacement
} from './data/create-data-dummy.json';
import { DO_ORDER_MANAGEMENT as CONTENT } from '../../../../../public/localisation/lang_en.json';
import config from '../../../../config';
import CreateOrder from '../CreateOrder';

const interceptCustomerList = () => {
  cy.intercept('GET', `${config.distributorOrders.customerDropdownList}**`, {
    statusCode: 200,
    body: { data: customerList.data }
  }).as('getCustomerList');
};

const typeCustomerAndPo = (customerCode, poNumber) => {
  cy.findByRole('combobox', { name: /Select Customer/i }).type(customerCode);
  cy.contains(customerCode).click();
  cy.get('#po-number').type(poNumber);
  cy.getByCy('do-type-select').click();
  cy.getByCy('do-type-INDIA_STOCK_TRANSFER_DIFF_LE').click();
};

const validateCSV = (filePath, successMessage) => {
  cy.getByCy('validate-csv-container').selectFile(filePath, { force: true, action: 'drag-drop' });
  cy.getByCy('validate-csv-btn').should('not.be.disabled').click();
  cy.getByCy('csv-validation-message').should('exist').and('have.text', successMessage);
  cy.getByCy('table-container').should('exist');
};

describe('Create Order Component', () => {
  beforeEach(() => {
    interceptCustomerList();
    cy.mount(<CreateOrder />);
  });

  it('should render correctly', () => {
    cy.getByCy('create-order-container').should('exist');
    cy.useSelector('distributorOrders.customerDropDownList.data').should('have.length', 33);

    // Stepper
    [CONTENT.ENTER_ORDER_DETAILS, CONTENT.SELECT_AND_VALIDATE_CSV, CONTENT.PLACE_NEW_ORDER].forEach(
      (step) => {
        cy.getByCy(`stepper-${step}`).should('exist');
      }
    );

    // Step 1 - Enter Order Details
    cy.findByRole('combobox', { name: /Select Customer/i }).should('exist');
    cy.getByCy('auto-complete-input').find('label').should('have.text', CONTENT.SELECT_CUSTOMER);
    cy.get('#po-number').should('have.attr', 'placeholder', CONTENT.ENTER_PO_NUMBER);
    cy.getByCy('all-fields-are-required').should(
      'have.text',
      `*${CONTENT.ALL_FIELDS_ARE_REQUIRED}`
    );
    cy.getByCy('do-type-select').should('exist');
    cy.getByCy('confirm-button').should('have.text', CONTENT.CONFIRM).and('be.disabled');
  });
});

describe('CSV Validation Scenarios', () => {
  beforeEach(() => {
    interceptCustomerList();
  });

  it('should display error when CSV validation fails', () => {
    cy.intercept('POST', `${config.distributorOrders.validateCSV}**`, {
      statusCode: 500,
      body: { data: validateCsvError.data }
    }).as('getValidatedData');

    cy.mount(<CreateOrder />);
    typeCustomerAndPo('Myntra23', '123456');

    cy.getByCy('confirm-button').should('not.be.disabled').click();

    // CSV Validation Step
    cy.getByCy('sample-csv-text').should('have.text', CONTENT.SAMPLE_CSV_FILE);
    cy.getByCy('sample-csv-download-btn').click();
    cy.getByCy('validate-csv-container').should('exist');
    cy.getByCy('validate-csv-container').click();
    validateCSV('cypress/doValidateCsv/validateCsvError.csv', CONTENT.ERRORS_FOUND_IN_CSV);
    // clear CSV File
    cy.getByCy('clear-file-icon').should('exist');
    cy.getByCy('clear-file-icon').click();
    cy.getByCy('validate-csv-container').click();
    validateCSV('cypress/doValidateCsv/validateCsvError.csv', CONTENT.ERRORS_FOUND_IN_CSV);
    // Retry with correct CSV
    cy.getByCy('change-btn').should('exist');
    cy.getByCy('change-btn').click();
    cy.intercept('POST', `${config.distributorOrders.validateCSV}**`, {
      statusCode: 200,
      body: { data: validateCsvSuccess.data }
    }).as('getValidatedData');
    cy.getByCy('change-btn').selectFile('cypress/doValidateCsv/validateCsvSuccess.csv', {
      force: true
    });
    cy.getByCy('validate-csv-btn').should('not.be.disabled').click();
  });
});

describe('should work correctly if customer api gives error', () => {
  beforeEach(() => {
    cy.intercept('GET', `${config.distributorOrders.customerDropdownList}**`, {
      statusCode: 500,
      body: { data: 'Internal Server error' }
    }).as('getCustomerListError');
    cy.mount(<CreateOrder />);
  });

  it('should work correctly if customer api gives error', () => {
    cy.getByCy('confirm-button').should('be.disabled');
  });
});

describe('Complete Order Workflow', () => {
  beforeEach(() => {
    interceptCustomerList();
    cy.intercept(
      'POST',
      `${config.distributorOrders.createOrder}/${validateCsvSuccess.data.referenceOrderId}**`,
      {
        statusCode: 200,
        body: { data: orderPlacement.data }
      }
    ).as('getValidatedData');
  });

  it('should complete the order creation process successfully', () => {
    cy.intercept('POST', `${config.distributorOrders.validateCSV}**`, {
      statusCode: 200,
      body: { data: validateCsvSuccess.data }
    }).as('getValidatedData');

    cy.mount(<CreateOrder />);
    typeCustomerAndPo('Myntra23', '123456');

    cy.getByCy('confirm-button').should('not.be.disabled').click();

    // Order details step
    cy.getByCy('facility-code-details').should('exist').and('have.text', 'Facility: 0QNXS');
    cy.getByCy('po-number-details').should('exist').and('have.text', 'PO: 123456');
    cy.getByCy('do-type-details')
      .should('exist')
      .and('contain.text', ' DO Type: INDIA_STOCK_TRANSFER_DIFF_LE');
    cy.getByCy('change-details-btn')
      .should('exist')
      .and('have.text', CONTENT.CHANGE_DETAILS)
      .click();

    // Revalidate the details and move to CSV validation steps
    cy.getByCy('confirm-button').click();
    cy.getByCy('validate-csv-container').trigger('dragleave');
    validateCSV(
      'cypress/doValidateCsv/validateCsvSuccess.csv',
      CONTENT.CSV_FILE_SUCCESSFULLY_VALIDATED
    );
    cy.getByCy('orderDetails-container').should('exist');
    cy.getByCy('total-so-value-content').should('exist');
    cy.getByCy('total-so-value-content').should('have.text', CONTENT.TOTAL_SO_VALUE);
    cy.getByCy('total-so-value').should('exist');
    cy.getByCy('total-so-value').should(
      'have.text',
      `₹ ${validateCsvSuccess.data.pidListing.reduce(
        (acc, curr) => acc + curr.quantity * curr.price,
        0
      )}`
    );
    cy.getByCy('total-pid-content').should('have.text', CONTENT.TOTAL_PID);
    cy.getByCy('total-pid').should('have.text', validateCsvSuccess.data.pidListing.length);
    cy.getByCy('total-qty-content').should('have.text', CONTENT.TOTAL_QTY);
    cy.getByCy('total-qty').should(
      'have.text',
      validateCsvSuccess.data.pidListing.reduce((acc, curr) => acc + curr.quantity, 0)
    );
    cy.getByCy('create-order-btn').should('exist');
    cy.getByCy('create-order-btn').should('have.text', CONTENT.CREATE_ORDER);
    cy.getByCy('create-order-btn').click();
    cy.getByCy('success-message-container').should('exist');
    cy.getByCy('success-image').should('exist');
    cy.getByCy('success-image').should(
      'have.attr',
      'src',
      `${import.meta.env.VITE_STATIC_SERVER}/images/success-icon.png`
    );
    cy.getByCy('success-message').should('exist');
    cy.getByCy('success-message').should('have.text', CONTENT.ORDER_CREATED_SUCCESSFULLY);
    cy.getByCy('do-number').should('have.text', `${CONTENT.DO_NUMBER}:`);
    cy.getByCy('doNumber').should('have.text', orderPlacement.data);
    cy.getByCy('create-new-order').should('exist');
    cy.getByCy('create-new-order').should('have.text', CONTENT.CREATE_NEW_ORDER);
    cy.getByCy('create-new-order').click();
    cy.findByRole('combobox', { name: /Select Customer/i }).should('have.text', '');
    cy.get('#po-number').should('have.text', '');
    cy.getByCy('confirm-button').should('be.disabled');
  });
});
