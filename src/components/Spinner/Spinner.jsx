import React from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import { Box } from '@mui/material';

/**
 *
 * @typedef {import('@mui/material').BoxProps} BoxProps
 */

/**
 *
 * @typedef {Object} SpinnerProps
 * @property {string} [progressClassname]
 * @property {string} [size]
 */

/**
 * @param {SpinnerProps & BoxProps} rest
 */
const Spinner = ({ progressClassname = null, size = '25px', ...boxProps }) => (
  <Box data-cy="spinner" {...boxProps}>
    <CircularProgress size={size} className={progressClassname} />
  </Box>
);

export default Spinner;
