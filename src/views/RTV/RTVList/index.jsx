import Box from '@mui/material/Box';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { useDispatch, useSelector } from 'react-redux';
import useFilterHook from 'components/common/useFilterHook';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  RTV_LIST,
  approveRejectGatepassLoad,
  approveRejectGatepassReset,
  exportGatepassListLoad,
  getRTVListLoad
} from 'redux/reducers/RTVlist.slice';
import RTVListHeader from './RTVListHeader';
import RTVListBody from './RTVListBody';

const initialRequest = {
  type: 'gatepass',
  pageRequest: { pageNumber: 0, pageSize: 35, sortKey: 'created_at', sortOrder: 'DESC' }
};

const RTVgatepassList = () => {
  const location = useLocation();
  const initialReq = useRef(false);
  const [requestBody, setRequestBody] = useState(initialRequest);

  const dispatch = useDispatch();
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.RTV_GATEPASS);
  const approveRejectSuccess = useSelector((state) => state[RTV_LIST].approveReject.isSuccess);

  const dispatchListLoadFun = () => dispatch(getRTVListLoad(requestBody));

  useEffect(() => {
    if (approveRejectSuccess) {
      dispatchListLoadFun();
      dispatch(approveRejectGatepassReset());
    }
  }, [approveRejectSuccess]);

  useEffect(() => {
    if (initialReq.current) {
      dispatchListLoadFun();
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current = true;
    const queryFilter = getAllQueryParam(window.location?.search);
    const payload = generatePayloadForSearchAPI(queryFilter, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => {
      const updatedRequest = {
        type: 'gatepass',
        pageRequest: { ...prevReqBody.pageRequest },
        ...payload
      };

      return {
        ...updatedRequest
      };
    });
  }, [location?.search, dispatch]);

  const fetchMoreData = (sortOrder, sortKey, pageNumber) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNumber,
        sortKey,
        sortOrder
      }
    }));
  };

  const approvalRejectFun = (status, gatepass_num, reason) => {
    dispatch(
      approveRejectGatepassLoad({
        status,
        gatepass_num,
        reason
      })
    );
  };

  const exportFun = () => dispatch(exportGatepassListLoad(requestBody));

  return (
    <Box sx={{ padding: '24px', height: 'calc(100vh - 65px)', background: '#ffff' }}>
      <RTVListHeader CONTENT={CONTENT} />
      <RTVListBody
        fetchMoreData={fetchMoreData}
        selectedFilterList={selectedFilterList}
        onChangeFilterList={onChangeFilterList}
        CONTENT={CONTENT}
        approvalRejectFun={approvalRejectFun}
        deleteSelectedFilter={deleteSelectedFilter}
        resetFilters={resetFilters}
        exportFun={exportFun}
      />
    </Box>
  );
};

export default RTVgatepassList;
