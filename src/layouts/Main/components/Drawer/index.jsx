import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import Drawer from '@mui/material/Drawer';
import Box from '@mui/material/Box';
import MenuIcon from '@mui/icons-material/Menu';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { MenuItem, MenuList, Skeleton } from '@mui/material';
import Collapse from '@mui/material/Collapse';
import LkInput from 'components/MaterialUi/LkInput';
import { selectNavigationData } from 'redux/reducers/consul';
import Version from '../../../../Version';

const useStyles = makeStyles()({
  paper: {
    width: 260,
    background: '#333333',
    display: 'flex',
    justifyContent: 'space-between'
  },
  title: {
    fontWeight: 'bold',
    fontSize: 20,
    color: '#FFFFFF',
    display: 'flex',
    justifyContent: 'space-between'
  },
  list: {
    padding: '16px 0'
  },
  arrowBackIcon: {
    fontSize: 13,
    position: 'relative',
    top: -5,
    left: 6
  },
  inputBox: {
    borderRadius: 4,
    border: '1px solid #DDDDDD'
  },
  input: {
    color: '#DDDDDD',
    fontSize: 12,
    padding: 0,
    paddingRight: 9,
    height: 30
  },
  lispan: {
    width: 4,
    height: 4,
    marginRight: 5,
    borderRadius: 50
  },
  menuItem: {
    paddingTop: 0,
    paddingBottom: 0
  },
  menuItemSelected: {
    background: 'black',
    color: '#00BAC6'
  },
  version: {
    color: '#FFFFFF',
    fontSize: 15,
    marginBottom: 24,
    marginLeft: 24
  },
  skeletonColor: {
    backgroundColor: '#ffffff36'
  }
});

// @TODO Move it to common place instead of importing in every View
const LeftDrawer = ({ openDrawer, handleClose, selectedFacility }) => {
  const { pathname } = useLocation();
  const { classes, cx } = useStyles();
  const { navigationDataLoading, navigationData } = useSelector(selectNavigationData);
  const [searchMenu, setSearchMenu] = useState('');
  const [menuListData, setMenuListData] = useState(navigationData);
  const [selectedMenuList, setSelectedMenuList] = useState([]);

  const EXTERNAL_LINKS = [
    {
      href: `https://${
        window.location.host
      }/search/redirect/reversereceiving?facilityCode=${selectedFacility}`,
      target: '_blank',
      name: 'Reverse Receiving'
    }
  ];

  useEffect(() => {
    setMenuListData(navigationData);
  }, [navigationData]);

  const handleMenuListClick = (key) => {
    setSelectedMenuList((prevSelectedMenuList) => {
      if (prevSelectedMenuList.includes(key)) {
        return prevSelectedMenuList.filter((eachKey) => eachKey !== key);
      }
      return [...prevSelectedMenuList, key];
    });
  };

  const searchOnChildren = (navData, trimmedSearchStr) => {
    let filteredData = [];
    navData.forEach((eachNavLink) => {
      if (
        eachNavLink?.children?.length > 0 &&
        !eachNavLink.name.toLowerCase().includes(trimmedSearchStr)
      ) {
        const filteredChildren = searchOnChildren(eachNavLink?.children, trimmedSearchStr);
        if (filteredChildren.length > 0) {
          filteredData = [...filteredData, { ...eachNavLink, children: filteredChildren }];
        }
      } else if (eachNavLink.name.toLowerCase().includes(trimmedSearchStr)) {
        filteredData = [...filteredData, eachNavLink];
      }
    });

    return filteredData;
  };

  useEffect(() => {
    const trimmedSearchStr = searchMenu.trim().toLocaleLowerCase();
    let filteredNav = navigationData;
    if (trimmedSearchStr !== '') {
      filteredNav = searchOnChildren(navigationData, trimmedSearchStr);
    }
    setMenuListData(filteredNav);
  }, [searchMenu]);

  const goToHref = (item) => {
    window.location.href = `${window.location.origin}${item.href}`;
    handleClose(true);
  };

  const renderEachListItem = (itemData) => {
    if (Array.isArray(itemData?.children)) {
      const parentSelected = itemData.children.some((t) => pathname === t.path);
      return (
        <Box key={itemData.id}>
          <MenuItem
            className={classes.menuItem}
            data-cy={itemData.id}
            onClick={() => handleMenuListClick(itemData.id)}
          >
            <Box
              display="flex"
              pl={1}
              pr={1}
              height={40}
              justifyContent="space-between"
              alignItems="center"
              flex={1}
              className="text-white"
            >
              <Box className={`${parentSelected ? 'fw-bold' : ''} `}>{itemData.name}</Box>
              <img
                className="image-container"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/arrow-down.svg`}
                alt="img"
              />
            </Box>
          </MenuItem>
          <Collapse in={selectedMenuList.includes(itemData.id)}>
            {itemData.children.map((listItems) => {
              const isSelected = pathname === listItems.path;
              if (Array.isArray(listItems?.children)) {
                return (
                  <Box pl={2} key={listItems.id}>
                    {renderEachListItem(listItems)}
                  </Box>
                );
              }
              let itemProps = {
                onClick: () => goToHref(listItems)
              };
              if (listItems.path) {
                itemProps = {
                  component: Link,
                  to: listItems.path,
                  onClick: () => handleClose(true)
                };
              }
              return (
                <MenuItem
                  key={listItems.id}
                  data-cy={listItems.id}
                  className={cx({
                    [classes.menuItem]: true,
                    [classes.menuItemSelected]: isSelected
                  })}
                  {...itemProps}
                >
                  <Box
                    pl={2}
                    height={30}
                    display="flex"
                    alignItems="center"
                    className={isSelected ? 'text-primary' : 'text-white'}
                  >
                    <Box
                      className={classes.lispan}
                      style={{ background: isSelected ? '#00BAC6' : 'white' }}
                    />
                    {listItems.name}
                  </Box>
                </MenuItem>
              );
            })}
          </Collapse>
        </Box>
      );
    }
    return (
      <MenuItem
        key={itemData.id}
        data-cy={itemData.id}
        className={cx({
          [classes.menuItem]: true,
          [classes.menuItemSelected]: pathname === itemData.path
        })}
        onClick={() => handleClose(true)}
        component={Link}
        to={itemData.path}
      >
        <Box
          pl={1}
          height={30}
          alignItems="center"
          display="flex"
          className={`${pathname === 'monitor' ? 'text-primary fw-bold' : 'text-white'}`}
        >
          {itemData.name}
        </Box>
      </MenuItem>
    );
  };

  const loadSkeletons = (numberOfSkeletons) => {
    const skeltonArray = Array(numberOfSkeletons).fill(null);
    return skeltonArray.map((_, index) => (
      // eslint-disable-next-line react/no-array-index-key
      <Box p={2} key={index}>
        <Skeleton
          variant="text"
          width="100%"
          animation="wave"
          height={35}
          className={classes.skeletonColor}
        />
        <Skeleton
          variant="text"
          width="80%"
          animation="wave"
          height={25}
          className={classes.skeletonColor}
        />
        <Skeleton
          variant="text"
          width="80%"
          animation="wave"
          height={25}
          className={classes.skeletonColor}
        />
      </Box>
    ));
  };

  const list = () => (
    <Box className={classes.list}>
      <Box pl={3} pr={3}>
        <Box className={classes.title}>
          <Link to="/dashboard">
            <Box> NexS </Box>
          </Link>
          <Box
            className="cursor-pointer"
            data-cy="main-navigation-close"
            onClick={() => handleClose(false)}
          >
            <ArrowBackIosIcon className={classes.arrowBackIcon} />
            <MenuIcon />
          </Box>
        </Box>
        <Box mt={5}>
          <LkInput
            className={classes.inputBox}
            value={searchMenu}
            onChange={(e) => setSearchMenu(e.target.value)}
            fullWidth
            size="small"
            placeholder="Search"
            InputProps={{
              className: classes.input,
              endAdornment: (
                <img
                  className="image-container"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/search.svg`}
                  alt=""
                />
              )
            }}
          />
        </Box>
      </Box>
      <Box mt={2}>
        <MenuList>
          {navigationDataLoading ? loadSkeletons(3) : menuListData.map(renderEachListItem)}
          {EXTERNAL_LINKS.map((item) => (
            <MenuItem
              key={item.name}
              className={classes.menuItem}
              onClick={() => handleClose(true)}
              component="a"
              href={item.href}
              target={item.target || ''}
            >
              <Box pl={1} height={30} alignItems="center" display="flex" className="text-white">
                {item.name}
              </Box>
            </MenuItem>
          ))}
        </MenuList>
      </Box>
    </Box>
  );

  return (
    <div>
      <Drawer
        classes={{ paper: classes.paper }}
        open={openDrawer}
        onClose={() => handleClose(false)}
      >
        <Box>{list()}</Box>
        <Box className={classes.version}>
          <Version />
        </Box>
      </Drawer>
    </div>
  );
};

export default LeftDrawer;
