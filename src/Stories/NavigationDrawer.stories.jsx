import React from 'react';
import NavigationDrawer from 'layouts/Main/components/Drawer/NavigationDrawer';
import { useArgs } from '@storybook/preview-api';
import LKButton from 'components/Button/LKButton';
import { Box } from '@mui/material';

export default {
  title: 'Components/NavigationDrawer',
  component: NavigationDrawer,
  args: {
    modalwidth: '500px',
    anchor: 'right',
    heading: 'Drawer title',
    openDrawer: false,
    disableEnforceFocus: false,
    children: (
      <Box mt={1} mx={3}>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
        labore et dolore magna aliqua. Iaculis nunc sed augue lacus viverra vitae. Malesuada
        pellentesque elit eget gravida cum sociis. Pretium vulputate sapien nec sagittis aliquam
        malesuada bibendum arcu. Cras adipiscing enim eu turpis egestas. Ut lectus arcu bibendum at
        varius. Nulla pellentesque dignissim enim sit amet venenatis urna. Tempus urna et pharetra
        pharetra massa massa ultricies mi quis. Vitae congue mauris rhoncus aenean. Enim ut tellus
        elementum sagittis vitae et.
      </Box>
    )
  },
  argTypes: {
    handleClose: { action: 'handleClose' },
    anchor: {
      control: 'select',
      options: ['right', 'left', 'top', 'bottom']
    }
  }
};

const Template = (args) => {
  const [, setArgs] = useArgs();

  const handleClose = () => {
    setArgs({ ...args, openDrawer: false });
  };

  const handleOpen = (anchor) => {
    setArgs({ ...args, openDrawer: true, anchor });
  };

  return (
    <>
      <LKButton onClick={()=>handleOpen('right')}>RIGHT</LKButton>
      <LKButton onClick={()=>handleOpen('left')}>LEFT</LKButton>
      <LKButton onClick={()=>handleOpen('top')}>TOP</LKButton>
      <LKButton onClick={()=>handleOpen('bottom')}>BOTTOM</LKButton>
      <NavigationDrawer {...args} handleClose={handleClose} />
    </>
  );
};

export const Drawer = Template.bind({});
