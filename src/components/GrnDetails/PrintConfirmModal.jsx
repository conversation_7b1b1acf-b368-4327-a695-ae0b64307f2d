import React from 'react'
import LkModal from 'components/Modal/Modal'
import { Box, MenuItem } from '@mui/material'
import LkSelect from 'components/MaterialUi/LkSelectBox'

const PrintConfirmModal  = ({open,handleClose,printFun}) =>{

    const handleClosefun = () =>{
        handleClose()
        // window.print()

    }
    return <LkModal
    open={open}
    handleClose={handleClose}
    title="Print Options"
    subTitle="Please select from various options below:"
    modalWidth="520px"
    modalHeight="334px"
    primaryBtnText="Print"
    primaryBtn={printFun}
    >
        <Box width="270px" margin="0 auto" mt={6}> 

            <LkSelect style={{ height: 40 }} label={false} fullWidth={true} value={0} >
                    <MenuItem value={0}>{"GRN"}</MenuItem>
                    
                </LkSelect>
        </Box>
    </LkModal>
}

export default PrintConfirmModal