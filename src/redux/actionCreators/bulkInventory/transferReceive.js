import * as action from '../../actionTypes/bulkInventory'

// Transfer List
export const transferListLoad = (data) => ({ type: action.TRANSFER_RECEIVE_LIST_LOAD, payload: data });
export const transferListSuccess = (data) => ({ type: action.TRANSFER_RECEIVE_LIST_SUCCESS, payload: data });
export const transferListFail = (data) => ({ type: action.TRANSFER_RECEIVE_LIST_FAIL, payload: data });
export const transferListReset = (data) => ({ type: action.TRANSFER_RECEIVE_LIST_RESET, payload: data });


//  GATEPASS_SCAN

export const gatePassScanReset = (data) => ({ type: action.GATEPASS_SCAN_RESET, payload: data });
export const gatePassScanLoad = (data) => ({ type: action.GATE_PASS_SCAN_LOAD, payload: data });
export const gatePassScanSuccess = (data) => ({ type: action.GATEPASS_SCAN_SUCCESS, payload: data });
export const gatePassScanFail = (data) => ({ type: action.GATEPASS_SCAN_FAIL, payload: data });

