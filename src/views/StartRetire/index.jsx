import React, { useEffect, useRef, useState } from 'react';

import { useLocation } from 'react-router-dom';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { getStartRetireListLoad } from 'redux/reducers/startRetire';
import { useDispatch } from 'react-redux';
import { omit } from 'utils/lodash';
import useFilterHook from 'components/common/useFilterHook';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';
import StartRetireBody from './StartRetireBody';
import StartRetireHeader from './StartRetireHeader';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    height: 'calc(100vh - 65px)'
  }
}));

const initialRequest = {
  pageRequest: {
    pageNumber: 0,
    pageSize: 35,
    sortKey: 'createdAt',
    sortOrder: 'DESC'
  }
};

const StartRetire = () => {
  const location = useLocation();
  const { classes } = useStyles();
  const initialReq = useRef(false);
  const [requestBody, setRequestBody] = useState(initialRequest);
  const dispatch = useDispatch();
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter } = useFilterHook();

  const dispatchListLoadFun = () => dispatch(getStartRetireListLoad(requestBody));

  useEffect(() => {
    if (initialReq.current) {
      dispatchListLoadFun();
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current = true;
    const queryFilter = getAllQueryParam(window.location.search);
    const payload = generatePayloadForSearchAPI(queryFilter, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => {
      let upldatedRequest = { pageRequest: { ...prevReqBody.pageRequest }, ...payload };
      if (prevReqBody.barcode) {
        // sending barcode
        upldatedRequest = {
          ...upldatedRequest,
          barcode: prevReqBody.barcode
        };
      }
      return {
        ...upldatedRequest
      };
    });
  }, [location.search, dispatch]);

  const fetchMoreData = (sortOrder, sortKey, pageNumber) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNumber,
        sortKey,
        sortOrder
      }
    }));
  };

  const onGlobalSearch = (barcode) => {
    if (barcode) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        pageRequest: {
          ...prevReqBody.pageRequest,
          pageNumber: 0
        },
        barcode
      }));
    } else {
      setRequestBody((prevReqBody) => ({ ...omit(prevReqBody, 'barcode') }));
    }
  };

  return (
    <Box className={classes.root}>
      <StartRetireHeader
        onSearch={onGlobalSearch}
        selectedFilterList={selectedFilterList}
        deleteSelectedFilter={deleteSelectedFilter}
        fetchMoreListItems={dispatchListLoadFun}
      />
      <StartRetireBody
        fetchMoreData={fetchMoreData}
        selectedFilterList={selectedFilterList}
        onChangeFilterList={onChangeFilterList}
      />
    </Box>
  );
};

export default StartRetire;
