import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import ProductBarcodes from './ProductBarcodes';

const BarcodesContainer = (props) => {
  const {
    setUnicomOrderCode,
    renderData,
    setRenderData
  } = props;
  const { shipment, products } = useSelector((state) => state.order);

  const [productData, setProductData] = useState('');
  const [currBarcode, setCurrBarcode] = useState('');
  const [selectedIndex, setSelectedIndex] = useState('');

  useEffect(() => {
    const selectedProduct = shipment[0];
    const selectedBarcode = selectedProduct?.product?.order?.barcode;
    products.forEach((itemGroup, index) => {
      if (selectedBarcode === itemGroup.order.barcode) {
        setSelectedIndex(index);
        setProductData(selectedProduct);
        setCurrBarcode(selectedBarcode);
        setUnicomOrderCode(selectedProduct.product.unicomOrderCode);
      }
    });
  }, [shipment, products]);

  return (
    <div>
      <ProductBarcodes
        setRenderData={setRenderData}
        renderData={renderData}
        productIndex={selectedIndex}
        productData={productData}
        selectedBarcode={currBarcode}
      />
    </div>
  );
};

export default BarcodesContainer;
