import React from 'react';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import { keys } from 'utils/lodash';

const useStyles = makeStyles()({
  toolTip: {
    maxWidth: 756
  }
});

const PIDDetailsToolTip = ({
  pid,
  data,
  imageUrl,
  name,
  classification,
  productUrl,
  isShowOnlyDesc = false
}) => {
  const { classes, cx } = useStyles();

  if (isShowOnlyDesc) {
    return (
      <Box width="350px" padding="10px">
        <Box className="fs14 text-center">{name}</Box>
      </Box>
    );
  }
  return (
    <div className={`display-flex ${classes.toolTip}`}>
      {classification !== 11356 && (
        <div className="pd-t10 pd-b20 pd-l20 pd-r20 flex1">
          <Box className="image-containers">
            <img className="image-container" src={imageUrl} alt="img" />
          </Box>
          <div className="mr-t5 fs14 text-center">{name}</div>
        </div>
      )}
      <div className="flex1 pd-15 border-left-ee">
        {classification === 11356 && <div className="fs14 mr-b8 text-center">{name}</div>}
        {keys(data.powers).length > 0 && (
          <div
            className={`display-flex justify-content-space-around mr-b20  
                mr-t8 border-grey5-radiusbase pd-10`}
          >
            {keys(data.powers).map((key) => (
              <div key={key}>
                <div className="fs12 text-66">{key}</div>
                <div className="fs14 text-a2 fw-bold">{data.powers[key] ?? '-'}</div>
              </div>
            ))}
          </div>
        )}
        <div className="display-flex justify-content-space-between pd-b15 border-bottom-ee">
          <div className="fs12">PID</div>
          <div className="fs14 fw-bold text-turquioise_surf pd-r10">
            <a href={productUrl} target="_blank" rel="noopener noreferrer">
              {pid}
            </a>
          </div>
        </div>

        {data.obj &&
          keys(data.obj).map((key) => (
            <div
              key={key}
              className={cx('display-flex justify-content-space-between pd-t12 gap10', {
                'pd-b12 border-bottom-ee': key !== 'Category'
              })}
            >
              <div className="fs12">{key}</div>
              <div className="fs14 fw-bold pd-r10">{data.obj[key] ?? '-'}</div>
            </div>
          ))}
      </div>
    </div>
  );
};

export default PIDDetailsToolTip;
