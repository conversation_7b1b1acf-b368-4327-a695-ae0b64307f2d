import React from 'react';
import { Box } from '@mui/material';
import { ThemeProvider } from '@mui/material';
import theme from '../src/theme/index.js';
import '../src/styles/index.scss';

export default {
  decorators: [
    (Story) => (
      <ThemeProvider theme={theme}>
        <Box display="flex" justifyContent="center">
          <Story />
        </Box>
      </ThemeProvider>
    )
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    }
  }
};
