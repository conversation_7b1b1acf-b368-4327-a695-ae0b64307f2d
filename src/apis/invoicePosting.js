import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getInvoicePostingList = (payload) =>
  tokenInstance().post(config.invoicePosting.list, payload);

export const createInvoicePostingApi = (payload) =>
  tokenInstance().post(config.invoicePosting.createInvoice, payload);

export const updateInvoicePostingApi = (payload) =>
  tokenInstance().put(config.invoicePosting.createInvoice, payload);

export const getInvoiceById = (payload) =>
  tokenInstance().get(`${config.invoicePosting.createInvoice}/${payload}`);

export const processInvoiceById = (payload) =>
  tokenInstance().post(`${config.invoicePosting.processInvoice}/${payload}`);
