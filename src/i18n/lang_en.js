const en = {
  NEXS: 'NexS',
  NEXS_PANEL: 'NexS',
  ENGLISH: 'English',
  HINDI: 'Hindi',
  CHINESE: 'Chinese',
  QC_PANEL: 'Quality Check Panel',
  SCAN_PRODUCT: 'Scan Product',
  SCAN_PRODUCT_TO_START_QC: 'Scan product barcode below to start QC',
  SIDEBAR_QUALITY_CHECK: 'Quality Check',
  SIDEBAR_AUTO_QUALITY_CHECK: 'Auto Quality Check',
  SIDEBAR_RETURN_PUTAWAY_FRANCHISE: 'Return Putaway Franchise',
  SIDEBAR_RETURN_PUTAWAY_OWN_STORE: 'Return Putaway Own Store',
  SIDEBAR_POWER_CARD: 'Power Card',
  SIDEBAR_BIN_BARCODE_TRACKER: 'Bin Barcode Tracker',
  SIDEBAR_PICKING: 'Picking',
  SIDEBAR_MERGE_PICKED_ITEMS: 'Merge Picked Items',
  SIDEBAR_PICK_SUMMARY: 'Pick Summary',
  SIDEBAR_SKIP_PICKING: 'Skip Picking',
  ORDER_ORIGIN: 'Order Origin',
  ORDER_ID: 'Order ID',
  SHIPMENT_ID: 'Shipment ID',
  ORDER_TYPE: 'Order Type',
  CHANNEL: 'Channel',
  DISPATCH: 'Dispatch',
  HIGH_VALUE_ORDER: 'High Value Order',
  FACILITY: 'FACILITY',
  TODO_SUMMARY: 'ToDo Summary',
  HOLD: 'HOLD',
  UNHOLD: 'UNHOLD',
  REASON_FOR_HOLD: 'Reason For Hold?',
  ENTER_REASON_FOR_HOLDING: 'Please enter your reason for holding the product',
  PRIMARY_REASON: 'Primary Reason',
  SECONDARY_REASON: 'Secondary Reason',
  TERTIARY_REASON: 'Tertiary Reason',
  SELECT: 'Select',
  UNCLASSIFIED: 'Unclassified',
  CANCEL: 'CANCEL',
  SUBMIT: 'SUBMIT',
  ARE_YOU_SURE: 'Are you sure?',
  ARE_YOU_SURE_TO_UNHOLD_ITEM: 'Are you sure you want to unhold this item?',
  ARE_YOU_SURE_TO_FAIL_ITEM: 'Are you sure you want to mark this item as fail?',
  YES: 'YES',
  QC_COMPLETE: 'QC Completed Successfully!',
  QC_ON_HOLD: 'Order is on Hold!',
  QC_FAIL: 'Order Failed!',
  OC_PENDING: 'Order is Pending!',
  QC_COMPLETE_OF_ALL_ITEMS_TEXT: 'Below is the summary of QC for all products in this order',
  SCAN_PRODUCT_BARCODE: 'Scan Product Barcode',
  SCAN_LENS: 'Scan Frame/Lens',
  SCAN_BARCODE_TO_START_QC: 'Scan tray barcode to start QC',
  SCAN_LENS_BARCODE: 'Scan Lens Barcode',
  SCAN_FRAME: 'Scan Frame/Lens',
  SCAN_TO_COMPLETE_QC: 'Scan to Complete QC',
  SCAN_NEXT_PRODUCT: 'Scan Next Product',
  SCAN_NEXT_PRODUCT_BARCODE: 'Scan Next Product Barcode',
  BARCODE_ALREADY_SCANNED: 'Barcode already scanned!',
  INVALID_BARCODE_SCANNED: 'Invalid barcode scanned',
  BARCODE_SCANNED_SUCCESSFULLY: 'Barcode Scanned Successfully!',
  PLEASE_SCANNED_ALL_BARCODES: 'Please Scan All Barcodes!',

  P_PANEL: 'Packing Panel',
  P_PANEL_MSLP: 'Packing Panel (MSLP)',
  CHECK_PACKING: 'CHECK PACKING',
  USERNAME: 'Username',
  EMPLOYEE_ID: 'Employee ID',
  EMAIL: 'Email',
  PACKING_MODE: 'Packing Mode',
  PRINT_SHIPMENT: 'Print Shipment',
  AUTO_PRINT: 'Auto-Print',
  AUTO_BAG: 'Auto-Bag',
  ENABLE_SCANNING: 'Enable Scanning',
  SCAN_PRODUCT_BARCODE_TO_START_PACKING: 'Scan Product Barcode to Start Packing',
  FACILITY_CODE: 'Facility Code',
  ORDER_SOURCE: 'Order Source',
  ADD_REASON_FOR_HOLD: 'Add Reason for Hold',
  ENTER_REASON_FOR_HOLDING_ORDER: 'Please enter your reason for holding the order',
  ARE_YOU_SURE_TO_UNHOLD: 'Are you sure you want to unhold?',
  ORDER_ON_HOLD: 'This order is on hold. Reason:',
  SCAN_BARCODE: 'Scan Barcode',
  RE_SCAN_BARCODE: 'Re-scan barcode',
  SCANNED_QTY: 'Scanned Qty:',
  SCANNED_ITEMS: 'Scanned Items:',
  PID: 'PID',
  MODEL: 'Model',
  COLOUR: 'Colour',
  FRAME_TYPE: 'Frame Type',
  QUANTITY: 'Quantity',
  FRAME: 'Frame',
  LENS: 'Lens',
  QTY: 'Qty',
  BRAND: 'Brand',
  CATEGORY: 'Category',
  SCAN_AGAIN_TO_COMPLETE: 'Scan Again to Complete',
  RE_SCAN_PRODUCT_BARCODE: 'Re-scan Product Barcode',
  TOTAL_QTY: 'Total Qty',
  TOTAL_ITEMS: 'Total Items',
  BARCODE: 'Barcode',
  AWB: 'AWB',
  INVALID_CODE_SCANNED: 'Invalid code scanned',
  PACKING_COMPLETE: 'Packing Done Successfully!',
  PACKING_COMPLETE_OF_ALL_ITEMS_TEXT: 'Below is the summary of all items in this order.',
  SCAN_PRODUCT_BARCODE_SHIPMENT_ID: 'Scan Product Barcode/Shipment ID',
  SCAN_FITTING_ID: 'Scan Fitting ID',
  SCAN_TRAY_ID: 'Scan Tray ID',
  SCAN_MONO_CARTON: 'Scan Mono Carton',
  SCAN_SHIPMENT_ID: 'Scan Shipment ID',
  ACCESSORIES: 'Accessories',
  LOYALTY_SERVICES: 'Loyalty Services',
  IMAGE: 'Image',
  S_NO: 'S.No.',
  PRINT_SHIPMENT_INVOICE: 'Print Shipment & Invoice',
  DO_NOT_PRINT_INVOICE: 'Do Not Print Invoice',
  FRANCHISE_ORDER: 'Franchise S2C',
  SAVE_CHANGES: 'Save Changes',
  ERR_GIVE_TO_MANUAL_PACKER: 'Please, give it to manual packer',
  NOT_VALID_PACKER: 'LoggedIn user is not a valid packer, Please assign packing mode to user',
  SYSTEM_ERROR_OCCURRED: 'System Error Occurred. Please try again.',
  FAIL: 'FAIL',
  FAILED: 'FAILED'
};

export default en;
