import { useTheme } from '@emotion/react';
import { Box } from '@mui/material';
import React from 'react';

/**
 *
 * @typedef {import('@mui/material').BoxProps} BoxProps
 */

/**
 *
 * @typedef {Object} VerticalDividerProps
 * @property {String} color
 * @property {String} borderWidth
 */

/**
 *
 * @param { VerticalDividerProps & BoxProps} rest
 */

const VerticalDivider = ({ color, borderWidth = '1px', ...rest }) => {
  const theme = useTheme();
  const borderColor = color ?? theme.palette.verticalDivider;
  return <Box borderRight={`${borderWidth} solid ${borderColor}`} {...rest} />;
};

export default VerticalDivider;
