import { useRef, useEffect } from 'react';

const useLkTableInfiniteScroll = (setNextPage, hasMoreItemsToload, isDataFetching) => {
  const firstLoadingRowRef = useRef(null);

  let observer = null;
  const options = {
    root: null,
    threshold: 0,
    rootMargin: '0px'
  };

  const callBackFunction = (entries) => {
    const [entry] = entries;
    if (entry.isIntersecting && !isDataFetching) {
      setNextPage();
    }
  };

  useEffect(() => {
    observer = new IntersectionObserver(callBackFunction, options);
    if (firstLoadingRowRef.current) {
      observer.observe(firstLoadingRowRef.current);
    }

    return () => {
      if (firstLoadingRowRef.current) {
        observer.unobserve(firstLoadingRowRef.current);
      }
    };
  }, [firstLoadingRowRef, options, isDataFetching]);

  return { firstLoadingRowRef };
};

export default useLkTableInfiniteScroll;
