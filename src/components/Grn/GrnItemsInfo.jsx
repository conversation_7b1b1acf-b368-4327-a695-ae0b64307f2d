import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import ItemInfo from '../common/ItemInfo';
import ItemDetailedInfo from '../common/ItemDetailedInfo';
import BarCodeInput from './BarCodeInput';
import useTotalCountOfItems from './useTotalCountOfItems';

const GrnItemsInfo = (props) => {
  const {
    estimatedQty,
    total_scanned,
    addPidModal,
    setOpenAddPidModal,
    toggleModal,
    pidStatus,
    clearSearch,
    min_to_scan,
    createGrnLocal
  } = props;
  const { meta, pidDetail, QCData, qcDetailsSuccess } = useSelector((state) => state.grn);

  const countInfo = useTotalCountOfItems(QCData);
  const [infoData, setInfoData] = useState([]);
  const [minToScan, setMinToScan] = useState(min_to_scan);
  let expiryDetails = false;
  if (pidDetail && meta && pidDetail?.result?.data) {
    const { categoryId } = pidDetail.result.data;

    expiryDetails = categoryId === 19153 || categoryId === 11354;
  }

  useEffect(() => {
    if (qcDetailsSuccess?.result?.data) {
      const { min_scan_quantity } = qcDetailsSuccess.result.data;
      setMinToScan(min_scan_quantity);
    }
  }, [qcDetailsSuccess]);

  useEffect(() => {
    if (countInfo) {
      setInfoData(countInfo);
     
    }
  }, [countInfo]);

  return (
    <>
      <div className="display-flex">
        <ItemInfo
          subText={createGrnLocal.TOTAL_ITEMS}
          pidStatus={pidStatus}
          text={estimatedQty}
          totalScanned={total_scanned}
          toggleModal={toggleModal}
        />
        <div className="mr-l16">
          <ItemInfo subText={createGrnLocal.MIN_TO_SCAN} pidStatus={pidStatus} text={minToScan} />
        </div>
        <div className="mr-l16 flex1">
          <ItemDetailedInfo pidStatus={pidStatus} infoData={infoData} />
        </div>
      </div>
      {/* 

            if both category and prefilled invice  show expirydetails
              example caterory sunglass dont have ,contactglass wil have expiry details
            based on prefilled invice we are making asn api call
                 */}
      <BarCodeInput
        pidStatus={pidStatus}
        toggleAddPidModal={addPidModal}
        toggleAddMoreItemModal={toggleModal}
        setOpenAddPidModal={setOpenAddPidModal}
        is_box_required={pidDetail?.result?.data?.is_box_required}
        prefilled_invoice={meta?.invoice?.prefilled_invoice}
        expiryDetails={expiryDetails}
        clearSearch={clearSearch}
      />
    </>
  );
};
export default GrnItemsInfo;
