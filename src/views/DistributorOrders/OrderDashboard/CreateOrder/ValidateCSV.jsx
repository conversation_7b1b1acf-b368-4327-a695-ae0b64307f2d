import React, { useEffect, useRef, useState } from 'react';
import Box from '@mui/material/Box';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import Button from '@mui/material/Button';
import { useDispatch, useSelector } from 'react-redux';
import { LOGIN } from 'redux/reducers/login';
import {
  DISTRIBUTOR_ORDERS,
  getValidateCSVLoad,
  getValidateCSVReset
} from 'redux/reducers/distributorOrders';
import { makeStyles } from 'tss-react/mui';
import UploadedFileDisplay from './components/UploadedFileDisplay';
import FileUploadButton from './components/FIleUploadButton';

const useStyles = makeStyles()(() => ({
  fileUploadButton: {
    cursor: 'pointer',
    color: '#3C3C3C',
    fontSize: '16px',
    border: '1px dashed #dedede',
    width: '60%',
    height: '17%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    borderRadius: '12px',
    boxShadow: '0 2px 1px #00000033, 0 1px 1px #00000024, 0 1px 3px #0000001F'
  },
  required: {
    color: '#e31a0d',
    fontSize: '12px',
    paddingTop: '8px',
    textTransform: 'uppercase',
    fontWeight: '400',
    paddingLeft: '4px'
  },
  fileName: {
    background: '#eef7ee'
  },
  colorBlack: {
    color: 'black'
  },
  successMessage: {
    color: '#3B873E',
    fontWeight: '500'
  },
  fileDrag: {
    backgroundColor: '#DDDDDD',
    border: '2px dashed #3c3c3c'
  },
  errorFile: {
    background: '#ffedec'
  },
  clearIcon: {
    fontSize: '16px',
    position: 'absolute',
    top: '-12px',
    right: '10px'
  }
}));

const ValidateCSV = ({ orderDetails, setActiveStep, isSuccess, CONTENT }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const fileInputRef = useRef(null);

  const [fileName, setFileName] = useState('');
  const [file, setFile] = useState('');
  const [dragging, setDragging] = useState(false);

  const { pidListing, csvErrors } = useSelector((state) => state[DISTRIBUTOR_ORDERS].csvData);
  const empCode = useSelector((state) => state[LOGIN].user.userDetail?.empCode);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);

  const sampleCSV = [['Item PID', 'Item Name', 'Item Type', 'Qty', 'Selling Price']];

  let csvData = '';
  sampleCSV.forEach((value, index) => {
    if (index === 0) {
      csvData = `${value.join()}\n`;
    }
  });

  const csvFile = new Blob([csvData], { type: 'application/csv' });

  const handleFileUpload = (event) => {
    const uploadedFile = event.target.files[0];
    if (uploadedFile && uploadedFile.type === 'text/csv') {
      setFile(uploadedFile);
      setFileName(uploadedFile.name);
    }
    const inputFile = event.target;
    inputFile.value = '';
  };

  const onDrop = (event) => {
    event.preventDefault();
    const droppedFiles = event.dataTransfer.files;
    setFile(droppedFiles[0]);
    setFileName(droppedFiles[0].name);
    setDragging(false);
  };

  const onDragOver = (event) => {
    event.preventDefault();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(false);
  };

  const csvHeaderConfig = [
    {
      name: CONTENT.PID,
      key: 'productId',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: CONTENT.QUANTITY,
      key: 'quantity',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: CONTENT.UNIT_PRICE_WITH_TAX,
      key: 'price',
      style: { minWidth: 150, maxWidth: 150 },
      align: 'center'
    }
  ];

  const csvErrorHeaderConfig = [
    {
      name: 'CSV Row No',
      key: 'line',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: 'PID',
      key: 'productId',
      align: 'center',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: 'Error Found',
      key: 'error',
      align: 'center',
      style: { minWidth: 200, maxWidth: 200 }
    }
  ];

  useEffect(() => {
    if (pidListing?.length > 0) {
      setActiveStep(2);
    }
  }, [pidListing]);

  const validateCSV = () => {
    if (file) {
      const formData = new FormData();
      formData.set('csvFile', file);
      formData.append('facilityCode', facilityCode);
      formData.append('poNumber', orderDetails.poNumber);
      formData.append('customerCode', orderDetails.customer.code);
      formData.append('doType', orderDetails.doType);
      formData.append('userName', empCode);
      dispatch(getValidateCSVLoad(formData));
    }
  };

  const removeFile = () => {
    setActiveStep(1);
    setFile('');
    setFileName('');
    dispatch(getValidateCSVReset());
  };

  const changeCSVHandler = () => {
    removeFile();

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
      fileInputRef.current.click();
    }
  };

  return (
    <>
      {/* Added <input /> here so that it can be used by both 
      FileUploadButton and FileUploadDisplay */}
      <input
        id="fileInput"
        onChange={handleFileUpload}
        className="display-none"
        type="file"
        accept=".csv,text/csv"
        ref={fileInputRef}
      />
      {!fileName ? (
        <FileUploadButton
          onDrop={onDrop}
          onDragOver={onDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          dragging={dragging}
          classes={classes}
          CONTENT={CONTENT}
          csvFile={csvFile}
          handleFileUpload={handleFileUpload}
        />
      ) : (
        <UploadedFileDisplay
          fileName={fileName}
          pidListing={pidListing}
          csvErrors={csvErrors}
          classes={classes}
          CONTENT={CONTENT}
          removeFile={removeFile}
        />
      )}
      <Box className="pd-t25" width="60%">
        {!pidListing?.length > 0 ? (
          <Button
            data-cy="validate-csv-btn"
            fullWidth
            color="primary"
            type="submit"
            variant="contained"
            onClick={validateCSV}
            disabled={!fileName}
          >
            {CONTENT.VALIDATE_CSV}
          </Button>
        ) : (
          <Button
            fullWidth
            color="primary"
            type="submit"
            variant="outlined"
            onClick={changeCSVHandler}
            disabled={isSuccess}
          >
            {CONTENT.CHANGE_CSV}
          </Button>
        )}
      </Box>
      <Box
        width="60%"
        className="fs12 pd-t15 display-flex flex-direction-column align-items-baseline"
      >
        <Box>1. {CONTENT.ALL_COLUMN_FIELDS_IN_CSV_REQUIRED}.</Box>
        <Box>2. {CONTENT.THERE_SHULD_BE_NO_TYPING_ERR}. </Box>
        <Box>3. {CONTENT.DATA_TYPE_SHOULD_BE_CORRECT}.</Box>
        <Box>4. {CONTENT.ONLY_SINGLE_FILE_ALLOWED}.</Box>
        <Box>5. {CONTENT.MAXIMUM_FILE_SIZE_FIVE_MB}.</Box>
      </Box>
      <Box className="display-flex flex-direction-column align-items-baseline pd-t20">
        {(pidListing?.length > 0 || csvErrors?.length > 0) && (
          <>
            <Box
              data-cy="csv-validation-message"
              width="100%"
              className={
                pidListing?.length > 0 ? `${classes.successMessage} fs12` : classes.required
              }
            >
              {pidListing?.length > 0
                ? CONTENT.CSV_FILE_SUCCESSFULLY_VALIDATED
                : CONTENT.ERRORS_FOUND_IN_CSV}
            </Box>
            <Box data-cy="table-container" className="pd-t10">
              <LkTable
                rowSize={60}
                tableData={pidListing || csvErrors}
                tableHeight={400}
                totalRowsCount={pidListing?.length || csvErrors?.length}
                headerConfig={pidListing?.length > 0 ? csvHeaderConfig : csvErrorHeaderConfig}
                showTableCount={false}
              />
            </Box>
          </>
        )}
      </Box>
    </>
  );
};

export default ValidateCSV;
