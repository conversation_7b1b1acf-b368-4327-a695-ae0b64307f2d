import React from 'react';
import { Input } from '@mui/icons-material';
import { Button } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import LabeledBox from './LabeledBox';

const useStyles = makeStyles()(() => ({
  inputButton: {
    transform: 'rotate(-90deg)'
  },
  buttonText: {
    alignSelf: 'center'
  }
}));
const UploadBox = (props) => {
  const { classes } = useStyles();
  const { label, className } = props;
  return (
    <LabeledBox className={className} label={label}>
      <Button color="primary" disabled startIcon={<Input className={classes.inputButton} />}>
        UPLOAD
      </Button>
    </LabeledBox>
  );
};

export default UploadBox;
