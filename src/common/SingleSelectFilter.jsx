import React, { useState, useCallback, useEffect } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import DoneIcon from '@mui/icons-material/Done';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import FilterWrapper from './FilterWrapper';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  listItem: {
    padding: 6,
    margin: 0,
    paddingLeft: '10px'
  },
  listItemIcon: {
    minWidth: '30px'
  },
  active: {
    background: '#F5F5F5'
  },
  ul: {
    paddingTop: 0,
    paddingBottom: 0
  },
  doneIcon: {
    fontSize: '1rem',
    color: '#00bac6',
    marginRight: '5px'
  },
  blank: {
    width: '21px',
    display: 'inline-block'
  }
}));

const SingleSelectFilter = ({ onSubmit, marginLeft, listData, columnName, selectedFilterList }) => {
  const { classes, cx } = useStyles();
  const [open, setOpen] = useState(false);
  const [activeItem, setActiveItem] = useState(0);
  const setOpenFun = useCallback((value) => setOpen(value), [setOpen]);

  const onChange = (value) => () => {
    setOpen(false);
    setActiveItem(value);
    onSubmit([{ key: columnName, value }]);
  };

  useEffect(() => {
    let tempValue = [];
    tempValue = selectedFilterList.filter(({ key }) => key === columnName);
    setActiveItem(tempValue.length ? tempValue[0].value : '');
  }, [selectedFilterList]);

  return (
    <FilterWrapper
      disabled={false}
      width="180px"
      onSubmit={() => null}
      hideSubmit
      onCloseUpdateFilter={() => null}
      open={open}
      marginLeft={marginLeft}
      setOpen={setOpenFun}
    >
      <Box display="flex">
        <Box width="100%" pb={1}>
          <List pt={0} className={classes.ul}>
            {listData.map((value) => {
              const labelId = `checkbox-list-label-${value}`;
              const active = value === activeItem;
              return (
                <ListItem
                  key={value}
                  role={undefined}
                  className={cx({
                    [classes.listItem]: true,
                    [classes.active]: active
                  })}
                  button
                  onClick={onChange(value)}
                >
                  <ListItemText
                    id={labelId}
                    primary={
                      <div className="fs12 display-flex align-items-center">
                        {active ? (
                          <DoneIcon className={classes.doneIcon} />
                        ) : (
                          <span className={classes.blank} />
                        )}
                        {value}
                      </div>
                    }
                  />
                </ListItem>
              );
            })}
          </List>
        </Box>
      </Box>
    </FilterWrapper>
  );
};

export default SingleSelectFilter;
