import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import LkModal from 'components/Modal/Modal';
import { formatDateDashedSeparated } from 'utils/helpers';
import { useDispatch, useSelector } from 'react-redux';
import { toastOpen } from 'redux/actionCreators/toast';
import { getQcDataLoad, updateGrnItemLoad, updateGrnItemReset } from 'redux/actionCreators/grn';

const EditExpiryModal = ({ data: { open, date, barcode }, handleClose }) => {
  const [dateValue, setDateValue] = useState(formatDateDashedSeparated(date));
  const {
    grnItemSearchSuccess,
    meta,
    pidDetail,
    isProceedToGreenChannel,
    itemUpdateSuccess,
    itemupdateError,
    pidList
  } = useSelector((state) => state.grn);
  const dispatch = useDispatch();

  const submit = () => {
    if (dateValue === '') {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: 'Please Select Valid Date',
          severity: 'error'
        })
      );
    }

    const { grn_code, pid, qc_fail_code, qc_status, qc_pass_box_barcode } = grnItemSearchSuccess;
    const payload = {
      barcode,
      expiry_date: dateValue,
      grn_code,
      meta,
      pid,
      qc_fail_code,
      qc_reason: '',
      qc_status,
      green_channel: isProceedToGreenChannel[pid],
      type: pidList.result.data.type
    };

    const { is_box_required } = pidDetail.result.data;
    if (is_box_required) {
      payload.box_barcode = qc_pass_box_barcode;
    }
    return dispatch(updateGrnItemLoad(payload));
  };

  useEffect(() => {
    if (itemUpdateSuccess) {
      const { data } = pidDetail.result;
      dispatch(getQcDataLoad(data));
      handleClose();
    }
    if (itemupdateError) {
      const { data } = pidDetail.result;
      dispatch(getQcDataLoad(data));
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: itemupdateError.display_message,
          severity: 'error'
        })
      );
      dispatch(updateGrnItemReset());
    }
  }, [itemupdateError, itemUpdateSuccess, dispatch, pidDetail]);

  return (
    <LkModal
      open={open}
      handleClose={handleClose}
      title="Edit Expiry Date"
      subTitle={`Edit expiry date for the ${barcode} barcode`}
      primaryBtnText="save"
      primaryBtn={submit}
    >
      <Box mt={4} width={430}>
        <TextField
          id="expiryDate"
          type="date"
          fullWidth
          value={dateValue}
          onChange={(e) => {
            setDateValue(e.target.value);
          }}
          inputProps={{
            min: formatDateDashedSeparated(Date.now())
          }}
          variant="outlined"
          size="small"
          InputLabelProps={{
            shrink: true
          }}
        />
      </Box>
    </LkModal>
  );
};

export default EditExpiryModal;
