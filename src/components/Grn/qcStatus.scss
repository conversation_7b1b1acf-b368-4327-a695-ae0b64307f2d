@use "sass:map";
@use "../../styles/variables";

.qc-status {
   
    .tags {
        font-size: 8px;
        background-color: variables.$color-primary;
        border-radius: 20px !important;
        padding: 5px 10px;
        color: variables.$color-white;
        font-weight: bold;
    }
    .boxOverflow {
        height: 240px;
        overflow-y: scroll;
    }
}
.qc-container {
    > div:not(:first-child) {
        border-left: 1px solid map.get($map: variables.$colors-grey, $key: dd);
    }
    >div:first-child{
        .titleBorderRadius{    
        border-top-left-radius: 8px ;
        }
    }
    >div:last-child{
        .titleBorderRadius{    
        border-top-right-radius: 8px ;
        }
    }
  
}

