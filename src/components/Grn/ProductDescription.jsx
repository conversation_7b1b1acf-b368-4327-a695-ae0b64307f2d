import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import LkInput from 'components/MaterialUi/LkInput';

const styles = makeStyles()(() => ({
  root: {
    height: '170px',
    borderRadius: '8px',
    border: '1px solid #DDDDDD'
  },
  resize: {
    padding: '0px',
    paddingLeft: '3px',
    height: '24px',
    fontSize: '9px'
  },
  table: {
    height: '111px',
    borderTop: '1px solid #EEEEEE'
  },
  tableCell: {
    height: '104px',
    overflowY: 'scroll'
  },
  active: {
    background: '#00BAC6',
    borderRadius: '2px ',
    padding: '0 3px',
    color: '#FFFFFF'
  },
  inactive: {
    padding: '0 3px'
  }
}));

const ProductDescription = ({ descriptionData = {}, selectDesc }) => {
  const [search, setSearch] = useState({ 'SPH Power': '', 'CYL Power': '', AP: '' });
  const [selectedItem, setSelectedItem] = useState({});

  const { classes } = styles();
  const [data, setData] = useState(descriptionData);

  useEffect(() => {
    setData(descriptionData);
  }, [descriptionData]);

  const searchFun = (key, e) => {
    const { value } = e.target;
    const filterData = { ...descriptionData };
    if (value !== '') {
      filterData[key] = descriptionData[key]?.filter((temp) => temp.toString().includes(value));
    }
    setSearch({ ...search, [key]: value });
    setData({ ...filterData });
  };

  const selectFun = (key, item) => {
    let obj = selectedItem;
    if (obj[key] && obj[key] === item) {
      delete obj[key];
    } else {
      obj = { ...selectedItem, [key]: item };
    }
    setSelectedItem(obj);
    selectDesc(obj);
  };

  return (
    <Box display="flex" justifyContent="space-between">
      {Object.keys(data).map((t) => {
        const neg = descriptionData[t].some((item) => item < 0);
        const pos = descriptionData[t].some((item) => item >= 0);
        const negNum = data[t].filter((item) => item < 0);
        const posNum = data[t].filter((item) => item >= 0);
        const width = neg && pos ? '145px' : '100px';
        return (
          <Box key={t} mb={5} mt={2} style={{ width }} className={classes.root}>
            <Box className="text-center fs10 fw-bold mr-t10 mr-b10">{t}</Box>
            <Box pl={0.8} pr={0.8}>
              <LkInput
                fullWidth
                size="medium"
                placeholder="Search here..."
                value={search[t]}
                onChange={(e) => searchFun(t, e)}
                InputProps={{ classes: { input: classes.resize } }}
                className={classes.input}
              />
            </Box>
            <Box mt={1} className={classes.table} display="flex" height="111px">
              {negNum.length > 0 && (
                <Box p={1} pb={0} flex={1} className={classes.tableCell}>
                  {negNum.map((temp) => (
                    <Box m={1} ml={0} mt={0} key={temp} className="fs10 cursor-pointer">
                      <span
                        className={selectedItem[t] === temp ? classes.active : classes.inactive}
                        onClick={() => selectFun(t, temp)}
                      >
                        {temp}
                      </span>
                    </Box>
                  ))}
                </Box>
              )}
              {posNum.length > 0 && (
                <Box
                  p={1}
                  flex={1}
                  className={classes.tableCell}
                  borderLeft={negNum.length > 0 ? '1px solid #DDDDDD' : ''}
                >
                  {posNum.map((temp) => (
                    <Box m={1} ml={0} mt={0} key={temp} className="fs10  cursor-pointer">
                      <span
                        className={selectedItem[t] === temp ? classes.active : classes.inactive}
                        onClick={() => selectFun(t, temp)}
                      >
                        {temp}
                      </span>
                    </Box>
                  ))}
                </Box>
              )}
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

export default ProductDescription;
