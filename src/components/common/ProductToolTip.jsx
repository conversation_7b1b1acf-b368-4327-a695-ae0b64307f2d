import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Spinner from 'components/Spinner/Spinner';
import { Box } from '@mui/material';
import { CATEGORY_MAPPING } from 'config/CategoryMapping';
import { searchByDescPrductsLoad } from '../../redux/actionCreators/grn';
import PIDDetailsToolTip from './PIDDetailsToolTip';

const ProductToolTip = ({ poId = '', pid = '', classification = '' }) => {
  const classificationNumber = Number(classification);
  const dispatch = useDispatch();
  const { productSeachByDescLoading, productSeachByDesc, productSeachByDescFail } = useSelector(
    (state) => state.grn
  );
  const [data, setData] = useState({ powers: {} });
  useEffect(() => {
    const tempProductType =
      classificationNumber === 11354 || classificationNumber === 11356
        ? CATEGORY_MAPPING[classification]
        : 'other';
    const obj = {
      purchase_order_code: poId,
      product_type: tempProductType,
      search_query: pid,
      pid,
      isPrescriptionLens: tempProductType === 'prescription-lens'
    };
    dispatch(searchByDescPrductsLoad(obj));
  }, [poId, pid, dispatch, classification]);

  useEffect(() => {
    if (productSeachByDesc) {
      const resultTemp = {};
      const powers = {};
      const { product_details } = productSeachByDesc;
      if (product_details[0]) {
        const {
          model_number,
          name,
          brand,
          product_color,
          size,
          sph,
          cyl,
          ap,
          ed,
          axis_type,
          axis,
          index,
          classification: apiClassifcation
        } = product_details[0];

        const tempClassification = classificationNumber || apiClassifcation;

        if (tempClassification === 11354) {
          resultTemp.Brand = brand || '';
          powers.SPH = sph ?? '';
          powers.CYL = cyl ?? '';
          powers.AP = ap ?? '';
          powers.ED = ed ?? '';
        }
        if (tempClassification === 19153) {
          resultTemp.Brand = brand || '';
        }
        if (tempClassification === 11356) {
          powers.SPH = sph ?? '';
          powers.CYL = cyl ?? '';
          powers.AP = ap ?? '';
          powers.ED = ed ?? '';
          const [packageName] = name.split('_S');
          resultTemp.Package = packageName;
          resultTemp.Index = index ?? '';
          resultTemp.Axis = axis ?? '';
          resultTemp['Axis Type'] = axis_type ?? '';
          resultTemp.Brand = brand ?? '';
        } else {
          resultTemp.Model = model_number || '';
          resultTemp.Brand = brand || '';
          resultTemp.Color = product_color || '';
          resultTemp.Size = size || '';
        }
        resultTemp.Category = CATEGORY_MAPPING[tempClassification] || '';

        const temp = {
          obj: resultTemp,
          powers
        };
        setData(temp);
      }
    }
  }, [productSeachByDesc, classification]);

  if (productSeachByDescLoading) {
    return (
      <Box p={4} textAlign="center">
        <Spinner />
      </Box>
    );
  }
  if (productSeachByDescFail) {
    return (
      <Box p={2} className="fs14  fw-bold " textAlign="center">
        {productSeachByDescFail}
      </Box>
    );
  }
  if (!productSeachByDesc) {
    return (
      <Box p={2} className="fs14  fw-bold " textAlign="center">
        FAILED TO LOAD
      </Box>
    );
  }
  return (
    <PIDDetailsToolTip
      pid={pid}
      data={data}
      imageUrl={productSeachByDesc?.product_details[0]?.image_url}
      name={productSeachByDesc?.product_details[0]?.name}
      classification={classification || productSeachByDesc?.product_details[0]?.classification}
      productUrl={productSeachByDesc?.product_details[0]?.product_url}
    />
  );
};

export default ProductToolTip;
