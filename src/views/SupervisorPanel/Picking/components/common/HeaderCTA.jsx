import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Autocomplete, Box, Button, MenuItem } from '@mui/material';
import Spinner from 'components/Spinner/Spinner';
import LkInput from 'components/MaterialUi/LkInput';
import {
  moveFromASRSToManualLoad,
  supervisorPickingAssignRequest,
  supervisorPickingNotFoundRequest
} from 'redux/actionCreators/supervisorPickingSkipped';
import ConfirmationModal from './ConfirmationModal';

const HeaderCTA = ({ CONTENT, classes, selectedItems, isDiscarded, setSelectedItems, onHold }) => {
  const dispatch = useDispatch();
  const [autoCompleteValue, setAutoCompleteValue] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);

  const asrsToManualActionLoading = useSelector(
    (state) => state.supervisorPickingSkipped.asrsToManual.isLoading
  );
  const { data: users, isLoading: isUsersDataLoading } = useSelector(
    (state) => state.supervisorPickingSkipped.users
  );

  const markToNotFound = () => {
    const selectedIds = selectedItems.map((eachItem) => eachItem.id);
    dispatch(
      supervisorPickingNotFoundRequest({
        requestBody: { id_list: selectedIds.join(',') },
        onHold,
        selectedIds
      })
    );
    setSelectedItems([]);
  };

  const assignItToUser = () => {
    const selectedIds = selectedItems.map((eachItem) => eachItem.id);
    dispatch(
      supervisorPickingAssignRequest({
        requestBody: { id_list: selectedIds.join(','), assigned_to: selectedUser.emp_code },
        onHold,
        selectedIds
      })
    );
    setSelectedItems([]);
    setSelectedUser(null);
  };

  const moveToManualFromASRS = () => {
    const selectedIds = selectedItems.map((eachItem) => eachItem.shipmentId);
    dispatch(
      moveFromASRSToManualLoad({
        shippingPackageIdList: selectedIds,
        order_state: 'DISCARD_TO_MANUAL'
      })
    );
  };

  if (selectedItems.length) {
    if (isDiscarded) {
      return (
        <Button
          disabled={asrsToManualActionLoading}
          variant="contained"
          color="primary"
          sx={{ width: 'max-content' }}
          onClick={moveToManualFromASRS}
        >
          {asrsToManualActionLoading ? <Spinner /> : CONTENT.MOVE_TO_MANUAL_FROM_ASRS}
        </Button>
      );
    }

    const renderUserItem = (renderProps, empCode) => {
      const user = users.find((item) => item.emp_code === empCode);
      return (
        <li {...renderProps} key={user.emp_code}>
          <MenuItem key={user.emp_code} value={user} onClick={() => setSelectedUser(user)}>
            <Box display="flex" flexDirection="column">
              <Box color="#3C3C3C" lineHeight="143%" fontSize="14px" letterSpacing="0.15px">
                {user.username}
              </Box>
              <Box
                color="rgba(60, 60, 60, 0.54)"
                lineHeight="20.02px"
                fontSize="14px"
                letterSpacing="0.15px"
              >
                {user.emp_code}
              </Box>
            </Box>
          </MenuItem>
        </li>
      );
    };

    return (
      <>
        {selectedUser && (
          <ConfirmationModal
            CONTENT={CONTENT}
            classes={classes}
            selectedUser={selectedUser}
            setSelectedUser={setSelectedUser}
            selectedItems={selectedItems}
            assignItToUser={assignItToUser}
          />
        )}
        <Button
          className={classes.notFoundBtn}
          onClick={markToNotFound}
          variant="text"
          color="primary"
        >
          {CONTENT.NOT_FOUND}
        </Button>
        <Autocomplete
          value={autoCompleteValue}
          disabled={isUsersDataLoading}
          className={classes.usersSelect}
          getOptionLabel={(option) => option}
          renderOption={(renderProps, option) => renderUserItem(renderProps, option)}
          renderInput={(params) => <LkInput {...params} fullWidth label={CONTENT.ASSIGN_PICKER} />}
          onInputChange={(_event, newInputValue) => setAutoCompleteValue(newInputValue)}
          options={users
            .map((user) => user.emp_code)
            .filter((emp) => emp.includes(autoCompleteValue.toUpperCase()))}
        />
      </>
    );
  }
  return null;
};

export default HeaderCTA;
