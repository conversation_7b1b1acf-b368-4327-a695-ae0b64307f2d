import React, { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import UploadCsv from '../UploadCsv/UploadCsv';
import {
  uploadVendorMasterLoad,
  downloadVendorMasterReset,
  fileDownloadVendorLoad,
  fileDownloadVendorReset,
  vendorProccessStatusLoad,
  vendorProccessStatusReset,
  uploadVendorMasterReset,
  downloadVendorMasterLoad
} from '../../redux/actionCreators/vendorMaster';
import { fileDownload } from '../../utils/helpers';
import { toastOpen } from '../../redux/actionCreators/toast';
import useProccessStatusHook from '../../common/useProccessStatusHook';

let selectedImportFile;

const UploadVendorMaster = (props) => {
  const { open, close } = props;
  const {
    uploadVendorMaterData,
    uploadVendorMasterLoading,
    fileDownloadData,
    vendorProccessStatus,
    vendorProccessStatusFail,
    downloadVendorMaterData
  } = useSelector((state) => state.vendorMaster);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.VENDOR_ITEM_MASTER);
  const [vendorStatus, vendorStatusSuccessFul, setVendorStatus] = useProccessStatusHook({
    processingStatus: vendorProccessStatus,
    processingStatusFail: vendorProccessStatusFail,
    processingStatusLoad: vendorProccessStatusLoad,
    processingStatusReset: vendorProccessStatusReset
  });
  const dispatch = useDispatch();
  const sampleCSV = [
    [
      'product_id',
      'package_name',
      'vendor_code',
      'unit_price_without_tax',
      'enabled',
      'lens_id',
      'sph',
      'cyl',
      'gtin',
      'upc',
      'axis',
      'ap',
      'base_curve',
      'vendor_sku',
      'diameter',
      'brand'
    ],
    [
      90121328,
      'package name',
      'WHOLESALE01',
      200,
      1,
      123,
      2,
      2,
      1234,
      345,
      12,
      10,
      12,
      21321,
      11,
      'brand'
    ]
  ];

  useEffect(
    () => () => {
      selectedImportFile = null;
      dispatch(downloadVendorMasterReset());
      dispatch(fileDownloadVendorReset());
      dispatch(uploadVendorMasterReset());
    },
    [dispatch]
  );

  useEffect(() => {
    if (vendorStatusSuccessFul) {
      setTimeout(() => {
        close();
      }, 2000);
    }
  }, [vendorStatusSuccessFul]);

  useEffect(() => {
    if (uploadVendorMaterData?.data?.req_id) {
      setVendorStatus({ refNo: uploadVendorMaterData.data.req_id });
    }
  }, [uploadVendorMaterData]);

  useEffect(() => {
    if (uploadVendorMaterData?.data) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: uploadVendorMaterData?.meta?.displayMessage,
          severity: 'success'
        })
      );
    }
  }, [uploadVendorMaterData]);

  const resetState = useCallback(() => {
    close();
  }, []);

  useEffect(() => {
    if (downloadVendorMaterData) {
      fileDownload(downloadVendorMaterData, 'VENDOR_ITEM_REPORT');
    }
  }, [downloadVendorMaterData]);

  useEffect(() => {
    if (fileDownloadData) {
      fileDownload(fileDownloadData, 'VENDOR_ITEM_REPORT');
    }
  }, [fileDownloadData]);

  const uploadVendor = useCallback(() => {
    const formData = new FormData();
    formData.set('file', selectedImportFile);
    dispatch(uploadVendorMasterLoad(formData));
  }, [dispatch, uploadVendorMasterLoad]);

  const selectFile = (e) => {
    selectedImportFile = e;
  };

  const downloadErrorReport = useCallback(() => {
    if (uploadVendorMaterData?.response?.data?.data?.file) {
      dispatch(fileDownloadVendorLoad(uploadVendorMaterData.response.data.data.file));
    } else if (uploadVendorMaterData?.data?.req_track_id) {
      dispatch(downloadVendorMasterLoad(uploadVendorMaterData.data.req_track_id));
    }
  }, [uploadVendorMaterData]);

  return (
    <UploadCsv
      open={open}
      onClose={close}
      subTitle={CONTENT.PLEASE_SELECT_CSV_TO_IMPORT_VIM}
      selectFiles={(e) => selectFile(e)}
      sampleCSV={sampleCSV}
      checkedItemLabel=""
      failDataMessage={
        uploadVendorMaterData?.response?.data?.data?.file ||
        (uploadVendorMaterData?.data?.req_track_id &&
        vendorProccessStatus?.status === 'done' &&
        Boolean(vendorProccessStatus?.error_msg?.length)
          ? uploadVendorMaterData?.data?.file
          : null)
      }
      downloadErrorReport={downloadErrorReport}
      uploadCsvPass={uploadVendorMaterData?.data}
      handleSubmit={uploadVendor}
      resetState={resetState}
      sampleFileName="Sample_Vendor_Item_Upload"
      localUpload={!uploadVendorMaterData?.data?.req_track_id}
      importLoading={uploadVendorMasterLoading || vendorStatus}
      showSampleArray={false}
    />
  );
};

export default UploadVendorMaster;
