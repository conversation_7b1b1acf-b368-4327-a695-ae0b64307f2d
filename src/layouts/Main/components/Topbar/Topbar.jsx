import React, { useState, useCallback } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import { TextField, Autocomplete, Box } from '@mui/material';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import MenuIcon from '@mui/icons-material/Menu';

import { changeFacility } from 'redux/reducers/settings';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import localStorageHelper from 'utils/localStorageHelper';
import Drawer from '../Drawer';
import LanguageSwitch from './LanguageSwitch';

const useStyles = makeStyles()(() => ({
  root: {
    boxShadow: 'none',
    zIndex: 1000
  },
  title: {
    // color: 'white'
    marginLeft: 5
  },
  flexGrow: {
    flexGrow: 1
    // alignSelf: 'center'
  },
  flexContainer: {
    width: '100%',
    display: 'flex',
    alignItems: 'center'
  },
  justifyContentSpaceBetween: {
    justifyContent: 'space-between'
  },
  rightElem: {
    flexGrow: 1,
    justifyContent: 'flex-end'
  },
  centerElem: {
    width: '100%',
    textAlign: 'center',
    flexGrow: 1
  },
  field: {
    width: 150
  }
}));

const Topbar = (props) => {
  const { className, children, ...rest } = props;
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();

  const [openDrawer, setSOpenDrawer] = useState(false);
  const closeDrawer = useCallback(() => setSOpenDrawer(false), [setSOpenDrawer]);

  const {
    selectedFacility,
    facilitiesObj: { facilities }
  } = useSelector((state) => state.settings);

  useKeyboardShortcut([17, 219], () => {
    setSOpenDrawer(true);
  });

  useKeyboardShortcut([17, 221], () => {
    setSOpenDrawer(false);
  });

  const facilityChange = (val) => {
    localStorageHelper.setItem('facility-code', val);
    dispatch(changeFacility(val));
  };

  const opt = [
    {
      code: 'DK02',
      name: 'Staging Facility'
    },
    {
      code: 'LKH03',
      name: 'Staging Facility 2'
    }
  ];

  return (
    <>
      <AppBar {...rest} className={cx(classes.root, className)} color="inherit">
        <Toolbar>
          <div className={cx(classes.flexContainer, classes.justifyContentSpaceBetween)}>
            <div className={cx(classes.flexContainer, classes.flexGrow)}>
              <IconButton
                edge="start"
                className={classes.menuButton}
                onClick={() => setSOpenDrawer(true)}
                data-cy="main-navigation"
                color="inherit"
                aria-label="menu"
              >
                <MenuIcon />
              </IconButton>
              <RouterLink to="/dashboard">
                <Typography className={classes.title} variant="h2">
                  NexS
                </Typography>
              </RouterLink>
              <div style={{ height: '24px', marginLeft: '22px', marginRight: '18px' }}>
                <Divider style={{ backgroundColor: '#c4c4c4' }} orientation="vertical" />
              </div>
              <Box sx={{ width: 150 }}>
                <Autocomplete
                  disableClearable
                  value={selectedFacility || opt[0]?.code}
                  onChange={(_, newValue) => facilityChange(newValue)}
                  options={facilities?.map(({ code }) => code)}
                  renderInput={(params) => (
                    <TextField {...params} size="small" label="Select Facility" />
                  )}
                />
              </Box>
              {window.location.host !== import.meta.env.VITE_PROD_URL && <LanguageSwitch />}
            </div>
            <div className={classes.centerElem} data-cy="title">
              {children ? React.Children.toArray(children)[0] : null}
            </div>
            <div className={cx(classes.flexContainer, classes.rightElem)}>
              {children ? React.Children.toArray(children)[1] : null}
            </div>
          </div>
        </Toolbar>
        <Divider />
      </AppBar>
      <Drawer
        openDrawer={openDrawer}
        handleClose={closeDrawer}
        selectedFacility={selectedFacility}
      />
    </>
  );
};

export default Topbar;
