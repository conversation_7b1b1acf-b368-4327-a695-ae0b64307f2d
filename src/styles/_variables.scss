@use 'sass:map';
// font family
$base-font-family: "Roboto", "Arial", "Helvetica", sans-serif;

$base-font-size: 18px;

// font size primary
$font-size-b1: 18px;
$font-size-b2: 16px;
$font-size-b3: 14px;
$font-size-b4: 12px;
$font-size-b5: 10px;

// font size headings
$font-size-h1: 36px;
$font-size-h2: 32px;
$font-size-h3: 28px;
$font-size-h4: 24px;
$font-size-h5: 18px;
$font-size-h6: 16px;
$font-size-h7: 14px;
$font-size-h8: 12px;

// font size others
$font-size-caption: 10px;

// line height
$line-height-base: 1;
$line-height-heading: 1.2;

// opacity
$opacity-base: 0.7;

// colors

$color-primary: #00bac6;
$color-yellow: #faa61a;
$color-red: #df3747;
$color-warning: #ffc107;

$colors-secondary: (
    fandango: #a3238e,
    dark_pink: #ef4e74,
    fandango_dark_pink: linear-gradient(90deg, #a3238e 0%, #ef4e74 100%),
    purpureus: #a33ca8,
    blue: #0088cb,
    purpureus_blue: linear-gradient(90deg, #a33ca8 0%, #0088cb 100%),
    paris_green: #45c476,
    light_pink: #f3f6fa,
);

$colors-grey: (
    33: #333333,
    66: #666666,
    99: #999999,
    cc: #cccccc,
    dd: #dddddd,
    ee: #eeeeee,
    f2: #f2f2f2,
    f5: #f5f5f5,
    fa: #fafafa,
    a2: #00bba2,
    94: #558294,
    22: #ff5722,
    28: #e2262b,
);

$font-color-base: map.get($colors-grey, grey1);
$color-white: #ffffff;
$color-black: #000000;
$color-header-bg: #222d32;
$color-3c: #3c3c3c;

// border
$border-radius-base: 8px;
$border-radius-caption: 20px;

$bg-dark_pink_dot1: rgba(239, 78, 116, 0.1);
$bg-paris_green_dot1: rgba(69, 196, 118, 0.1);
$bg-bright_yellow_dot1: rgba(250, 166, 26, 0.1);
$bg-mikado_yellow_dot1: rgba(255, 194, 14, 0.1);
$bg-turquioise_surf_dot1: rgba(0, 186, 198, 0.1);
$bg-red_purple_dot9: rgba(237, 9, 115, 0.9);
