/* eslint-disable max-len */
import React from 'react';
import { alpha } from '@mui/material/styles';
import { makeStyles , withStyles } from 'tss-react/mui';
import Typography from '@mui/material/Typography';
import { TreeView } from '@mui/x-tree-view/TreeView';
import { TreeItem } from '@mui/x-tree-view/TreeItem';
import SvgIcon from '@mui/material/SvgIcon';

const MinusSquare = (props) => (
  <SvgIcon fontSize="inherit" style={{ width: 14, height: 14 }} {...props}>
    <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 11.023h-11.826q-.375 0-.669.281t-.294.682v0q0 .401.294 .682t.669.281h11.826q.375 0 .669-.281t.294-.682v0q0-.401-.294-.682t-.669-.281z" />
  </SvgIcon>
);

const PlusSquare = (props) => (
  <SvgIcon fontSize="inherit" style={{ width: 14, height: 14 }} {...props}>
    <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 12.977h-4.923v4.896q0 .401-.281.682t-.682.281v0q-.375 0-.669-.281t-.294-.682v-4.896h-4.923q-.401 0-.682-.294t-.281-.669v0q0-.401.281-.682t.682-.281h4.923v-4.896q0-.401.294-.682t.669-.281v0q.401 0 .682.281t.281.682v4.896h4.923q.401 0 .682.281t.281.682v0q0 .375-.281.669t-.682.294z" />
  </SvgIcon>
);


export const LKTreeView = ({ children, ...props }) => (
  <TreeView
    defaultCollapseIcon={<MinusSquare />}
    defaultExpandIcon={<PlusSquare />}
    defaultEndIcon={<PlusSquare />}
    {...props}
  >
    {children}
  </TreeView>
);

const treeStyle = makeStyles()(() => ({
  label: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBlock: 5
  },
  labelText: {
    fontSize: 16
  }
}));
export const LKTreeItem = withStyles((props) => {
  const { classes } = treeStyle();
  const { labelText, child, labelIcon, loader, ...others } = props;
  return (
    <TreeItem
      icon={props.labelIcon ? <span /> : loader}
      label={
        <div className={classes.label}>
          <Typography className={classes.labelText} variant="body2">
            {labelText}
          </Typography>
          {child}
        </div>
      }
      {...others}
    />
  );
}, (theme) => ({
  selected: {
    '& .MuiTreeItem-content .MuiTreeItem-label': {
      backgroundColor: '#fff !important'
    }
  },
  label: {
    '&:hover': {
      backgroundColor: '#fff'
    }
  },
  iconContainer: {
    '& .close': {
      opacity: 0.3
    }
  },
  group: {
    marginLeft: 7,
    paddingLeft: 18,
    borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.4)}`
  }
}));
