import navigation from './navigation.json';
import JITLensMarkDamageReasons from './JITLensMarkDamageReasons.json';
import idleSessionTimer from './idleSessionTimer.json';
import storePackingConsolidationFacility from './storePackingConsolidationFacility.json';
import adhocASRSPicking from './adhocASRSPicking.json';
import featureToggle from './featureToggle.json';
import traySeries from './traySeries.json';
import classification from './classification.json';
import monitorPanelFilter from './monitorPanelFilter.json';
import autoFocusTimer from './autoFocusTimer.json';
import stockTakeFacility from './stockTakeFacility.json';
import vendorName from './vendorName.json';
import doCountry from './doCountry.json';

export default {
  navigation,
  JITLensMarkDamageReasons,
  idleSessionTimer,
  adhocASRSPicking,
  featureToggle,
  storePackingConsolidationFacility,
  traySeries,
  classification,
  monitorPanelFilter,
  autoFocusTimer,
  stockTakeFacility,
  vendorName,
  doCountry
};
