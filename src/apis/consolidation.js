import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

// CONSOLIDATION OPTIONS API
export const consolidationAPI = () => tokenInstance().get(config.consolidation.getConsolidationAPI);

// CONSOLIDATIONS DATA LIST API
export const getPigeonholeList = ({ id, page, size, search }) =>
  tokenInstance().get(config.consolidation.getPigeonholeListAPI, {
    params: { type: id, page, size, search }
  });

// CONSOLIDATION CHILD DATA LIST API
export const getPigeonholeChildList = (boxCode) =>
  tokenInstance().get(`${config.consolidation.getPigeonholeChildAPI}/${boxCode}/details`);

export const consolidationScanAPi = (payload) =>
  tokenInstance().post(config.consolidation.scanConsolidation, payload);
