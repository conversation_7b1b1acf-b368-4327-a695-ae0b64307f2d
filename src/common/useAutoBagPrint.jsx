import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { isEmpty } from 'utils/lodash';
import { LOGIN } from 'redux/reducers/login';
import constant from 'constant';
import usePermissions from './usePermissions';

const isBarcodeAlreadyScanned = (barcode) => {
  const now = Date.now();
  const { VITE_ONE_DAY_IN_MILISECONDS } = import.meta.env;
  const shipmentsStr = localStorage.getItem(constant.AUTO_BAG_PRINTED_SHIPMENTS);

  if (!shipmentsStr) {
    return false;
  }

  const shipments = JSON.parse(shipmentsStr);

  const shipmentKeys = Object.keys(shipments);

  const recentKeys = shipmentKeys.filter(
    (key) => now - shipments[key] <= VITE_ONE_DAY_IN_MILISECONDS
  );

  const filteredShipments = recentKeys.reduce((acc, key) => {
    acc[key] = shipments[key];
    return acc;
  }, {});

  localStorage.setItem(constant.AUTO_BAG_PRINTED_SHIPMENTS, JSON.stringify(filteredShipments));

  return !!filteredShipments?.[barcode];
};

const useAutoBagPrint = (barcode) => {
  const { type = 'autobag' } = useParams();
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const { isPackingSuperVisor = false } = usePermissions(userDetail?.permission);

  const { printShipmentData } = useSelector((state) => state.printShipment);

  const isAlreadyScanned = type === 'autobag' && isBarcodeAlreadyScanned(barcode);

  useEffect(() => {
    if (!isEmpty(printShipmentData) && type === 'autobag' && !isPackingSuperVisor) {
      const currentTime = Date.now();
      const prevData = JSON.parse(localStorage.getItem(constant.AUTO_BAG_PRINTED_SHIPMENTS));
      const newShipmentData = {
        ...prevData,
        [printShipmentData?.shippingPackageId]: currentTime
      };
      localStorage.setItem(constant.AUTO_BAG_PRINTED_SHIPMENTS, JSON.stringify(newShipmentData));
    }
  }, [printShipmentData, isPackingSuperVisor, type]);

  return isAlreadyScanned;
};

export default useAutoBagPrint;
