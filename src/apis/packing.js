import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const markPackingDone = (payload) => {
  const { packingData } = payload;
  const URL = `${config.apiPath.packingDone}`;
  return tokenInstance()
    .post(URL, packingData)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const lsmCourierAwb = (payload) => {
  const { shippingPackageId, lsmCourierHost } = payload;
  const URL = `${
    lsmCourierHost.includes('preprod')
      ? config.apiPath.lsmShipmentsHostPreProd
      : config.apiPath.lsmShipmentsHostProd
  }${config.apiPath.lsmShipments}/${shippingPackageId}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const printPackingInvoiceAPI = (payload) => {
  const { panel, shipmentId: shippingPackageId, forcePrint } = payload;
  return tokenInstance({ panel })
    .get(config.apiPath.packingPrintInvoice, {
      responseType: 'arraybuffer',
      params: { shippingPackageId, forcePrint }
    })
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const isCardBarcodeValid = (payload) =>
  tokenInstance().post(config.apiPath.cardBarcodeValid, payload);
