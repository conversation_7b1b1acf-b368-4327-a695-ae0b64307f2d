import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const getIqcDetail = (payload) =>
  tokenInstance().get(config.inwardQc.getIqcDetail, { params: payload });

export const saveGrnProduct = ({ grn_code }) =>
  tokenInstance().post(config.inwardQc.saveGrnProduct, null, { params: { grn_code } });

export const getIqcBarcodeDetailApi = ({ barcode, grnCode }) =>
  tokenInstance().post(config.inwardQc.getBarcodeDetails, null, {
    params: { barcode, grn_code: grnCode }
  });

export const scanBarcodeApi = (payload) =>
  tokenInstance().post(config.inwardQc.scanBarcode, payload);

export const failReasonsListApi = () => tokenInstance().post(config.inwardQc.getFailReason);

export const markIqcCompleteApi = ({ grnCode, boxCode, pid }) =>
  tokenInstance().post(
    `${config.inwardQc.iqcComplete}?grn_code=${grnCode}&box_code=${boxCode}&pid=${pid}`,
    null
  );

export const deleteBarcodeApi = ({ barcode, grn_code }) =>
  tokenInstance().put(config.inwardQc.deleteBarcode, null, { params: { barcode, grn_code } });

export const invoiceIqcSummary = ({ invoiceRefNo }) =>
  tokenInstance().get(config.inwardQc.invoiceIqcSummary, {
    params: { invoice_ref_no: invoiceRefNo }
  });

export const bulkApproveAndReject = (payload) =>
  tokenInstance().post(config.inwardQc.approveOrReject, null, { params: payload });
