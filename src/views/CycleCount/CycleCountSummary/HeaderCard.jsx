import React, { useCallback } from 'react';
import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import Spinner from 'components/Spinner/Spinner';
import { numberWithCommas } from 'utils/helpers';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()((theme, { backgroundColor }) => ({
  container: {
    display: 'flex',
    height: '83px',
    minWidth: '220px',
    alignItems: 'center',
    borderRadius: '10px',
    boxShadow: '3px 3px 12px rgba(0, 53, 55, 0.12)',
    background: backgroundColor
  },
  imageStyle: {
    width: '82px',
    height: '51px',
    marginLeft: '16px'
  },
  cardRightPart: {
    display: 'flex',
    flexDirection: 'column',
    padding: '19px 24px',
    width: '153px'
  },
  label: {
    fontSize: '14px',
    color: '#666666',
    fontWeight: 400,
    marginBottom: '7px'
  },
  numberValue: {
    fontSize: '20px',
    color: '#333333',
    fontWeight: 600
  }
}));

const HeaderCard = ({ name, label, summaryData, isLoading }) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CYCLE_COUNT);

  const TYPE = {
    BINS_SCANNED: { label: CONTENT.BINS_SCANNED, src: 'bins_scanned.svg', background: '#E5F8F9' },
    UPDATE_LOCATION: {
      label: CONTENT.UPDATE_LOCATION,
      src: 'updateLocation.svg',
      background: '#f6e9f3'
    },
    VALID_BARCODES: {
      label: CONTENT.VALID_BARCODE,
      src: 'valid_barcodes.svg',
      background: '#edf7ed'
    },
    INVALID_BARCODES: {
      label: CONTENT.INVALID_BARCODES,
      src: 'invalid_barcodes.svg',
      background: '#feeceb'
    },
    MISSING_BARCODES: {
      label: CONTENT.NOT_FOUND,
      src: 'missing_barcodes.svg',
      background: '#fff5e5'
    }
  };
  const { classes } = useStyles({ backgroundColor: TYPE[label].background });

  const renderValue = useCallback(() => {
    if (isLoading) {
      return <Spinner />;
    }
    return numberWithCommas(summaryData[name]);
  }, [isLoading, summaryData]);

  return (
    <Card className={classes.container}>
      <img
        className={classes.imageStyle}
        src={`${import.meta.env.VITE_STATIC_SERVER}/images/${TYPE[label].src}`}
        alt={label}
      />
      <Box className={classes.cardRightPart}>
        <span className={classes.label}>{TYPE[label].label}</span>
        <span className={classes.numberValue}>{renderValue()}</span>
      </Box>
    </Card>
  );
};

export default HeaderCard;
