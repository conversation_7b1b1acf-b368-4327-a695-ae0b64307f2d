import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getStartRetireListApi = (payload) =>
  tokenInstance().post(config.startRetire.list, payload);

export const bulkuploadApi = (payload) =>
  tokenInstance().post(config.startRetire.bulkUpload, payload);

export const singleBarcodeUploadApi = (payload) =>
  tokenInstance().post(config.startRetire.singleUpload, payload);
