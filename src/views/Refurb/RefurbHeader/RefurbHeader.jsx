import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import { COMPLETED, PENDING, PENDING_GOOD_PUTAWAY } from 'config/refurb';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import { refurbBarcodeOperationReset, refurbStatusCountRequest } from 'redux/reducers/refurb';
import RefurbHeaderCard from './RefurbHeaderCard';

const useStyles = makeStyles()(() => ({
  container: {
    boxSizing: 'border-box',
    minHeight: 122,
    display: 'flex',
    justifyContent: 'space-between'
  },
  cards: {
    padding: '16px 0px 16px 16px',
    display: 'flex',
    gap: 16
  }
}));

const MonitorHeader = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { type } = useParams();
  const {
    isLoading,
    data: { pendingRefurbishment = 0, refurbCompleted = 0, pendingGoodPutaway = 0 }
  } = useSelector((state) => state.refurb.refurbStatusCount);
  const { barcodeOperationSuccess } = useSelector((state) => state.refurb);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.REFURBISHMENT);
  const REFURB_TYPES = {
    [PENDING]: [CONTENT.PENDING_REFURBISHMENT],
    [COMPLETED]: [CONTENT.REFURB_COMPLETED],
    [PENDING_GOOD_PUTAWAY]: [CONTENT.PENDING_GOOD_PUTAWAY]
  };

  // harsha
  useEffect(() => {
    dispatch(refurbStatusCountRequest());
  }, []);

  useEffect(() => {
    if (barcodeOperationSuccess) {
      dispatch(refurbStatusCountRequest());
      dispatch(refurbBarcodeOperationReset());
    }
  }, [barcodeOperationSuccess]);

  const onTypeChange = (refurbType) => navigate(`/refurb/${refurbType}`);

  return (
    <Box className={classes.container}>
      <Box className={classes.cards}>
        <RefurbHeaderCard
          title={REFURB_TYPES.pending}
          count={pendingRefurbishment}
          onClick={() => onTypeChange(PENDING)}
          isActive={!type || REFURB_TYPES[type] === REFURB_TYPES.pending}
          isLoading={isLoading}
        />
        <RefurbHeaderCard
          title={REFURB_TYPES.completed}
          count={refurbCompleted}
          onClick={() => onTypeChange(COMPLETED)}
          isActive={REFURB_TYPES[type] === REFURB_TYPES.completed}
          isLoading={isLoading}
        />
        <RefurbHeaderCard
          title={REFURB_TYPES.pendingGoodPutaway}
          count={pendingGoodPutaway}
          onClick={() => onTypeChange(PENDING_GOOD_PUTAWAY)}
          isActive={REFURB_TYPES[type] === REFURB_TYPES.pendingGoodPutaway}
          isLoading={isLoading}
        />
      </Box>
    </Box>
  );
};

export default MonitorHeader;
