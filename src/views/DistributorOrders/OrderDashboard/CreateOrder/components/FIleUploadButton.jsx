import React from 'react';
import Box from '@mui/material/Box';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import clsx from 'clsx';

import { fileDownload } from 'utils/helpers';

const FileUploadButton = ({
  onDrop,
  onDragOver,
  onDragEnter,
  onDragLeave,
  dragging,
  classes,
  CONTENT,
  csvFile
}) => (
  <>
    <Box
      component="label"
      htmlFor="fileInput"
      data-cy="validate-csv-container"
      onDrop={onDrop}
      onDragOver={onDragOver}
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      sx={{ paddingBlock: '25px', paddingInline: '15px' }}
      className={`${clsx(classes.fileUploadButton, { [classes.fileDrag]: dragging })}`}
    >
      <Box>
        <FileUploadOutlinedIcon />
      </Box>
      <Box>{CONTENT.DROP_HERE_OR_SELECT_CSV}</Box>
      <Box className={classes.required}>({CONTENT.REQUIRED})</Box>
    </Box>
    <Box className="display-flex fs14 justify-content-space-between pd-12">
      <Box data-cy="sample-csv-text" className="mr-r10">
        {CONTENT.SAMPLE_CSV_FILE}
      </Box>
      <Box
        data-cy="sample-csv-download-btn"
        onClick={() => fileDownload(csvFile, 'DO-Orders-sample-file')}
      >
        <Box className="text-turquioise_surf" style={{ cursor: 'pointer' }}>
          <img
            className="mr-r8"
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/download.svg`}
            alt="img"
          />
          {CONTENT.DOWNLOAD}
        </Box>
      </Box>
    </Box>
  </>
);

export default FileUploadButton;
