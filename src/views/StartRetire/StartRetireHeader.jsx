import React, { useRef, useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';

import FileUpload from 'components/FileUpload/FileUpload';
import LkInput from 'components/MaterialUi/LkInput';
import {
  singleBarcodeUploadReset,
  bulkUploadLoad,
  bulkUploadReset
} from 'redux/reducers/startRetire';
import { fileDownload, debounce } from 'utils/helpers';
import { LOCALISATION } from 'redux/reducers/localisation';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import SinglePidUploadModal from './SinglePidUploadModal';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 4
  },

  button: {
    marginLeft: 10,
    borderRadius: 12,
    width: 'fit-content'
  },
  modalSubTitle: {
    width: 500,
    paddingBottom: '10px',
    lineHeight: '18px'
  }
}));

const defaultUploadModalState = { single: false, bulk: false };

const StartRetireHeader = ({
  selectedFilterList,
  deleteSelectedFilter,
  fetchMoreListItems,
  onSearch
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const selectedImportFile = useRef(null);
  const [openUploadModal, setOpenUploadModal] = useState(defaultUploadModalState);
  const [failData, setFailData] = useState('');

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.START_RETIRE);

  const {
    bulkUpload: { isSuccess: isSuccessBulkUpload, errorFile, isLoading },
    singleBarcodeUpload: { isSuccess: singleUploadSuccess, isLoading: isLoadingSingleUpload }
  } = useSelector((state) => state.startRetire);
  const closeModal = useCallback(() => {
    setFailData('');
    dispatch(bulkUploadReset());
    setOpenUploadModal(defaultUploadModalState);
  }, []);

  const sampleCSV = [['BARCODE', 'NEW PID', 'OLD PID']];

  useEffect(() => {
    if (isSuccessBulkUpload) {
      setFailData('');
      fetchMoreListItems();
    }
  }, [dispatch, isSuccessBulkUpload]);

  useEffect(() => {
    if (singleUploadSuccess) {
      fetchMoreListItems();
      setOpenUploadModal(defaultUploadModalState);
      dispatch(singleBarcodeUploadReset());
    }
  }, [singleUploadSuccess]);

  useEffect(() => {
    if (errorFile) {
      setFailData(CONTENT.INVALID_CONTENT);
    }
  }, [errorFile]);

  const modalSubTitle = () => (
    <p className={classes.modalSubTitle}>{CONTENT.SELECT_CSV_FILE_IMPORT_PID_DETLS}</p>
  );

  const openModalFun = (name) => setOpenUploadModal((oldState) => ({ ...oldState, [name]: true }));

  const upLoadSelectedFileFun = useCallback((file) => {
    if (file) {
      const formData = new FormData();
      formData.set('csvFile', file);
      dispatch(bulkUploadLoad(formData));
    }
  }, []);

  const downloadreportFun = () => {
    fileDownload(errorFile, 'CourierCutoffUploaddError');
  };

  const delayedQuery = useCallback(
    debounce(({ target: { value } }) => onSearch(value), 500),
    []
  );

  return (
    <>
      <Box className={classes.container}>
        <Box className="display-flex gap10">
          <Box width={380}>
            <LkInput
              fullWidth
              variant="outlined"
              id="globalSearchId"
              onChange={delayedQuery}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                  </InputAdornment>
                )
              }}
              placeholder={CONTENT.SEARCH_BARCODE_HERE}
            />
          </Box>

          <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />
        </Box>

        <Box>
          <Button
            color="primary"
            className={classes.button}
            type="submit"
            variant="contained"
            onClick={() => openModalFun('single')}
          >
            {CONTENT.SINGLE_PID}
          </Button>
          <Button
            color="primary"
            className={classes.button}
            type="submit"
            name="bulk"
            variant="contained"
            data-cy="upload Start retire"
            onClick={() => openModalFun('bulk')}
          >
            {CONTENT.BULK_UPLOAD}
          </Button>
        </Box>
      </Box>
      <FileUpload
        open={openUploadModal.bulk}
        close={closeModal}
        subTitle={modalSubTitle()}
        selectFiles={upLoadSelectedFileFun}
        sampleCSV={sampleCSV}
        checkedItemLabel=""
        resetState={(e) => {
          selectedImportFile.current = e;
        }}
        sampleFileName="sample_start_retire_upload"
        showSampleArray={false}
        failDataMessage={failData}
        importLoading={isLoading}
        disableImport
        uploadCsvPass={isSuccessBulkUpload}
        downloadReport={downloadreportFun}
      />
      {openUploadModal.single && (
        <SinglePidUploadModal
          isLoading={isLoadingSingleUpload}
          open={openUploadModal.single}
          close={closeModal}
        />
      )}
    </>
  );
};

export default StartRetireHeader;
