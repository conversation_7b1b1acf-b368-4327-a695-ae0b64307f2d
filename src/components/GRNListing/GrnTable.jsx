/* eslint-disable no-param-reassign */

import React, { useState, useEffect, useCallback } from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import LockIcon from '@mui/icons-material/Lock';

import { useSelector, useDispatch } from 'react-redux';
import { toastOpen } from 'redux/actionCreators/toast';
import { grnSearchLoad, reassignGRNReset } from 'redux/actionCreators/grnListing';

import LkToolTip from 'components/ToolTip/ToolTip';
import LkTable from 'components/MaterialUi/LkTable/LkTable';

import { genericDateFormatted, getQueryParam } from 'utils/helpers';
import useWindowResize from 'common/useWindowResize';

import { LOCALISATION } from 'redux/reducers/localisation';
import LkChip from '../MaterialUi/LkChip';
import ReassignUserModal from './ReassignUserModal';
import ReassignSuccessModal from './ReassignSuccessModal';
import './grntable.css';

const styles = makeStyles()(() => ({
  container: {
    borderTop: '1px solid #DDDDDD',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
    height: 600,
    position: 'relative'
  },
  vendorName: {
    width: 160
  },
  tableCellMaxWidth: {
    width: 110
  },
  showingButton: {
    position: 'fixed',
    bottom: 60
  },
  showingButtonData: {
    border: '1px solid #DDDDDD',
    background: 'white',
    boxShadow: '0px 16px 32px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  }
}));

const statusNameMapping = {
  created: 'Created',
  closed: 'Closed',
  in_progress: 'In Progress',
  CREATED: 'created',
  IQC_IN_PROGRESS: 'IQC_IN_PROGRESS',
  IQC_COMPLETE: 'IQC_COMPLETE'
};

const statusMapping = {
  created: 'success',
  closed: '',
  in_progress: 'brightYellow',
  CREATED: 'success',
  IQC_IN_PROGRESS: 'warning',
  IQC_COMPLETE: 'primary'
};

const PAGE_SIZE = 35;

let fromPo = null;
let totalApiCall = 0;

const GRNTable = ({ objPayload, sortingData, selectedFilterList, onChangeFilterList }) => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 300);

  const [modalData] = useState({});
  const [initialSortByState, setInitalSortByState] = useState({
    id: 'created_at',
    order: 'DESC'
  });
  const [openSuccessModal, setOpenSuccessModal] = useState(false);
  const [assignTo, setAssignTo] = useState('not Found');
  const [reAssignModal, setReassignModal] = useState(false);
  const [tableHeightValue, setTableHeightValue] = useState(0);

  const {
    grnSearchSuccess,
    grnSearchFail,
    grnSearchLoading,
    reassignSuccess,
    reassignFail,
    totalGrnRecordsCount
  } = useSelector((state) => state.grnListing);
  const {
    autoSuggestionListGRN_GRN_Number,
    autoSuggestionListGRN_PO,
    autoSuggestionListGRN_Vendor,
    autoSuggestionListGRN_Vendor_Invoice_Number,
    autoSuggestionListGRN_Assigned_To,
    autoSuggestionListGRN_Created_By,
    autoSuggestionListGRN_Status
  } = useSelector((state) => state.filters);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_LIST);

  useEffect(
    () => () => {
      fromPo = null;
      totalApiCall = 0;
    },

    []
  );

  useEffect(() => {
    if (grnSearchSuccess && reassignSuccess) {
      setOpenSuccessModal(true);
      dispatch(reassignGRNReset());
    }
  }, [reassignSuccess, dispatch, grnSearchSuccess]);

  useEffect(() => {
    if (grnSearchSuccess) {
      totalApiCall = 1;
    }
  }, [grnSearchSuccess]);

  useEffect(() => {
    if (grnSearchFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: grnSearchFail?.message || grnSearchFail?.meta?.displayMessage,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [grnSearchFail, dispatch]);

  useEffect(() => {
    fromPo = getQueryParam(window.location.search.replace('?', ''), 'fromPo');
    if (fromPo) {
      setTableHeightValue(tableHeight - 60);
    } else {
      setTableHeightValue(tableHeight);
    }
  }, []);

  useEffect(() => {
    if (sortingData) {
      const { key, order } = sortingData;

      setInitalSortByState({ id: key, order });
    }
  }, [sortingData.key, sortingData]);

  // const unBlockPidFun = (grn_code) => {
  //   handleChange('e', 1, grn_code);
  // };

  // const onHoverFun = (data, index) => {
  //     const { created_by, grn_code, invoice_num } = data

  //     setModalData({ created_by, grn_code, invoice_num, index })
  // }
  const closeReassignModal = useCallback((value) => setReassignModal(value), [setReassignModal]);

  const closeSuccessModal = useCallback(() => setOpenSuccessModal(false), [setOpenSuccessModal]);

  const setValueFun = useCallback((e) => setAssignTo(e.target.value), [setAssignTo]);

  const fetchMoreListItems = (sortType, sortKey, nextPagePage) => {
    objPayload.payload.pageRequest.sortKey = sortKey;
    objPayload.payload.pageRequest.sortOrder = sortType;
    objPayload.payload.pageRequest.pageNumber = nextPagePage;
    sortingData.key = sortKey;
    sortingData.order = sortType;
    if (totalApiCall) {
      if (!nextPagePage) {
        if (!grnSearchLoading) {
          // to restrict 2 api calls on reset  filters
          objPayload.isReset = true;
          dispatch(grnSearchLoad(objPayload));
        }
      } else {
        objPayload.isReset = false;
        dispatch(grnSearchLoad(objPayload));
      }
    }
  };

  const headerConfig = [
    {
      name: CONTENT.GRN_NO,
      key: 'grn_code',
      align: 'left',
      supportSort: true,
      marginLeft: '20px',
      style: { minWidth: '180px', maxWidth: '180px' },
      formatBody: useCallback(
        ({ grn_code }) => (
          <a
            href={`${window.location.origin}/grn/details/${grn_code}`}
            className="text-primary fw-bold "
          >
            {' '}
            {grn_code}
          </a>
        ),
        []
      ),
      filterData: {
        type: 'autoSelect',
        columnName: 'GRN_GRN_Number',
        listData: autoSuggestionListGRN_GRN_Number,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.GRN_STATUS,
      key: 'grn_status',
      align: 'left',
      supportSort: true,
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ grn_status }) => (
          <LkChip label={statusNameMapping[grn_status]} type={statusMapping[grn_status]} />
        ),
        []
      ),
      filterData: {
        type: 'status',
        columnName: 'GRN_Status',
        listData: autoSuggestionListGRN_Status,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.UNICOMM_GRN,
      key: 'unicom_grn_code',
      align: 'left',
      className: classes.vendorName,
      supportSort: true,
      style: { minWidth: '150px', maxWidth: '150px' }
    },
    {
      name: CONTENT.PO_NO,
      key: 'po_num',
      align: 'left',
      supportSort: true,
      marginLeft: '110px',
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ po_num }) => (
          <a
            href={`${window.location.origin}/po/detail?poNum=${po_num}&fromPo=true`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary fw-bold "
          >
            {' '}
            {po_num}
          </a>
        ),
        []
      ),
      filterData: {
        type: 'autoSelect',
        columnName: 'GRN_PO',
        listData: autoSuggestionListGRN_PO,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.VENDOR_INV_NO,
      key: 'vendor_invoice_number',
      align: 'left',
      supportSort: true,
      style: { minWidth: '170px', maxWidth: '170px' },
      formatBody: useCallback(
        ({ invoice_reference_number, vendor_invoice_number }) => (
          <a
            href={`
            ${window.location.origin}/invoice/view?invoiceRefNumber=${invoice_reference_number}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary fw-bold "
          >
            {' '}
            {vendor_invoice_number}
          </a>
        ),
        []
      ),
      filterData: {
        type: 'autoSelect',
        columnName: 'GRN_Vendor_Invoice_Number',
        listData: autoSuggestionListGRN_Vendor_Invoice_Number,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.VENDOR_NAME,
      key: 'vendor_code',
      align: 'left',
      className: classes.vendorName,
      supportSort: true,
      marginLeft: '40px',
      style: { minWidth: '170px', maxWidth: '170px' },
      filterData: {
        type: 'autoSelect',
        columnName: 'GRN_Vendor',
        listData: autoSuggestionListGRN_Vendor,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.CREATED_BY,
      key: 'created_by',
      align: 'left',
      supportSort: true,
      marginLeft: '70px',
      style: { minWidth: '150px', maxWidth: '150px' },
      filterData: {
        type: 'autoSelect',
        columnName: 'GRN_Created_By',
        listData: autoSuggestionListGRN_Created_By,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.ASSIGNED_TO,
      key: 'assigned_to',
      supportSort: true,
      marginLeft: '50px',
      align: 'left',
      style: { minWidth: '150px', maxWidth: '150px' },
      filterData: {
        type: 'autoSelect',
        columnName: 'GRN_Assigned_To',
        listData: autoSuggestionListGRN_Assigned_To,
        selectedFilterList,
        apiName: 'grn'
      }
    },
    {
      name: CONTENT.CREATED_ON,
      key: 'created_at',
      align: 'left',
      supportSort: true,
      marginLeft: '-10px',
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ created_at }) => (
          <div className="fw-bold">{genericDateFormatted(created_at, 'utcTOlocalDate')}</div>
        ),
        []
      ),
      filterData: {
        type: 'dateRange',
        columnName: 'GRN_CREATED',
        selectedFilterList
      }
    },
    {
      name: CONTENT.TOTAL_QTY,
      key: 'total_quantity',
      supportSort: true,
      align: 'right',
      style: { minWidth: '130px', maxWidth: '130px' }
    },
    {
      name: CONTENT.QC_COMPLETE,
      key: 'qc_complete',
      supportSort: true,
      align: 'right',
      className: classes.tableCellMaxWidth,
      style: { minWidth: '160px', maxWidth: '160px' }
    },
    {
      name: '',
      key: '',
      align: 'right',
      style: { minWidth: '90px', maxWidth: '90px' },
      supportSort: true,
      formatBody: useCallback(
        ({ grn_status }) =>
          !fromPo && (
            <Box display="flex" className="grnactions" justifyContent="space-around">
              {grn_status !== 'closed' && (
                <>
                  {' '}
                  <Box>
                    <LkToolTip
                      placement="left-end"
                      title={
                        <Box p={1} className="fs14 ">
                          {CONTENT.UNBLOCK_PRODUCTS}{' '}
                        </Box>
                      }
                    >
                      <LockIcon
                        // disabling Blocked tabs for now
                        // onClick={() => unBlockPidFun(data.grn_code)}
                        color="disabled"
                      />
                    </LkToolTip>
                  </Box>
                  <Box mt={0.2}>
                    <LkToolTip
                      placement="left-end"
                      title={
                        <Box p={1} className="fs14 ">
                          {CONTENT.REASSIGN_GRN}
                        </Box>
                      }
                    >
                      {/* <img className="cursor-pointer" onClick={reAssignFun}
                          src={import.meta.env.VITE_STATIC_SERVER + '/images/user.svg'}
                           alt="img" /> */}
                      {/* enable once user authentication comes */}
                      <AssignmentIndIcon color="disabled" />
                    </LkToolTip>
                  </Box>
                </>
              )}
            </Box>
          ),
        []
      )
    }
  ];

  return (
    <div>
      <ReassignSuccessModal
        open={openSuccessModal}
        closeModal={closeSuccessModal}
        assignTo={assignTo}
      />
      {reAssignModal && (
        <ReassignUserModal
          reassignGRNReset={reassignGRNReset}
          reassignFail={reassignFail}
          open={reAssignModal}
          reassignSuccess={reassignSuccess}
          data={modalData}
          closeModal={closeReassignModal}
          value={assignTo}
          setValue={setValueFun}
        />
      )}

      <LkTable
        tableHeight={tableHeightValue}
        headerConfig={headerConfig}
        isDataFetching={grnSearchLoading}
        tableData={grnSearchSuccess}
        totalRowsCount={totalGrnRecordsCount}
        dataRequestFunction={fetchMoreListItems}
        initialSortBy={initialSortByState}
        pageLimit={PAGE_SIZE}
        setFilters={onChangeFilterList}
        rowSize={60}
      />
    </div>
  );
};

export default React.memo(GRNTable);
