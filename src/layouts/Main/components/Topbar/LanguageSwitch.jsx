import React from 'react';
import { useSelector } from 'react-redux';
import MenuItem from '@mui/material/MenuItem';

import { LOCALISATION } from 'redux/reducers/localisation';
import LkInput from 'components/MaterialUi/LkInput';

import localStorageHelper from 'utils/localStorageHelper';

export const SUPPORTED_LANGUAGES = [
  { label: 'ENGLISH', code: 'en' },
  // { label: 'HINDI', code: 'hi' }
  // { label: 'CHINESE', code: 'zh' }
];

const LanguageSwitch = () => {
  const localeData = useSelector((state) => state[LOCALISATION].localeData.global);
  const currentLanguage = localStorageHelper.getItem('lang-code');

  const handleLangChange = (e) => {
    const lang = e.target.value;
    localStorageHelper.setItem('lang-code', lang);
    window.location.reload();
  };

  return (
    <LkInput
      sx={{ marginLeft: 2, width: '150px' }}
      select
      options={SUPPORTED_LANGUAGES}
      onChange={handleLangChange}
      value={currentLanguage || 'en'}
    >
      {SUPPORTED_LANGUAGES.map((lang) => (
        <MenuItem key={lang.code} value={lang.code}>
          {localeData[lang.label]}
        </MenuItem>
      ))}
    </LkInput>
  );
};

export default LanguageSwitch;
