import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';

import Box from '@mui/material/Box';

import {
  DEFAULT_FILTERS,
  MONITOR_TYPES,
  MONITOR_V3_FR_TYPES,
  TABLE_INFO_API_CONFIG,
  defaultTotalOrderPayload
} from 'config/monitor';
import { fileDownload, listToCSVString } from 'utils/helpers';
import { invert } from 'utils/lodash';
import MonitorHeader from './MonitorHeader/MonitorHeader';
import MonitorBody from './MonitorBody/MonitorBody';

import {
  monitorDataInfoTableRequest,
  monitorDataRequest,
  monitorFiltersReset,
  monitorJitDataInfoTableRequest,
  monitorTagDetailsExportDataRequest,
  monitorTagDetailsExportDataReset,
  monitorTotalOrderDataLoad
} from '../../redux/actionCreators/monitor';

const useStyles = makeStyles()(() => ({
  container: {
    background: '#F5F5F5',
    height: 'calc(100vh - 65px)'
  }
}));
const Monitor = () => {
  const { classes } = useStyles();
  const [showTable, setShowTable] = useState(false);
  const [monitorHeaderHeight, setMonitorHeaderHeight] = useState(0);
  const [selectedOrderType, setSelectedOrderType] = useState(MONITOR_TYPES.FULFILLABLE_ORDERS);
  const [totalOrderSelectedTab, setTotalOrderSelectedTab] = useState('');
  const [totalOrderFilters, setTotalOrderFilters] = useState(defaultTotalOrderPayload());
  const [activeTab, setActiveTab] = useState('');
  const { data: exportData } = useSelector((state) => state.monitor.monitorTagDetailsExportList);
  const columnsRef = useRef();

  const ref = useRef();
  const dispatch = useDispatch();
  const {
    selected: { data: filtersData },
    templates: { dataLoaded }
  } = useSelector((state) => state.monitor.filters);

  const getMonitorData = () =>
    dispatch(
      monitorDataRequest({
        filters: filtersData,
        version: 'v3',
        activeTab,
        selectedOrderType
      })
    );

  const callMonitorInfoTableApi = (key, filterKey, filterValue) => {
    const filterPayload = { ...filtersData, BINARY: { [filterKey]: filterValue } };
    dispatch(
      monitorDataInfoTableRequest({
        key,
        filters: filterPayload,
        version: 'v3'
      })
    );
  };

  const getMonitorInfoTableData = useCallback(() => {
    dispatch(
      monitorDataInfoTableRequest({
        key: 'Total',
        filters: filtersData,
        version: 'v3'
      })
    );
    TABLE_INFO_API_CONFIG.forEach(({ key, filterKey, filterValue }) =>
      callMonitorInfoTableApi(key, filterKey, filterValue)
    );
    dispatch(
      monitorJitDataInfoTableRequest({
        filters: { ...filtersData, BINARY: { JIT: true } },
        version: 'v3'
      })
    );
  }, []);

  const exportToCSV = (exportDataDetails) => {
    const csvString = listToCSVString(exportDataDetails, columnsRef.current, columnsRef.current);
    fileDownload(csvString, totalOrderSelectedTab);
  };

  const setSelectedOrderTypeFun = (value) => {
    setSelectedOrderType(value);
    if (value === 'TOTAL_ORDERS') {
      setTotalOrderSelectedTab('FULFILLABLE ORDERS');
      const payload = defaultTotalOrderPayload({ isFulfillable: 0 });
      setTotalOrderFilters(payload);
      dispatch(monitorTotalOrderDataLoad(payload));
    } else {
      setTotalOrderSelectedTab('');
    }
  };

  const totalOrderExportFun = (frTag, priority, pageSize) => {
    const REVERSE_VALUE = invert(MONITOR_V3_FR_TYPES);

    dispatch(
      monitorTagDetailsExportDataRequest({
        filters: {
          ...DEFAULT_FILTERS,
          PICKING_PRIORITY: [priority]
        },
        category: REVERSE_VALUE[totalOrderSelectedTab],
        frTag,
        page: 0,
        pageSize,
        version: 'v3',
        globalSearch: ''
      })
    );
  };

  useEffect(() => {
    if (dataLoaded) {
      getMonitorData();
    }
  }, [filtersData, dataLoaded]);

  useEffect(() => {
    getMonitorInfoTableData();
  }, []);

  useEffect(
    () => () => {
      dispatch(monitorFiltersReset());
    },
    []
  );

  useEffect(() => {
    setTimeout(() => {
      setMonitorHeaderHeight(ref.current?.clientHeight || 0);
    }, 100);
  }, [showTable, ref.current?.clientHeight]);

  const refreshDataFun = () => {
    if (selectedOrderType === 'TOTAL_ORDERS') {
      dispatch(monitorTotalOrderDataLoad(totalOrderFilters));
    } else {
      getMonitorData();
      getMonitorInfoTableData();
    }
  };

  useEffect(() => {
    if (exportData.length && selectedOrderType === 'TOTAL_ORDERS') {
      columnsRef.current = Object.keys(exportData[0]);
      exportToCSV(exportData);
      dispatch(monitorTagDetailsExportDataReset());
    }
  }, [exportData, selectedOrderType, totalOrderSelectedTab]);

  const renderScreen = () => (
    <>
      <Box ref={ref}>
        <MonitorHeader
          selectedTab={activeTab}
          changeTab={setActiveTab}
          refreshData={refreshDataFun}
          setTotalOrderFilters={setTotalOrderFilters}
          selectedOrderType={selectedOrderType}
          setSelectedOrderType={setSelectedOrderTypeFun}
          showTable={showTable}
          setShowTable={setShowTable}
          totalOrderSelectedTab={totalOrderSelectedTab}
          setTotalOrderSelectedTab={setTotalOrderSelectedTab}
          totalOrderFilters={totalOrderFilters}
        />
      </Box>

      <MonitorBody
        selectedTab={selectedOrderType}
        monitorHeaderHeight={monitorHeaderHeight}
        activeCategory={activeTab}
        totalOrderSelectedTab={totalOrderSelectedTab}
        totalOrderExportFun={totalOrderExportFun}
      />
    </>
  );

  return <Box className={classes.container}>{renderScreen()}</Box>;
};

export default Monitor;
