// load order details
export const GET_ORDER_DETAILS_LOAD = 'GET_ORDER_DETAILS_LOAD';
export const GET_ORDER_DETAILS_SUCCESS = 'GET_ORDER_DETAILS_SUCCESS';
export const GET_ORDER_DETAILS_FAIL = 'GET_ORDER_DETAIL_FAIL';
export const GET_ORDER_DETAILS_RESET = 'GET_ORDER_DETAIL_RESET';
export const UPDATE_ORDER_DETAILS_STATUS = 'UPDATE_ORDER_DETAILS_STATUS';

// load order info by barcode
export const GET_ORDER_BY_BARCODE_LOAD = 'GET_ORDER_BY_BARCODE_LOAD';
export const GET_ORDER_BY_BARCODE_SUCCESS = 'GET_ORDER_BY_BARCODE_SUCCESS';
export const GET_ORDER_BY_BARCODE_FAIL = 'GET_ORDER_BY_BARCODE_FAIL';

// load unicom comments
export const GET_UNICOM_COMMENTS_LOAD = 'GET_UNICOM_COMMENTS_LOAD';
export const GET_UNICOM_COMMENTS_SUCCESS = 'GET_UNICOM_COMMENTS_SUCCESS';
export const GET_UNICOM_COMMENTS_FAIL = 'GET_UNICOM_COMMENTS_FAIL';

// clear order data
export const CLEAR_ORDER_DATA = 'CLEAR_ORDER_DATA';

// select product tab
export const SELECT_PRODUCT_TAB = 'SELECT_PRODUCT_TAB';

// set Shipment data
export const SET_SHIPMENT_DATA = 'SET_SHIPMENT_DATA';

export const SET_VALUE_BY_KEY = 'SET_VALUE_BY_KEY';

export const SET_MULTIPLE_VALUES = 'SET_MULTIPLE_VALUES';

export const SCAN_NEXT_BARCODE = 'SCAN_NEXT_BARCODE';

// SET LENS FOCUS when barcode scan
export const SET_LENS_FOCUS = 'SET_LENS_FOCUS';

// If button clicked in hold modal like Cancel or Submit (Used to set focus on barcode field)
export const IS_HOLD_UNHOLD_VISIT = 'IS_HOLD_UNHOLD_VISIT';

// Reset both inital apis (orderDetails and orderInfo) responses
export const RESET_INITIAL_API_RESPONSE = 'RESET_INITIAL_API_RESPONSE';
