import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import Skeleton from '@mui/material/Skeleton';
import Box from '@mui/material/Box';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';
import IconButton from '@mui/material/IconButton';
import { get } from 'utils/lodash';
import { StickyTableCell, StyledTableCell, StyledTableRow } from '../LkTableCommon';

const useStyles = makeStyles()(() => ({
  tableBody: {
    height: '100%',
    width: '100%'
  },
  rightStickyCell: {
    right: 0,
    background: 'inherit'
  },
  leftStickyCell: {
    left: 0,
    background: 'inherit'
  },
  dataRowCell: {
    fontSize: 15,
    lineHeight: '150%',
    letterSpacing: '0.15px',
    color: '#3C3C3C',
    wordBreak: 'break-word'
  },
  selectedRow: {
    background: '#00f2f714 !important'
  },
  warnedRow: {
    background:
      'linear-gradient(0deg,rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), #F44336 !important'
  },
  warnedCell: {
    color: '#D63D33'
  },
  expandedRow: {
    background: '#EDFAFB !important',
    border: '1px solid #4AC8D4',
    borderBottom: 'none'
  },
  expandedRowContent: {
    border: '1px solid #4AC8D4',
    borderTop: 'none'
  },
  highlightExpandedRow: {
    background: '#EDFAFB !important'
  },
  expandableBox: {
    marginLeft: -10,
    display: 'flex',
    alignItems: 'center'
  },
  iconButton: {
    padding: 1
  },
  childButton: {
    marginLeft: 25
  },
  disabledArrowBox: {
    marginLeft: 15
  }
}));

const LkTableBodyRow = ({
  rowIndex,
  selectedRows,
  warnedRows,
  isLoading,
  headerConfig,
  style,
  isExpanded,
  toggleRowSize,
  rowSize,
  columnWidth,
  plainTheme,
  border,
  firstLoadingRowRef,
  renderExtenedComponent: ChildComponent = null,
  id = null,
  rowData = null,
  onRowClick = null,
  isNonVertualizedTable = false
}) => {
  const { classes, cx } = useStyles();
  const rowStyles = { ...style };
  const formatCellData = useCallback(
    ({ key, formatBody, defaultValue = '-', enableExpander = false, isExpandable }) => {
      let isRowExpandable = true;
      if (isExpandable) {
        isRowExpandable = isExpandable(rowData);
      }
      let cell;
      if (formatBody) {
        cell = formatBody(rowData, rowIndex, isExpanded);
      } else {
        const value = get(rowData, key, null);
        cell = value ?? defaultValue;
      }

      if (enableExpander && isRowExpandable) {
        cell = (
          <Box className={classes.expandableBox}>
            <IconButton
              aria-label="expand row"
              size="small"
              onClick={toggleRowSize}
              data-cy={`expand-icon-${rowIndex}`}
              className={classes.iconButton}
            >
              {isExpanded ? (
                <KeyboardArrowDown data-cy="KeyboardArrowDown" />
              ) : (
                <KeyboardArrowRight data-cy="KeyboardArrowRight" />
              )}
            </IconButton>
            {cell}
          </Box>
        );
      } else if (enableExpander) {
        return <Box className={classes.disabledArrowBox}>{cell}</Box>;
      }
      return cell;
    },
    [isExpanded, rowData]
  );

  if (!isNonVertualizedTable) {
    rowStyles.display = 'table';
  }

  return (
    <>
      <StyledTableRow
        data-cy={`table-row-${rowIndex}`}
        component="div"
        style={{ ...rowStyles, height: rowSize }}
        className={cx({
          [classes.warnedRow]: warnedRows.includes(id),
          [classes.selectedRow]: selectedRows.includes(id) && !warnedRows.includes(id),
          [classes.expandedRow]: isExpanded && !warnedRows.includes(id)
        })}
        onClick={onRowClick ? () => onRowClick(rowData, rowIndex) : null}
        plaintheme={plainTheme.toString()}
        border={border?.toString()}
        ref={rowIndex === 0 && isLoading ? firstLoadingRowRef : null}
      >
        {headerConfig.map(
          (
            {
              key = null,
              align = 'left',
              leftSticky = false,
              rightSticky = false,
              style: cellStyles,
              ...columnConfig
            },
            index
          ) => {
            let cellData = null;
            if (isLoading) {
              cellData = <Skeleton variant="rectangular" animation="wave" />;
            } else {
              cellData = formatCellData({ ...columnConfig, key, rowData });
            }

            if (leftSticky || rightSticky) {
              return (
                <StickyTableCell
                  data-cy={`table-cell-${rowIndex}-${index}`}
                  component="div"
                  key={key}
                  align={align}
                  className={cx(classes.dataRowCell, {
                    [classes.leftStickyCell]: leftSticky,
                    [classes.rightStickyCell]: rightSticky
                  })}
                  style={{ minWidth: columnWidth, maxWidth: columnWidth, ...cellStyles }}
                >
                  {cellData}
                </StickyTableCell>
              );
            }
            return (
              <StyledTableCell
                data-cy={`table-cell-${rowIndex}-${index}`}
                component="div"
                key={key}
                align={align}
                className={cx(classes.dataRowCell, {
                  [classes.warnedCell]: warnedRows.includes(id)
                })}
                style={{ minWidth: columnWidth, maxWidth: columnWidth, ...cellStyles }}
              >
                {cellData}
              </StyledTableCell>
            );
          }
        )}
      </StyledTableRow>
      {isExpanded && ChildComponent && (
        <StyledTableRow
          component="div"
          style={{
            ...style,
            ...(!isNonVertualizedTable
              ? { top: style.top + rowSize, height: style.height - rowSize }
              : {})
          }}
          className={cx(classes.expandedRowContent, {
            [classes.warnedRow]: warnedRows.includes(id),
            [classes.highlightExpandedRow]: isExpanded && !warnedRows.includes(id)
          })}
          plaintheme={plainTheme.toString()}
        >
          {isNonVertualizedTable ? (
            <StyledTableCell colSpan={headerConfig.length}>
              {ChildComponent(rowData)}
            </StyledTableCell>
          ) : (
            ChildComponent(rowData)
          )}
        </StyledTableRow>
      )}
    </>
  );
};

export default LkTableBodyRow;

LkTableBodyRow.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  rowIndex: PropTypes.number.isRequired,
  rowData: PropTypes.instanceOf(Object),
  selectedRows: PropTypes.instanceOf(Array).isRequired,
  warnedRows: PropTypes.instanceOf(Array).isRequired,
  isLoading: PropTypes.bool.isRequired,
  headerConfig: PropTypes.arrayOf(PropTypes.instanceOf(Object).isRequired).isRequired,
  style: PropTypes.instanceOf(Object).isRequired,
  isExpanded: PropTypes.bool.isRequired,
  toggleRowSize: PropTypes.func.isRequired,
  rowSize: PropTypes.number.isRequired,
  renderExtenedComponent: PropTypes.func,
  columnWidth: PropTypes.number.isRequired,
  onRowClick: PropTypes.func,
  isNonVertualizedTable: PropTypes.bool
};
