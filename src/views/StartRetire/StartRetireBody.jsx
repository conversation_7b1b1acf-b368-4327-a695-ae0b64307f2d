import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';
import { genericDateFormatted, roundUptoFixedDigits } from 'utils/helpers';
import { START_RETIRE } from 'redux/reducers/startRetire';
import LkChip from 'components/MaterialUi/LkChip';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({
  root: {
    paddingTop: '15px'
  }
}));

const statusMapping = {
  DONE: 'processing',
  FAILED: 'failure',
  IN_PROGRESS: 'warning'
};

const keyMapping = {
  DONE: 'Done',
  FAILED: 'Not Done',
  IN_PROGRESS: 'In Process'
};

const initialSortingData = {
  id: 'createdAt',
  order: 'DESC'
};

const statusFilterList = ['IN_PROGRESS', 'FAILED', 'DONE', 'CREATED'];
const StartRetireBody = ({ fetchMoreData, selectedFilterList, onChangeFilterList }) => {
  const { classes } = useStyles();
  const [tableHeight] = useWindowResize(window.innerHeight - 180);
  const { data, isLoading, totalCount } = useSelector((state) => state[START_RETIRE].list);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.START_RETIRE);

  const headerConfig = [
    {
      name: CONTENT.START_RETIRE_ID,
      key: 'startRetireId',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: CONTENT.SOURCE_PID,
      key: 'oldPid',
      style: { minWidth: 80, maxWidth: 80 }
    },
    {
      name: CONTENT.DESTINATION_PID,
      key: 'newPid',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.BARCODE,
      key: 'barcode',
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ barcode }) => (
          <Link target="_blank" to={`/historyPage/${barcode}`}>
            {barcode}
          </Link>
        ),
        []
      )
    },
    {
      name: CONTENT.UNIT_PRICE,
      key: 'unitPrice',
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ unitPrice }) => (unitPrice != null ? roundUptoFixedDigits(unitPrice, 2) : '-'),
        []
      )
    },
    {
      name: CONTENT.DATE,
      key: 'createdAt',
      supportSort: true,
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ createdAt }) => (
          <Box className="fw-bold">{genericDateFormatted(createdAt, 'utcTOlocalDate')}</Box>
        ),
        []
      ),
      filterData: {
        type: 'dateRange',
        columnName: 'CREATED',
        selectedFilterList
      }
    },
    {
      name: CONTENT.REQESTED_BY,
      key: 'createdBy',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      style: { minWidth: 80, maxWidth: 80 },
      formatBody: useCallback(({ status }) => (
        <LkChip label={keyMapping[status] || status} type={statusMapping[status] || status} />
      )),
      filterData: {
        type: 'singleSelect',
        columnName: 'status',
        listData: statusFilterList,
        selectedFilterList
      }
    }
  ];

  return (
    <Box className={classes.root}>
      <LkTable
        rowSize={60}
        tableData={data}
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        totalRowsCount={totalCount}
        dataRequestFunction={fetchMoreData}
        headerConfig={headerConfig}
        initialSortBy={initialSortingData}
        setFilters={onChangeFilterList}
      />
    </Box>
  );
};

export default StartRetireBody;
