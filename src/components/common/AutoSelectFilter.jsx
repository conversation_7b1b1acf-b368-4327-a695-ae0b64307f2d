/* eslint-disable no-param-reassign */
import React, { useState, useEffect, useCallback } from 'react';

import Checkbox from '@mui/material/Checkbox';
import { Box } from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { makeStyles } from 'tss-react/mui';
import LkInput from 'components/MaterialUi/LkInput';
import { debounce } from 'utils/helpers';
import { useDispatch, useSelector } from 'react-redux';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import IconButton from '@mui/material/IconButton';
import Spinner from 'components/Spinner/Spinner';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { searchSuggestionLoad, searchSuggestionReset } from '../../redux/actionCreators/filters';
import FilterWrapper from './FilterWrapper';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" style={{ fill: '#999999' }} />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const useStyles = makeStyles()(() => ({
  container: {
    paddingBottom: 16,
    height: 150,
    overflow: 'scroll'
  },
  menuList: {
    maxHeight: 30
  },
  listItem: {
    padding: 0,
    margin: 0,
    paddingLeft: '15px'
  },
  listItemIcon: {
    minWidth: '30px'
  }
}));

const AutoSelectFilter = ({
  onSubmit,
  marginLeft,
  selectedFilterList,
  columnName,
  listData,
  postData,
  url,
  localSearch,
  apiName = '',
  type = 'GET',
  defaultValue = {
    key: '',
    value: ''
  }
}) => {
  const { classes } = useStyles();
  const [data, setData] = useState([]);
  const [listDataState, setListDataState] = useState([]);
  const [value, setValue] = useState('');
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);

  const { autoSuggestionListLoading, autoSuggestionListFail } = useSelector(
    (state) => state.filters
  );

  // const [placement, setPlacement] = useState();

  useEffect(() => {
    setListDataState(listData);
  }, [listData]);

  useEffect(() => {
    if (open && !listData) {
      if (type === 'POST') {
        dispatch(searchSuggestionLoad({ data: postData, type, url }));
      } else {
        dispatch(
          searchSuggestionLoad({
            data: {
              key: mapSearchFilterKey[columnName],
              value: '',
              name: columnName,
              apiName,
              [defaultValue.key]: defaultValue.value
            },
            type
          })
        );
      }
    }
  }, [open, listData, dispatch, columnName, apiName]);

  const submitData = () => {
    let temp = data.map((t) => ({ key: columnName, value: t }));
    if (!temp.length && value) {
      temp = [{ key: columnName, value }];
    }
    onSubmit(temp, columnName);
  };
  const onChangeFun = (onChangeValue) => {
    const currentIndex = data.indexOf(onChangeValue);
    const newChecked = [...data];

    if (currentIndex === -1) {
      newChecked.push(onChangeValue);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setData(newChecked);
  };
  const updateFilterChanges = useCallback(() => {
    let temp = selectedFilterList.filter((t) => t.key === columnName);
    temp = temp.map((t) => t.value);
    setData([...temp]);
  }, [setData, selectedFilterList, columnName]);

  useEffect(() => {
    updateFilterChanges();
  }, [selectedFilterList, updateFilterChanges]);

  const delayedQuery = useCallback(
    debounce((q) => {
      if (type === 'POST') {
        postData.searchKey = q;
        dispatch(searchSuggestionLoad({ data: postData, type, url }));
      } else {
        dispatch(
          searchSuggestionLoad({
            data: {
              key: mapSearchFilterKey[columnName],
              value: q,
              name: columnName,
              apiName,
              [defaultValue.key]: defaultValue.value
            },
            type
          })
        );
      }
    }, 300),
    []
  );

  const onChangeInputValue = ({ target: { value: inputValue } }) => {
    setValue(inputValue);
    if (localSearch) {
      const filteredData = listData.filter((row) =>
        row?.toLowerCase().includes(inputValue?.toLowerCase())
      );
      setListDataState(filteredData);
    } else {
      if (type === 'POST') {
        postData.searchKey = inputValue;
      }
      delayedQuery(inputValue);
    }
  };

  const clearSearchInput = () => {
    if (!localSearch) {
      if (type === 'POST') {
        dispatch(searchSuggestionLoad({ data: postData, type, url }));
      } else {
        dispatch(
          searchSuggestionLoad({
            data: {
              key: mapSearchFilterKey[columnName],
              value: '',
              name: columnName,
              apiName,
              [defaultValue.key]: defaultValue.value
            },
            type
          })
        );
      }
    } else {
      setListDataState(listData);
    }

    setValue('');
  };

  const setOpenfun = useCallback((v) => setOpen(v), [setOpen]);

  useEffect(() => {
    if (autoSuggestionListFail) {
      setOpenfun(false);
      dispatch(searchSuggestionReset());
    }
  }, []);

  const onSubmitWithEnter = (event) => {
    event.stopPropagation();
    if (event.which === 13) {
      submitData();
      setOpen(!open);
    }
  };

  return (
    <FilterWrapper
      disabled={!data.length}
      open={open}
      setOpen={setOpenfun}
      width="210px"
      onSubmit={submitData}
      marginLeft={marginLeft}
      onCloseUpdateFilter={updateFilterChanges}
      dataCy={columnName}
    >
      <Box pl={2} pr={2} pt={2} pb={1}>
        <LkInput
          label="Search here..."
          fullWidth
          value={value}
          onChange={onChangeInputValue}
          onKeyPress={onSubmitWithEnter}
          InputProps={{
            endAdornment: (
              <IconButton
                size="small"
                aria-label="toggle password visibility"
                onClick={clearSearchInput}
                edge="end"
              >
                {value !== '' && <HighlightOffIcon fontSize="small" />}
              </IconButton>
            )
          }}
        />
      </Box>
      <Box className={classes.container}>
        {listData &&
          !autoSuggestionListLoading &&
          (listDataState || []).map((name, index) => {
            const labelId = `checkbox-list-label-${name}`;
            const id = name + index;
            return (
              <ListItem
                data-cy={`checkbox-list-${index}`}
                key={id}
                role={undefined}
                className={classes.listItem}
                button
                onClick={() => onChangeFun(name)}
              >
                <ListItemIcon className={classes.listItemIcon}>
                  <Checkbox
                    edge="start"
                    checked={data.indexOf(name) !== -1}
                    // tabIndex={-1}
                    disableRipple
                    color="primary"
                    inputProps={{ 'aria-labelledby': labelId }}
                    icon={icon}
                    checkedIcon={checkedIcon}
                    onKeyPress={onSubmitWithEnter}
                  />
                </ListItemIcon>
                <ListItemText
                  id={labelId}
                  primary={<div className="fs12"> {name || '(Blanks)'}</div>}
                />
              </ListItem>
            );
          })}

        {autoSuggestionListLoading && (
          <Box textAlign="center" pt={1}>
            <Spinner />
          </Box>
        )}
        {!autoSuggestionListLoading && listDataState?.length === 0 && (
          <Box p={1} textAlign="center" className="fs14">
            No Matching Entries
          </Box>
        )}
      </Box>
    </FilterWrapper>
  );
};

export default AutoSelectFilter;
