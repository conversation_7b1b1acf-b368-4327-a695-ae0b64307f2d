export const MONITOR_DATA_LOAD = 'MONITOR_DATA_LOAD';
export const MONITOR_DATA_SUCCESS = 'MONITOR_DATA_SUCCESS';
export const MONITOR_DATA_ERROR = 'MONITOR_DATA_ERROR';

export const MONITOR_TAG_DETAILS_DATA_LOAD = 'MONITOR_TAG_DETAILS_DATA_LOAD';
export const MONITOR_TAG_DETAILS_DATA_SUCCESS = 'MONITOR_TAG_DETAILS_DATA_SUCCESS';
export const MONITOR_TAG_DETAILS_DATA_ERROR = 'MONITOR_TAG_DETAILS_DATA_ERROR';
export const MONITOR_TAG_DETAILS_EXPORT_DATA_LOAD = 'MONITOR_TAG_DETAILS_EXPORT_DATA_LOAD';
export const MONITOR_TAG_DETAILS_EXPORT_DATA_SUCCESS = 'MONITOR_TAG_DETAILS_EXPORT_DATA_SUCCESS';
export const MONITOR_TAG_DETAILS_EXPORT_DATA_ERROR = 'MONITOR_TAG_DETAILS_EXPORT_DATA_ERROR';
export const MONITOR_TAG_DETAILS_EXPORT_DATA_RESET = 'MONITOR_TAG_DETAILS_EXPORT_DATA_RESET';
export const MONITOR_FILTERS_UPDATE = 'MONITOR_FILTERS_UPDATE';
export const MONITOR_FILTERS_DATA_RESET = 'MONITOR_FILTERS_DATA_RESET';

export const MONITOR_FILTERS_DATA_LOAD = 'MONITOR_FILTERS_DATA_LOAD';
export const MONITOR_FILTERS_DATA_SUCCESS = 'MONITOR_FILTERS_DATA_SUCCESS';
export const MONITOR_FILTERS_DATA_ERROR = 'MONITOR_FILTERS_DATA_ERROR';

export const MONITOR_FILTERS_DATA_SAVE_LOAD = 'MONITOR_FILTERS_DATA_SAVE_LOAD';
export const MONITOR_FILTERS_DATA_SAVE_SUCCESS = 'MONITOR_FILTERS_DATA_SAVE_SUCCESS';
export const MONITOR_FILTERS_DATA_SAVE_RESET = 'MONITOR_FILTERS_DATA_SAVE_RESET';
export const MONITOR_FILTERS_DATA_SAVE_ERROR = 'MONITOR_FILTERS_DATA_SAVE_ERROR';

export const MONITOR_DETAILS_FILTERS_CONFIG_UPDATE = 'MONITOR_DETAILS_FILTERS_CONFIG_UPDATE';
export const MONITOR_DETAILS_FILTERS_CONFIG_RESET = 'MONITOR_DETAILS_FILTERS_CONFIG_RESET';

export const MONITOR_DATA_INFO_TABLE_LOAD = 'MONITOR_DATA_INFO_TABLE_LOAD';
export const MONITOR_DATA_INFO_TABLE_SUCCESS = 'MONITOR_DATA_INFO_TABLE_SUCCESS';
export const MONITOR_DATA_INFO_TABLE_ERROR = 'MONITOR_DATA_INFO_TABLE_ERROR';

export const MONITOR_JIT_DATA_INFO_TABLE_LOAD = 'MONITOR_JIT_DATA_INFO_TABLE_LOAD';
export const MONITOR_JIT_DATA_INFO_TABLE_SUCCESS = 'MONITOR_JIT_DATA_INFO_TABLE_SUCCESS';
export const MONITOR_JIT_DATA_INFO_TABLE_ERROR = 'MONITOR_JIT_DATA_INFO_TABLE_ERROR';

export const MONITOR_TOTAL_ORDER_COUNT_LOAD = 'MONITOR_TOTAL_ORDER_COUNT_LOAD';
export const MONITOR_TOTAL_ORDER_COUNT_SUCCESS = 'MONITOR_TOTAL_ORDER_COUNT_SUCCESS';
export const MONITOR_TOTAL_ORDER_COUNT_ERROR = 'MONITOR_TOTAL_ORDER_COUNT_ERROR';
export const MONITOR_TOTAL_ORDER_COUNT_RESET = 'MONITOR_TOTAL_ORDER_COUNT_RESET';

export const MONITOR_TOTAL_ORDER_DATA_LOAD = 'MONITOR_TOTAL_ORDER_DATA_LOAD';
export const MONITOR_TOTAL_ORDER_DATA_SUCCESS = 'MONITOR_TOTAL_ORDER_DATA_SUCCESS';
export const MONITOR_TOTAL_ORDER_DATA_ERROR = 'MONITOR_TOTAL_ORDER_DATA_ERROR';
export const MONITOR_TOTAL_ORDER_DATA_RESET = 'MONITOR_TOTAL_ORDER_DATA_RESET';
