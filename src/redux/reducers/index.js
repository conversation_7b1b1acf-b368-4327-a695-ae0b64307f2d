import invoiceReferenceCreation from './invoiceReferenceCreation';
import purchaseOrder from './purchaseOrder';
import grn from './grn';
import modal from './modal';
import toast from './toast';
import invoiceDashboard from './invoiceDashboard';
import shortCuts from './shortCuts';
import invoiceView from './invoiceView';
import grnListing from './grnListing';
import grnDetails from './grnDetails';
import debitNote from './debitNote';
import vendorMaster from './vendorMaster';
import settings from './settings';
import pdfDownload from './pdfDownload';
import filters from './filters';
import login from './login';
import putaway from './putaway';
import constraint from './constraint';
import layout from './layout';
import facility from './facility';
import transferReceive from './bulkInventory/transferReceive';
import putAwayList from './putAwayList';
import putawayDetail from './putawayDetail';
import barcodeHistory from './barcodeHistory';
import manifest from './manifest';
import fitting from './fitting';
import MEIEdging from './MEIEdging';
import trayReassign from './trayReassign';
import supervisorPickingSkipped from './supervisorPickingSkipped';
import storePacking from './storePacking';
import inventory from './inventory';
import loader from './loader';
import monitor from './monitor';
import consolidation from './consolidation';
import bulkInventory from './bulkInventory/bulkInventory';
import localisation from './localisation';
import order from './order';
import packing from './packing';
import qc from './qc';
import printShipment from './printShipment';
import barcodeSeries from './barcodeSeries';
import orderDetails from './orderDetails';
import cycleCount from './cycleCount';
import pickingList from './pickingListReducer';
import register from './register';
import eInvoice from './e-invoice';
import refurb from './refurb';
import invoicePosting from './invoicePosting';
import createPutaway from './createPutaway';
import supervisorPickingAssignment from './supervisorPickingAssignment';
import d365 from './d365';
import asrsDiscrepancy from './asrsDiscrepancyReducer';
import userManagement from './userManagement';
import courierCutoff from './courierCutoff';
import adhocASRSPicking from './adhocASRSPicking';
import manifestShippingMethod from './manifestShippingMethod';
import shipmentBags from './shipmentBags';
import inwardQC from './inwardQC';
import priceControl from './priceControl';
import addInventory from './addInventory';
import storeConsolidation from './storeConsolidation';
import consul from './consul';
import startRetire from './startRetire';
import reactiveTransfer from './reactiveTransfer';
import jitOrderSearch from './jitOrderSearch';
import handEdging from './handEdging';
import meiBlocking from './meiBlocking';
import edgingQC from './edgingQC';
import distributorOrders from './distributorOrders';
import FinanceDOOrders from './finance';
import pickingScan from './pickingScan';
import doShipmentList from './doShipmentList';
import reportingTool from './reportingTool';
import customer from './customer';
import vendorShipment from './vendorShipment';
import stockTake from './stockTake';
import stockTakeBarcodeScan from './stockTakeBarcodeScan';
import bulkOrderPicking from './bulkOrderPicking';
import RTVlist from './RTVlist.slice';
import RTVbarcodeScan from './RTVbarcodeScan.slice';
import autoGrn from './autoGrn.slice';
import asrsManualSync from './asrsManualSync.slice';

const staticReducers = {
  settings,
  login,
  toast,
  loader,
  shortCuts,
  filters,
  barcodeHistory,
  localisation,
  consul
};

const appReducers = {
  invoiceReferenceCreation,
  purchaseOrder,
  grn,
  modal,
  invoiceDashboard,
  invoiceView,
  grnListing,
  grnDetails,
  debitNote,
  vendorMaster,
  pdfDownload,
  putaway,
  constraint,
  layout,
  facility,
  transferReceive,
  putAwayList,
  putawayDetail,
  manifest,
  fitting,
  MEIEdging,
  supervisorPickingSkipped,
  storePacking,
  inventory,
  monitor,
  trayReassign,
  consolidation,
  bulkInventory,
  order,
  packing,
  qc,
  printShipment,
  barcodeSeries,
  orderDetails,
  cycleCount,
  register,
  pickingList,
  eInvoice,
  refurb,
  invoicePosting,
  createPutaway,
  supervisorPickingAssignment,
  d365,
  asrsDiscrepancy,
  userManagement,
  adhocASRSPicking,
  inwardQC,
  manifestShippingMethod,
  shipmentBags,
  courierCutoff,
  addInventory,
  storeConsolidation,
  startRetire,
  priceControl,
  reactiveTransfer,
  jitOrderSearch,
  handEdging,
  meiBlocking,
  edgingQC,
  distributorOrders,
  FinanceDOOrders,
  pickingScan,
  doShipmentList,
  reportingTool,
  customer,
  vendorShipment,
  stockTake,
  stockTakeBarcodeScan,
  bulkOrderPicking,
  RTVlist,
  RTVbarcodeScan,
  autoGrn,
  asrsManualSync
};

export default { ...staticReducers, ...appReducers };
