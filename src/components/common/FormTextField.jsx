import React from 'react';
import LkInput from '../MaterialUi/LkInput';

const FormTextField = (props) => {
  const {name, Field, field, validate, disabled, className, classes = {}, inputProps,
    // eslint-disable-next-line no-unused-vars
    fieldType = 'text',
    ...rest} = props;
  return (

    <Field
      className="form-control"
      name={`${name}.${field}`}
      validate={validate}
    >
      {({ input, meta }) => (
        <LkInput
          className={classes[className]}
          // style={{ width: 80 }}
          {...input}
          InputProps={inputProps}
          disabled={disabled}
          error={meta.error && meta.touched}
          {...rest}
          // onBlur={e => handleChange(fields,index,e.target.value,field,taxes,form)}
          // value={row.quantity}
          variant="outlined"
        />
      )}
    </Field>
  );
};

export default FormTextField
