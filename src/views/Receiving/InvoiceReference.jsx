import React, { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import { makeStyles } from 'tss-react/mui';
import Autocomplete from '@mui/material/Autocomplete';

import LkCard from 'components/MaterialUi/LkCard';
import LkInput from 'components/MaterialUi/LkInput';
import { isGrnOpenLoad } from 'redux/actionCreators/grn';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  searchVendorList,
  resetInvoiceReferenceNonRepeatableData,
  getApprovedPoList,
  searchVendorInvoiceNumber,
  getItemsVendorInvoiceCreation,
  createVendorInvoice,
  getItemsVendorInvoiceCreationSuccess,
  getItemsVendorInvoiceCreationReset
} from 'redux/actionCreators/invoiceReferenceCreation';
import { invoiceViewFail, invoiceViewLoad } from 'redux/actionCreators/invoiceView';
import {
  formatDateDashedSeparated,
  debounce,
  parseQuery,
  convertDateFormat,
  getQueryParam,
  diffBetweenTwoDates
} from 'utils/helpers';
import Spinner from 'components/Spinner/Spinner';
import { toastOpen } from 'redux/actionCreators/toast';
import InvoiceCreateAdditionalFields from 
  'components/InvoiceCreateAdditionalFields/InvoiceCreateAdditionalFields';
import InvoiceSummary from './InvoiceSummary';
import getInvoiceEditModel from './converteInvoiceModel';

const useStyles = makeStyles()((theme) => ({
  root: {
    padding: theme.spacing(3),
    height: '100%',
    display: 'flex',
    justifyContent: 'center'
  },
  card: {
    height: 750,
    margin: 'auto'
  },
  cardComponent: {
    paddingTop: 40,
    borderRadius: 12,
    transition: '0.3s',
    boxShadow: '0 8px 16px rgba(0,0,0,0.05)'
  },
  header: {
    textAlign: 'center'
  },
  horizontalCenter: {
    display: 'flex',
    justifyContent: 'center',
    marginBottom: 22
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  content: {
    marginTop: 15
  },
  field: {
    width: 300
  },
  addOnField: {
    width: 200
  },
  button: {
    textAlign: 'center',
    marginTop: 30,
    marginBottom: 40
  },
  verticalMiddle: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  }
}));

let venodrNameParam;
let poNumParam;

const InvoiceReference = () => {
  const { classes, cx } = useStyles();
  const [page, setPage] = useState(0);
  const [vendorText, setVendorText] = useState(undefined);
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);
  const [po, setPo] = useState(undefined);
  const [invoiceText, setInvoiceText] = useState('');
  const [date, setDate] = useState(undefined);
  const [createClicked, setCreateClicked] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const { invoiceViewData, invoiceViewError } = useSelector((state) => state.invoiceView);
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const {
    vendorList,
    poList,
    poListFail,
    invoiceNumberList,
    vendorInvoiceCreationItems,
    vendorInvoiceCreationItemsLoading,
    vendorInvoiceCreationItemsFail
  } = useSelector((state) => state.invoiceReferenceCreation);

  const location = useLocation();
  const dispatch = useDispatch();

  const handleSearch = (e, name) => {
    if (e.length < 3) {
      return;
    }
    if (name === 'vendor') {
      dispatch(searchVendorList(e));
    } else if (name === 'invoiceNumber') {
      dispatch(searchVendorInvoiceNumber(e));
    }
  };

  const delayedQuery = useCallback(
    debounce((e, name) => e.length > 0 && handleSearch(e, name), 500),
    []
  );
  const [mode, setMode] = useState({ editMode: false, closeInvoice: false });
  const [additionalFields, setAdditionalFields] = useState({
    b2b_invoice_date: null,
    bill_of_entry_amount: null,
    bill_of_entry_date: null,
    bill_of_entry_number: null,
    send_to_party: null,
    handover_party: null
  });

  const handleCancel = () => {
    setPage(0);
    setCreateClicked(false);
    setDate(undefined);
    setPo(undefined);
    setInvoiceText(undefined);
    setVendorText(undefined);
  };

  useEffect(() => {
    const queryParam = parseQuery(location.search);
    if (queryParam.invoiceRefNo) {
      const { editMode: editModeParam, closeInvoice } = queryParam;
      setMode({ editMode: editModeParam, closeInvoice });
      setEditMode(true);
      const payloadObj = {
        items: 'true',
        invoice_ref_no: queryParam.invoiceRefNo
      };
      dispatch(invoiceViewLoad(payloadObj));
    }
  }, []);

  useEffect(() => {
    if (editMode) {
      setDate(convertDateFormat(invoiceViewData?.data?.invoice_date, 'shortDate'));
      setInvoiceText({
        vendor_invoice_number: invoiceViewData?.data?.vendor_invoice_number,
        'invoice-date': convertDateFormat(invoiceViewData?.data?.invoice_date, 'shortDate')
      });
      setPo(invoiceViewData?.data?.po_num);
      setVendorText({
        name: invoiceViewData?.data?.vendor_name,
        currency: invoiceViewData?.data?.currency
      });
    }
  }, [invoiceViewData]);
  useEffect(() => {
    if (editMode && vendorText && po && invoiceText && date && po !== '') {
      const invoiceRefNo = parseQuery(location.search)?.invoiceRefNo;
      const {
        b2b_invoice_date,
        bill_of_entry_amount,
        bill_of_entry_date,
        bill_of_entry_number,
        send_to_party,
        handover_party
      } = invoiceViewData?.data || {};
      setAdditionalFields({
        b2b_invoice_date,
        bill_of_entry_amount,
        bill_of_entry_date,
        bill_of_entry_number,
        send_to_party,
        handover_party
      });
      const data = getInvoiceEditModel(invoiceViewData?.data, invoiceRefNo);
      if (mode.editMode) {
        const obj = { invoice_id: invoiceRefNo, pids: Object.keys(data.po_items) };
        dispatch(isGrnOpenLoad(obj));
      }
      dispatch(getItemsVendorInvoiceCreationSuccess(data));
      setPage(1);
    }
  }, [vendorText, po, invoiceText, date]);

  useEffect(() => {
    if (invoiceViewError) {
      setEditMode(false);
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: INVOICE.CANNOT_OPEN_EDIT_MODE,
          subHeading:
            invoiceViewError?.meta?.displayMessage || INVOICE.CANNOT_OPEN_EDIT_MODE_NETWORK_ERROR,
          severity: 'error'
        })
      );
      dispatch(invoiceViewFail({ response: { data: undefined } }));
    }
  }, [invoiceViewError]);

  useEffect(() => {
    if (vendorInvoiceCreationItemsFail && editMode) {
      setEditMode(false);
      handleCancel();
    }
  }, [vendorInvoiceCreationItemsFail]);

  useEffect(() => {
    if (vendorInvoiceCreationItemsFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: vendorInvoiceCreationItemsFail,
          severity: 'error'
        })
      );
      dispatch(getItemsVendorInvoiceCreationReset());
    }
  }, [vendorInvoiceCreationItemsFail]);
  useEffect(() => {
    if (createClicked && !vendorInvoiceCreationItemsLoading && vendorInvoiceCreationItems) {
      setPage(1);
    }
  }, [createClicked, vendorInvoiceCreationItemsLoading, vendorInvoiceCreationItems]);

  useEffect(() => {
    venodrNameParam = getQueryParam(location.search.replace('?', ''), 'vendorName');
    poNumParam = getQueryParam(location.search.replace('?', ''), 'poNum');
    if (venodrNameParam) {
      delayedQuery(venodrNameParam, 'vendor');
      setPo(poNumParam);
    }
  }, []);

  useEffect(() => {
    if (poListFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: poListFail,
          severity: 'error'
        })
      );
    }
  }, [poListFail]);

  const removeParams = (param) => {
    venodrNameParam = undefined;
    poNumParam = undefined;
    setPo(undefined);
    setVendorText(undefined);
    if (param) {
      dispatch(getApprovedPoList(undefined));
    }
  };

  useEffect(
    () => () => {
      removeParams();
      setInvoiceText(undefined);
      setDate(undefined);
      dispatch(getItemsVendorInvoiceCreationReset());
    },
    [dispatch]
  );

  useEffect(() => {
    if (vendorList && vendorList.length > 0 && venodrNameParam) {
      const { name, currency } = vendorList[0];
      setVendorText({ name, currency });
    }
  }, [vendorList]);

  const handleSubmitClicked = () => {
    dispatch(resetInvoiceReferenceNonRepeatableData());
    setCreateClicked(true);
    const { bill_of_entry_amount } = additionalFields;
    const q =
      !bill_of_entry_amount ||
      (bill_of_entry_amount && !Number.isNaN(parseFloat(bill_of_entry_amount)));

    if (vendorText && po && invoiceText && date && po !== '' && q) {
      const obj = {
        po_num: po,
        vendor_inv_no: invoiceText.vendor_invoice_number.trim(),
        ...additionalFields
      };
      dispatch(getItemsVendorInvoiceCreation(obj));
    }
  };

  const onChangeInvoiceDate = ({ target: { value } }) => {
    if (value) {
      const test = diffBetweenTwoDates(value);
      if (test >= 0) {
        setDate(value);
      }
    }
  };

  const vendor =
    vendorText && vendorText.length > 0
      ? [{ name: vendorText, code: vendorText }, ...vendorList]
      : vendorList;
  const invoiceNumbers =
    invoiceText && invoiceText.length > 0
      ? [{ vendor_invoice_number: invoiceText, 'invoice-date': undefined }, ...invoiceNumberList]
      : invoiceNumberList;

  if (page === 0) {
    if (editMode) {
      return <Spinner className="display-grid-center pd-15" />;
    }
    return (
      <div className={classes.root}>
        <div className={classes.card}>
          <LkCard width={showAdditionalFields ? 750 : 550}>
            <Typography className={classes.header} variant="h2">
              {INVOICE.CREATE_INVOICE}
            </Typography>
            <CardContent className={classes.content}>
              <Grid>
                {!venodrNameParam ? (
                  <div className={classes.horizontalCenter}>
                    <Autocomplete
                      autoComplete
                      size="small"
                      autoHighlight
                      autoSelect
                      className={classes.field}
                      filterOptions={(options) => options}
                      // error={createClicked && !vendorText ? 'Field is Required' : undefined}
                      getOptionLabel={(option) => option.name}
                      includeInputInList
                      onChange={(event, newValue) => {
                        setCreateClicked(false);
                        setPo(undefined);
                        setVendorText(newValue);
                        dispatch(getApprovedPoList(newValue?.code));
                      }}
                      onInputChange={(event, newInputValue) => {
                        delayedQuery(newInputValue, 'vendor');
                      }}
                      options={vendor}
                      renderInput={(params) => (
                        <TextField
                          label={INVOICE.SELECT_VENDOR}
                          {...params}
                          variant="outlined"
                          error={createClicked && !vendorText ? 'Field is Required' : undefined}
                        />
                      )}
                    />{' '}
                  </div>
                ) : (
                  <div className="wd-100" style={{ marginBottom: '20px', marginLeft: '121px' }}>
                    <span className="fw-bold">{INVOICE.VENDOR_NAME}: </span> {venodrNameParam}
                  </div>
                )}

                {!poNumParam ? (
                  <div className={classes.horizontalCenter}>
                    <Autocomplete
                      autoComplete
                      size="small"
                      autoSelect
                      className={classes.field}
                      disabled={poList && poList.length < 1}
                      onChange={(e, newValue) => setPo(newValue)}
                      getOptionLabel={(option) => option}
                      includeInputInList
                      options={poList}
                      renderInput={(params) => (
                        <TextField
                          label={INVOICE.SELECT_PO}
                          {...params}
                          variant="outlined"
                          error={createClicked && !po ? 'Field is Required' : undefined}
                        />
                      )}
                    />
                  </div>
                ) : (
                  <div className="wd-100" style={{ marginBottom: '20px', marginLeft: '121px' }}>
                    <span className="fw-bold">{INVOICE.PO_NUM}: </span> {poNumParam}{' '}
                    <span
                      onClick={() => removeParams(true)}
                      className="fs12 cursor-pointer mr-l25 text-right text-turquioise_surf"
                    >
                      Remove
                    </span>
                  </div>
                )}

                <div className={classes.horizontalCenter}>
                  <Autocomplete
                    autoComplete
                    size="small"
                    autoHighlight
                    autoSelect
                    className={classes.field}
                    getOptionLabel={(option) => option.vendor_invoice_number}
                    // error={createClicked && !invoiceText ? 'Field is Required' : undefined}
                    includeInputInList
                    onChange={(event, newValue) => {
                      setInvoiceText(newValue);
                      setDate(newValue?.invoice_date);
                    }}
                    onInputChange={(event, newInputValue) => {
                      setInvoiceText(newInputValue);
                      delayedQuery(newInputValue, 'invoiceNumber');
                    }}
                    isOptionEqualToValue={(option, value) =>
                      option.vendor_invoice_number === value.vendor_invoice_number
                    }
                    options={invoiceNumbers}
                    renderInput={(params) => (
                      <TextField
                        label={INVOICE.ENTER_INVOICE_NUMBER}
                        {...params}
                        variant="outlined"
                        error={createClicked && !invoiceText ? 'Field is Required' : undefined}
                      />
                    )}
                  />
                </div>
                <div className={classes.horizontalCenter}>
                  <LkInput
                    InputLabelProps={{
                      shrink: true
                    }}
                    inputProps={{
                      max: formatDateDashedSeparated(Date.now())
                    }}
                    className={classes.field}
                    error={createClicked && !date ? 'Field is Required' : undefined}
                    id="date"
                    label={INVOICE.INVOICE_DATE}
                    onChange={onChangeInvoiceDate}
                    type="date"
                    value={date || ''}
                    variant="outlined"
                  />
                </div>
                {!showAdditionalFields ? (
                  <div
                    onClick={() => {
                      setShowAdditionalFields(true);
                    }}
                    className="text-turquioise_surf text-center fs14 cursor-pointer"
                  >
                    {INVOICE.SHOW_ADDITIONAL_FIELDS}
                  </div>
                ) : (
                  <InvoiceCreateAdditionalFields
                    classes={classes}
                    additionalFields={additionalFields}
                    setAdditionalFields={setAdditionalFields}
                    createClicked={createClicked}
                  />
                )}
                <div className={cx(classes.horizontalCenter, classes.button)}>
                  {vendorInvoiceCreationItemsLoading ? (
                    <Button
                      style={{ borderRadius: '8px', width: '114px' }}
                      color="primary"
                      disabled={vendorInvoiceCreationItemsLoading}
                      variant="contained"
                    >
                      <Spinner />
                    </Button>
                  ) : (
                    <Button
                      style={{ borderRadius: '8px' }}
                      color="primary"
                      onClick={() => handleSubmitClicked()}
                      variant="contained"
                    >
                      {INVOICE.CREATE}
                    </Button>
                  )}
                </div>
              </Grid>
            </CardContent>
          </LkCard>
        </div>
      </div>
    );
  }
  if (page === 1) {
    return (
      <InvoiceSummary
        date={date}
        handleCancel={() => handleCancel()}
        invoiceText={
          invoiceText.vendor_invoice_number ? invoiceText.vendor_invoice_number : invoiceText
        }
        po={po}
        invoiceData={invoiceViewData}
        vendorText={vendorText}
        onFormSubmit={(obj) => createVendorInvoice(obj)}
        vendorInvoiceCreationItems={vendorInvoiceCreationItems}
        invoiceViewData={invoiceViewData}
        mode={mode}
        additionalFields={additionalFields}
      />
    );
  }
  return null;
};

export default InvoiceReference;
