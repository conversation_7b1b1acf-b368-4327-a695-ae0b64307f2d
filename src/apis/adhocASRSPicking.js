import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getAdhocASRSPickingListAPI = (payload) =>
  tokenInstance().post(config.adhocASRSPicking.adhocASRSPickingList, payload);

export const getAdhocPIDdetailsListAPI = (transferCode) =>
  tokenInstance().get(`${config.adhocASRSPicking.adhocPidDetailsList}/${transferCode}`);

export const getSkuCountListAPI = (pids) =>
  tokenInstance().post(config.adhocASRSPicking.skuCountList, pids);
