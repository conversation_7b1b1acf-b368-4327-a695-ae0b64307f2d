import React, { useCallback, memo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import { Box, Tab, Tabs } from '@mui/material';
import Assignment from '../Assignment/Assignment';
// import Picking from '../Picking/Picking';

const useStyles = makeStyles()(() => ({
  container: {
    position: 'relative'
  },
  customTabRoot: {
    color: '#00B9C6'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  },
  divider: {
    borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
  },
  filters: {
    position: 'absolute',
    top: 5,
    right: 0
  },
  field: {
    width: 150,
    background: '#fff',
    marginLeft: 16
  }
}));

const TabPanel = ({ children, value, index }) => value === index && children;

const AssignmentTabs = ({ CONTENT }) => {
  const { classes } = useStyles();
  const { innerTab } = useParams();
  const navigate = useNavigate();

  const handleChange = useCallback((_event, newValue) => {
    navigate(`/supervisor/picking/assignment/${newValue}`);
  }, []);

  return (
    <Box className={classes.container}>
      <Box className={classes.divider}>
        <Tabs
          value={innerTab}
          onChange={handleChange}
          aria-label="Picker Tabs"
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          <Tab value="pickerAllocation" label={CONTENT.PICKER_ALLOCATION} />
          <Tab value="pickingConfig" label={CONTENT.PICKING_CONFIG} disabled />
        </Tabs>
      </Box>
      <TabPanel value={innerTab} index="pickerAllocation">
        <Assignment CONTENT={CONTENT} />
      </TabPanel>
      {/* TODO: remove the comment after picking config is done */}
      {/* <TabPanel value={innerTab} index="pickingConfig">
        <Picking />
      </TabPanel> */}
    </Box>
  );
};

export default memo(AssignmentTabs);
