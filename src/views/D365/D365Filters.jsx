import React, { useMemo } from 'react';
import Box from '@mui/material/Box';
// eslint-disable-next-line max-len
import DateRangeAccordion from 'views/Monitor/MonitorBody/MonitorBodyFiltersAccordion/DateRangeAccordion';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import { useDispatch, useSelector } from 'react-redux';
import { monitorFiltersUpdate } from 'redux/actionCreators/monitor';
import { LOCALISATION } from 'redux/reducers/localisation';

const D365Filters = () => {
  const dispatch = useDispatch();
  const {
    data: {
      DATE_RANGE: { from, to }
    }
  } = useSelector((state) => state.monitor.filters.selected);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.D365);
  const deleteData = (name) => {
    dispatch(
      monitorFiltersUpdate({
        filter: name,
        updatedFilters: { from: '', to: '' }
      })
    );
  };

  const DATE_RANGE_OPTIONS = [
    { key: CONTENT.ALL_PERIOD, value: 'ALL PERIOD' },
    { key: CONTENT.TODAY, value: 'TODAY' },
    { key: CONTENT.LAST_THREE_DAYS, value: 'LAST 3 DAYS' },
    { key: CONTENT.LAST_FIVE_DAYS, value: 'LAST 5 DAYS' },
    { key: CONTENT.LAST_TEN_DAYS, value: 'LAST 10 DAYS' },
    { key: CONTENT.LAST_FIFTEEN_DAYS, value: 'LAST 15 DAYS' },
    { key: CONTENT.LAST_NINTY_DAYS, value: 'LAST 90 DAYS' },
    { key: CONTENT.CUSTOM_RANGE, value: 'Custom Range' }
  ];

  const finalSelectedFilters = useMemo(
    () => [
      {
        key: 'DATE_RANGE',
        keyValue: 'Date Range',
        type: 'DATE_RANGE',
        values: {
          from,
          to
        }
      }
    ],
    [from]
  );
  return (
    <Box display="flex" justifyContent="space-between" className="bg-white flex1" p={2}>
      <Box>
        {from ? <FilterChipsV2 filters={finalSelectedFilters} deleteData={deleteData} /> : null}
      </Box>
      <Box>
        <DateRangeAccordion dateOptions={DATE_RANGE_OPTIONS} />
      </Box>
    </Box>
  );
};

export default D365Filters;
