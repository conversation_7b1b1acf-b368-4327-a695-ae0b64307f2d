// Invoice Reference

export const SEARCH_VENDOR_LIST = 'SEARCH_VENDOR_LIST';
export const SEARCH_VENDOR_LIST_SUCCESS = 'SEARCH_VENDOR_LIST_SUCCESS';
export const SEARCH_VENDOR_LIST_FAIL = 'SEARCH_VENDOR_LIST_FAIL';
export const SEARCH_VENDOR_LIST_RESET = 'SEARCH_VENDOR_LIST_RESET';

export const GET_APPROVED_PO_LIST = 'GET_APPROVED_PO_LIST';
export const GET_APPROVED_PO_LIST_LOAD = 'GET_APPROVED_PO_LIST_LOAD';
export const GET_APPROVED_PO_LIST_SUCCESS = 'GET_APPROVED_PO_LIST_SUCCESS';
export const GET_APPROVED_PO_LIST_FAIL = 'GET_APPROVED_PO_LIST_FAIL';

export const SEARCH_VENDOR_INVOICE_NUMBER_LIST = 'SEARCH_VENDOR_INVOICE_NUMBER_LIST';
export const SEARCH_VENDOR_INVOICE_NUMBER_LIST_SUCCESS =
  'SEARCH_VENDOR_INVOICE_NUMBER_LIST_SUCCESS';
export const SEARCH_VENDOR_INVOICE_NUMBER_LIST_FAIL = 'SEARCH_VENDOR_INVOICE_NUMBER_LIST_FAIL';

// Invoice Summary

export const GET_ITEMS_VENDOR_INVOICE_CREATION = 'GET_ITEMS_VENDOR_INVOICE_CREATION';
export const GET_ITEMS_VENDOR_INVOICE_CREATION_LOAD = 'GET_ITEMS_VENDOR_INVOICE_CREATION_LOAD';
export const GET_ITEMS_VENDOR_INVOICE_CREATION_SUCCESS =
  'GET_ITEMS_VENDOR_INVOICE_CREATION_SUCCESS';
export const GET_ITEMS_VENDOR_INVOICE_CREATION_FAIL = 'GET_ITEMS_VENDOR_INVOICE_CREATION_FAIL';
export const GET_ITEMS_VENDOR_INVOICE_CREATION_RESET = 'GET_ITEMS_VENDOR_INVOICE_CREATION_RESET';

export const CREATE_VENDOR_INVOICE = 'CREATE_VENDOR_INVOICE';
export const CREATE_VENDOR_INVOICE_LOAD = 'CREATE_VENDOR_INVOICE_LOAD';
export const CREATE_VENDOR_INVOICE_SUCCESS = 'CREATE_VENDOR_INVOICE_SUCCESS';
export const CREATE_VENDOR_INVOICE_FAIL = 'CREATE_VENDOR_INVOICE_FAIL';

// Get Create Invoice Status

export const GET_VENDOR_INVOICE_CREATION_STATUS = 'GET_VENDOR_INVOICE_CREATION_STATUS';
export const GET_VENDOR_INVOICE_CREATION_STATUS_LOAD = 'GET_VENDOR_INVOICE_CREATION_STATUS_LOAD';
export const GET_VENDOR_INVOICE_CREATION_STATUS_FAIL = 'GET_VENDOR_INVOICE_CREATION_STATUS_FAIL';
export const GET_VENDOR_INVOICE_CREATION_STATUS_SUCCESS =
  'GET_VENDOR_INVOICE_CREATION_STATUS_SUCCESS';
export const GET_VENDOR_INVOICE_CREATION_STATUS_RESET = 'GET_VENDOR_INVOICE_CREATION_STATUS_RESET';

// CLear Data

export const RESET_INVOICE_REFERENCE_NON_REPEATABLE_DATA =
  'RESET_INVOICE_REFERENCE_NON_REPEATABLE_DATA';
export const RESET_VENDOR_INVOICE_CREATION_DATA = 'RESET_VENDOR_INVOICE_CREATION_DATA';

// close invoice

export const CLOSE_INVOICE_LOAD = 'CLOSE_INVOICE_LOAD';
export const CLOSE_INVOICE_SUCCESS = 'CLOSE_INVOICE_SUCCESS';
export const CLOSE_INVOICE_FAIL = 'CLOSE_INVOICE_FAIL';
export const CLOSE_INVOICE_RESET = 'CLOSE_INVOICE_RESET';

// import invoice
export const IMPORT_INVOICE_LOAD = 'IMPORT_INVOICE_LOAD';
export const IMPORT_INVOICE_SUCCESS = 'IMPORT_INVOICE_SUCCESS';
export const IMPORT_INVOICE_FAIL = 'IMPORT_INVOICE_FAIL';
export const IMPORT_INVOICE_RESET = 'IMPORT_INVOICE_RESET';

// export invoice
export const EXPORT_INVOICE_LOAD = 'EXPORT_INVOICE_LOAD';
export const EXPORT_INVOICE_SUCCESS = 'EXPORT_INVOICE_SUCCESS';
export const EXPORT_INVOICE_FAIL = 'EXPORT_INVOICE_FAIL';
export const EXPORT_INVOICE_RESET = 'EXPORT_INVOICE_RESET';

// download error report(import invoice)
export const DOWNLOAD_INVOICE_ERROR_REPORT_LOAD = 'DOWNLOAD_INVOICE_ERROR_REPORT_LOAD';
export const DOWNLOAD_INVOICE_ERROR_REPORT_SUCCESS = 'DOWNLOAD_INVOICE_ERROR_REPORT_SUCCESS';
export const DOWNLOAD_INVOICE_ERROR_REPORT_FAIL = 'DOWNLOAD_INVOICE_ERROR_REPORT_FAIL';
export const DOWNLOAD_INVOICE_ERROR_REPORT_RESET = 'DOWNLOAD_INVOICE_ERROR_REPORT_RESET';

// Reset invoiceReferenceCreation
export const RESET_INVOICE_REFERENCE_CREATION = 'RESET_INVOICE_REFERENCE_CREATION';

// get CL PID Info
export const GET_PID_INFO_LOAD = 'GET_PID_INFO_LOAD';
export const GET_PID_INFO_SUCCESS = 'GET_PID_INFO_SUCCESS';
export const GET_PID_INFO_FAIL = 'GET_PID_INFO_FAIL';
export const GET_PID_INFO_RESET = 'GET_PID_INFO_RESET';
