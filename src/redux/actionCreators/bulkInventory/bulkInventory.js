import * as action from '../../actionTypes/bulkInventory';

// Get Pid Details
export const fetchPidDataLoad = (data) => ({ type: action.FETCH_PID_DATA_LOAD, payload: data });
export const fetchPidDataSuccess = (data) => ({
  type: action.FETCH_PID_DATA_SUCCESS,
  payload: data
});
export const fetchPidDataFail = (data) => ({ type: action.FETCH_PID_DATA_FAIL, payload: data });

// Edit Transfer List
export const editCreateTransferLoad = (data) => ({
  type: action.EDIT_CREATE_TRANSFER_LOAD,
  payload: data
});
export const editCreateTransferSuccess = (payload) => ({
  type: action.EDIT_CREATE_TRANSFER_SUCCESS,
  payload
});
export const editCreateTransferFail = (data) => ({
  type: action.EDIT_CREATE_TRANSFER_FAIL,
  payload: data
});
export const resetInventoryData = (data) => ({ type: action.RESET_INVENTORY_DATA, payload: data });

// Download Csv File
export const downloadTransferCsvLoad = (data) => ({
  type: action.DOWNLOAD_TRANSFER_CSV_LOAD,
  payload: data
});
export const downloadTransferCsvSuccess = (data) => ({
  type: action.DOWNLOAD_TRANSFER_CSV_SUCCESS,
  payload: data
});
export const downloadTransferCsvFail = (data) => ({
  type: action.DOWNLOAD_TRANSFER_CSV_FAIL,
  payload: data
});

// Upload Csv File
export const uploadTransferCsvLoad = (data) => ({
  type: action.UPLOAD_CREATE_TRANSFER_LOAD,
  payload: data
});
export const uploadTransferCsvSuccess = (data) => ({
  type: action.UPLOAD_CREATE_TRANSFER_SUCCESS,
  payload: data
});
export const uploadTransferCsvFail = (data) => ({
  type: action.UPLOAD_CREATE_TRANSFER_FAIL,
  payload: data
});
export const uploadTransferCsvReset = () => ({
  type: action.UPLOAD_CREATE_TRANSFER_RESET
});

// Transfer Details
export const transferDetailsLoad = (data) => ({
  type: action.GET_TRANSFER_DETAILS_LOAD,
  payload: data
});

export function transferDetailsSuccess(data, checkBarcode) {
  return {
    type: action.GET_TRANSFER_DETAILS_SUCCESS,
    payload: { data, checkBarcode }
  };
}

export const transferDetailsFail = (data) => ({
  type: action.GET_TRANSFER_DETAILS_FAIL,
  payload: data
});

// Get Barcodes for transfer ID
export const getBarcodesForTidLoad = (data) => ({ type: action.GET_BARCODES_LOAD, payload: data });
export const getBarcodesForTidSuccess = (data) => ({
  type: action.GET_BARCODES_SUCCESS,
  payload: data
});
export const getBarcodesForTidFail = (data) => ({ type: action.GET_BARCODES_FAIL, payload: data });
export const resetBarcodesData = () => ({ type: action.RESET_BARCODES_DATA });

// CReate Transfer
export const createdTransferListLoad = (data) => ({
  type: action.GET_CREATED_TRANSFER_LOAD,
  payload: data
});
export const createdTransferListSuccess = (data) => ({
  type: action.GET_CREATED_TRANSFER_SUCCESS,
  payload: data
});
export const createdTransferListFail = (data) => ({
  type: action.GET_CREATED_TRANSFER_FAIL,
  payload: data
});

export const scanBarcodeLoad = (data) => ({ type: action.SCAN_BARCODE_LOAD, payload: data });
export const scanBarcodeSuccess = (data) => ({
  type: action.SCAN_BARCODE_LOAD_SUCCESS,
  payload: data
});
export const scanBarcodeFail = (data) => ({ type: action.SCAN_BARCODE_LOAD_FAIL, payload: data });

export const deleteAbarcodeLoad = (data) => ({ type: action.DELETE_A_BARCODE_LOAD, payload: data });
export const deleteAbarcodeSuccess = (data) => ({
  type: action.DELETE_A_BARCODE_SUCCESS,
  payload: data
});
export const deleteAbarcodeFail = (data) => ({ type: action.DELETE_A_BARCODE_FAIL, payload: data });

export const changeTransferStatusLoad = (data) => ({
  type: action.CHANGE_TRANSFER_STATUS_LOAD,
  payload: data
});
export const changeTransferStatusSuccess = (data) => ({
  type: action.CHANGE_TRANSFER_STATUS_SUCCESS,
  payload: data
});
export const changeTransferStatusFail = () => ({ type: action.CHANGE_TRANSFER_STATUS_FAIL });

// Download Transfer Summary
export const downloadTransferSummaryLoad = (data) => ({
  type: action.DOWNLOAD_TRANSFER_SUMMARY_LOAD,
  payload: data
});
export const downloadTransferSummarySuccess = (data) => ({
  type: action.DOWNLOAD_TRANSFER_SUMMARY_SUCCESS,
  payload: data
});
export const downloadTransferSummaryFail = (data) => ({
  type: action.DOWNLOAD_TRANSFER_SUMMARY_FAIL,
  payload: data
});

// Download Transfer Details
export const downloadTransferDetailsLoad = (data) => ({
  type: action.DOWNLOAD_TRANSFER_DETAILS_LOAD,
  payload: data
});
export const downloadTransferDetailsSuccess = (data) => ({
  type: action.DOWNLOAD_TRANSFER_DETAILS_SUCCESS,
  payload: data
});
export const downloadTransferDetailsFail = (data) => ({
  type: action.DOWNLOAD_TRANSFER_DETAILS_FAIL,
  payload: data
});
export const downloadTransferDetailsReset = () => ({
  type: action.DOWNLOAD_TRANSFER_DETAILS_RESET
});

export const toggleBoxWiseTransfer = () => ({ type: action.TOGGLE_BOX_WISE_TRANSFER });

export const deletBoxFromTransferRequest = (payload) => ({
  type: action.DELETE_BOX_TRANSFER_REQUEST,
  payload
});

export const deleteBoxFromTransferSuccess = (payload) => ({
  type: action.DELETE_BOX_TRANSFER_SUCCESS,
  payload
});

export const deleteBoxFromTransferFail = (payload) => ({
  type: action.DELETE_BOX_TRANSFER_FAIL,
  payload
});

export const validateTransferBoxRequest = (payload) => ({
  type: action.VALIDATE_BOX_TRANSFER_REQUEST,
  payload
});

export const validateTransferBoxSuccess = (payload) => ({
  type: action.VALIDATE_BOX_TRANSFER_SUCCESS,
  payload
});

// Print invoice actions
export const transferPrintInvoiceRequest = (payload) => ({
  type: action.TRANSFERS_PRINT_INVOICE_LOAD,
  payload
});

export const transferPrintInvoiceSuccess = (payload) => ({
  type: action.TRANSFERS_PRINT_INVOICE_SUCCESS,
  payload
});

export const transferPrintInvoiceFail = () => ({
  type: action.TRANSFERS_PRINT_INVOICE_FAIL
});

export const transferPrintInvoiceReset = () => ({
  type: action.TRANSFERS_PRINT_INVOICE_RESET
});
export const replaceFaultyBarcodeRequest = (payload) => ({
  type: action.REPLACE_FAULTY_BARCODE_REQUEST,
  payload
});

export const replaceFaultyBarcodeSuccess = () => ({
  type: action.REPLACE_FAULTY_BARCODE_SUCCESS
});

export const replaceFaultyBarcodeFail = () => ({
  type: action.REPLACE_FAULTY_BARCODE_FAIL
});

export const replaceFaultyBarcodeReset = () => ({ type: action.REPLACE_FAULTY_BARCODE_RESET });

// Generate AWB number

export const generateAWBnumberRequest = (payload) => ({
  type: action.GENERATE_AWB_NUMBER_REQUEST,
  payload
});

export const generateAWBnumberSuccess = (payload) => ({
  type: action.GENERATE_AWB_NUMBER_SUCCESS,
  payload
});

export const generateAWBnumberFail = () => ({
  type: action.GENERATE_AWB_NUMBER_FAIL
});

export const generateAWBnumberReset = () => ({
  type: action.GENERATE_AWB_NUMBER_RESET
});

// Print Shipping label

export const transferPrintShippingLabelRequest = (payload) => ({
  type: action.TRANSFER_PRINT_SHIPPING_LABEL_REQUEST,
  payload
});

export const transferPrintShippingLabelSuccess = (payload) => ({
  type: action.TRANSFER_PRINT_SHIPPING_LABEL_SUCCESS,
  payload
});

export const transferPrintShippingLabelFail = () => ({
  type: action.TRANSFER_PRINT_SHIPPING_LABEL_FAIL
});

export const transferPrintShippingLabelReset = () => ({
  type: action.TRANSFER_PRINT_SHIPPING_LABEL_RESET
});

// VALIDATE BOX BARCODE

export const validateBoxBarcodeRequest = (payload) => ({
  type: action.VALIDATE_BOX_BARCODE_REQUEST,
  payload
});

export const validateBoxBarcodeSuccess = (payload) => ({
  type: action.VALIDATE_BOX_BARCODE_SUCCESS,
  payload
});

export const validateBoxBarcodeFail = () => ({
  type: action.VALIDATE_BOX_BARCODE_FAIL
});

export const validateBoxBarcodeReset = () => ({
  type: action.VALIDATE_BOX_BARCODE_RESET
});
