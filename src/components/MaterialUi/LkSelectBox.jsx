import React from 'react';
import { Select, InputLabel } from '@mui/material';
import FormControl from '@mui/material/FormControl';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()(() => ({
  formControl: {
    display: 'block'
  }
}));

const LkSelect = (props) => {
  const {
    className,
    children,
    label,
    labelValue,
    onChange,
    variant = 'standard',
    margin = 'none',
    disabled = false,
    error,
    size,
    ...rest
  } = props;

  const { classes, cx } = useStyles();

  return (
    <FormControl
      variant={variant}
      size={size}
      className={classes.formControl}
      disabled={disabled}
      error={error}
    >
      {label && <InputLabel htmlFor={labelValue}>{labelValue}</InputLabel>}
      <Select
        variant={variant}
        {...rest}
        MenuProps={{
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'left'
          },
          transformOrigin: {
            vertical: 'top',
            horizontal: 'left'
          }
        }}
        className={cx(classes.root, className)}
        margin={margin}
        onChange={onChange}
        // input={<Input />}
        label={labelValue}
      >
        {children}
      </Select>
    </FormControl>
  );
};

export default LkSelect;
