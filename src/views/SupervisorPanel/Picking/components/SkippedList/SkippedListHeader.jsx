import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import SearchIcon from '@mui/icons-material/Search';
import { Button, Box, InputAdornment } from '@mui/material';

import { fileDownload } from 'utils/helpers';
import LkInput from 'components/MaterialUi/LkInput';
import FileUpload from 'components/FileUpload/FileUpload';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';

import { CONSUL } from 'redux/reducers/consul';
import {
  bulkUploadDiscardedShipmentLoad,
  bulkUploadDiscardedShipmentReset
} from 'redux/actionCreators/supervisorPickingSkipped';

import HeaderCTA from '../common/HeaderCTA';

const useStyles = makeStyles()((theme) => ({
  header: {
    padding: 16,
    background: '#ffffff',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  searchIcon: {
    fontSize: '1.1rem'
  },
  searchInput: {
    minWidth: '300px'
  },
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    width: '70%'
  },
  checkedContainer: {
    display: 'flex',
    alignContent: 'center'
  },
  resetBtn: {
    borderRadius: theme.spacing(1),
    marginLeft: theme.spacing(2),
    height: 38
  },
  notFoundBtn: {
    fontWeight: 500,
    fontSize: 15,
    lineHeight: '26px'
  },
  holdBtn: {
    width: 160,
    height: 36,
    fontSize: 15,
    borderRadius: '4px',
    marginLeft: 16
  },
  usersSelect: {
    width: 190,
    height: 40,
    marginLeft: 16
  },
  exportIcon: {
    width: 38,
    height: 38,
    color: 'rgba(60, 60, 60, 0.54)',
    marginLeft: 16
  },
  cancelBtn: {
    borderRadius: 8,
    width: '104px',
    height: '42px',
    marginRight: '20px'
  },
  confirmBtn: {
    borderRadius: 8,
    width: '239px',
    height: '42px'
  }
}));

const SkippedListHeader = ({
  onHold,
  selectedItems,
  setSelectedItems,
  onGlobalSearch,
  globalSearch,
  setGlobalSearch,
  enableReset,
  reset,
  appliedFilter,
  deleteFilter,
  CONTENT,
  getDataForExport,
  exportDataLoading,
  totalRecords,
  isDiscarded
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const bulkUploadMoveToManualFileRef = useRef(null);
  const bulkUploadDiscardFileRef = useRef(null);

  const [openBulkMoveToManualModal, setOpenBulkMoveToManualModal] = useState(false);
  const [openBulkDiscardModal, setOpenBulkDiscardModal] = useState(false);
  const [uploadFileErrorMsg, setuploadFileErrorMsg] = useState('');

  const { isLoading, isSuccess, isError, failedRecords } = useSelector(
    (state) => state.supervisorPickingSkipped.bulkUploadDiscardedShipment
  );
  const featureToggle = useSelector((state) => state[CONSUL].featureToggle.data);
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);

  const showMoveToManual =
    featureToggle?.superVisorPickingSkipped?.[selectedFacility]?.showMoveToManual;
  const showMoveToDiscard =
    featureToggle?.superVisorPickingSkipped?.[selectedFacility]?.showMoveToDiscard;

  useEffect(() => {
    if (isError && !isLoading) {
      setuploadFileErrorMsg('Operation completed with errors.');
    }
  }, [isError, isLoading]);

  const closeBulkMoveToManualModal = () => {
    setOpenBulkMoveToManualModal(false);
    bulkUploadMoveToManualFileRef.current = null;
    setuploadFileErrorMsg('');
    dispatch(bulkUploadDiscardedShipmentReset());
  };

  const closeBulkDiscardModal = () => {
    setOpenBulkDiscardModal(false);
    bulkUploadDiscardFileRef.current = null;
    setuploadFileErrorMsg('');
    dispatch(bulkUploadDiscardedShipmentReset());
  };

  const handleFileMoveToManualUploadSubmit = () => {
    const file = bulkUploadMoveToManualFileRef.current;
    if (file) {
      const formData = new FormData();
      formData.set('csvFile', file);
      formData.set('order_state', 'DISCARD_TO_MANUAL');
      dispatch(bulkUploadDiscardedShipmentLoad(formData));
    }
  };

  const handleFileDiscardUploadSubmit = () => {
    const file = bulkUploadDiscardFileRef.current;
    if (file) {
      const formData = new FormData();
      formData.set('csvFile', file);
      formData.set('order_state', 'DISCARD');
      dispatch(bulkUploadDiscardedShipmentLoad(formData));
    }
  };

  const downloadErrorReportFn = () => {
    if (failedRecords && Array.isArray(failedRecords) && failedRecords.length > 0) {
      const csvHeader = 'Failed Shipment';
      const csvRows = failedRecords.join('\n');
      const csvContent = `${csvHeader}\n${csvRows}`;

      fileDownload(csvContent, 'failed_shipments_report', 'csv');
    } else {
      const csvContent = 'Failed Shipment\nNo failed records found';
      fileDownload(csvContent, 'failed_shipments_report', 'csv');
    }
  };

  return (
    <Box className={classes.header}>
      <Box className={classes.searchContainer}>
        <LkInput
          variant="outlined"
          value={globalSearch}
          className={classes.searchInput}
          onChange={(e) => setGlobalSearch(e.target.value)}
          onKeyPress={(e) =>
            e.which === 13 &&
            onGlobalSearch([
              { value: e.target.value.trim(), key: 'increment_id', name: 'Order ID' }
            ])
          }
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="disabled" className={classes.searchIcon} />
              </InputAdornment>
            )
          }}
          placeholder={CONTENT.SEARCH}
        />
        {appliedFilter.length > 0 && (
          <FilterChipsV2 filters={appliedFilter} deleteData={deleteFilter} />
        )}
      </Box>
      <Box className={classes.checkedContainer}>
        <HeaderCTA
          CONTENT={CONTENT}
          classes={classes}
          selectedItems={selectedItems}
          isDiscarded={isDiscarded}
          setSelectedItems={setSelectedItems}
          onHold={onHold}
        />
        {isDiscarded && showMoveToDiscard && (
          <Button
            variant="contained"
            disabled={isLoading}
            color="primary"
            sx={{ width: 'max-content' }}
            size="medium"
            onClick={() => setOpenBulkDiscardModal(true)}
            className={classes.resetBtn}
          >
            {CONTENT.MOVE_TO_DISCARD_UPLOAD}
          </Button>
        )}

        {isDiscarded && showMoveToManual && (
          <Button
            variant="contained"
            disabled={isLoading}
            color="primary"
            sx={{ width: 'max-content' }}
            size="medium"
            onClick={() => setOpenBulkMoveToManualModal(true)}
            className={classes.resetBtn}
          >
            {CONTENT.MOVE_TO_MANUAL_UPLOAD}
          </Button>
        )}
        <Button
          variant="outlined"
          disabled={exportDataLoading || !totalRecords}
          onClick={getDataForExport}
          color="primary"
          size="medium"
          className={classes.resetBtn}
        >
          {CONTENT.EXPORT}
        </Button>
        <Button
          variant="outlined"
          disabled={!enableReset}
          onClick={enableReset ? reset : null}
          color="primary"
          size="medium"
          className={classes.resetBtn}
        >
          {CONTENT.RESET}
        </Button>
      </Box>
      {openBulkMoveToManualModal && (
        <FileUpload
          open={openBulkMoveToManualModal}
          close={closeBulkMoveToManualModal}
          subTitle="Please upload a list of shipment IDs to move to manual."
          selectFiles={(file) => {
            bulkUploadMoveToManualFileRef.current = file;
          }}
          sampleCSV={[['ShipmentId'], ['SNXS1260000003580998'], ['SNXS1260000003580565']]}
          checkedItemLabel=""
          sampleFileName="sample_discarded_shipment"
          showSampleArray={false}
          importLoading={isLoading}
          failDataMessage={uploadFileErrorMsg}
          downloadReport={downloadErrorReportFn}
          uploadCsvPass={isSuccess}
          handleSubmit={handleFileMoveToManualUploadSubmit}
        />
      )}
      {openBulkDiscardModal && (
        <FileUpload
          open={openBulkDiscardModal}
          close={closeBulkDiscardModal}
          subTitle="Please upload a list of shipment IDs to mark shipments as discarded."
          selectFiles={(file) => {
            bulkUploadDiscardFileRef.current = file;
          }}
          sampleCSV={[['ShipmentId'], ['SNXS1260000003580998'], ['SNXS1260000003580565']]}
          checkedItemLabel=""
          sampleFileName="sample_discarded_shipment"
          showSampleArray={false}
          importLoading={isLoading}
          failDataMessage={uploadFileErrorMsg}
          downloadReport={downloadErrorReportFn}
          uploadCsvPass={isSuccess}
          handleSubmit={handleFileDiscardUploadSubmit}
        />
      )}
    </Box>
  );
};

export default SkippedListHeader;
