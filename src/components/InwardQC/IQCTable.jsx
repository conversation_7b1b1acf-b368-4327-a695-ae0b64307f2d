import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkChip from 'components/MaterialUi/LkChip';

import useWindowResize from 'common/useWindowResize';

import { getIqcTableDataLoad, getIqcTableDataReset } from 'redux/reducers/inwardQC';
import { getUserPutawayLoad } from 'redux/actionCreators/putaway';
import LkToolTip from 'components/ToolTip/ToolTip';
import { LOGIN } from 'redux/reducers/login';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({
  mainContainer: {
    display: 'flex'
  }
}));

const PAGE_SIZE = 30;

const sortingData = {
  id: 'pid',
  order: 'DESC'
};

const defaultReqObj = {
  isReset: true,
  payload: {
    page_num: 0,
    page_size: PAGE_SIZE,
    sort_by: 'pid',
    sort_order: 'DESC'
  }
};

const statusNameMapping = {
  CREATED: 'created',
  IQC_IN_PROGRESS: 'IQC in progress',
  IQC_DONE: 'IQC Done',
  IQC_COMPLETE: 'IQC Complete'
};

const statusMapping = {
  CREATED: 'primary',
  IQC_IN_PROGRESS: 'warning',
  IQC_DONE: 'success',
  IQC_COMPLETE: 'brightYellow'
};

const IQCTable = () => {
  const { classes } = useStyles();
  const { grnCode } = useParams();
  const dispatch = useDispatch();

  const [tableHeight] = useWindowResize(window.innerHeight - 250);

  const [requestBody, setRequestBody] = useState({
    ...defaultReqObj,
    payload: { ...defaultReqObj.payload, grn_code: grnCode }
  });

  const isInitialRequest = useRef(true);

  const iqcTableData = useSelector((state) => state.inwardQC.iqcTableData);
  const  userDetail  = useSelector((state) => state[LOGIN].user.userDetail);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_DETAIL);

  useEffect(() => {
    dispatch(getIqcTableDataLoad(requestBody));
    isInitialRequest.current = false;
    return () => {
      dispatch(getIqcTableDataReset());
    };
  }, [requestBody]);

  useEffect(() => {
    if (userDetail) {
      dispatch(getUserPutawayLoad({ type: 'GRN', userid: userDetail.empCode }));
    }
  }, [dispatch, userDetail]);

  const headerConfig = [
    {
      key: 'pid',
      name: CONTENT.PID,
      supportSort: true,
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      key: 'boxCode',
      name: CONTENT.BOX_ID,
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      key: 'productDesc',
      name: CONTENT.PRODUCT_DESCRIPTION,
      formatBody: useCallback(
        ({ productDesc }) => (
          <Box>
            {productDesc.length > 80 ? (
              <LkToolTip
                placement="bottom-end"
                title={<Box p={1}>{productDesc} </Box>}
                className="dtoolTip"
              >
                <div className="ellipsis-vertical">{productDesc}</div>
              </LkToolTip>
            ) : (
              productDesc || '-'
            )}
          </Box>
        ),
        []
      ),
      style: { minWidth: 180, maxWidth: 180 }
    },
    {
      key: 'samplingQty',
      name: CONTENT.QUANTITY,
      align: 'right',
      style: { minWidth: 80, maxWidth: 80 }
    },
    {
      key: 'qcPassQty',
      name: CONTENT.QC_PASS,
      align: 'right',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      key: 'qcFailQty',
      name: CONTENT.QC_FAIL,
      align: 'right',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      key: 'pending',
      name: CONTENT.PENDING_QC,
      formatBody: useCallback(
        ({ samplingQty, qcPassQty, qcFailQty }) => <p>{samplingQty - (qcPassQty + qcFailQty)}</p>,
        []
      ),
      align: 'right',
      style: { minWidth: 100, maxWidth: 100, marginRight: '40px' }
    },
    {
      key: 'status',
      name: CONTENT.STATUS,
      formatBody: useCallback(
        ({ status }) => <LkChip label={statusNameMapping[status]} type={statusMapping[status]} />,
        []
      ),
      style: { minWidth: 140, maxWidth: 140 }
    }
  ];

  const updateRequestBody = useCallback((sortType, sortKey, page) => {
    if (!isInitialRequest.current) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        isReset: !page,
        payload: {
          ...prevReqBody.payload,
          page_num: page,
          sort_by: sortKey,
          sort_order: sortType,
          grn_code: grnCode
        }
      }));
    }
  }, []);

  return (
    <Box className={classes.mainContainer}>
      <Box flex={1}>
        <LkTable
          headerConfig={headerConfig}
          isDataFetching={iqcTableData.isLoading}
          tableHeight={tableHeight}
          tableData={iqcTableData.data}
          pageNumber={0}
          pageLimit={PAGE_SIZE}
          rowSize={100}
          initialSortBy={sortingData}
          dataRequestFunction={updateRequestBody}
          totalRowsCount={iqcTableData.totalRowCount}
        />
      </Box>
    </Box>
  );
};

export default IQCTable;
