import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const getBulkOrdersDetailsApi = ({ shipmentId }) =>
  tokenInstance().get(`${config.bulkOrderPicking.getScanDetails}/${shipmentId}`);

export const getPickingCompleteApi = (payload) =>
  tokenInstance().put(
    `${config.bulkOrderPicking.complete}/${payload.summaryId}/complete`,
    payload?.filteredPayload
  );

export const getBarcodeScanApi = (payload) =>
  tokenInstance().post(config.bulkOrderPicking.scan, null, {
    params: payload
  });
