import { createSlice } from '@reduxjs/toolkit';

export const ASRS_MANUAL_SYNC = 'asrsManualSync';

const initialState = {
  orderCount: {
    isLoading: false,
    data: []
  },
  createWave: {}
};

const asrsManualSyncSlice = createSlice({
  name: ASRS_MANUAL_SYNC,
  initialState,
  reducers: {
    getOrdersCountLoad: (state) => {
      state.orderCount.isLoading = true;
    },
    getOrdersCountSuccess: (state, { payload }) => {
      state.orderCount.isLoading = false;
      state.orderCount.data = payload;
    },
    getOrdersCountFail: (state) => {
      state.orderCount.isLoading = false;
    },
    createWaveLoad: (state, { payload }) => {
      state.createWave[payload.waveName] = true;
    },
    createWaveComplete: (state, { payload }) => {
      state.createWave[payload.waveName] = false;
    }
  }
});

export const {
  getOrdersCountLoad,
  getOrdersCountSuccess,
  getOrdersCountFail,
  createWaveLoad,
  createWaveComplete
} = asrsManualSyncSlice.actions;

export default asrsManualSyncSlice.reducer;
