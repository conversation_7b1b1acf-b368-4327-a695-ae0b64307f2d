# syntax = docker/dockerfile:experimental

# build
FROM node:22.14.0-alpine as builder
WORKDIR /app

ARG STATIC_SERVER
ARG TARGETPLATFORM
ARG APP_ENV
ARG CURRENT_DATE
ARG NEWRELIC_LICENSE_KEY

ENV PUBLIC_URL ${STATIC_SERVER}/ui-builds/${APP_ENV}/${CURRENT_DATE}
ENV VITE_APP_ENV ${APP_ENV}
ENV VITE_NEWRELIC_LICENSE_KEY ${NEWRELIC_LICENSE_KEY}

RUN apk update && apk --no-cache add python3 make g++ py3-pip aws-cli
RUN aws --version

COPY . .
# Install dependencies
RUN npm ci --ignore-scripts

# Build the application
RUN npm run build

RUN aws s3 cp /app/build/assets s3://nexs-prod-static/ui-builds/${APP_ENV}/${CURRENT_DATE}/assets --recursive
RUN aws s3 cp /app/build/manifest.json s3://nexs-prod-static/ui-builds/${APP_ENV}/${CURRENT_DATE}/

RUN rm -rf /app/build/assets
RUN rm /app/build/manifest.json

FROM nginx:1.27.5-alpine
ARG APP_ENV

ADD nginx_${APP_ENV}.conf /etc/nginx/nginx.conf

RUN adduser -u 1000 -G nginx -D appuser

## add permissions
RUN chown -R appuser:nginx /usr/share/nginx/html/ && \
  chmod -R 755 /usr/share/nginx/html/ && \
  chown -R appuser:nginx /var/cache/nginx && \
  chown -R appuser:nginx /var/log/nginx && \
  chown -R appuser:nginx /etc/nginx/conf.d && \
  touch /var/run/nginx.pid && \
  chown -R appuser:nginx /var/run/nginx.pid && \
  chown -R appuser:nginx /var

## switch to non-root user
USER appuser

COPY --from=builder /app/build/ /usr/share/nginx/html/

EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]