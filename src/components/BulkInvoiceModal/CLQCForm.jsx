import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import IconButton from '@mui/material/IconButton';
import AddCircle from '@mui/icons-material/AddCircle';
import DeleteOutlined from '@mui/icons-material/DeleteOutlineOutlined';

import { StyledTableCell, StyledTableRow } from 'theme/table.theme';
import LkInput from 'components/MaterialUi/LkInput';
import { getPidInfoLoad, getPidInfoReset } from 'redux/actionCreators/invoiceReferenceCreation';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { keys } from 'utils/lodash';

const initialObj = {
  product_id: '',
  product_desc: '',
  vendor_unit_cost_price: '',
  bulk_qc_failed_qty: '',
  reason: ''
};
const CLQCForm = ({ onSubmit, classes, vendorInvoiceData, confirm, clFailData }) => {
  const dispatch = useDispatch();
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const [pidData, setPidData] = useState([...clFailData]);
  const { vendor_invoice_number, po_num } = vendorInvoiceData;
  const { data } = useSelector((state) => state.invoiceReferenceCreation.pidInfo);

  const headerConfig = [
    {
      name: INVOICE.PID,
      headerStyle: { width: '135px' }
    },
    {
      name: INVOICE.ITEM_DESCRIPTION,
      headerStyle: { width: '200px' }
    },

    {
      name: INVOICE.UNIT_PRICE,
      headerStyle: { width: '105px' }
    },

    {
      name: INVOICE.QC_FAIL_BULK_QTY,
      headerStyle: { width: '135px' }
    },
    {
      name: INVOICE.QC_FAIL_REASON,
      headerStyle: { width: '300px' }
    },
    {
      name: ''
    }
  ];

  useEffect(() => {
    if (keys(data).length) {
      setPidData((prevValue) =>
        prevValue.map((val, index) => {
          if (index === prevValue.length - 1) {
            return {
              ...val,
              ...data,
              product_data_id: data.product_id,
              product_id: val.product_id,
              product_desc: data.product_desc,
              vendor_unit_cost_price: data.vendor_unit_cost_price
            };
          }
          return val;
        })
      );
    }
    return () => {
      dispatch(getPidInfoReset());
    };
  }, [data]);

  const handleRowDelete = (index) => {
    const tempPidData = [...pidData];
    tempPidData.splice(index, 1);
    setPidData([...tempPidData]);
  };

  const handleChange = (e, index, key) => {
    const newValue = e.target.value;
    const isPidExist = pidData.some((pid) => pid.product_data_id === e.target.value);
    if (!isPidExist || newValue !== '') {
      const tempPidData = [...pidData];
      tempPidData[index][key] = newValue;
      setPidData([...tempPidData]);
    }
  };

  const getPidInfoChange = (e, index) => {
    const newValue = e.target.value;
    if (e.keyCode === 13) {
      const isPidExist = pidData.some((pid, i) => i !== index && pid.product_id === newValue);
      if (isPidExist) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: INVOICE.DUPLICATE_PIDS,
            severity: 'error'
          })
        );
      } else if (newValue !== '') {
        dispatch(getPidInfoLoad({ vendor_invoice_number, po_num, product_id: newValue }));
      }
    }
  };

  const getPidInfo = (e, index) => {
    const newValue = e.currentTarget.value;
    const isPidExist = pidData.some((pid, i) => i !== index && pid.product_id === newValue);
    if (isPidExist) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: INVOICE.DUPLICATE_PIDS,
          severity: 'error'
        })
      );
    } else if (newValue !== '') {
      dispatch(getPidInfoLoad({ vendor_invoice_number, po_num, product_id: newValue }));
    }
  };

  const tableheight = useMemo(
    () => (!pidData?.length || pidData?.length === 1 ? 106 : pidData.length * 59 + 49),
    [pidData]
  );

  const isValid = useMemo(
    () =>
      pidData.every((item) =>
        Object.values(item).every((value) => value !== '' && value !== null && value !== undefined)
      ) && pidData.length !== 0,
    [pidData]
  );

  return (
    <form onSubmit={onSubmit}>
      <div className={classes.tableBulkqc}>
        <TableContainer sx={{ maxHeight: '180px' }} component={Paper}>
          <Table
            sx={{
              height: tableheight
            }}
            stickyHeader
            className={classes.table}
            aria-label="sticky table"
          >
            <TableHead>
              <TableRow>
                {headerConfig.map(({ name, headerStyle }) => (
                  <StyledTableCell key={name} style={headerStyle} align="left">
                    {name}
                  </StyledTableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {pidData.map((row, index) => (
                <StyledTableRow key={row.product_id}>
                  <StyledTableCell sx={{ width: '105px' }}>
                    <LkInput
                      autoFocus
                      variant="outlined"
                      value={pidData[index]?.product_id}
                      onKeyDown={(e) => getPidInfoChange(e, index)}
                      onBlur={(e) => getPidInfo(e, index)}
                      disabled={
                        pidData[index]?.product_id?.toString() ===
                          pidData[index]?.product_data_id?.toString() &&
                        pidData[index]?.product_id !== ''
                      }
                      onChange={(e) => handleChange(e, index, 'product_id')}
                    />
                  </StyledTableCell>
                  <StyledTableCell sx={{ width: '200px' }}>
                    <div className="ellipsis-vertical-one">{pidData[index]?.product_desc}</div>
                  </StyledTableCell>
                  <StyledTableCell sx={{ width: '125px' }}>
                    <LkInput
                      variant="outlined"
                      disabled
                      value={pidData[index].vendor_unit_cost_price}
                      onChange={(e) => handleChange(e, index, 'unitPrice')}
                    />
                  </StyledTableCell>
                  <StyledTableCell sx={{ width: '105px' }}>
                    <LkInput
                      variant="outlined"
                      value={pidData[index].bulk_qc_failed_qty}
                      onChange={(e) => handleChange(e, index, 'bulk_qc_failed_qty')}
                    />
                  </StyledTableCell>
                  <StyledTableCell style={{ width: '105px' }}>
                    <LkInput
                      fullWidth
                      variant="outlined"
                      value={pidData[index].reason}
                      onChange={(e) => handleChange(e, index, 'reason')}
                    />
                  </StyledTableCell>
                  <StyledTableCell sx={{ padding: '16px 10px', width: '20px' }}>
                    <IconButton onClick={() => handleRowDelete(index)}>
                      <DeleteOutlined />
                    </IconButton>
                  </StyledTableCell>
                </StyledTableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Button
          sx={{
            display: 'flex',
            marginInline: 'auto',
            marginTop: 2,
            borderRadius: 1,
            height: 36
          }}
          color="primary"
          disabled={!pidData[pidData.length - 1]?.product_data_id && pidData.length !== 0}
          onClick={() => {
            setPidData((prevValue) => [...prevValue, { ...initialObj }]);
          }}
          variant="outlined"
          startIcon={<AddCircle />}
        >
          {INVOICE.ADD_PID}
        </Button>
      </div>
      <div className="display-flex justify-content-center mr-b40">
        <div className="mr-l5">
          <Button
            onClick={() => confirm({ invoiceData: pidData })}
            style={{ borderRadius: 8 }}
            variant="contained"
            color="primary"
            disabled={!isValid}
          >
            {INVOICE.CONFIRM}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default CLQCForm;
