import React, { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import { makeStyles } from 'tss-react/mui';
import useOrderHook from 'common/useOrderHook';

import PackingSuccess from './PackingSuccess';
import PackingPanelBarcode from './PackingPanelBarcode';
import ShipmentHeader from '../QualityCheck/ShipmentHeader';

const styles = makeStyles()(() => ({
  header: {
    fontWeight: 700
  },
  seperator: {
    backgroundColor: '#fff',
    padding: '0 0 24px 0'
  },
  seperatorLine: {
    height: '1px',
    background: '#ddd',
    margin: '0 178px'
  },
  qcWrapper: {
    display: 'flex',
    justifyContent: 'center',
    background: '#fff',
    paddingBottom: '65px'
  },
  viaUnicomTitle: {
    padding: '140px 0 90px 0px',
    textAlign: 'center',
    backgroundColor: '#fff',
    fontWeight: '700',
    fontSize: '24px'
  }
}));

const PackingSummary = () => {
  useOrderHook();
  const { classes } = styles();

  const [shipmentHeaderHeight, setShipmentHeaderHeight] = useState(0);

  useEffect(() => {
    /** Used to determine height of shipmentHeader to give paddingTop to content */
    if (document.getElementById('shipment_header') && !shipmentHeaderHeight) {
      setShipmentHeaderHeight(document.getElementById('shipment_header').offsetHeight);
    }
    /** End */
  }, []);

  return (
    <div>
      <Box>
        <ShipmentHeader />
      </Box>
      <div style={{ paddingTop: shipmentHeaderHeight }}>
        <div className={classes.qcWrapper}>
          <Box width="auto" boxShadow="0 8px 16px rgba(0,0,0,0.05)" mt={2} borderRadius="12px">
            <Card
              sx={{
                pt: 6,
                pb: 6
              }}
              style={{ boxShadow: 'rgb(0 0 0 / 30%) 0px 8px 16px' }}
            >
              <PackingPanelBarcode />
            </Card>
          </Box>
        </div>
        <div className={classes.seperator}>
          <div className={classes.seperatorLine} />
        </div>
        <Box>
          <PackingSuccess />
        </Box>
      </div>
    </div>
  );
};

export default PackingSummary;
