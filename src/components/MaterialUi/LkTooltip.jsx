import React from 'react';
import { Tooltip } from '@mui/material';
import PropTypes from 'prop-types';
import { withStyles, makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()(() => ({
  root: {}
}));

const LightTooltip = withStyles(Tooltip, (theme) => ({
  tooltip: {
    backgroundColor: theme.palette.common.white,
    color: 'rgba(0, 0, 0, 0.87)',
    boxShadow: theme.shadows[1],
    fontSize: 11
  }
}));

const LkInput = (props) => {
  const { className, light, children, ...rest } = props;

  const { classes, cx } = useStyles();

  return light ? (
    <LightTooltip {...rest} className={cx(classes.root, className)}>
      {children}
    </LightTooltip>
  ) : (
    <Tooltip {...rest} className={cx(classes.root, className)}>
      {children}
    </Tooltip>
  );
};

LkInput.propTypes = {
  className: PropTypes.string,
  margin: PropTypes.string,
  onChange: PropTypes.func,
  style: PropTypes.object,
  variant: PropTypes.string
};

export default LkInput;
