import * as action from '../actionTypes/purchaseOrder';

// po list
export function purchaseOrderListLoad(data) {
    return { type: action.PURCHAE_ORDER_LIST_LOAD, payload: data };
}

export function purchaseOrderListSuccess(data) {
    return { type: action.PURCHAE_ORDER_LIST_SUCCESS, payload: data };
}

export function purchaseOrderListFail(data) {
    return { type: action.PURCHAE_ORDER_LIST_FAIL, payload: data };
}

export function purchaseOrderListReset(data) {
    return { type: action.PURCHAE_ORDER_LIST_RESET, payload: data };
}

// po detail
export function purchaseOrderDetailLoad(data) {
    return { type: action.PURCHASE_ORDER_DETAIL_LOAD, payload: data };
}

export function purchaseOrderDetailSuccess(data) {
    return { type: action.PURCHASE_ORDER_DETAIL_SUCCESS, payload: data };
}

export function purchaseOrderDetailFail(data) {
    return { type: action.PURCHASE_ORDER_DETAIL_FAIL, payload: data };
}
export function purchaseOrderDetailReset(data) {
    return { type: action.PURCHASE_ORDER_DETAIL_RESET, payload: data };
}

// upload item
export function uploadItemLoad(data) {
    return { type: action.UPLOAD_ITEM_LOAD, payload: data };
}

export function uploadItemSuccess(data) {
    return { type: action.UPLOAD_ITEM_SUCCESS, payload: data };
}

export function uploadItemFail(data) {
    return { type: action.UPLOAD_ITEM_FAIL, payload: data };
}

export function uploadItemPercent(data) {
    return { type: action.UPLOAD_ITEM_PERCENT, payload: data };
}


// get upload item error report
export function getUploadItemErrorReportLoad(filename) {
    return { type: action.GET_UPLOAD_ITEM_ERROR_LOAD, payload: filename };
}

export function getUploadItemErrorReportSuccess(data) {
    return { type: action.GET_UPLOAD_ITEM_ERROR_SUCCESS, payload: data };
}

export function getUploadItemErrorReportFail(data) {
    return { type: action.GET_UPLOAD_ITEM_ERROR_FAIL, payload: data };
}

// submit po
export function submitPurchaseOrderLoad(data) {
    return { type: action.SUBMIT_PURCHASE_ORDER_LOAD, payload: data };
}

export function submitPurchaseOrderSuccess(data) {
    return { type: action.SUBMIT_PURCHASE_ORDER_SUCCESS, payload: data };
}

export function submitPurchaseOrderFail(data) {
    return { type: action.SUBMIT_PURCHASE_ORDER_FAIL, payload: data };
}

export function submitPurchaseOrderReset(data) {
    return { type: action.SUBMIT_PURCHASE_ORDER_RESET, payload: data };
}

// generate Po num
export function generatePoNumLoad() {
    return { type: action.GENERATE_PURCHASE_ORDER_NUMBER_LOAD };
}

export function generatePoNumSuccess(data) {
    return { type: action.GENERATE_PURCHASE_ORDER_NUMBER_SUCCESS, payload: data };
}

export function generatePoNumFail(data) {
    return { type: action.GENERATE_PURCHASE_ORDER_NUMBER_FAIL, payload: data };
}

// submit approval
export function submitApprovalLoad(payload) {
    return { type: action.SUBMIT_APPROVAL_LOAD, payload };
}

export function submitApprovalSuccess(data) {
    return { type: action.SUBMIT_APPROVAL_SUCCESS, payload: data };
}

export function submitApprovalFail(data) {
    return { type: action.SUBMIT_APPROVAL_FAIL, payload: data };
}

export function submitApprovalReset(data) {
    return { type: action.SUBMIT_APPROVAL_RESET, payload: data };
}

// amend po
export function amendPoLoad(payload) {
    return { type: action.AMEND_PO_LOAD, payload };
}

export function amnedPoSuccess(data) {
    return { type: action.AMEND_PO_SUCCESS, payload: data };
}

export function amnedPoFail(data) {
    return { type: action.AMEND_PO_FAIL, payload: data };
}

export function amnedPoReset(data) {
    return { type: action.AMEND_PO_RESET, payload: data };
}

export function amendPoDraft(data) {
    return { type: action.AMEND_PO_DRAFT, payload: data };
}

export function amendDraftReset(data) {
    return { type: action.AMEND_DRAFT_RESET, payload: data };
}

// hold po
export function holdPoLoad(payload) {
    return { type: action.HOLD_PO_LOAD, payload };
}

export function holdPoSuccess(data) {
    return { type: action.HOLD_PO_SUCCESS, payload: data };
}

export function holdPoFail(data) {
    return { type: action.HOLD_PO_FAIL, payload: data };
}

export function holdPoReset(data) {
    return { type: action.HOLD_PO_RESET, payload: data };
}


// edit po
export function editPo(data) {
    return { type: action.EDIT_PO_SET, payload: data };
}
export function resetCreatePo() {
    return { type: action.RESET_CREATE_PO };
}

// unhold po
export function unHoldPoLoad(payload) {
    return { type: action.UNHOLD_PO_LOAD, payload };
}

export function unHoldSuccess(data) {
    return { type: action.UNHOLD_PO_SUCCESS, payload: data };
}

export function unHoldFail(data) {
    return { type: action.UNHOLD_PO_FAIL, payload: data };
}

export function unHoldReset(data) {
    return { type: action.UNHOLD_PO_RESET, payload: data };
}

// close po
export function closePoLoad(payload) {
    return { type: action.CLOSE_PO_LOAD, payload };
}

export function closePoSuccess(data) {
    return { type: action.CLOSE_PO_SUCCESS, payload: data };
}

export function closePoFail(data) {
    return { type: action.CLOSE_PO_FAIL, payload: data };
}

export function closePoReset(data) {
    return { type: action.CLOSE_PO_RESET, payload: data };
}

// get pid details
export function getPidDetailsLoad(data) {
    return { type: action.GET_PID_DETAILS_LOAD, payload: data };
}

export function getPidDetailsSuccess(data) {
    return { type: action.GET_PID_DETAILS_SUCCESS, payload: data };
}

export function getPidDetailsFail(data) {
    return { type: action.GET_PID_DETAILS_FAIL, payload: data };
}

// export po
export function exportPoLoad(data) {
    return { type: action.EXPORT_PO_LOAD, payload: data };
}

export function exportPoSuccess(data) {
    return { type: action.EXPORT_PO_SUCCESS, payload: data };
}

export function exportPoFail(data) {
    return { type: action.EXPORT_PO_FAIL, payload: data };
}

export function exportPoReset(data) {
    return { type: action.EXPORT_PO_RESET, payload: data };
}

// po processing status
export function processingStatusLoad(data) {
    return { type: action.PROCESSING_STATUS_LOAD, payload: data };
}

export function processingStatusSuccess(data) {
    return { type: action.PROCESSING_STATUS_SUCCESS, payload: data };
}

export function processingStatusFail(data) {
    return { type: action.PROCESSING_STATUS_FAIL, payload: data };
}

export function resetUploadCSV() {
  return { type: action.RESET_UPLOAD_CSV };
}

export function processingStatusReset(data) {
    return { type: action.PROCESSING_STATUS_RESET, payload: data };
}


// po APPROVAL
export function markPoApprovalLoad(data) {
    return { type: action.MARK_PO_APPROVE_LOAD, payload: data };
}

export function markPoApprovalSuccess(data) {
    return { type: action.MARK_PO_APPROVE_SUCCESS, payload: data };
}

export function markPoApprovalFail(data) {
    return { type: action.MARK_PO_APPROVE_FAIL, payload: data };
}


export function markPoApprovalReset(data) {
    return { type: action.MARK_PO_APPROVE_RESET, payload: data };
}
