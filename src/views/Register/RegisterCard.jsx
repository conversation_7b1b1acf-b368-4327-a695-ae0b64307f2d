import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Button, IconButton } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LkInput from 'components/MaterialUi/LkInput';
import { useDispatch } from 'react-redux';
import { setRegisterationLoad } from 'redux/reducers/register';

const useStyle = makeStyles()(() => ({
  container: {
    width: 450,
    borderRadius: 8,
    backgroundColor: '#ffffff',
    boxShadow: '4px 4px 15px rgba(0, 0, 0, 0.05)',
    textAlign: 'center',
    padding: '0 38px',
    position: 'relative'
  },
  backButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    cursor: 'pointer'
  },
  backIcon: {
    color: '#3C3C3C8A'
  },
  heading: {
    fontSize: 24,
    fontWeight: 500,
    marginTop: 32,
    marginBottom: 12
  },
  subHeading: {
    fontWeight: 400,
    fontSize: 14,
    width: 267,
    margin: 'auto',
    color: '#666',
    lineHeight: '120%'
  },
  input: {
    marginTop: 24,
    marginBottom: 16,
    display: 'flex',
    flexDirection: 'column'
  },
  inputFont: {
    fontSize: 16
  },
  inputError: {
    fontSize: 12,
    color: '#ef4e74',
    textAlign: 'left',
    marginTop: 6
  },
  submitBtn: {
    borderRadius: 4,
    marginBottom: 24,
    paddingBlock: 8
  }
}));

const RegisterCard = ({ isLoading }) => {
  const empCodeRef = useRef('');
  const formRef = useRef('');
  const [empCodeError, setEmpCodeError] = useState(false);
  const { classes } = useStyle();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleInput = (e) => {
    if (!e.target.value.trim()) {
      setEmpCodeError(true);
    } else {
      setEmpCodeError(false);
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!empCodeRef.current.value.trim()) {
      setEmpCodeError(true);
    }
    if (!isLoading && empCodeRef.current.value.trim()) {
      dispatch(setRegisterationLoad({ employeeCode: empCodeRef.current.value }));
      formRef.current.reset();
    }
  };
  return (
    <div className={classes.container}>
      <h3 className={classes.heading}>Create New Account</h3>
      <IconButton
        className={classes.backButton}
        onClick={() => navigate('/login')}
        size="large"
      >
        <ArrowBackIcon className={classes.backIcon} />
      </IconButton>
      <Box margin="0 auto">
        <form autoComplete="off" noValidate onSubmit={handleSubmit} ref={formRef}>
          <h6 className={classes.subHeading}>
            We will send a link on Email ID linked to your employee code
          </h6>
          <Box className={classes.input}>
            <LkInput
              label="Employee Code"
              name="empCode"
              fullWidth
              onChange={handleInput}
              inputRef={empCodeRef}
              InputProps={{
                classes: {
                  input: classes.inputFont
                }
              }}
            />
            <span className={classes.inputError}>
              {empCodeError && 'Employee code is required'}
            </span>
          </Box>
          <Button
            disabled={isLoading}
            variant="contained"
            fullWidth
            color="primary"
            type="submit"
            className={classes.submitBtn}
          >
            CONTNIUE
          </Button>
        </form>
      </Box>
    </div>
  );
};

export default RegisterCard;
