import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { convertToQueryString } from '../utils/helpers';

export const getManifestListApi = (payload) => {
  const f = convertToQueryString(payload.payload);
  return tokenInstance()
    .get(`${config.manifest.list}?${f}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getManifestRemoveApi = (payload) =>
  tokenInstance()
    .get(`${config.manifest.remove}/${payload}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const manifestFetchChannelApi = () =>
  tokenInstance()
    .get(`${config.manifest.fetch}/channel`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const getManifestDetailApi = ({ manifestId = null, bagId = null, bagSearch = false }) => {
  if (bagSearch) {
    return tokenInstance().get(config.manifest.fetchBag, { params: {
      bagId, manifestId
    } });
  }
  return tokenInstance().get(`${config.manifest.fetch}/${manifestId}`);
};

export const searchAWBApi = (data) => {
  const { id, value } = data;
  return tokenInstance()
    .post(`${config.manifest.add}/shipment/${id}/${value}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const closeManifestApi = (id) => tokenInstance().post(`${config.manifest.close}/${id}`);

export const getShippingProviderApi = (data) => {
  const { orderType, page } = data;
  return tokenInstance()
    .get(`${config.manifest.shippingProvider}/${orderType}?page=0&size=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const saveManifestApi = (payload) =>
  tokenInstance()
    .post(config.manifest.save, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const printManifestApi = (data) => {
  const { format, id } = data;
  let reqFormat = 'pdf';
  let finalPath = `${config.manifest.print}/${id}?format=${format.toUpperCase()}`;
  if (format === 'print') {
    finalPath = `${config.manifest.print}/summary/${id}`;
  }
  if (format === 'csv') {
    reqFormat = 'csv';
  }
  return tokenInstance()
    .get(finalPath, { responseType: 'arraybuffer' })
    .then((response) => {
      const blob = new Blob([response.data], { type: `application/${reqFormat}` });
      if (format === 'csv') {
        return blob;
      }
      const win = window.open('');
      const URL = window.URL || window.webkitURL;
      const dataUrl = URL.createObjectURL(blob);
      win.location.href = dataUrl;
      return null;
    })
    .catch((error) => {
      throw error;
    });
};

export const manifestShipmentDeleteApi = ({ manifestId, shippingId }) =>
  tokenInstance().put(`${config.manifest.deleteShipment}/${shippingId}/${manifestId}`);

export const retyManifestApi = (id) =>
  tokenInstance().post(`${config.manifest.retryManifest}/${id}`);


export const printSGInvoiceApi = ({ manifestId, type, shipments }) => {
  let URL = `${config.manifest[type]}`;
  if (type === 'sgTaxInvoice') {
    URL = `${config.manifest[type]}/${manifestId}`;
  }
  return tokenInstance().post(URL, shipments, { responseType: 'arraybuffer' });
};