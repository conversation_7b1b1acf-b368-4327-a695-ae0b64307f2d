import React from 'react';
import MenuItem from '@mui/material/MenuItem';
import { IconButton, Box, Fade } from '@mui/material';
import MoreVert from '@mui/icons-material/MoreVert';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import MenuList from '@mui/material/MenuList';
import FlexBox from '../core/FlexBox';

const Shortcuts = (props) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [open, setOpen] = React.useState(false);

  const handleClose = () => setOpen(false);

  const handleClick = (event) => {
    setOpen(true);
    setAnchorEl(event.currentTarget);
    // setAnchorEl(event.currentTarget);
  };
  const { data = [], right = 0 } = props;

  const handleListKeyDown = (event) => {
    if (event.key === 'Tab') {
      event.preventDefault();
      setOpen(false);
    }
  };

  return (
    <Box>
      <IconButton
        aria-controls="simple-menu"
        aria-haspopup="true"
        onClick={handleClick}
        size="large"
      >
        <MoreVert />
      </IconButton>
      <Popper
        open={open}
        anchorEl={anchorEl}
        role={undefined}
        transition
        disablePortal
        placement="bottom-end"
      >
        {({ TransitionProps }) => (
          <Fade
            {...TransitionProps}
            // style={{ transformOrigin: placement === 'bottom' ? 'center top' : 'center bottom' }}
          >
            <Paper style={{ right, position: 'relative' }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList autoFocusItem={open} id="menu-list-grow" onKeyDown={handleListKeyDown}>
                  {data.map((d) => {
                    const { disabled = false } = d;
                    return (
                      <MenuItem disabled={disabled} key={d.name} onClick={d.onClick}>
                        <FlexBox width={200} justifyContent="space-between">
                          <Box fontSize={14}>
                            {' '}
                            {d.src && <img className="mr-r8" src={d.src} alt="img" />}
                            {d.name}
                          </Box>
                          <div className="fs14 text-99">{d.key}</div>
                        </FlexBox>
                      </MenuItem>
                    );
                  })}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Fade>
        )}
      </Popper>
    </Box>
  );
};

export default Shortcuts;
