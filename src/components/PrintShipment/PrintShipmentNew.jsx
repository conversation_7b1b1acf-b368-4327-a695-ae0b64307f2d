import React, { useRef, useState, useEffect } from 'react';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { useReactToPrint } from 'react-to-print';
import { useDispatch, useSelector } from 'react-redux';
import {
  getPrintShipmentDetailLoad,
  getPrintShipmentDetailReset,
  isPrintShipmentDone,
  getPrintShipmentMPDTCLoad
} from 'redux/actionCreators/printShipment';
import { showLoader, hideLoader } from 'redux/actionCreators/loader';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import Spinner from 'components/Spinner/Spinner';
import PrintShipment from 'components/common/PrintShipment';
import { isEmpty } from 'validate.js';
import './printShipment.css';
import ShipmentLabelFadex from './ShipmentLabelFadex';
import ShipmentPrintTemplate from './ShipmentPrintTemplate';
import { AGENT, DONE } from '../../views/Packing/SupervisorPackingConstants';

const styles = makeStyles()(() => ({
  btn: {
    borderRadius: 8
  }
}));

const PrintShipmentNew = ({
  enableAutoPrint,
  showPrintButton,
  disableAutoPrint,
  shippingPackageId,
  navChannel,
  unicomOrderCode,
  role,
  packingDetailStatus
}) => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const manualRetryCount = useRef(0);
  const componentRef = useRef(null);
  const [showPrintTemplate, setShowPrintTemplate] = useState(false);
  const [showPrintFadexTemplate, setShowPrintFadexTemplate] = useState(false);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.ORDER_DETAILS);
  const { printShipmentStatus, printShipmentData, printShipmentLoading, printShipmentMPDummyData } =
    useSelector((state) => state.printShipment);
  const featureToggle = useSelector((state) => state.consul.featureToggle.data);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);

  const handlePrint = useReactToPrint({
    contentRef: componentRef,
    onAfterPrint: () => {
      dispatch(showLoader());
      // @TODO review and update
      setTimeout(() => {
        dispatch(hideLoader());
        dispatch(isPrintShipmentDone('done'));
        const barcodeEl = document.getElementById('barcodeId');
        if (barcodeEl) {
          barcodeEl.focus();
        }
      }, 1000);
    }
  });

  const handlePrints = (manualRetry = false) => {
    if (featureToggle.forcePrintEnabled?.includes(facilityCode) && manualRetry) {
      manualRetryCount.current += 1;
    }
    if (navChannel === 'MPDTC') {
      dispatch(
        getPrintShipmentMPDTCLoad({
          shippingPackageId,
          channel: navChannel
        })
      );
    } else {
      dispatch(
        getPrintShipmentDetailLoad({
          shippingPackageId,
          unicomOrderCode,
          manualRetry,
          forcePrint: manualRetryCount.current > 2
        })
      );
    }
  };

  const resetAllPrintShipment = () => {
    dispatch(getPrintShipmentDetailReset());
  };

  useEffect(
    () => () => {
      resetAllPrintShipment();
    },
    []
  );

  useEffect(
    () => () => {
      resetAllPrintShipment();
    },
    [printShipmentStatus]
  );

  useEffect(() => {
    if (printShipmentData) {
      if (printShipmentData.shippingProviderCode) {
        if (printShipmentData.shippingProviderCode.match(/FEDEX.*/)) {
          setShowPrintFadexTemplate(true);
          setTimeout(() => {
            handlePrint();
          }, 0);
          setTimeout(() => {
            setShowPrintFadexTemplate(false);
          }, 0);
        } else {
          setShowPrintTemplate(true);
          setTimeout(() => {
            handlePrint();
          }, 0);
          setTimeout(() => {
            setShowPrintTemplate(false);
          }, 0);
        }
      }
    }
  }, [printShipmentData]);

  useEffect(() => {
    if (!isEmpty(printShipmentMPDummyData)) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PRINTING_SHIPPING_LABEL,
          severity: 'success'
        })
      );
    }
  }, [printShipmentMPDummyData]);

  useEffect(() => {
    if (enableAutoPrint && showPrintButton && !printShipmentLoading) {
      handlePrints();
      disableAutoPrint(false);
    }
  }, [enableAutoPrint, shippingPackageId]);

  return (
    <>
      {showPrintTemplate && printShipmentData?.shippingProviderCode ? (
        <ShipmentPrintTemplate shipmentLabelData={printShipmentData} ref={componentRef} />
      ) : (
        ''
      )}

      {showPrintFadexTemplate && printShipmentData && (
        <ShipmentLabelFadex
          shipmentLabelData={printShipmentData}
          ref={componentRef}
          shipmentInvoiceData={printShipmentData.itemWiseData.shipmentInvoiceData}
          shipmentReturnData={printShipmentData.itemWiseData.shipmentReturnData}
          shipmentUnitPriceData={printShipmentData.itemWiseData.shipmentUnitPriceData}
        />
      )}
      {!isEmpty(printShipmentMPDummyData) && (
        <Box sx={{ display: 'none' }}>
          <PrintShipment printShipmentData={printShipmentMPDummyData} type="MPDTC" />
        </Box>
      )}
      {showPrintButton && !printShipmentLoading ? (
        <Button
          padding="8px 16px"
          color="primary"
          variant="outlined"
          className={classes.btn}
          onClick={() => handlePrints(true)}
          disabled={packingDetailStatus === DONE && role === AGENT}
        >
          Print Shipment
        </Button>
      ) : (
        ''
      )}
      {showPrintButton && printShipmentLoading ? (
        <Button
          padding="8px 16px"
          color="primary"
          variant="outlined"
          className={classes.btn}
          disabled
        >
          <Spinner />
        </Button>
      ) : (
        ''
      )}
    </>
  );
};

export default PrintShipmentNew;
