import React, { useCallback, useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Field, Form } from 'react-final-form';
import { FieldArray } from 'react-final-form-arrays';
import arrayMutators from 'final-form-arrays';
import { Box } from '@mui/material';

import Button from '@mui/material/Button';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import MenuItem from '@mui/material/MenuItem';
import Autocomplete from '@mui/material/Autocomplete';

import LkInput from 'components/MaterialUi/LkInput';
import LkSelect from 'components/MaterialUi/LkSelectBox';
import Spinner from 'components/Spinner/Spinner';
import Shortcuts from 'components/common/Shortcuts';
import { composeValidators, getQueryParam, debounce } from 'utils/helpers';
import { required } from 'utils/validation';

import {
  validateConsequenceLoad,
  validateConsequenceReset,
  validateConditionLoad,
  validateConditionReset,
  cmsMetaDataLoad,
  cmsMetaDataReset,
  saveConstraintLoad,
  saveConstraintReset,
  getConstraintListLoad,
  getConstraintListReset,
  facilityAutoCompleteLoad,
  facilityAutoCompleteReset
} from 'redux/actionCreators/constraint';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import ConstraintCreateHeader from './ConstraintCreateHeader';
import './constraintCreate.scss';

const init = {
  dropLocation: null,
  ruleId: null,
  priority: 0,
  ruleStatus: null,
  executionStrategy: 'SEQUENCE'
};

const mutatorObj = {
  ...arrayMutators,
  setDropLocation: (args, state, utils) => {
    utils.changeValue(state, 'dropLocation', () => args[0]);
  },
  setRuleType: (args, state, utils) => {
    utils.changeValue(state, 'ruleType', () => args[0]);
  },
  setRuleStatus: (args, state, utils) => {
    utils.changeValue(state, 'ruleStatus', () => args[0]);
  },
  setConsequenceList: (args, state, utils) => {
    utils.changeValue(state, 'consequenceRequestList', () => args[0]);
  },
  setRuleId: (args, state, utils) => {
    utils.changeValue(state, 'ruleId', () => args[0]);
  },
  setExecutionStrategy: (args, state, utils) => {
    utils.changeValue(state, 'executionStrategy', () => args[0]);
  }
};

const ConstraintCreate = () => {
  let mutators;
  let formValues;
  let formValid;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [destinationIndex, setDestinationIndex] = useState();
  const [facility, setFacility] = useState(null);
  const bodyRef = useRef();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CONSTRAINTS);

  const delayedQuery = useCallback(
    debounce((q) => q.length > 2 && dispatch(facilityAutoCompleteLoad({ value: q })), 500),
    []
  );

  const {
    validateConditionData,
    validateConditionProccessing,
    validateConsequenceData,
    validateConsequenceProccessing,
    cmsMetaData,
    constraintSaveSuccess,
    constraintSaveLoading,
    constraintSaveFail,
    constraintListData,
    constraintDraftLoading,
    cmsMetaProccessing,
    facilityData
  } = useSelector((state) => state.constraint);

  const submitReason = () => {
    // validateConsequenceLoad
  };

  const getFailcityMetadata = () => {
    const obj = {
      fetchDataTypeList: [
        'OPERATOR',
        'ATTRIBUTES',
        'SCAN_METHOD',
        'SCAN_TYPE',
        'ITEM_SCAN_TYPE',
        'APPROVAL_REQUIRED',
        'FACILITY',
        'RULE_TYPE',
        'LOCATION_SCAN_TYPE'
      ]
    };
    dispatch(cmsMetaDataLoad(obj));
  };

  useEffect(() => {
    // dispatch(facilityAutoCompleteLoad({ value: '' }));
    const ruleId = getQueryParam(window.location.search.replace('?', ''), 'ruleId');
    if (ruleId) {
      dispatch(getConstraintListLoad({ id: ruleId }));
    } else {
      getFailcityMetadata();
    }
    return () => {
      formValues = undefined;
      formValid = undefined;
      mutators = undefined;
      dispatch(saveConstraintReset());
      dispatch(validateConsequenceReset());
      dispatch(validateConditionReset());
      dispatch(cmsMetaDataReset());
      dispatch(facilityAutoCompleteReset());
      dispatch(getConstraintListReset());
    };
  }, []);

  useEffect(() => {
    if (validateConditionData) {
      const sucmsg = validateConditionData?.data?.data?.message;
      if (sucmsg) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: sucmsg,
            severity: 'success'
          })
        );
      }
    }
  }, [validateConditionData, dispatch]);

  useEffect(() => {
    if ((destinationIndex || destinationIndex === 0) && cmsMetaData && !cmsMetaProccessing) {
      let d;
      const p = cmsMetaData?.fetchPossibleValuesMap?.DESTINATION?.[0]?.forEach((element, index) => {
        const pl =
          cmsMetaData.fetchPossibleValuesMap.DESTINATION[0].length - 1 === index
            ? `${element}="entered_text"`
            : `${element}="entered_text" AND`;
        d = d ? `${d} ${pl}` : pl;
      });
      formValues.consequenceRequestList[destinationIndex].destination = d;
      mutators.setConsequenceList(formValues.consequenceRequestList);
      setDestinationIndex(null);
    }
  }, [destinationIndex, cmsMetaData, cmsMetaProccessing]);

  useEffect(() => {
    if (constraintSaveSuccess) {
      const msg = constraintSaveSuccess?.meta?.displayMessage;
      if (msg) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: msg,
            severity: 'success'
          })
        );
      }
      dispatch(saveConstraintReset());
    } else if (constraintSaveFail) {
      const msg = constraintSaveFail?.meta?.displayMessage;
      if (msg) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: msg,
            severity: 'error'
          })
        );
      }
      dispatch(saveConstraintReset());
    }
  }, [constraintSaveSuccess, constraintSaveFail]);

  useEffect(() => {
    if (constraintListData?.length > 0) {
      const { consequences, executionStrategy, facility, dropLocation } = constraintListData[0];
      const clone = getQueryParam(window.location.search.replace('?', ''), 'clone');
      consequences.forEach((element, index) => {
        consequences[index].approvalNeeded = element.approvalRequired;
      });
      constraintListData[0].consequenceRequestList = consequences;
      mutators.setConsequenceList(consequences);
      mutators.setDropLocation(dropLocation);
      setFacility(facility);
      getMetadata(facility);
      changeExecutionStrategy(executionStrategy);
      if (clone) {
        mutators.setRuleId(null);
      }
    }
  }, [constraintListData]);

  useEffect(() => {
    if (validateConsequenceData) {
      const errMsg = validateConsequenceData?.response?.data?.meta?.displayMessage;
      const sucmsg = validateConsequenceData?.data?.data?.valid;
      if (sucmsg) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Valid',
            severity: 'success'
          })
        );
      } else if (errMsg) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: errMsg,
            severity: 'success'
          })
        );
      }
    }
  }, [validateConsequenceData]);

  const validateCondition = (values) => {
    const { condition } = values;
    if (condition) {
      dispatch(validateConditionLoad(condition));
    }
  };

  const validateConsequence = (values, err) => {
    const { consequenceRequestList, ruleType, dropLocation } = values;
    if (!(err?.consequenceRequestList?.length > 0) && facility) {
      const obj = {};
      obj.consequenceRequestList = convertConseqenceData(consequenceRequestList, ruleType);
      obj.dropLocation = dropLocation;
      obj.facility = facility;
      obj.ruleType = ruleType;
      dispatch(validateConsequenceLoad(obj));
    }
  };

  const scrollToBottom = () => {
    bodyRef.current.scrollTop = bodyRef.current.scrollHeight - bodyRef.current.clientHeight;
  };

  const pushCons = (push, err) => {
    if (!(err?.consequenceRequestList?.length > 0)) {
      push('consequenceRequestList', {
        destination: null,
        destinationType: null,
        scanMethod: null,
        scanType: null
      });
      setTimeout(() => {
        scrollToBottom();
      }, 500);
    }
  };

  const getMetadata = (facility, destinationType, destinationIndex, values, dropLocation) => {
    const fetchDataTypeList = [
      'OPERATOR',
      'ATTRIBUTES',
      'SCAN_METHOD',
      'SCAN_TYPE',
      'ITEM_SCAN_TYPE',
      'APPROVAL_REQUIRED',
      'FACILITY',
      'DESTINATION_TYPE',
      'RULE_TYPE',
      'LOCATION_SCAN_TYPE',
      'DROP_LOCATION'
    ];
    if (facility) {
      const obj = {
        dontReset: true,
        facility,
        destinationType,
        dropLocation,
        fetchDataTypeList: destinationType
          ? [...fetchDataTypeList, 'DESTINATION']
          : fetchDataTypeList
      };
      dispatch(cmsMetaDataLoad(obj));
    }
    if (destinationType) {
      values.consequenceRequestList[destinationIndex].destinationType = destinationType;
      mutators.setConsequenceList(values.consequenceRequestList);
      setDestinationIndex(destinationIndex);
    }
  };

  const convertConseqenceData = (data, ruletype) => {
    data.forEach((value, index) => {
      data[index].type = 'PUTAWAY';
      data[index].sequence = index + 1;
    });
    return data;
  };

  const saveConstraint = useCallback(
    (status, formValues) => {
      if (status !== 'clone') {
        const { consequenceRequestList, ruleType } = formValues;
        const obj = formValues;
        obj.consequenceRequestList = convertConseqenceData(consequenceRequestList, ruleType);
        obj.ruleStatus = status;
        mutators.setRuleStatus(status);
        obj.facility = facility;
        dispatch(saveConstraintLoad(obj));
      } else {
        window.open(
          `${window.location.origin}/constraint/create/?ruleId=
          ${constraintListData[0].ruleId}&clone=true`
        );
      }
    },
    [dispatch, saveConstraintLoad, constraintListData, facility]
  );

  const goToConstraintList = useCallback(() => {
    navigate('/constraint/list');
  }, []);

  const changeExecutionStrategy = (value) => {
    mutators.setExecutionStrategy(value);
  };

  const checkConditionError = () => {
    const msg = validateConditionData?.response?.data?.meta?.displayMessage;
    return msg;
  };

  const dragConsequnce = (ev, index) => {
    ev.dataTransfer.setData('dragConsquence', index);
  };

  const allowDrop = (ev) => {
    ev.preventDefault();
  };

  const drop = (ev, index) => {
    ev.preventDefault();
    const data = ev.dataTransfer.getData('dragConsquence');
    const { consequenceRequestList } = formValues;
    const temp = consequenceRequestList[index];
    consequenceRequestList[index] = consequenceRequestList[data];
    consequenceRequestList[data] = temp;
    mutators.setConsequenceList(consequenceRequestList);
  };

  const changeRuleType = (data) => {
    if (data.target.value) {
      mutators.setConsequenceList([]);
      mutators.setDropLocation(null);
      mutators.setRuleType(data.target.value);
    }
  };

  return (
    <div className="constraint-create bg-white">
      <Form
        mutators={mutatorObj}
        initialValues={(constraintListData.length > 0 && constraintListData[0]) || init}
        onSubmit={submitReason}
        render={({
          handleSubmit,
          form: {
            mutators: { push }
          },
          values,
          errors,
          valid,
          form
        }) => {
          mutators = form.mutators;
          formValues = values;
          formValid = valid;
          return (
            <>
              <ConstraintCreateHeader
                cancel={() => goToConstraintList()}
                saveConstraint={(data) => saveConstraint(data, formValues)}
                formValues={formValues}
                formValid={formValid}
                constraintSaveLoading={constraintSaveLoading}
                constraintDraftLoading={constraintDraftLoading}
                facility={facility}
                CONTENT={CONTENT}
              />
              <div style={{ paddingTop: '100px' }} className="pd-20">
                <form
                  onSubmit={handleSubmit}
                  name="reason-po-form"
                  className="constrain-create-from"
                  noValidate
                >
                  <div className="display-flex justify-content-space-between">
                    <div className="flex1">
                      <div className="fw-bold fs14 mr-b15">{CONTENT.GENERAL_DETAILS}</div>
                      <div className="display-flex border-bf0">
                        <div className="flex1">
                          <Field name="title" validate={composeValidators(required)}>
                            {({ input, meta }) => (
                              <div className="material-group">
                                <LkInput {...input} fullWidth label={CONTENT.RULE_NAME} />
                                <span className="input-error">{meta.touched && meta.error}</span>
                              </div>
                            )}
                          </Field>
                        </div>
                        <div className="flex1 mr-l30">
                          <Field name="ruleType">
                            {({ input, meta }) => (
                              <LkSelect
                                className="wd-100"
                                size="small"
                                style={{ height: 40 }}
                                label
                                labelValue={CONTENT.SELECT_RULE_TYPE}
                                {...input}
                                fullWidth
                                onChange={(data) => changeRuleType(data)}
                              >
                                {cmsMetaData?.fetchPossibleValuesMap?.RULE_TYPE?.map((desc) => (
                                  <MenuItem key={desc} value={desc}>
                                    {desc}
                                  </MenuItem>
                                ))}
                              </LkSelect>
                            )}
                          </Field>
                        </div>
                      </div>
                      <div className="flex1 mr-t30">
                        <Field name="description" validate={composeValidators(required)}>
                          {({ input, meta }) => (
                            <div className="material-group">
                              <LkInput {...input} fullWidth label={CONTENT.DESCRIPTION} />
                              <span className="input-error">{meta.touched && meta.error}</span>
                            </div>
                          )}
                        </Field>
                      </div>
                    </div>
                    <div className="flex1 mr-l40">
                      <div className="fw-bold fs14 mr-b15">{CONTENT.FACILITIES_LINKED}</div>
                      <div className="display-flex">
                        <div className="flex1">
                          {/* <Field name="facility" validate={required}>
                                                    {({ input, meta, ...rest }) => ( */}
                          <div className="material-group">
                            <Autocomplete
                              value={facility}
                              //  {...rest}
                              options={facilityData?.data?.data || []}
                              onInputChange={(event, newInputValue) => {
                                console.log(newInputValue);
                              }}
                              onChange={(ev, newValue) => {
                                setFacility(newValue?.split('-')[0].trim());
                                mutators.setConsequenceList([]);
                                mutators.setDropLocation(null);
                                getMetadata(newValue?.split('-')[0].trim());
                              }}
                              onKeyUp={(e) => delayedQuery(e.target.value)}
                              getOptionLabel={(option) => option}
                              filterOptions={(options) => options}
                              renderInput={(params) => (
                                <LkInput
                                  {...params}
                                  className="input-box"
                                  fullWidth
                                  label={CONTENT.ENTER_FACILITY}
                                />
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="create-rule mr-t40">
                    <div className="fw-bold fs14 mr-b15">{CONTENT.CREATE_RULE}</div>
                    <div className="create-rule-container display-flex bg-fa pd-15 border-radius-8">
                      <div className="validate-query pd-r15">
                        <div className="validate-action mr-b15 display-flex">
                          {!validateConditionProccessing && (
                            <Button
                              disabled={!values?.condition}
                              onClick={() => validateCondition(values)}
                              className="btn-validate"
                            >
                              <img
                                className="mr-r10"
                                src={`${
                                  import.meta.env.VITE_STATIC_SERVER
                                }/images/ValidateIcon.svg`}
                                alt="validate"
                              />
                              {CONTENT.VALIDATE}
                            </Button>
                          )}
                          {validateConditionProccessing && (
                            <Button disabled className="btn-validate">
                              <Spinner />
                            </Button>
                          )}
                        </div>
                        <div>
                          <Field name="condition" validate={composeValidators(required)}>
                            {({ input, meta }) => (
                              <div className="material-group bg-white">
                                <LkInput
                                  onKeyPress={(e) => e.which === 13 && validateCondition(values)}
                                  {...input}
                                  fullWidth
                                  label={CONTENT.CONDITION}
                                  multiline
                                  rows={15}
                                />
                                {!checkConditionError() && (
                                  <span style={{ fontSize: '12px' }} className="input-error">
                                    {meta.touched && meta.error}
                                  </span>
                                )}
                                <span style={{ fontSize: '12px' }} className="fs12 input-error">
                                  {checkConditionError()}
                                </span>
                              </div>
                            )}
                          </Field>
                        </div>
                      </div>
                      <div className="mr-l15 flex3">
                        <div className="mr-b5">
                          {cmsMetaData?.fetchPossibleValuesMap && facility && values.ruleType && (
                            <>
                              <div className="validate-action display-flex">
                                {!validateConsequenceProccessing && (
                                  <Button
                                    onClick={() => validateConsequence(values, errors)}
                                    className="btn-validate"
                                  >
                                    <img
                                      className="mr-r10"
                                      src={`${
                                        import.meta.env.VITE_STATIC_SERVER
                                      }/images/ValidateIcon.svg`}
                                      alt="validate"
                                    />
                                    {CONTENT.VALIDATE}
                                  </Button>
                                )}
                                {validateConsequenceProccessing && (
                                  <Button disabled className="btn-validate">
                                    <Spinner />
                                  </Button>
                                )}
                              </div>
                              <div className="display-flex">
                                <div className="fs14 mr-r40 mr-b20 mr-t20">
                                  {CONTENT.EVALUATION_METHOD}
                                </div>
                                <RadioGroup
                                  row
                                  name="executionStrategy"
                                  value={values?.executionStrategy}
                                  onChange={(data) => changeExecutionStrategy(data?.target?.value)}
                                >
                                  <Field component={Radio} type="radio" name="executionStrategy">
                                    {({ input, meta, ...rest }) => (
                                      <FormControlLabel
                                        control={<Radio {...rest} color="primary" />}
                                        label={CONTENT.SEQUENTIAL}
                                        value="SEQUENCE"
                                      />
                                    )}
                                  </Field>
                                  <Field component={Radio} type="radio" name="executionStrategy">
                                    {({ input, meta, ...rest }) => (
                                      <FormControlLabel
                                        control={<Radio {...rest} color="primary" />}
                                        label={CONTENT.ANY}
                                        value="ANY"
                                      />
                                    )}
                                  </Field>
                                </RadioGroup>
                                {values.ruleType !== 'CYCLE_COUNT' && (
                                  <div style={{ marginLeft: '70px' }}>
                                    <Field
                                      name="dropLocation"
                                      validate={composeValidators(required)}
                                    >
                                      {({ input, meta }) => (
                                        <LkSelect
                                          className="wd-200"
                                          size="small"
                                          style={{ height: 40 }}
                                          label="Select Drop Location"
                                          labelValue={CONTENT.SELECT_DROPLOCATION}
                                          {...input}
                                          fullWidth
                                          onChange={(data) => {
                                            mutators.setConsequenceList([]);
                                            mutators.setDropLocation(data.target.value);
                                            getMetadata(
                                              facility,
                                              null,
                                              null,
                                              null,
                                              data.target.value
                                            );
                                          }}
                                        >
                                          {cmsMetaData.fetchPossibleValuesMap?.DROP_LOCATION?.map(
                                            (desc) => (
                                              <MenuItem key={desc} value={desc}>
                                                {desc}
                                              </MenuItem>
                                            )
                                          )}
                                        </LkSelect>
                                      )}
                                    </Field>
                                  </div>
                                )}
                              </div>
                              {(values?.dropLocation || values.ruleType === 'CYCLE_COUNT') && (
                                <div className="contraint-table border-ee">
                                  <div
                                    className="header display-flex justify-content-space-between
                                   pd-15 bg-white fw-bold"
                                  >
                                    {values.executionStrategy === 'SEQUENCE' && (
                                      <div style={{ width: '5%' }} />
                                    )}
                                    <div
                                      style={{
                                        width: values.ruleType !== 'CYCLE_COUNT' ? '10%' : '20%'
                                      }}
                                      className="pd-5"
                                    >
                                      {CONTENT.DESTINATION_TYPE}
                                    </div>
                                    <div
                                      style={{
                                        width: values.ruleType !== 'CYCLE_COUNT' ? '40%' : '70%'
                                      }}
                                      className="pd-5"
                                    >
                                      {CONTENT.DESTINATION}
                                    </div>
                                    {values.ruleType !== 'CYCLE_COUNT' && (
                                      <>
                                        <div style={{ width: '10%' }} className="pd-5">
                                          {CONTENT.SCAN_METHOD}
                                        </div>
                                        <div style={{ width: '10%' }} className="pd-5">
                                          {CONTENT.LOCATION_SCAN}
                                        </div>
                                        <div style={{ width: '10%' }} className="pd-5">
                                          {CONTENT.ITEM_SCAN_TYPE}
                                        </div>
                                        <div style={{ width: '10%' }} className="pd-5">
                                          {CONTENT.APPROVAL_NEEDED}?
                                        </div>
                                      </>
                                    )}
                                    <div style={{ width: '5%' }} className="pd-5" />
                                  </div>
                                  <div ref={bodyRef} className="body">
                                    <FieldArray name="consequenceRequestList">
                                      {({ fields }) =>
                                        fields.map((name, index) => (
                                          <div
                                            onDrop={(ev) => drop(ev, index)}
                                            onDragOver={(ev) => allowDrop(ev)}
                                            draggable={values.executionStrategy === 'SEQUENCE'}
                                            onDragStart={(ev) => dragConsequnce(ev, index)}
                                            key={name}
                                            className="row display-flex justify-content-space-between pd-15"
                                          >
                                            {values.executionStrategy === 'SEQUENCE' && (
                                              <div
                                                style={{ width: '5%' }}
                                                className="pd-5 align-self-center"
                                              >
                                                <img
                                                  src={`${
                                                    import.meta.env.VITE_STATIC_SERVER
                                                  }/images/DragIn.svg`}
                                                  alt="Drag"
                                                />
                                              </div>
                                            )}
                                            <div
                                              style={{
                                                width:
                                                  values.ruleType !== 'CYCLE_COUNT' ? '10%' : '20%'
                                              }}
                                              className="pd-5"
                                            >
                                              <Field
                                                name={`${name}.destinationType`}
                                                validate={composeValidators(required)}
                                              >
                                                {({ input, meta }) => (
                                                  <LkSelect
                                                    style={{ height: 40 }}
                                                    label={false}
                                                    {...input}
                                                    fullWidth
                                                    onChange={(data) => {
                                                      getMetadata(
                                                        facility,
                                                        data.target.value,
                                                        index,
                                                        values,
                                                        values?.dropLocation
                                                      );
                                                    }}
                                                  >
                                                    {cmsMetaData.fetchPossibleValuesMap?.
                                                      DESTINATION_TYPE?.[0]?.map(
                                                        (desc) => (
                                                          <MenuItem key={desc} value={desc}>
                                                            {desc}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                  </LkSelect>
                                                )}
                                              </Field>
                                            </div>
                                            <div
                                              style={{
                                                width:
                                                  values.ruleType !== 'CYCLE_COUNT' ? '40%' : '70%'
                                              }}
                                              className="pd-5"
                                            >
                                              <Field
                                                name={`${name}.destination`}
                                                validate={composeValidators(required)}
                                              >
                                                {({ input, meta }) => (
                                                  <div className="material-group bg-white">
                                                    <LkInput
                                                      {...input}
                                                      fullWidth
                                                      label={CONTENT.CONDITION}
                                                      multiline
                                                      rows={5}
                                                    />
                                                    {meta.error && meta.touched ? (
                                                      <span
                                                        style={{ fontSize: '12px' }}
                                                        className="input-error"
                                                      >
                                                        {meta.touched && meta.error}
                                                      </span>
                                                    ) : (
                                                      (
                                                        validateConsequenceData?.response?.data
                                                          ?.data?.consequenceValidationResponse
                                                          ?.validationResponseList ||
                                                        validateConsequenceData?.response?.data
                                                          ?.data?.validationResponseList
                                                      )?.map((err) => {
                                                        const { sequence, message, valid } = err;
                                                        if (
                                                          sequence ===
                                                            values.consequenceRequestList[index]
                                                              .sequence &&
                                                          !valid
                                                        ) {
                                                          return (
                                                            <span
                                                              key={message}
                                                              style={{ fontSize: '12px' }}
                                                              className="input-error"
                                                            >
                                                              {message}
                                                            </span>
                                                          );
                                                        }
                                                      })
                                                    )}
                                                  </div>
                                                )}
                                              </Field>
                                            </div>
                                            {values.ruleType !== 'CYCLE_COUNT' && (
                                              <div style={{ width: '10%' }} className="pd-5">
                                                <Field
                                                  name={`${name}.scanMethod`}
                                                  validate={
                                                    values.ruleType !== 'CYCLE_COUNT'
                                                      ? composeValidators(required)
                                                      : null
                                                  }
                                                >
                                                  {({ input, meta }) => (
                                                    <LkSelect
                                                      style={{ height: 40 }}
                                                      label={false}
                                                      {...input}
                                                      fullWidth
                                                    >
                                                      {cmsMetaData.fetchPossibleValuesMap?.SCAN_METHOD?.map(
                                                        (desc) => (
                                                          <MenuItem key={desc} value={desc}>
                                                            {desc}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                    </LkSelect>
                                                  )}
                                                </Field>
                                              </div>
                                            )}
                                            {values.ruleType !== 'CYCLE_COUNT' && (
                                              <div style={{ width: '10%' }} className="pd-5">
                                                <Field
                                                  name={`${name}.locationScanType`}
                                                  validate={
                                                    values.ruleType !== 'CYCLE_COUNT'
                                                      ? composeValidators(required)
                                                      : null
                                                  }
                                                >
                                                  {({ input, meta }) => (
                                                    <LkSelect
                                                      style={{ height: 40 }}
                                                      label={false}
                                                      {...input}
                                                      fullWidth
                                                    >
                                                      {cmsMetaData.fetchPossibleValuesMap?.LOCATION_SCAN_TYPE?.map(
                                                        (desc) => (
                                                          <MenuItem key={desc} value={desc}>
                                                            {desc}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                    </LkSelect>
                                                  )}
                                                </Field>
                                              </div>
                                            )}
                                            {values.ruleType !== 'CYCLE_COUNT' && (
                                              <div style={{ width: '10%' }} className="pd-5">
                                                <Field name={`${name}.scanType`}>
                                                  {({ input, meta }) => (
                                                    <LkSelect
                                                      disabled
                                                      style={{ height: 40 }}
                                                      label={false}
                                                      {...input}
                                                      fullWidth
                                                    >
                                                      {cmsMetaData.fetchPossibleValuesMap?.SCAN_TYPE?.map(
                                                        (desc) => (
                                                          <MenuItem key={desc} value={desc}>
                                                            {desc}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                    </LkSelect>
                                                  )}
                                                </Field>
                                              </div>
                                            )}
                                            {values.ruleType !== 'CYCLE_COUNT' && (
                                              <div style={{ width: '10%' }} className="pd-5">
                                                <Field name={`${name}.approvalNeeded`}>
                                                  {({ input, meta }) => (
                                                    <LkSelect
                                                      disabled
                                                      style={{ height: 40 }}
                                                      label={false}
                                                      {...input}
                                                      fullWidth
                                                    >
                                                      {cmsMetaData.fetchPossibleValuesMap?.APPROVAL_REQUIRED?.map(
                                                        (desc) => (
                                                          <MenuItem key={desc} value={desc}>
                                                            {desc}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                    </LkSelect>
                                                  )}
                                                </Field>
                                              </div>
                                            )}
                                            <div style={{ width: '5%' }} className="pd-5">
                                              <Shortcuts
                                                data={[
                                                  {
                                                    name: CONTENT.DELETE,
                                                    onClick: () => fields.remove(index)
                                                  }
                                                ]}
                                              />
                                            </div>
                                          </div>
                                        ))
                                      }
                                    </FieldArray>
                                  </div>
                                  <div className="add-condition pd-15 bg-white">
                                    <Box
                                      onClick={() => pushCons(push, errors)}
                                      className="cursor-pointer display-flex"
                                    >
                                      <div>
                                        <img
                                          className="mr-r10 mr-l10"
                                          src={`${
                                            import.meta.env.VITE_STATIC_SERVER
                                          }/images/Add.svg`}
                                          alt="add"
                                        />
                                      </div>
                                      <div className="align-self-center">
                                        {CONTENT.ADD_CONDITION}
                                      </div>
                                    </Box>
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                        <div />
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </>
          );
        }}
      />
    </div>
  );
};

export default ConstraintCreate;
