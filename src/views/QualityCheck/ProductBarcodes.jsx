import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import { getConsulKeyValue } from 'redux/reducers/consul';
import { toastOpen } from 'redux/actionCreators/toast';
import { updateQCData, markQcDone, fittingByStationCodeLoad } from 'redux/actionCreators/qc';
import usePermissions from 'common/usePermissions';
import constant from 'constant';
import localStorageHelper from 'utils/localStorageHelper';
import {
  scanNextBarcode,
  getOrderDetailsReset,
  setLensFocus,
  setHoldUnholdVisit
} from 'redux/actionCreators/order';
import { LOCALISATION } from 'redux/reducers/localisation';
import { verifyPartialOrders, checkCLProductExpiry } from 'utils/helpers';
import { LOGIN } from 'redux/reducers/login';
import LkInput from 'components/MaterialUi/LkInput';
import { isEmpty, keys } from 'utils/lodash';
import { Button } from '@mui/material';
import Spinner from 'components/Spinner/Spinner';
import ExpiryWarningModal from 'components/Modal/ExpiryWarningModal';
import BarCodeWrapper from './BarCodeWrapper';

const styles = makeStyles()((theme) => ({
  barcode: {
    position: 'relative',
    width: '808px',
    display: 'flex',
    marginRight: '30px',
    padding: '10px 5px 0px 5px',
    background: '#00BAC6',
    borderRadius: 8
  },
  disabled: {
    backgroundColor: 'rgb(238, 238, 238)',
    width: '300px'
  },
  root: {
    background: 'white',
    borderRadius: theme.spacing(0.5)
  },
  productCardLabel: {
    top: '-14px',
    left: '24px',
    color: '#FFFFFF',
    width: '90px',
    border: '2px solid #FFFFFF',
    position: 'absolute',
    fontSize: '10px',
    padding: '7px 0px 6px 0px',
    background: '#0A5D5B',
    justifyContent: 'center',
    textAlign: 'center',
    fontWeight: '700',
    lineHeight: '12px',
    borderRadius: '222px',
    textTransform: 'uppercase'
  },
  progressClassname: {
    color: '#FFFFFF',
    display: 'flex',
    alignItems: 'center'
  }
}));

const ProductBarcodes = (props) => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { productData, productIndex, renderData, setRenderData } = props;
  const { BAROCDDE_CLASSIFICATION } = useSelector((state) => state.consul.classification.data);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const stationCode = useState(localStorageHelper.getItem(constant.WORKSTATION));
  const { isQCSuperVisor } = usePermissions(userDetail?.permission);
  const {
    products,
    orderDetails,
    selectedIndex,
    isHoldUnholdActionClicked,
    allProductBarcodes,
    scannedBarCode,
    shipmentHeaders
  } = useSelector((state) => state.order);

  const { qcData, qcCompleteData, qcCompleteFail } = useSelector((state) => state.qc);
  const QC = useSelector((state) => state[LOCALISATION].localeData.QC);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);
  const { qcLensPowerDetailsFacilityList, clExpiryCheckFacilityList } = useSelector(
    (state) => state.consul.featureToggle.data
  );
  const { isLoading: reScanFittingLoading } = useSelector((state) => state.qc.qcLensoMeterData);
  const isBulkOrder = shipmentHeaders?.navChannel.toLowerCase().includes('bulk');
  const facilityUrl =
    keys(qcLensPowerDetailsFacilityList)?.length > 0
      ? qcLensPowerDetailsFacilityList?.[facilityCode]
      : '';
  const isSameFacility = orderDetails[0].order.facilityCode === facilityCode;

  // State for expiry warning modal
  const [showExpiryWarning, setShowExpiryWarning] = useState(false);
  const [expiryWarningData, setExpiryWarningData] = useState({ daysToExpiry: 0 });

  const isShowRescanBtn = !!facilityUrl && isSameFacility && !isBulkOrder;

  const {
    product: {
      order: { barcode: productBarcode, uwItemId: productUwItemId, product, unicomOrderCode } = {}
    } = {},
    left: { order: { barcode: leftBarcode, uwItemId: leftUwItemId } = {} } = {},
    right: { order: { barcode: rightBarcode, uwItemId: rightUwItemId } = {} } = {}
  } = productData || {};
  const [nextBarcodeValue, setNextBarcodeValue] = useState('');

  const returnToast = (subHeading, heading = 'Error', severity = 'error') =>
    dispatch(
      toastOpen({
        isToastOpen: true,
        heading,
        subHeading,
        severity,
        autoHideDuration: 7000
      })
    );

  const isBarCodeScanned = (barcode) =>
    qcData.findIndex(
      (x) =>
        x.barcode &&
        barcode &&
        x.barcode.trim().toUpperCase() === barcode.trim().toUpperCase() &&
        x.isScanningDone
    ) > -1;

  const resetValue = (id, byBpass) => {
    const domId = document.getElementById(id);
    if (domId && productIndex >= selectedIndex && !byBpass) {
      domId.value = '';
    }
  };

  const renderDataPaint = () => {
    dispatch(setLensFocus(null));
    const renderD = [];
    const { classification, productId } = product;
    const isDisabled = productIndex < selectedIndex;
    resetValue('next_product_field');
    if (
      !isEmpty(BAROCDDE_CLASSIFICATION) &&
      (BAROCDDE_CLASSIFICATION.SINGLE_BARCODE.includes(classification) ||
        BAROCDDE_CLASSIFICATION.EXCEPTIONS_SINGLE_BARCODE.includes(productId))
    ) {
      renderD.push(
        {
          title: QC.SCAN_FRAME,
          placeholder: QC.SCAN_FRAME,
          barcode: productBarcode,
          uwItemId: productUwItemId,
          disabled: true,
          autoFill: true
        },
        {
          title: QC.SCAN_TO_COMPLETE_QC,
          placeholder: QC.SCAN_TO_COMPLETE_QC,
          uwItemId: productUwItemId,
          barcode: productBarcode,
          disabled: isDisabled,
          autoFill: isBarCodeScanned(productBarcode),
          isCLType: product?.order?.product?.hsnClassification === 'contact-lens' ?? false
        }
      );
      setTimeout(() => {
        resetValue(`${productUwItemId}_1`);
        const id = document.getElementById(`${productUwItemId}_1`);
        if (id && !isDisabled) {
          id.focus();
        }
      }, 0);
    } else if (
      !isEmpty(BAROCDDE_CLASSIFICATION) &&
      BAROCDDE_CLASSIFICATION.MUTIPLE_BARCODE.includes(classification)
    ) {
      let isFrameScanned;
      let isLeftLensScanned;
      let isRightLensScanned;
      let defaultUwItemId = null;
      if (
        productBarcode.trim().toUpperCase() === scannedBarCode.trim().toUpperCase() ||
        isBarCodeScanned(productBarcode)
      ) {
        isFrameScanned = true;
        defaultUwItemId = productUwItemId;
      }

      if (
        (leftBarcode && leftBarcode.trim().toUpperCase() === scannedBarCode.trim().toUpperCase()) ||
        isBarCodeScanned(leftBarcode) ||
        isDisabled
      ) {
        isLeftLensScanned = true;
        defaultUwItemId = leftUwItemId;
      }

      if (
        rightBarcode?.trim().toUpperCase() === scannedBarCode.trim().toUpperCase() ||
        isBarCodeScanned(rightBarcode) ||
        isDisabled
      ) {
        isRightLensScanned = true;
        defaultUwItemId = rightUwItemId;
      }

      renderD.push({
        title: QC.SCAN_FRAME,
        placeholder: QC.SCAN_FRAME,
        barcode: productBarcode,
        uwItemId: productUwItemId,
        disabled: isFrameScanned,
        autoFill: isFrameScanned
      });
      if (productData.left) {
        renderD.push({
          title: QC.SCAN_LENS,
          placeholder: QC.SCAN_LENS,
          barcode: leftBarcode,
          uwItemId: leftUwItemId,
          disabled: isLeftLensScanned,
          autoFill: isLeftLensScanned,
          lensType: 'left'
        });
      }
      if (productData.right) {
        renderD.push({
          title: QC.SCAN_LENS,
          placeholder: QC.SCAN_LENS,
          barcode: rightBarcode,
          uwItemId: rightUwItemId,
          disabled: isRightLensScanned,
          autoFill: isRightLensScanned,
          lensType: 'right'
        });
      }
      if (!productData.left && !productData.right) {
        renderD.push({
          title: QC.SCAN_TO_COMPLETE_QC,
          placeholder: QC.SCAN_TO_COMPLETE_QC,
          uwItemId: productUwItemId,
          barcode: productBarcode,
          disabled: isDisabled,
          autoFill: isBarCodeScanned(productBarcode)
        });
      }

      if (defaultUwItemId && (productData.left || productData.right)) {
        dispatch(updateQCData({ isScanningDone: true }, defaultUwItemId));
      }
      setTimeout(() => {
        resetValue(`${leftUwItemId}_1`, isBarCodeScanned(leftBarcode));
        resetValue(`${rightUwItemId}_2`, isBarCodeScanned(rightBarcode));
        let id = document.getElementById(`${leftUwItemId}_1`);
        if (!productData.left && !productData.right) {
          id = document.getElementById(`${productUwItemId}_1`);
        }
        if (id && !isDisabled) {
          id.focus();
        }
      }, 0);
    }

    /** Sort renderData according to autoFill value */
    let sortedRenderData = [];
    const trueData = [];
    const falseData = [];
    renderD.forEach((item) => {
      if (item.autoFill) {
        trueData.push(item);
      } else {
        falseData.push(item);
      }
    });
    sortedRenderData = trueData.concat(falseData);

    setRenderData(sortedRenderData);

    /** Give focus to last empty input */
    setTimeout(() => {
      let focusDone = false;
      sortedRenderData.forEach((item, index) => {
        const elementRef = document.getElementById(`${item.uwItemId}_${index}`);
        if (
          elementRef &&
          (!elementRef.value || (elementRef.value && !elementRef.value.length)) &&
          !focusDone
        ) {
          elementRef.focus();
          focusDone = true;
        }
      });
      if (!focusDone) {
        const nextProdRef = document.getElementById('next_product_field');
        if (nextProdRef) {
          nextProdRef.focus();
        }
      }
    }, 0);
  };

  useEffect(() => {
    dispatch(getConsulKeyValue(['classification']));
  }, []);

  useEffect(() => {
    if (productIndex || productIndex === 0) {
      /** On changing product index, input values should empty. */
      // eslint-disable-next-line array-callback-return
      renderData.map((item, index) => {
        const inputRef = document.getElementById(`${item.uwItemId}_${index}`);
        if (inputRef) {
          inputRef.value = '';
        }
      });
      renderDataPaint();
    }
    setNextBarcodeValue('');
  }, [productIndex]);

  useEffect(
    () => () => {
      dispatch(setLensFocus(null));
    },
    []
  );

  const removeLastScannedItemFun = () => {
    setTimeout(() => {
      const itemId = renderData[renderData.length - 1]?.uwItemId;
      const inputIndex = renderData.length - 1;
      const inputRef = document.getElementById(`${itemId}_${inputIndex}`);
      if (inputRef) {
        const lastItem = renderData.filter((item) => item.barcode === inputRef.value)[0];
        dispatch(
          updateQCData(
            {
              isScanningDone: false
            },
            lastItem?.uwItemId
          )
        );
        inputRef.value = '';
        inputRef.focus();
      }
    }, 0);
  };

  useEffect(() => {
    if (
      qcCompleteFail?.response?.data?.error_message ||
      qcCompleteFail?.response?.data?.meta?.displayMessage ||
      qcCompleteFail?.response?.data?.meta?.message
    ) {
      // Below: Is used to remove last scanned input
      // value and give focus When markQCDone API got failed
      removeLastScannedItemFun();
    }
  }, [qcCompleteFail]);

  useEffect(() => {
    if (qcCompleteData) {
      dispatch(getOrderDetailsReset());
      navigate('/qc-summary');
    }
  });

  const isExistingScan = () => {
    let isValid = true;
    renderData.forEach((element) => {
      const { barcode } = element;
      if (
        qcData.filter(
          (x) =>
            x.barcode &&
            barcode &&
            x.barcode.trim().toUpperCase() === barcode.trim().toUpperCase() &&
            x.isScanningDone
        ).length === 0
      ) {
        isValid = false;
      }
    });
    return isValid;
  };

  const nextScanValid = (index, uwItemId, lensType) => {
    returnToast(QC.BARCODE_SCANNED_SUCCESSFULLY, 'Success', 'success');
    dispatch(setLensFocus(lensType));
    dispatch(
      updateQCData(
        {
          isScanningDone: true
        },
        uwItemId
      )
    );
    const validBarCodes = qcData.filter((x) => !x.isAccessories).length;
    if (
      qcData.filter((x) => !x.isAccessories && x.isScanningDone === true).length === validBarCodes
    ) {
      if (isQCSuperVisor) {
        // Verify partial operation has performed
        const count = verifyPartialOrders(qcData);
        if (count > 1) {
          returnToast("Partial Operations can't be performed either mark all FAIL or UNHOLD");
          removeLastScannedItemFun();
          return 0;
        }
      }

      dispatch(
        markQcDone({
          salesOrderId: unicomOrderCode,
          qcData
        })
      );
      /** End */
    } else if (index < renderData.length - 1) {
      // eslint-disable-next-line no-shadow
      const { uwItemId } = renderData[index + 1];
      const id = document.getElementById(`${uwItemId}_${index + 1}`);
      if (id) {
        setRenderData((prevRenderData) =>
          prevRenderData.map((eachRenderData, currentIndex) => {
            if (currentIndex === index) {
              return { ...eachRenderData, disabled: true };
            }
            return eachRenderData;
          })
        );
        id.focus();
      }
    } else if (index === renderData.length - 1) {
      if (productIndex === products.length - 1) {
        returnToast(QC.PLEASE_SCANNED_ALL_BARCODES);
      } else if (isExistingScan()) {
        const id = document.getElementById('next_product_field');
        if (id) {
          id.focus();
        }
      }
    }
    return false;
  };

  const scanLens = (value, index) => {
    const scannedBarcodeIndex = renderData.findIndex(
      (x) => x.barcode && value && x.barcode.trim().toUpperCase() === value.trim().toUpperCase()
    );
    if (scannedBarcodeIndex > -1) {
      const { barcode, uwItemId, lensType } = renderData[scannedBarcodeIndex];
      if (
        qcData.filter(
          (x) =>
            x.barcode &&
            barcode &&
            x.barcode.trim().toUpperCase() === barcode.trim().toUpperCase() &&
            x.isScanningDone
        ).length > 0
      ) {
        const barcodeInputRef = document.getElementById(`${uwItemId}_${scannedBarcodeIndex + 1}`);
        if (barcodeInputRef) {
          barcodeInputRef.value = '';
        }

        returnToast(QC.BARCODE_ALREADY_SCANNED);
      } else {
        // eslint-disable-next-line no-use-before-define
        nextScanValid(index, uwItemId, lensType);
      }
    } else {
      const barcodeInputRef = document.getElementById(`${renderData[index].uwItemId}_${index}`);
      if (barcodeInputRef) {
        barcodeInputRef.value = '';
      }
      returnToast(QC.INVALID_BARCODE_SCANNED);
    }
  };

  const onChangeHandler = (e, index) => {
    const { barcode, uwItemId, lensType } = renderData[index];
    const { value } = e.target;
    const { classification } = product;
    if (product?.hsnClassification === 'contact-lens') {
      const expiryDate = productData?.product?.order?.expiryDate;
    }
    if (
      !isEmpty(BAROCDDE_CLASSIFICATION) &&
      BAROCDDE_CLASSIFICATION.MUTIPLE_BARCODE.includes(classification)
    ) {
      scanLens(value, index);
    } else if (barcode && value && barcode.trim().toUpperCase() !== value.trim().toUpperCase()) {
      e.target.value = '';
      returnToast(QC.INVALID_BARCODE_SCANNED);
      dispatch(
        updateQCData(
          {
            isScanningDone: false
          },
          uwItemId
        )
      );
    } else {
      nextScanValid(index, uwItemId, lensType);
    }
  };

  useEffect(() => {
    if (isHoldUnholdActionClicked) {
      let focusDone = false;
      renderData.forEach((element, index) => {
        const elementRef = document.getElementById(`${element.uwItemId}_${index}`);
        if (
          elementRef &&
          (!elementRef.value || (elementRef.value && !elementRef.value.length)) &&
          !focusDone
        ) {
          elementRef.focus();
          focusDone = true;
        }
      });
      if (!focusDone) {
        const id = document.getElementById('next_product_field');
        if (id) {
          id.focus();
        }
      }
      dispatch(setHoldUnholdVisit(false));
    }
  }, [isHoldUnholdActionClicked]);

  const scanNextBarcodeOnKeyPress = (e) => {
    if (isExistingScan()) {
      const barcodeInput = e.target.value;
      let isBarcodeValid = false;
      // eslint-disable-next-line no-plusplus
      for (let i = selectedIndex + 1; i < products.length; i++) {
        // eslint-disable-next-line no-shadow
        const product = products[i];
        const { order } = product;
        const { barcode } = order;
        if (
          barcode &&
          barcodeInput &&
          barcode.trim().toUpperCase() === barcodeInput.trim().toUpperCase()
        ) {
          isBarcodeValid = true;
        }
      }
      if (isBarcodeValid) {
        dispatch(scanNextBarcode({ data: orderDetails, barcode: barcodeInput }));
      } else {
        let isAlreadyExist = false;
        // eslint-disable-next-line no-plusplus
        for (let i = 0; i <= selectedIndex; i++) {
          // eslint-disable-next-line no-shadow
          const product = products[i];
          const { order } = product;
          const { barcode } = order;
          if (
            barcode &&
            barcodeInput &&
            barcode.trim().toUpperCase() === barcodeInput.trim().toUpperCase()
          ) {
            isAlreadyExist = true;
          }
        }
        e.target.value = '';
        if (isAlreadyExist) {
          returnToast(QC.BARCODE_ALREADY_SCANNED);
        } else {
          returnToast(QC.INVALID_BARCODE_SCANNED);
        }
      }
    } else {
      returnToast(QC.PLEASE_SCANNED_ALL_BARCODES);
    }
  };

  const reScanPowerDetails = () => {
    dispatch(
      fittingByStationCodeLoad({
        url: qcLensPowerDetailsFacilityList?.[facilityCode] ?? '',
        stationCode: stationCode[0],
        fittingId: productData?.left?.order.fittingId ?? ''
      })
    );
  };

  // const handleQCFail = () => {
  // // Mark the current product as QC failed
  // qcData.forEach((data) => {
  //   const { uwItemId } = data;
  //   dispatch(
  //     updateQCData(
  //       {
  //         isScanningDone: false,
  //         status: 'QCFailed'
  //       },
  //       uwItemId
  //     )
  //   );
  // });

  //   returnToast('Product marked as
  // QC FAIL due to expiry. Please repick.', 'QC Failed', 'warning');
  // };

  return (
    <div className="display-flex justify-content-space-between">
      {/* <ExpiryWarningModal
        open={showExpiryWarning}
        onClose={() => setShowExpiryWarning(false)}
        // onFail={handleQCFail}
        daysToExpiry={expiryWarningData.daysToExpiry}
      /> */}
      <Box className={`${classes.barcode}`}>
        {products.length > 1 && (
          <div className={classes.productCardLabel}>Product {productIndex + 1}</div>
        )}
        {renderData.map((data, index) => {
          const { title, placeholder, disabled, autoFill, barcode, uwItemId } = data;
          const tempKey = `${barcode}-${index}`;
          return (
            <BarCodeWrapper
              key={tempKey}
              noRadius
              width={20}
              flex={1}
              padding={1}
              //   disable={uwItemId ? false : (productIndex < shipment.length ? false : true)}
              title={title}
            >
              <LkInput
                id={`${uwItemId || ''}_${index}`}
                //   disabled={uwItemId ? false : true}
                disabled={disabled}
                value={autoFill ? barcode : undefined}
                className={classes.root}
                variant="outlined"
                placeholder={placeholder}
                fullWidth
                size="small"
                onKeyUp={(e) => e.keyCode === 13 && onChangeHandler(e, index)}
              />
            </BarCodeWrapper>
          );
        })}
      </Box>
      {products.length > 1 && productIndex !== products.length - 1 && (
        <Box className={`${classes.barcode} ${classes.disabled}`}>
          {products.length > 1 && (
            <div className={classes.productCardLabel} style={{ background: '#ddd' }}>
              {QC.PRODUCT} {productIndex + 2}
            </div>
          )}
          <BarCodeWrapper
            noRadius
            width={20}
            flex={1}
            padding={1}
            disable
            title={QC.SCAN_NEXT_PRODUCT}
          >
            <LkInput
              disabled={!(productIndex >= selectedIndex)}
              id="next_product_field"
              className={classes.root}
              variant="outlined"
              placeholder={QC.SCAN_NEXT_PRODUCT_BARCODE}
              onChange={(e) => setNextBarcodeValue(e.target.value)}
              value={
                productIndex >= selectedIndex
                  ? nextBarcodeValue
                  : allProductBarcodes[productIndex + 1]
              }
              fullWidth
              size="small"
              onKeyUp={(e) => e.keyCode === 13 && scanNextBarcodeOnKeyPress(e)}
            />
          </BarCodeWrapper>
        </Box>
      )}
      {isShowRescanBtn && (
        <Box className="display-flex justify-content-space-between align-items-center">
          <Button
            size="medium"
            onClick={reScanPowerDetails}
            variant="contained"
            color="primary"
            fullWidth
            disabled={reScanFittingLoading}
          >
            {reScanFittingLoading ? (
              <Spinner progressClassname={classes.progressClassname} />
            ) : (
              'Re Scan Power'
            )}
          </Button>
        </Box>
      )}
    </div>
  );
};

export default ProductBarcodes;
