import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { convertToQueryString } from '../utils/helpers';
import localStorageHelper from '../utils/localStorageHelper';

export const grnBoxCodeApi = (payload) =>
  tokenInstance()
    .post(config.grn.boxScanner, payload)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const qcDetailApi = (payload) =>
  tokenInstance({
    showPutaway: localStorageHelper.getItem('showPutaway')
  })
    .post(config.grn.qcDetail, payload)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const getGrnList = (payload) => {
  const { invoiceNumber } = payload;
  const URL = `${config.grn.grnList}?invoice=${invoiceNumber}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const createGrn = (payload) => {
  const URL = `${config.grn.grnCreate}`;
  return tokenInstance()
    .post(URL, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getInvoicesByVendorId = (payload) => {
  const f = convertToQueryString(payload);
  const url = `${config.grn.invoiceVendor}?${f}`;
  // url = url.slice(0, -1)
  return tokenInstance()
    .get(url)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getInvoiceDetail = (payload) => {
  const { invoiceNum } = payload;
  const URL = `${config.grn.invoiceDetail}?invoice_ref_num=${invoiceNum}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getPidList = (payload) => {
  const { grnNo } = payload;
  const URL = `${config.grn.pidList}${grnNo}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getPidDetails = (payload) => {
  const { grnNo, pid } = payload;
  const URL = `${config.grn.pidDetails}${grnNo}/${pid}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};
// QC Sampling
export const getPidQcSampling = (payload) => {
  const { grnNo, pid } = payload;
  const URL = `${config.grn.itemSampling}${grnNo}/${pid}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

// Edit Item
export const getEditItem = (payload) => {
  const { barCode } = payload;
  const URL = `${config.grn.editItem}${barCode}`;
  return tokenInstance()
    .get(URL)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));
};

// Delete Item
export const deleteItem = (payload) => {
  const { barCode, type, po_id } = payload;
  const URL = `${config.grn.deleteItem}${barCode}?type=${type}&po_id=${po_id}`;
  return tokenInstance({
    showPutaway: localStorageHelper.getItem('showPutaway')
  })
    .delete(URL)
};

export const addPid = (payload) => {
  const URL = `${config.grn.addPid}`;
  return tokenInstance()
    .post(URL, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const updateQty = (payload) => {
  const URL = `${config.grn.updateQty}`;
  return tokenInstance()
    .put(URL, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const convertPid = (payload) => {
  const URL = `${config.grn.convertPid}`;
  return tokenInstance()
    .post(URL, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getSamplingQty = (payload) => {
  const URL = `${config.grn.getSamplingQty}`;
  return tokenInstance()
    .post(URL, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getGrnSummary = (payload) => {
  const { grnNo } = payload;
  const URL = `${config.grn.grnSummary}${grnNo}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getBoxItems = (payload) => {
  const { grn_code, pid } = payload;
  const URL = `${config.grn.getBoxItems}${grn_code}/${pid}`;
  return tokenInstance()
    .get(URL)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));
};

export const getExpiryDetails = ({ barcode, po_number, vendor_invoice_number }) => {
  // eslint-disable-next-line max-len
  const URL = `${config.grn.getExpiryDetails}?barcode=${barcode}&po_number=${po_number}&vendor_invoice_num=${vendor_invoice_number}`;
  return tokenInstance()
    .get(URL)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));
};

export const productMismatchApi = (payload) =>
  tokenInstance()
    .post(config.grn.productMismatch, payload)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const updateGrnItemApi = (payload) =>
  tokenInstance({
    showPutaway: localStorageHelper.getItem('showPutaway')
  })
    .put(config.grn.updateGrnItem, payload)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const closeGrnApi = (grnCode) =>
  tokenInstance({
    showPutaway: localStorageHelper.getItem('showPutaway')
  })
    .put(`${config.grn.closeGrn}${grnCode}`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const getAllGrnForAnInvoiceApi = (invNumber) =>
  tokenInstance()
    .get(`${config.grn.grnDetails}?invoice=${invNumber}`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const getSearchByDescApi = (payload) =>
  tokenInstance().get(config.product.searchByDesc, { params: payload });

export const getProductDetailsApi = (payload) => {
  const { isPrescriptionLens = false, ...rest } = payload;
  const url = isPrescriptionLens ? config.product.searchByDesc : config.product.productDetails;
  const headerTemp = {
    facility: localStorageHelper.getItem('facility-code'),
    source: 'DEALSKART'
  };

  return tokenInstance(headerTemp, true)
    .get(url, { params: rest })
    .then((response) => {
      if (isPrescriptionLens) {
        return { response: response.data };
      }
      return { response };
    })
    .catch((error) => {
      throw error;
    });
};

export const isGrnOpenApi = (payload) =>
  tokenInstance()
    .post(config.grn.isGrnOpen, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });

export const closeAllOpenGrnApi = (invoice) =>
  tokenInstance()
    .put(`${config.grn.closeAllOpenGrn}${invoice}`)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
