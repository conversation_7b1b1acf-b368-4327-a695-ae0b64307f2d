import { Box, Button } from '@mui/material';
import LkModal from 'components/Modal/Modal';
import React from 'react';
import AssignPickerTable from '../SkippedList/AssignPickerTable';

const ConfirmationModal = ({
  CONTENT,
  classes,
  selectedUser,
  setSelectedUser,
  selectedItems,
  assignItToUser
}) => (
  <LkModal
    modalWidth="609px"
    open
    title={CONTENT.ASSIGN_PICKER}
    subTitle={`${CONTENT.ARE_YOU_SURE_WANT_TO_ASSIGN}  ${selectedUser.username}
       ${CONTENT.THE_FOLLOWING_PIDS}`}
    handleClose={() => setSelectedUser(null)}
  >
    <Box marginTop="43px">
      <AssignPickerTable data={selectedItems} />
    </Box>
    <Box display="flex" justifyContent="center" marginTop="44px">
      <Box>
        <Button
          className={classes.cancelBtn}
          variant="outlined"
          color="primary"
          onClick={() => setSelectedUser(null)}
        >
          {CONTENT.CANCEL}
        </Button>
      </Box>
      <Box>
        <Button
          className={classes.confirmBtn}
          color="primary"
          variant="contained"
          onClick={assignItToUser}
        >
          {CONTENT.YES_ASSIGN}
        </Button>
      </Box>
    </Box>
  </LkModal>
);

export default ConfirmationModal;
