@use "sass:map";
@use "../../styles/variables";

.upload-csv {
    .file-upload-container,
    .file-container {
        margin: auto;
        width: max-content;
        margin-top: 9px;
    }
    .progressbar {
        height: 10px;
        border-radius: 12px;
        width: 350px;
        margin: auto;
    }
    .file-image {
        top: 4px;
        right: 6px;
    }
    .csv-row:nth-child(even) {
        background-color: map.get(variables.$colors-grey, ee);
    }
    .op-5 {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .tick-mark-img {
        height: 10px;        
        left: 5px;        
    }
}
