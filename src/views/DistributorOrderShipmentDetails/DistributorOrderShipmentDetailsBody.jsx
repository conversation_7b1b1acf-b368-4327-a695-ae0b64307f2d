import React, { useCallback, useMemo } from 'react';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkToolTip from 'components/ToolTip/ToolTip';
import useWindowResize from 'common/useWindowResize';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import { CONSUL } from 'redux/reducers/consul';
import { useSelector } from 'react-redux';
import { genericDateFormatted } from '../../utils/helpers';

const initailSortingData = {
  id: 'updatedOn',
  order: 'DESC'
};

const DistributorOrderShipmentDetailsBody = ({
  pageLimit,
  fetchMoreListItems,
  pageNumber,
  CONTENT,
  data,
  isLoading,
  totalCount
}) => {
  const [tableHeight] = useWindowResize(window.innerHeight - 280);
  const { shouldShowHsnColumnForDoPicking = false } = useSelector(
    (state) => state[CONSUL].featureToggle.data
  );

  const headerConfigBase = [
    {
      name: CONTENT.PRODUCT_ID,
      key: 'productId',
      formatBody: useCallback(
        ({ productId }) => (
          <Link to={`/inventory/${productId}`} target="_blank">
            {productId}
          </Link>
        ),
        []
      )
    },
    {
      name: CONTENT.REQUIRED_QTY,
      key: 'requiredQuantity'
    },

    {
      name: CONTENT.QTY_SCANNED,
      key: 'quantityScanned'
    },
    {
      name: CONTENT.PID_DESC,
      key: 'description',
      align: 'left',
      style: { minWidth: 300, maxWidth: 300 },
      formatBody: useCallback(
        ({ description }) => (
          <Box>
            {description.length > 100 ? (
              <LkToolTip
                placement="bottom-start"
                title={
                  <Box width={350} p={1}>
                    {description}{' '}
                  </Box>
                }
              >
                <Box className="ellipsis-vertical">{description}</Box>
              </LkToolTip>
            ) : (
              description || '-'
            )}
          </Box>
        ),
        []
      )
    },
    {
      name: CONTENT.UPDATED_ON,
      key: 'updatedOn',
      supportSort: true,
      formatBody: useCallback(({ updatedOn }) => genericDateFormatted(updatedOn), [])
    },
    {
      name: CONTENT.UPDATED_BY,
      key: 'updatedBy'
    }
  ];

  const headerConfig = useMemo(() => {
    const hasHsnColumn = headerConfigBase.some((col) => col.name === CONTENT.CATEGORY);

    if (shouldShowHsnColumnForDoPicking === hasHsnColumn) {
      return headerConfigBase;
    }

    if (shouldShowHsnColumnForDoPicking) {
      const newItem = {
        name: CONTENT.CATEGORY,
        key: 'hsnClassification'
      };

      return [...headerConfigBase.slice(0, 3), newItem, ...headerConfigBase.slice(3)];
    }
    return headerConfigBase.filter((col) => col.name !== CONTENT.CATEGORY);
  }, [shouldShowHsnColumnForDoPicking, headerConfigBase]);

  return (
    <Box className="pd-t10" data-cy="distributorOrderShipmentDetailsBody">
      <LkTable
        rowSize={60}
        pageLimit={pageLimit}
        tableData={data}
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        totalRowsCount={totalCount}
        headerConfig={headerConfig}
        pageNumber={pageNumber}
        dataRequestFunction={fetchMoreListItems}
        initialSortBy={initailSortingData}
      />
    </Box>
  );
};

export default DistributorOrderShipmentDetailsBody;
