import React from 'react';
import { withStyles } from 'tss-react/mui';

import Slider from '@mui/material/Slider';

const LKInputSlider = withStyles(Slider, (_theme, _params, classes) => ({
  root: {
    color: '#00B9C6',
    height: 8
  },
  thumb: {
    height: 24,
    width: 24,
    backgroundColor: '#fff',
    border: '2px solid currentColor',
    [`&:focus, &:hover, &.${classes.active}`]: {
      boxShadow: 'inherit'
    }
  },
  active: {},
  valueLabel: {
    left: 'calc(-50% + 4px)'
  },
  track: {
    height: 8,
    borderRadius: 4
  },
  rail: {
    height: 8,
    borderRadius: 8,
    background: 'linear-gradient(0deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5)), #4ECED4'
  },
  mark: {
    backgroundColor: '#bfbfbf',
    height: 6,
    width: 1,
    marginTop: -3,
    top: 34
  },
  markActive: {
    border: '1px solid #3C3C3C'
  },
  markLabel: {
    fontWeight: '400',
    fontSize: 12,
    lineHeight: '166%',
    letterSpacing: '0.4px',
    color: 'rgba(60, 60, 60, 0.54)',
    top: 38
  },
  markLabelActive: {
    color: '#3C3C3C'
  }
}));

const InputSlider = ({ value, step, marks, valueLabelFormat, onChange, min, max }) => ( 
  <LKInputSlider
    valueLabelDisplay="auto"
    marks={marks}
    value={value}
    min={min}
    max={max}
    step={step}
    valueLabelFormat={valueLabelFormat}
    onChange={onChange}
  />
);

export default InputSlider;

