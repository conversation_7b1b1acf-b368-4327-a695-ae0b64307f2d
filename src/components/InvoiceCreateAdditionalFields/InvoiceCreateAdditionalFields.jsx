import React from 'react';
import LkInput from 'components/MaterialUi/LkInput';
import { formatDateDashedSeparated } from '../../utils/helpers';

const InvoiceCreateAdditionalFields = props => {
    const { classes, additionalFields, setAdditionalFields, createClicked } = props;
    const { b2b_invoice_date, bill_of_entry_amount, bill_of_entry_date, bill_of_entry_number, send_to_party, handover_party } = additionalFields;
    return <div className="invoice-additional mr-t25">
        <div className="pd-l30 pd-r30 pd-b25">
            <div className="border-top-ee"></div>
        </div>
        <div className="fs16 fw-bold text-33 mr-b25 text-center">Additional Fields (Optional)</div>
        <div className="display-flex justify-content-space-between">
            <LkInput
                InputLabelProps={{
                    shrink: true,
                }}
                inputProps={{
                    max: formatDateDashedSeparated(Date.now())
                }}
                className={classes.addOnField}
                id="b2b-date"
                label="B2B Invoice Date"
                type="date"
                variant="outlined"
                onChange={e => setAdditionalFields({ ...additionalFields, b2b_invoice_date: e.target.value })}
                value={b2b_invoice_date}
            />
            <LkInput
                InputLabelProps={{
                    shrink: true,
                }}
                className={classes.addOnField}
                id="handover-by"
                label="Handover By"
                type="text"
                variant="outlined"
                value={handover_party}
                onChange={e => setAdditionalFields({ ...additionalFields, handover_party: e.target.value })}
            />
            <LkInput
                InputLabelProps={{
                    shrink: true,
                }}
                className={classes.addOnField}
                label="Send to Party"
                type="text"
                variant="outlined"
                value={send_to_party}
                onChange={e => setAdditionalFields({ ...additionalFields, send_to_party: e.target.value })}
            />
        </div>
        <div className="display-flex justify-content-space-between mr-t25">
            <LkInput
                InputLabelProps={{
                    shrink: true,
                }}
                className={classes.addOnField}
                id="bill-amount"
                label="Bill Of Entry Amount"
                error={createClicked && bill_of_entry_amount && isNaN(bill_of_entry_amount) ? 'Must be Integer' : undefined}
                type="text"
                variant="outlined"
                value={bill_of_entry_amount}
                onChange={e => setAdditionalFields({ ...additionalFields, bill_of_entry_amount: e.target.value })}
                helperText={createClicked && bill_of_entry_amount && isNaN(bill_of_entry_amount) ? 'Must be Integer' : undefined}
            />
            <LkInput
                InputLabelProps={{
                    shrink: true,
                }}
                inputProps={{
                    max: formatDateDashedSeparated(Date.now())
                }}
                className={classes.addOnField}
                id="bill-enter-date"
                label="Bill Of Entry Date"
                type="date"
                variant="outlined"
                value={bill_of_entry_date}
                onChange={e => setAdditionalFields({ ...additionalFields, bill_of_entry_date: e.target.value })}
            />
            <LkInput
                InputLabelProps={{
                    shrink: true,
                }}
                className={classes.addOnField}
                id="bill-entry-number"
                label="Bill Of Entry Number"
                type="text"
                variant="outlined"
                value={bill_of_entry_number}
                onChange={e => setAdditionalFields({ ...additionalFields, bill_of_entry_number: e.target.value })}
            />
        </div>
    </div>
};

export default InvoiceCreateAdditionalFields;