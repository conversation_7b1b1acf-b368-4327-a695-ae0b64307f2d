
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getGrnPidApi = (data) => {
    const { grnCode, pageNum } = data
    return tokenInstance().get(`${config.grnDetails.getGrnPids}?grn_code=${grnCode}&page=${pageNum}`)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const getGrnblockedPidApi = (data) => {
    const key = data?.key
    const value = data?.value
    const pageNum = data?.pageNum || 0
    const grnCode = data?.grnCode
    const url = key ? `${config.grnDetails.getGrnBlockedPids}?page=${pageNum}&grnCode=${grnCode}&${key}=${value}` :
        `${config.grnDetails.getGrnBlockedPids}?page=${pageNum}&grnCode=${grnCode}`
    return tokenInstance().get(url)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const exportGrnBarcodeDetailsApi = (grn_code) => {
    return tokenInstance().get(`/nexs/api/grn/v1/${grn_code}/export-barcode-details`)
        .then(response => ({ response }))
        .catch(error => { throw error });
}


export const grnPdfApi = (grn_code) => {
    return tokenInstance().get(`${config.grnDetails.grnPdfDownload}/${grn_code}`,
        { responseType: 'arraybuffer' }
    )
        .then(response => ({ response }))
        .catch(error => { throw error });
}
