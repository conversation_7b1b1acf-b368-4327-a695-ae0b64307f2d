import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Form, Field } from 'react-final-form';
import LkModal from 'components/Modal/Modal';
import { Button } from '@mui/material';
import LkInput from 'components/MaterialUi/LkInput';
import { required } from 'utils/validation';
import { composeValidators } from 'utils/helpers';
import { LOCALISATION } from 'redux/reducers/localisation';
import { toastOpen } from 'redux/actionCreators/toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { setLayoutCreateData } from '../../redux/actionCreators/layout';

const LayoutCreateModal = (props) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { open, close } = props;
  const dispatch = useDispatch();
  const { layoutCreateData } = useSelector((state) => state.layout);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.LAYOUT_PAGE);

  const submitReason = (values) => {
    const { isClone } = props;
    if (isClone && values.name.toUpperCase() === layoutCreateData?.nameCopy.toUpperCase()) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: `${CONTENT.LAYOUT_NAME_NOT_BE} ${layoutCreateData?.nameCopy}`,
          severity: 'error'
        })
      );
    } else {
      const obj = { ...layoutCreateData, ...values };
      dispatch(setLayoutCreateData(obj));
      if (location?.pathname !== '/layoutpage/create') {
        navigate('/layoutpage/create');
      }
      close();
    }
  };

  return (
    <LkModal
      open={open}
      title={CONTENT.NAME_YOUR_LAYOUT_SCHEMA}
      subTitle={CONTENT.GIVE_YOUR_LAYOUT_SCHEMA}
      handleClose={() => close()}
    >
      <Form
        onSubmit={submitReason}
        initialValues={{ name: layoutCreateData?.name, description: layoutCreateData?.description }}
        render={({ handleSubmit }) => (
          <form
            onSubmit={handleSubmit}
            name="reason-po-form"
            className="refrence-detail-form"
            noValidate
          >
            <Field name="name" validate={composeValidators(required)}>
              {({ input, meta }) => (
                <div className="material-group mr-t40">
                  <LkInput {...input} fullWidth label={CONTENT.NAME} />
                  <span className="input-error">
                    {meta.error && meta.touched ? meta.error : ''}
                  </span>
                </div>
              )}
            </Field>
            <Field name="description" validate={composeValidators(required)}>
              {({ input, meta }) => (
                <div className="material-group mr-t40">
                  <LkInput {...input} fullWidth label={CONTENT.DESCRIPTION} multiline rows={4} />
                  <span className="input-error">
                    {meta.error && meta.touched ? meta.error : ''}
                  </span>
                </div>
              )}
            </Field>
            <div className="display-flex justify-content-center mr-b40 mr-t40">
              <div>
                <Button
                  type="submit"
                  style={{ borderRadius: 8 }}
                  variant="contained"
                  color="primary"
                >
                  {CONTENT.CONTINUE}
                </Button>
              </div>
            </div>
          </form>
        )}
      />
    </LkModal>
  );
};

export default LayoutCreateModal;
