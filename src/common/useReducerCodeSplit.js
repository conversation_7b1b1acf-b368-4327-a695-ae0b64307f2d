import { useContext, useEffect } from 'react';
import { ReactReduxContext } from 'react-redux';

const loadReducers = async (reducers) => {
  const reducerPromises = reducers.map((reducer) =>
    import(`../redux/reducers/${reducer}.js`).then((module) => ({
      name: reducer,
      reducer: module.default
    }))
  );

  const loadedReducers = await Promise.all(reducerPromises);
  return loadedReducers.reduce((acc, { name, reducer }) => {
    acc[name] = reducer;
    return acc;
  }, {});
};

const loadSagas = async (sagas) => {
  const sagaPromises = sagas.map((saga) =>
    import(`../redux/sagas/${saga}.js`).then((module) => ({
      name: saga,
      saga: module.default
    }))
  );

  const loadedSagas = await Promise.all(sagaPromises);
  return loadedSagas.reduce((acc, { name, saga }) => {
    acc[name] = saga;
    return acc;
  }, {});
};

const useReducerCodeSplit = (reducers = [], sagas = []) => {
  const { store } = useContext(ReactReduxContext);
  const state = store.getState();
  const reducerMap = Object.keys(state);
  const reducersLoaded = reducers.every((reducer) => reducerMap.includes(reducer));

  useEffect(() => {
    const loadCodeSplit = async () => {
      if (!reducersLoaded) {
        const [loadedReducers, loadedSagas] = await Promise.all([
          loadReducers(reducers),
          loadSagas(sagas)
        ]);

        store.reducerManager.add(loadedReducers);
        store.sagaManager.runSaga(loadedSagas);
      }
    };

    loadCodeSplit();

    return () => {
      store.reducerManager.remove(reducers);
      store.sagaManager.cancelSaga(sagas);
    };
  }, []);

  return reducersLoaded;
};

export default useReducerCodeSplit;
