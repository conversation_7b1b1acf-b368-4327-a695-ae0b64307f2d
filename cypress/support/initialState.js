import CONTENT from '../../public/localisation/lang_en.json';

export const initialState = {
  settings: {
    selectedFacility: '0QNXS',
    facilitiesObj: {
      getFacilitiesLoad: false,
      facilities: [
        {
          code: 'SA01',
          name: 'Saudi Facility',
          facilityType: 'WAREHOUSE'
        },
        {
          code: 'LKSTUAE1',
          name: 'UAE Nexs Test Store',
          facilityType: 'WAREHOUSE'
        },
        {
          code: 'QNXS2',
          name: 'NexS Product Test',
          facilityType: 'WAREHOUSE'
        },
        {
          code: '0QNXS',
          name: 'NexS Product Test',
          facilityType: 'WAREHOUSE'
        },
        {
          code: 'UAE1',
          name: 'testingUAE1',
          facilityType: 'WAREHOUSE'
        },
        {
          code: 'LG02',
          name: 'testingLG',
          facilityType: 'WAREHOUSE'
        },
        {
          code: '0QNXS4',
          name: '0QNXS4',
          facilityType: 'WAREHOUSE'
        },
        {
          code: 'LKH03',
          name: '<PERSON>KH03',
          facilityType: 'UNICOM'
        },
        {
          code: 'LKHTest783',
          name: 'L<PERSON>HTest783',
          facilityType: 'COCO'
        },
        {
          code: 'test78',
          name: 'test78',
          facilityType: 'WAREHOUSE'
        },
        {
          code: '0QNXS3',
          name: '0QNXS3',
          facilityType: 'WAREHOUSE'
        },
        {
          code: 'BW02',
          name: 'BW02',
          facilityType: 'UNICOM'
        },
        {
          code: 'BW01',
          name: 'BW01',
          facilityType: 'UNICOM'
        },
        {
          code: 'asd',
          name: 'asd',
          facilityType: 'PLANT'
        },
        {
          code: 'NXS2',
          name: 'NeXS-Manesar Facility',
          facilityType: 'WAREHOUSE'
        }
      ]
    },
    globalTopBar: {}
  },
  localisation: {
    localeData: CONTENT
  }
};
