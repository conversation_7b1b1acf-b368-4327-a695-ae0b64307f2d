import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import Box from '@mui/material/Box';
import StatusFilter from 'components/common/StatusFilter';
import AutoSelectFilter from 'components/common/AutoSelectFilter';
import InputFilter from 'components/common/InputFilter';
import DateSelectFilter from 'components/common/DateSelectFilter';
import CustomFilter from 'components/common/CustomFilter';
import SingleSelectFilter from 'components/common/SingleSelectFilter';

const LkTableHeaderFilter = ({
  name,
  submitFilter,
  defaultValue,
  postData,
  url,
  dateOptions,
  methodType = 'GET',
  type = null,
  columnName = null,
  apiName = null,
  listData = null,
  selectedFilterList = [],
  clearOnSubmit = true,
  localSearch = false
}) => {
  const renderFilter = useMemo(() => {
    switch (type) {
      case 'status': {
        return (
          <StatusFilter
            listData={listData}
            onSubmit={submitFilter}
            selectedFilterList={selectedFilterList}
            // marginLeft={marginLeft}
            columnName={columnName}
            apiName={apiName}
          />
        );
      }
      case 'autoSelect': {
        return (
          <AutoSelectFilter
            listData={listData}
            onSubmit={submitFilter}
            selectedFilterList={selectedFilterList}
            columnName={columnName}
            apiName={apiName}
            localSearch={localSearch}
            defaultValue={defaultValue}
            type={methodType}
            postData={postData}
            url={url}
          />
        );
      }
      case 'input': {
        return (
          <InputFilter
            onSubmit={submitFilter}
            selectedFilterList={selectedFilterList}
            // marginLeft={marginLeft}
            columnName={columnName}
            apiName={apiName}
            name={name}
            clearOnSubmit={clearOnSubmit}
          />
        );
      }
      case 'dateRange': {
        return (
          <DateSelectFilter
            onSubmit={submitFilter}
            columnName={columnName}
            selectedFilterList={selectedFilterList}
            dateOptions={dateOptions}
            // marginLeft={marginLeft}
          />
        );
      }
      case 'singleSelect': {
        return (
          <SingleSelectFilter
            onSubmit={submitFilter}
            columnName={columnName}
            listData={listData}
            selectedFilterList={selectedFilterList}
          />
        );
      }
      case 'custom': {
        return (
          <CustomFilter
            listData={listData}
            onSubmit={submitFilter}
            columnName={columnName}
            selectedFilterList={selectedFilterList}
            // marginLeft={marginLeft}
          />
        );
      }
      default:
        return null;
    }
  }, [listData, selectedFilterList, columnName]);

  return <Box pl={0.5}>{renderFilter}</Box>;
};

export default LkTableHeaderFilter;

LkTableHeaderFilter.propTypes = {
  type: PropTypes.string,
  name: PropTypes.string.isRequired,
  submitFilter: PropTypes.func.isRequired,
  columnName: PropTypes.string,
  apiName: PropTypes.string,
  listData: PropTypes.oneOfType([PropTypes.instanceOf(Array), PropTypes.instanceOf(Object)]),
  selectedFilterList: PropTypes.instanceOf(Array),
  clearOnSubmit: PropTypes.bool,
  localSearch: PropTypes.bool
};
