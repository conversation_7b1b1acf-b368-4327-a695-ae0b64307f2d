
const generateValues = (from<PERSON><PERSON><PERSON>, toRange, diff) => {
    let result = []
    while (fromRange <= toRange) {
        result.push(fromRange)
        fromRange = fromRange + diff
    }
    return result
}
export const productDescriptionData = {
    "SPH Power": generateValues(-20, 20, .25),
    "CYL Power": generateValues(-7, 0, .25),
    "AP": generateValues(.75, 4.5, .25)
};


const otherPowerData = {
    AXIS: generateValues(1, 180, 1),
    BC: ["B"]
};

export const POWER_DATA = {
    Power: {
        api_key: 'power',
        type: 'dd',
        values: [
            { api_key: 'sph', key: 'SPH', values: productDescriptionData["SPH Power"] },
            { api_key: 'cyl', key: 'CYL', values: productDescriptionData["CYL Power"] },
            { api_key: 'axis', key: 'AXIS', values: otherPowerData.AXIS },
            { api_key: 'ap', key: 'AP', values: productDescriptionData.AP },
            { api_key: 'base_curve', key: 'BC', values: otherPowerData.BC }
        ]
    }
};
