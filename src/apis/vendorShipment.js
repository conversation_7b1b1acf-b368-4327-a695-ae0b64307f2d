import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const getPoListByVendorApi = ({ vendorName }) =>
  tokenInstance().get(`${config.vendorShipment.poListByVendor}/${vendorName}?status=DRAFT`);

export const createVendorShipmentApi = (payload) =>
  tokenInstance().post(config.vendorShipment.create, payload);

export const getDetailsByShipment = (payload) =>
  tokenInstance().get(config.vendorShipment.detailsByShipment, { params: payload });

export const uploadVendorPoShipmentItemsApi = ({ payload, formData }) =>
  tokenInstance().post(config.vendorShipment.uploadCsv, formData, { params: payload });

export const createShipmentItemsApi = (payload) =>
  tokenInstance().post(config.vendorShipment.createShipmentItems, payload);

export const getPidDetailsApi = (payload = {}) =>
  tokenInstance().post(config.catalog.productDetails, payload);

export const vendorShipmentListAPI = (payload) =>
  tokenInstance().post(config.vendorShipmentList.list, payload);

export const vendorShipmentDetailsAPI = (payload) =>
  tokenInstance().get(config.vendorShipmentList.details, { params: payload });
