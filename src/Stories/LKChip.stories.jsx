import LkChip from 'components/MaterialUi/LkChip';
import React from 'react';

export default {
  title: 'Components/Chip',
  component: LkChip,
  args: {
    // default values
    size: 'small',
    variant: 'outlined',
    type: ''
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['contained', 'outlined']
    },
    size: {
      control: 'select',
      options: ['small', 'medium']
    },
    type: {
      control: 'select',
      options: [
        '',
        'success',
        'error',
        'CLOSED',
        'warning',
        'info',
        'grey2',
        'brightYellow',
        'parisGreen',
        'RETURN_AWAITED',
        'red',
        'filter',
        'primary',
        'CREATED',
        'DRAFT',
        'PENDING',
        'ACTIVE',
        'INACTIVE',
        'DISCARDED',
        'secondarDark',
        'failure',
        'processing'
      ]
    },
    label: {
      control: 'text',
      description: 'Add Label Text'
    }
  }
};

const Template = (args) => <LkChip {...args} />;

export const SmallChip = Template.bind({});

SmallChip.args = {
  size: 'small',
  type: 'success',
  label: 'LK Chip'
};

export const MediumChip = Template.bind({});

MediumChip.args = {
  size: 'medium',
  type: 'processing',
  label: 'LK Chip'
};
