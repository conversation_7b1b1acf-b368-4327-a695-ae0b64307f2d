import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import { fileDownload } from 'utils/helpers';
import LkToolTip from 'components/ToolTip/ToolTip';
import { closeAllOpenGRNLoad } from 'redux/actionCreators/grn';
import {
  exportInvoiceLoad,
  exportInvoiceReset
} from 'redux/actionCreators/invoiceReferenceCreation';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';

const InfoStrapAction = ({
  infoStripButton,
  discardFun,
  status,
  invoiceRefNum,
  debit_note_number,
  openPrintModal,
  vendorName,
  po_num,
  invoiceName,
  vendorId,
  contains_batch_no
}) => {
  const navigate = useNavigate();
  const { closeAllOpenGrnLoading } = useSelector((state) => state.grn);
  const { exportInvoiceData, exportInvoiceDataFail, exportInvoiceLoading } = useSelector(
    (state) => state.invoiceReferenceCreation
  );
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const dispatch = useDispatch();

  const closeAllOpenGrnFun = () => {
    dispatch(closeAllOpenGRNLoad(invoiceRefNum));
  };

  const exportFun = () =>
    !exportInvoiceLoading && dispatch(exportInvoiceLoad({ fromDetails: true, invoiceRefNum }));

  useEffect(() => {
    if (exportInvoiceData?.response?.data) {
      fileDownload(exportInvoiceData.response.data, `Invoice-NEXS-${invoiceRefNum}`);
      dispatch(exportInvoiceReset());
    } else if (exportInvoiceDataFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: exportInvoiceDataFail.response.data.message,
          severity: 'error'
        })
      );
      dispatch(exportInvoiceReset());
    }
  }, [exportInvoiceData, exportInvoiceDataFail, dispatch]);

  const toolTipElement = () => (
    <div className="fs14 invoiceActions">
      <div
        className="display-flex justify-content-space-between pd-10 cursor-pointer"
        onClick={() => openPrintModal(true)}
      >
        <div className="fs14">
          {' '}
          <img
            className="mr-r8"
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/print.svg`}
            alt="img"
          />
          {INVOICE.PRINT}
        </div>
        <div className="text-99">Ctrl+P</div>
      </div>
      {status !== 'CLOSED' && (
        <div
          className="fs14 pd-8 pd-l12 cursor-pointer pd-b12 po-action"
          onClick={closeAllOpenGrnFun}
        >
          {INVOICE.CLOSE_GRNS}
        </div>
      )}
      {status !== 'DISCARDED' && status !== 'CLOSED' && (
        <div className="fs14  pd-10 pd-l12  cursor-pointer" onClick={discardFun}>
          {INVOICE.DISCARD}
        </div>
      )}
      <div className="fs14  pd-10 pd-l12  cursor-pointer" onClick={exportFun}>
        {INVOICE.EXPORT_LOWERCASE}
      </div>
    </div>
  );

  return (
    <Box className="display-flex" alignItems="center">
      {(status === 'CREATED' || status === 'PENDING') && (
        <Box mr={1}>
          <Button
            color="primary"
            variant="outlined"
            onClick={() =>
              navigate(`/invoice/create?invoiceRefNo=${invoiceRefNum}&editMode=true`)
            }
            className={infoStripButton}
          >
            {INVOICE.EDIT}
          </Button>
        </Box>
      )}
      {status !== 'CLOSED' && status !== 'DISCARDED' && (
        <Box mr={1}>
          {' '}
          <Button
            disabled={status === 'PENDING'}
            variant="outlined"
            onClick={() =>
              navigate(// eslint-disable-next-line max-len
                `/grn/create?vendorName=${vendorName}&poNumber=${po_num}&invoiceRefNum=${invoiceRefNum}&invoiceName=${invoiceName}&vendorId=${vendorId}&contains_batch_no=${contains_batch_no}`)
            }
            color="primary"
            className={infoStripButton}
          >
            {INVOICE.START_RECEIVING}
          </Button>{' '}
        </Box>
      )}
      {(status === 'CREATED' || status === 'PENDING' || status === 'IQC_COMPLETE') && (
        <Button
          disabled={status === 'PENDING' || closeAllOpenGrnLoading}
          variant="contained"
          onClick={() =>
            navigate(`/invoice/create?invoiceRefNo=${invoiceRefNum}&closeInvoice=true`)
          }
          color="primary"
          className={infoStripButton}
        >
          {INVOICE.CLOSE_INVOICE}
        </Button>
      )}
      {status !== 'CREATED' && debit_note_number && (
        <Button
          variant="contained"
          onClick={() => navigate(`/debitNote/detail?dnNum=${debit_note_number}`)}
          color="primary"
          className={infoStripButton}
        >
          {INVOICE.VIEW_DEBIT_NOTE}
        </Button>
      )}
      <Box ml={3} mr={2}>
        <LkToolTip placement="bottom" title={toolTipElement()} width="300px">
          <IconButton>
            <MoreVertIcon />
          </IconButton>
        </LkToolTip>
      </Box>
    </Box>
  );
};

export default React.memo(InfoStrapAction);
