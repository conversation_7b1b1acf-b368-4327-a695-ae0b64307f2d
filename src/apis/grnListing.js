
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const grnSearchApi = (data) => {
    const key = data?.key
    const value = data?.value
    const pageNum = data?.pageNum || 0
    const url = key ? `${config.grnListing.searchGRN}?page=${pageNum}&${key}=${value}` :
    `${config.grnListing.searchGRN}?page=${pageNum}`
    return tokenInstance().get(url) 
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const blockedPidApi = (data) => {
    const key = data?.key
    const value = data?.value
    const pageNum = data?.pageNum || 0
    const url = key ? `${config.grnListing.blockedPid}?page=${pageNum}&${key}=${value}` :
    `${config.grnListing.blockedPid}?page=${pageNum}`
    return tokenInstance().get(url)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const reassignGrnApi = (payload) => {
    return tokenInstance().put(`${config.grnListing.reassignGrn}`, payload)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const exportGrnDetailsApi = (data) => {
    const key = data?.key
    const value = data?.value
    const url = key ? `${config.grnListing.exportGrnDetail}?${key}=${value}` : 
    `${config.grnListing.exportGrnDetail}`
    return tokenInstance().get(url)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const manualOverrideApi = (data) => {
    const {payload,filter} = data
    const key = filter?.key
    const value = filter?.value
    const grnCode = filter?.grnCode
    let url = grnCode ? `${config.grnListing.manualOverride}?${key}=${value}&grnCode=${grnCode}` : 
    `${config.grnListing.manualOverride}?${key}=${value}`
    return tokenInstance().put(url,payload) 
        .then(response => ({ response }))
        .catch(error => { throw error });
}
