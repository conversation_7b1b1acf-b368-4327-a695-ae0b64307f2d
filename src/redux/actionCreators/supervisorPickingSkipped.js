import * as action from '../actionTypes/supervisorPickingSkipped';

export const supervisorPickingSkippedDataRequest = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_LOAD,
  payload: data
});
export const supervisorPickingSkippedDataSuccess = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_SUCCESS,
  payload: data
});
export const supervisorPickingSkippedDataFail = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_FAIL,
  payload: data
});
export const supervisorPickingSkippedDataReset = () => ({
  type: action.GET_SUPERVISOR_PICKING_SKIPPED_DATA_RESET
});

export const supervisorPickingUsersRequest = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_USERS_LOAD,
  payload: data
});
export const supervisorPickingUsersSuccess = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_USERS_SUCCESS,
  payload: data
});
export const supervisorPickingUsersFail = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_USERS_FAIL,
  payload: data
});

export const supervisorPickingHoldDataRequest = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_HOLD_DATA_LOAD,
  payload: data
});
export const supervisorPickingHoldDataSuccess = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_HOLD_DATA_SUCCESS,
  payload: data
});
export const supervisorPickingHoldDataFail = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_HOLD_DATA_FAIL,
  payload: data
});
export const supervisorPickingHoldDataReset = () => ({
  type: action.GET_SUPERVISOR_PICKING_HOLD_DATA_RESET
});

export const supervisorPickingCategoriesRequest = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_CATAGORIES_LOAD,
  payload: data
});
export const supervisorPickingCategoriesSuccess = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_CATAGORIES_SUCCESS,
  payload: data
});
export const supervisorPickingCategoriesFail = (data) => ({
  type: action.GET_SUPERVISOR_PICKING_CATAGORIES_FAIL,
  payload: data
});

export const supervisorPickingNotFoundRequest = (data) => ({
  type: action.SUPERVISOR_PICKING_NOT_FOUND_REQUEST,
  payload: data
});
export const supervisorPickingNotFoundSuccess = (data) => ({
  type: action.SUPERVISOR_PICKING_NOT_FOUND_SUCCESS,
  payload: data
});
export const supervisorPickingNotFoundFail = (data) => ({
  type: action.SUPERVISOR_PICKING_NOT_FOUND_FAIL,
  payload: data
});

export const supervisorPickingHoldRequest = (data) => ({
  type: action.SUPERVISOR_PICKING_HOLD_REQUEST,
  payload: data
});
export const supervisorPickingHoldSuccess = (data) => ({
  type: action.SUPERVISOR_PICKING_HOLD_SUCCESS,
  payload: data
});
export const supervisorPickingHoldFail = (data) => ({
  type: action.SUPERVISOR_PICKING_HOLD_FAIL,
  payload: data
});

export const supervisorPickingUnHoldRequest = (data) => ({
  type: action.SUPERVISOR_PICKING_UN_HOLD_REQUEST,
  payload: data
});
export const supervisorPickingUnHoldSuccess = (data) => ({
  type: action.SUPERVISOR_PICKING_UN_HOLD_SUCCESS,
  payload: data
});
export const supervisorPickingUnHoldFail = (data) => ({
  type: action.SUPERVISOR_PICKING_UN_HOLD_FAIL,
  payload: data
});

export const supervisorPickingAssignRequest = (data) => ({
  type: action.SUPERVISOR_PICKING_ASSIGN_REQUEST,
  payload: data
});
export const supervisorPickingAssignSuccess = (data) => ({
  type: action.SUPERVISOR_PICKING_ASSIGN_SUCCESS,
  payload: data
});
export const supervisorPickingAssignFail = (data) => ({
  type: action.SUPERVISOR_PICKING_ASSIGN_FAIL,
  payload: data
});

export const supervisorPickingScanRequest = (data) => ({
  type: action.SUPERVISOR_PICKING_SCAN_REQUEST,
  payload: data
});
export const supervisorPickingScanSuccess = (data) => ({
  type: action.SUPERVISOR_PICKING_SCAN_SUCCESS,
  payload: data
});
export const supervisorPickingScanFail = (data) => ({
  type: action.SUPERVISOR_PICKING_SCAN_FAIL,
  payload: data
});

export const getExportSkippedDataLoad = (data) => ({
  type: action.GET_EXPORT_SKIPPED_DATA_LOAD,
  payload: data
});
export const getExportSkippedDataSuccess = (data) => ({
  type: action.GET_EXPORT_SKIPPED_DATA__SUCCESS,
  payload: data
});
export const getExportSkippedDataFail = (data) => ({
  type: action.GET_EXPORT_SKIPPED_DATA__FAIL,
  payload: data
});
export const getExportSkippedDataReset = (data) => ({
  type: action.GET_EXPORT_SKIPPED_DATA__RESET,
  payload: data
});

export const getUploadPidDataLoad = (data) => ({
  type: action.GET_UPLOAD_PID_DATA_LOAD,
  payload: data
});
export const getUploadPidDataSuccess = (data) => ({
  type: action.GET_UPLOAD_PID_DATA_SUCCESS,
  payload: data
});
export const getUploadPidDataFail = (data) => ({
  type: action.GET_UPLOAD_PID_DATA_FAIL,
  payload: data
});
export const getUploadPidDataReset = (data) => ({
  type: action.GET_UPLOAD_PID_DATA_RESET,
  payload: data
});

export const moveFromASRSToManualLoad = (data) => ({
  type: action.MOVE_FROM_ASRS_TO_MANUAL_LOAD,
  payload: data
});

export const moveFromASRSToManualSuccess = (data) => ({
  type: action.MOVE_FROM_ASRS_TO_MANUAL_SUCCESS,
  payload: data
});

export const moveFromASRSToManualFail = (data) => ({
  type: action.MOVE_FROM_ASRS_TO_MANUAL_FAIL,
  payload: data
});

export const moveFromASRSToManualReset = (data) => ({
  type: action.MOVE_FROM_ASRS_TO_MANUAL_RESET,
  payload: data
});

export const bulkUploadDiscardedShipmentLoad = (data) => ({
  type: action.BULK_UPLOAD_DISCARDED_SHIPMENT_LOAD,
  payload: data
});

export const bulkUploadDiscardedShipmentSuccess = (data) => ({
  type: action.BULK_UPLOAD_DISCARDED_SHIPMENT_SUCCESS,
  payload: data
});

export const bulkUploadDiscardedShipmentFail = (data) => ({
  type: action.BULK_UPLOAD_DISCARDED_SHIPMENT_FAIL,
  payload: data
});

export const bulkUploadDiscardedShipmentReset = (data) => ({
  type: action.BULK_UPLOAD_DISCARDED_SHIPMENT_RESET,
  payload: data
});
