import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';

import { markPackingDoneReset } from 'redux/actionCreators/packing';
import ShipmentHeader from '../QualityCheck/ShipmentHeader';
import PackingEnableScanningOff from './PackingEnableScanningOff';

const styles = makeStyles()(() => ({
  container: {
    backgroundColor: '#fff'
  },
  barcodesContainer: {
    padding: '24px 24px 15px 24px'
  }
}));

function unloadListener(e) {
  const confirmationMessage = 'Changes will be lost if you reload.';
  (e || window.event).returnValue = confirmationMessage;
  return confirmationMessage;
}

const PackingPanelProcess = (props) => {
  const navigate = useNavigate();
  const {
    inputBarcode
  } = props;
  const dispatch = useDispatch();
  const { classes } = styles();
  const { orderDetails, orderDetailStatus } = useSelector((state) => state.order);
  const [shipmentHeaderHeight, setShipmentHeaderHeight] = useState(0);

  useEffect(() => {
    /** Used to determine height of shipmentHeader to give paddingTop to content */
    if (document.getElementById('shipment_header') && !shipmentHeaderHeight) {
      setShipmentHeaderHeight(document.getElementById('shipment_header').offsetHeight);
    }
    /** End */

    /** On Reload event */
    window.addEventListener('beforeunload', unloadListener);
    return () => {
      dispatch(markPackingDoneReset());
      window.removeEventListener('beforeunload', unloadListener);
    };
  }, []);

  /** On Reload: User should be on home page (Change path to '/packing') */
  useEffect(() => {
    if (!orderDetails || !orderDetails.length) {
      navigate('/packing');
    }
  }, [orderDetailStatus, orderDetails]);
  if (!orderDetails || !orderDetails.length) {
    return null;
  }
  return (
    <div className={classes.container}>
      <Box>
        <ShipmentHeader />
      </Box>
      <div style={{ paddingTop: shipmentHeaderHeight }}>
        <PackingEnableScanningOff inputBarcode={inputBarcode} />
      </div>
    </div>
  );
};

export default PackingPanelProcess;
