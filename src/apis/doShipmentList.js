import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const closePickingAPI = (payload) =>
  tokenInstance().post(config.doShipment.closePicking, payload);

export const createSummaryDOApi = (shippingPackageID) =>
  tokenInstance().post(config.doShipment.createSummary, shippingPackageID);

export const getShipmentListApi = (payload) =>
  tokenInstance().get(config.doShipment.getShipmentList, { params: payload });

export const getShipmentDetailsListApi = (payload) =>
  tokenInstance().get(config.doShipment.getShipmentDetailsList, { params: payload });

// PRINT DO SHIPMENT INVOICE
export const getDoShipmentInvoiceApi = ({ documentType, documentReferenceId, queryParams }) => {
  const URL = `${config.doShipment.getShipmentPrintInvoice}/${documentType}/${documentReferenceId}`;
  return tokenInstance().get(URL, {
    responseType: 'arraybuffer',
    params: queryParams
  });
};

export const updateBoxCountAPI = (payload) =>
  tokenInstance().put(config.doShipment.updateBoxCount, payload);

export const printInvoiceAPI = ({ shippingPackageId }) =>
  tokenInstance().get(config.doShipment.invoiceAPI, {
    responseType: 'arraybuffer',
    params: { shippingPackageId }
  });
