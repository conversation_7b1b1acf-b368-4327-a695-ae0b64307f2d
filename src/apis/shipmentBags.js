import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getShipmentBagsApi = ({ payload }) => {
  const { filters, pageRequest } = payload;

  return tokenInstance().post(
    config.shipmentBags.getShipmentBagsList,
    { ...filters },
    {
      params: pageRequest
    }
  );
};

export const bagReleaseApi = (payload) => {
  const { manifestShippingMethod, bagId } = payload;
  if (bagId) {
    return tokenInstance().put(config.shipmentBags.bagRelease, null, { params: { bagId } });
  }
  return tokenInstance().put(config.shipmentBags.bagRelease, null, {
    params: { manifestShippingMethod }
  });
};

export const getShipmentBagDetailsApi = (manifestId) =>
  tokenInstance().get(`${config.shipmentBags.getShipmentBagDetails}/${manifestId}`);
