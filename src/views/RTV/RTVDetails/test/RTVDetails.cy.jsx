import React from 'react';
import { genericDateFormatted } from 'utils/helpers';
import { Route, Routes } from 'react-router-dom';
import { RTV_STATUS_MAPPING } from 'config/rtvStatusConfig';
import config from '../../../../config';
import rtvDetailsResponseList from './data/rtvDetailsListResponse.json';
import headers from './data/headers.json';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'cypress-real-events/support';
import RTVgatepassDetailsList from '..';

const valueOrDefault = (val) => val ?? '-';

const checkTableCell = (index, value) => {
  cy.getByCy(`table-cell-0-${index}`).should('contain.text', value);
};

const submitForApproval = (status) => {
  cy.getByCy('submit-for-approval-btn').click();
  cy.intercept('POST', `${config.RTVlist.submitForApprove}*`, {
    statusCode: status,
    body: {}
  }).as('approveReject');
  cy.get('[data-cy="primary-btn"]').click();
};

describe('RTV List', () => {
  it('load RTV list  with listing data with APPROVED status', () => {
    cy.intercept('GET', `${config.RTVlist.detailsList}**`, {
      statusCode: 200,
      body: rtvDetailsResponseList
    }).as('rtvListResponse');

    cy.mount(
      <Routes>
        <Route path="/RTVgatepass/details/:gatepassNum" element={<RTVgatepassDetailsList />} />
      </Routes>,
      {
        state: {},
        routerProps: {
          initialEntries: [`/RTVgatepass/details/${121}`]
        }
      }
    );

    headers.forEach((header, index) => {
      cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
    });

    cy.wait('@rtvListResponse').then(({ response }) => {
      // Capturing the API response body
      const { count, productDesc, pid } = response.body.data.pidCounts[0];
      const firstRowData = [pid, productDesc, count];
      headers.forEach((header, index) => {
        cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
        if (index === 4) {
          checkTableCell(index, valueOrDefault(genericDateFormatted(firstRowData[index])));
        } else if (index === 8) {
          checkTableCell(
            index,
            valueOrDefault(RTV_STATUS_MAPPING[firstRowData[index]]?.text || firstRowData[index])
          );
        } else {
          checkTableCell(index, valueOrDefault(firstRowData[index]));
        }
      });
    });

    // will be  enabled for CREATED and REJECTED
    cy.getByCy('submit-for-approval-btn').should('not.exist');

    // ENABLED only for APPROVED status
    cy.getByCy('more-icon-button').should('be.enabled');
    cy.getByCy('more-icon-button').click();
    cy.getByCy('open-print-modal').click();

    // verify handleclose is working
    cy.getByCy('close-modal').click();

    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(1000); // added this wait,  to trigger onmouse event after closing the modal
    cy.getByCy('open-print-modal').click();

    // clicking select input
    cy.getByCy('select-print-options').click();
    cy.getByCy('Debit Note').click();

    cy.intercept('GET', `${config.pdfDownload.getDownloadPdf}/**`, {
      statusCode: 200,
      body: {}
    }).as('print-debitNote');

    cy.getByCy('confirm-print-btn').click();

    // Handling error case
    cy.getByCy('more-icon-button').realMouseDown();
    cy.getByCy('open-print-modal').click();

    // clicking select input
    cy.getByCy('select-print-options').click();
    cy.getByCy('Debit Note').click();

    cy.intercept('GET', `${config.pdfDownload.getDownloadPdf}/**`, {
      statusCode: 400,
      body: {}
    }).as('print-debitNote');

    cy.getByCy('confirm-print-btn').click();
  });

  it('Submit for approval', () => {
    rtvDetailsResponseList.data.status = 'CREATED';
    cy.intercept('GET', `${config.RTVlist.detailsList}**`, {
      statusCode: 200,
      body: rtvDetailsResponseList
    }).as('rtvListResponse');
    cy.mount(
      <Routes>
        <Route path="/RTVgatepass/details/:gatepassNum" element={<RTVgatepassDetailsList />} />
      </Routes>,
      {
        state: {},
        routerProps: {
          initialEntries: [`/RTVgatepass/details/${121}`]
        }
      }
    );

    submitForApproval(200);

    submitForApproval(400);

    cy.get('[data-cy="toaster-message"]').should('be.visible');
  });

  it('Discard gatepass', () => {
    rtvDetailsResponseList.data.status = 'CREATED';
    cy.intercept('GET', `${config.RTVlist.detailsList}**`, {
      statusCode: 200,
      body: rtvDetailsResponseList
    }).as('rtvListResponse');
    cy.mount(
      <Routes>
        <Route path="/RTVgatepass/details/:gatepassNum" element={<RTVgatepassDetailsList />} />
      </Routes>,
      {
        state: {},
        routerProps: {
          initialEntries: [`/RTVgatepass/details/${121}`]
        }
      }
    );
    cy.getByCy('discard-gatepass-btn').click();
    // Enterign reason for approval

    cy.get('[name="reason-for-submit"]').type('test');
    cy.intercept('POST', `${config.RTVlist.approveReject}*`, {
      statusCode: 200,
      body: {}
    }).as('approveReject');

    // verifing closeModal
    cy.getByCy('close-modal').click();

    cy.getByCy('discard-gatepass-btn').click();
    cy.get('[name="reason-for-submit"]').type('**********{enter}');
  });

  it('Routing to scan page', () => {
    rtvDetailsResponseList.data.status = 'CREATED';
    cy.intercept('GET', `${config.RTVlist.detailsList}**`, {
      statusCode: 200,
      body: rtvDetailsResponseList
    }).as('rtvListResponse');
    cy.mount(
      <Routes>
        <Route path="/RTVgatepass/details/:gatepassNum" element={<RTVgatepassDetailsList />} />
      </Routes>,
      {
        state: {},
        routerProps: {
          initialEntries: [`/RTVgatepass/details/${121}`]
        }
      }
    );
    cy.getByCy('rtv-details-body').should('exist');
    cy.getByCy('go-to-scan-page').click();
    cy.getByCy('rtv-details-body').should('not.exist');
  });
});

describe('RTV details Body', () => {
  beforeEach(() => {
    cy.intercept('GET', `${config.RTVlist.detailsList}**`, {
      statusCode: 200,
      body: rtvDetailsResponseList
    }).as('rtvListResponse');
    cy.mount(
      <Routes>
        <Route path="/RTVgatepass/details/:gatepassNum" element={<RTVgatepassDetailsList />} />
      </Routes>,
      {
        state: {},
        routerProps: {
          initialEntries: [`/RTVgatepass/details/${121}`]
        }
      }
    );
  });

  it('Opening barcode scanned drawer', () => {
    cy.getByCy('rtv-details-body').should('exist');

    cy.getByCy('product-0').click();
    // testing closing modal
    cy.getByCy('drawer-close-icon').click();
    cy.getByCy('product-1').click();

    // verfing export barcode
    cy.getByCy('export-barcode').click();
    // closing modal
    cy.getByCy('drawer-container').click();
  });

  it('verifing arrow back', () => {
    cy.getByCy('rtv-details-header-container').should('exist');
    cy.getByCy('arrow-back').click();
    cy.getByCy('rtv-details-header-container').should('not.exist');
  });
});
