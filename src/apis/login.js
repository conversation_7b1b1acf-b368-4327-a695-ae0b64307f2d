import { tokenInstance, publicInstance } from '../utils/axiosInstance';
import config from '../config';

export const loginApi = (payload) => {
  const headers = {
    'X-Lenskart-App-Id': config.user.AppId,
    'source-domain': window.location.origin
  };
  return publicInstance(headers).post(config.user.login, payload);
};

export const getUserDetailsApi = () => {
  const headers = {
    'X-Lenskart-App-Id': config.user.AppId
  };
  return tokenInstance(headers).get(config.user.userDetails);
};

export const forgetPasswordApi = (payload) => {
  const headers = {
    'X-Lenskart-App-Id': config.user.AppId
  };
  return tokenInstance(headers).post(`${config.user.forgotPassword}/${payload}`);
};

export const logoutUserApi = () => {
  const headers = {
    'X-Lenskart-App-Id': config.user.AppId,
    'source-domain': window.location.origin
  };
  return publicInstance(headers).post(config.user.logoutUser);
};

export const changePasswordApi = (payload) => {
  const headers = {
    'X-Lenskart-App-Id': config.user.AppId
  };
  return publicInstance(headers).post(config.user.changePassword, payload);
};
