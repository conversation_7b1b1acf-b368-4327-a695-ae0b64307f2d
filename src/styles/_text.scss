@use 'sass:map';
@use './variables' as *;
/** Headings **/
h1 {
  font-size: $font-size-h1;
}
h2 {
  font-size: $font-size-h2;
}
h3 {
  font-size: $font-size-h3;
}
h4 {
  font-size: $font-size-h4;
}
h5 {
  font-size: $font-size-h5;
}
h6 {
  font-size: $font-size-h6;
}
.h7 {
  font-weight: bold;
  font-size: $font-size-h7;
}
.h8 {
  font-weight: bold;
  font-size: $font-size-h8;
}

/** Types and Sizes **/
.text-b1 {
  font-size: $font-size-b1;
}
.text-b2 {
  font-size: $font-size-b2;
}
.text-b3 {
  font-size: $font-size-b3;
}
.text-b4 {
  font-size: $font-size-b4;
}
.text-b5 {
  font-size: $font-size-b5;
}
.text-caption {
  font-size: $font-size-caption;
}
.text-20 {
  font-size: 20px;
}

/** Alignment **/
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
/** Transformation **/
.text-lowercase {
  text-transform: lowercase;
}
.text-uppercase {
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize;
}
.text-transform-none {
  text-transform: none;
}
.text-underline {
  text-decoration: underline;
}
.text-no-underline {
  text-decoration: none;
}
.text-underline-hover:hover {
  text-decoration: underline;
}
.text-strike-through {
  text-decoration: line-through;
}
.fw-bold {
  font-weight: bold;
}
.fw-normal {
  font-weight: normal;
}
.fw-500 {
  font-weight: 500;
}
/** Colors **/
.text-link {
  color: $color-primary;
}
.text-link-hover:hover {
  color: $color-primary;
}
.text-dark_pink {
  color: map.get($colors-secondary, dark_pink);
}
.text-paris_green {
  color: map.get($colors-secondary, paris_green);
}
.text-white {
  color: $color-white;
}

.text-turquioise_surf {
  color: $color-primary;
}
/** Ellipsis **/
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-vertical {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.break-word {
  word-break: break-word;
}

.ellipsis-vertical-one {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-primary {
  color: $color-primary;
}
.text-33 {
  color: map.get($colors-grey, 33);
}
.text-66 {
  color: map.get($colors-grey, 66);
}
.text-99 {
  color: map.get($colors-grey, 99);
}
.text-cc {
  color: map.get($colors-grey, cc);
}
.text-dd {
  color: map.get($colors-grey, dd);
}
.text-ee {
  color: map.get($colors-grey, ee);
}
.text-f2 {
  color: map.get($colors-grey, f2);
}
.text-fa {
  color: map.get($colors-grey, fa);
}
.text-a2 {
  color: map.get($colors-grey, a2);
}
.text-94 {
  color: map.get($colors-grey, 94);
}
.text-22 {
  color: map.get($colors-grey, 22);
}
.text-28 {
  color: map.get($colors-grey, 28);
}
.text-cb {
  color: map.get($colors-secondary, blue);
}
.text-47 {
  color: $color-warning;
}

.text-3c {
  color: $color-3c;
}
.text-red {
  color: $color-red;
}
.text-bright-yellow {
  color: $color-yellow;
}

.vertical-align-middle {
  vertical-align: middle;
}
