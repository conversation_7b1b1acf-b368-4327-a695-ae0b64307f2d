import React from 'react';
import { Box, Chip } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import { LabeledBox } from './index';

const useStyles = makeStyles()({
  chip: {
    margin: '6px 4px',
    fontWeight: '500'
  },
  success: { backgroundColor: '#45C476' },
  error: { backgroundColor: '#DF3747' },
  warning: { backgroundColor: '#FAA61A' }
});

const ChipBox = (props) => {
  const { className, value, label, ...rest } = props;
  const { classes, cx } = useStyles();

  return (
    <LabeledBox className={className} {...rest} label={label}>
      {value?.map((d) => (
        <Box key={d.title} component="span" m={0.75}>
          <Chip
            onClick={d.onClick}
            className={cx(classes.chip, classes[d.type])}
            size="small"
            label={d.title}
            color="primary"
          />
        </Box>
      ))}
    </LabeledBox>
  );
};

export default ChipBox;
