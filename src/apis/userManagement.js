import { tokenInstance } from 'utils/axiosInstance';
import localStorageHelper from 'utils/localStorageHelper';
import config from '../config';

export const getActiveRole = ({ empCode }) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).get(`${config.userManagement.getUserRoles}/${empCode}/activeRoleGroups`);

export const getApproverRole = ({ empCode, approverRoleGroupId, roleGroupName }) => {
  let url = `${config.userManagement.getUserRoles}/${empCode}/approverRoleGroups`;
  if (approverRoleGroupId) {
    url = `${url}?approverRoleGroupId=${approverRoleGroupId}`;
  }
  if (roleGroupName) {
    url = `${url}?roleGroupName=${roleGroupName}`;
  }
  return tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).get(url);
};

export const getUserList = ({ empCode, ...rest }) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).get(`${config.userManagement.getUserList}/${empCode}/getAllUsers`, { params: rest });

export const getRolesToAddUser = ({ empCode }) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).get(`${config.userManagement.user}/${empCode}/getAllChildRoles/`);

export const createUser = (payload) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).post(`${config.userManagement.createUser}`, payload);

export const createBulkUser = (payload) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).post(`${config.userManagement.createBulkUser}`, payload);

export const getEmployeeCode = ({ empData }) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).get(`${config.userManagement.getEmployeeCode}/${empData}`);

export const deleteUser = ({ empCode, roleGroupId }) =>
  tokenInstance(
    {
      'X-Lenskart-App-Id': 'nexs-search',
      'facility-code': localStorageHelper.getItem('facility-code')
    },
    true
  ).put(`${config.userManagement.user}/${empCode}/roleGroupId/${roleGroupId}/disableRoleGroup`);
