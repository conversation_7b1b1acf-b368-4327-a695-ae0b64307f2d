import React from 'react';
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
import { withStyles,makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()(() => ({
  child: {
    height: '100%',
    overflow: 'scroll'
  }
}));

export const StyledInputLabel = withStyles(InputLabel, () => ({
  root: {
    color: '#666666',
    position: 'absolute',
    top: '-9px',
    backgroundColor: 'white',
    paddingLeft: 4,
    paddingRight: 4
  }
}));

const LabeledBox = ({ className, focused, onClick, maxHeight, children, label, ...rest }) => {
  const { classes } = useStyles();
  return (
    <Box
      maxHeight={maxHeight}
      className={className}
      {...rest}
      onClick={() => onClick && onClick()}
      p={2}
      border={1}
      borderRadius="8px"
      borderColor={focused ? 'primary.main' : 'grey.200'}
      position="relative"
    >
      <StyledInputLabel focused={focused}>{label}</StyledInputLabel>

      <Box maxHeight={maxHeight ? maxHeight - 20 : null} className={classes.child}>
        {children}
      </Box>
    </Box>
  );
};

export default LabeledBox;
