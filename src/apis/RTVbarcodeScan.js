import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const rtvBarcodeScanApi = (payload) =>
  tokenInstance().post(config.RTVbarcodeScan.scan, payload);

export const rtvBarcodeDeleteApi = (payload) =>
  tokenInstance().put(config.RTVbarcodeScan.delete, payload);

export const gatepassDetailsAPI = (gatepass_num) =>
  tokenInstance().get(config.RTVlist.detailsList, { params: { gatepass_num } });
