import React, { useCallback, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkInput from 'components/MaterialUi/LkInput';
import { toastOpen } from 'redux/actionCreators/toast';
import { numberOnly, debounce } from 'utils/helpers';
import {
  createWaveLoad,
  getOrdersCountLoad,
  getOrdersCountSuccess
} from 'redux/reducers/asrsManualSync.slice';
import Spinner from 'components/Spinner/Spinner';
import { LOCALISATION } from 'redux/reducers/localisation';

const ASRSManualSyncList = ({
  pageSize,
  fetchMoreListItems,
  appliedFilterFn,
  initialSortByData,
  pageNo
}) => {
  const dispatch = useDispatch();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.ASRS_MANUAL_SYNC);

  const { data: orderCountData = [], isLoading: orderCountLoading } = useSelector(
    (state) => state.asrsManualSync.orderCount
  );
  const createWaveLoading = useSelector((state) => state.asrsManualSync.createWave);

  const debouncedHandleChange = useCallback(
    debounce((e, index, eligibleCount) => {
      const { value } = e.target;
      const updatedData = orderCountData.map((item, i) =>
        i === index && item.orderToSync !== value ? { ...item, orderToSync: value } : item
      );
      dispatch(getOrdersCountSuccess(updatedData));
      if (value > eligibleCount) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.ASRS_ORDER_COUNT_VALIDATION,
            severity: 'error'
          })
        );
      }
    }, 300),
    [orderCountData]
  );

  const handleChange = (e, index, eligibleCount) => {
    debouncedHandleChange(e, index, eligibleCount);
  };

  const handleBlur = (e) => {
    const { value } = e.target;
    if (!value || Number(value) === 0) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Please Enter Order Sync Value',
          severity: 'error'
        })
      );
    }
  };

  const tableHeight = useMemo(
    () =>
      !orderCountData?.length || orderCountData?.length === 1
        ? 150
        : orderCountData.length * 59 + 49,
    [orderCountData]
  );

  const handleSync = (data, index) => {
    const { waveName, orderToSync, adverbEligibleWave } = data;
    if (Number(orderToSync ?? 0) > adverbEligibleWave) {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: `${CONTENT.ASRS_ORDER_COUNT_VALIDATION_SYNC} 
          ${orderCountData[index]?.orderToSync}`,
          severity: 'error'
        })
      );
    }
    return dispatch(createWaveLoad({ waveSize: orderToSync, waveName }));
  };

  const renderOrderToSync = useCallback(
    ({ orderToSync, adverbEligibleWave }, index) => (
      <Box>
        <LkInput
          fullWidth
          variant="outlined"
          placeholder={orderToSync?.toString() || 'Please Enter Order Sync Value'}
          onBlur={handleBlur}
          onChange={(e) => {
            handleChange(e, index, adverbEligibleWave);
          }}
          onKeyPress={(e) => numberOnly(e)}
        />
      </Box>
    ),
    [orderCountData]
  );

  useEffect(() => {
    dispatch(getOrdersCountLoad());
  }, []);

  const isSyncDisabled = useCallback((data) => {
    const { orderToSync, adverbEligibleWave } = data;
    if (!orderToSync || Number(orderToSync) === 0) {
      return true;
    }
    return Number(orderToSync) > adverbEligibleWave;
  }, []);

  const headerConfig = [
    {
      name: CONTENT.PICKING_CATEGORY,
      key: 'waveName',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: CONTENT.PENDING_ORDER_COUNT,
      key: 'totalOrders',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: CONTENT.ASRS_ELIGIBLE_ORDERS,
      key: 'adverbEligibleWave',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130 }
    },
    {
      name: CONTENT.ENTER_ORDER_COUNT_TO_SYNC,
      key: 'orderToSync',
      style: { minWidth: 130, maxWidth: 130 },
      formatBody: renderOrderToSync
    },
    {
      name: CONTENT.ACTION,
      key: 'actions',
      style: { minWidth: 130, maxWidth: 130 },
      align: 'center',
      formatBody: useCallback(
        (data, index) => (
          <Button
            variant="contained"
            color="primary"
            disabled={isSyncDisabled(data)}
            onClick={() => handleSync(data, index)}
          >
            {createWaveLoading[data.waveName] ? <Spinner size="15px" /> : 'Sync'}
          </Button>
        ),
        [createWaveLoading, orderCountData]
      )
    }
  ];

  return (
    <Box p={3} pt={5} width="65%" margin="0 auto" data-cy="table-container">
      <LkTable
        tableData={orderCountData}
        totalRowsCount={orderCountData?.length || 0}
        tableHeight={tableHeight}
        isDataFetching={orderCountLoading}
        showTableCount={false}
        headerConfig={headerConfig}
        pageLimit={pageSize}
        initialSortBy={initialSortByData}
        dataRequestFunction={fetchMoreListItems}
        setFilters={appliedFilterFn}
        pageNumber={pageNo}
        isNonVertualizedTable
      />
    </Box>
  );
};

export default ASRSManualSyncList;
