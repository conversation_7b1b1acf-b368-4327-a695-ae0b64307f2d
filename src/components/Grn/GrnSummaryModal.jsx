import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './PIDModal.scss';
import { makeStyles } from 'tss-react/mui';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import { TableRow, Button, Box } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';

import Spinner from 'components/Spinner/Spinner';
import LkModal from 'components/Modal/Modal';

import { getCurrencySumbol, roundUptoFixedDigits } from 'utils/helpers';

import { toastOpen } from '../../redux/actionCreators/toast';
import { getGrnSummaryLoad, grnCloseLoad } from '../../redux/actionCreators/grn';
import { StyledTableCell, StyledTableRow } from '../../theme/table.theme';

const useStyles = makeStyles()({
  table: {
    minWidth: 700
  }
});

const GrnSummaryModal = (props) => {
  const navigate = useNavigate();
  const {
    grnNo,
    open,
    openModal,
    createGrnLocal
  } = props;

  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { grnSummary, grnSummaryLoading, closeGrnSuccess, closeGrnFail } = useSelector(
    (state) => state.grn
  ); // grnSummaryProccessing

  useEffect(() => {
    if (grnNo) {
      dispatch(getGrnSummaryLoad({ grnNo }));
    }
  }, [dispatch, grnNo]);

  const infoBar = (label, value, status) => (
    <div className="">
      <div className="display-flex align-items-center">
        <div className="fs12">{label}</div>
        {status && <div className="tag-status mr-l10">{status}</div>}
      </div>

      <div className="fs16 mr-t10 fw-bold">{value}</div>
    </div>
  );

  const confirmFun = () => {
    dispatch(grnCloseLoad(grnNo));
  };

  const dispatchToastFunction = (heading, subHeading, severity) =>
    dispatch(
      toastOpen({
        isToastOpen: true,
        heading,
        subHeading,
        severity
      })
    );

  useEffect(() => {
    if (closeGrnSuccess) {
      dispatchToastFunction('Success', closeGrnSuccess.display_message, 'success');
      navigate('/grn/create');
    }
  }, [closeGrnSuccess, dispatchToastFunction]);

  useEffect(() => {
    if (closeGrnFail) {
      dispatchToastFunction('Error', closeGrnFail.display_message, 'error');
    }
  }, [closeGrnFail, dispatchToastFunction]);

  const convertedCurrency = getCurrencySumbol(grnSummary?.currency);

  return (
    <LkModal
      className="grn-summary-modal"
      open={open}
      upperHeading={grnNo}
      handleClose={() => openModal(false)}
    >
      {grnSummaryLoading ? (
        <Box textAlign="center" mt={8}>
          {' '}
          <Spinner />{' '}
        </Box>
      ) : (
        <>
          <div
            className="border-grey5-radiusbase pd-20 display-flex 
            justify-content-space-between mr-t40 align-items-center"
          >
            <div className="display-flex justify-content-space-between mr-r40 flex1">
              {infoBar(createGrnLocal.BOXES, grnSummary?.boxes)}
              {infoBar(createGrnLocal.VENDOR, grnSummary?.vendor)}
              {infoBar(createGrnLocal.INVOICE, grnSummary?.invoice_id)}
              {infoBar(createGrnLocal.PO, grnSummary?.po_id)}
              {infoBar(createGrnLocal.UNICOMM_GRN, grnSummary?.unicom_grn_code)}
              {infoBar(createGrnLocal.VENDOR_INVOICE_NUMBER, grnSummary?.vendor_invoice_num)}
              {infoBar(
                createGrnLocal.TOTAL_PRICE_WITH_TAXES,
                `${convertedCurrency}${roundUptoFixedDigits(grnSummary?.total_tax_price || 0)}`
              )}
            </div>
            <div className="">
              <Button variant="contained" onClick={confirmFun} color="primary">
                {createGrnLocal.CONFIRM}
              </Button>
            </div>
          </div>
          <div className="mr-t40">
            <TableContainer style={{ height: '50vh' }} className="border-grey5-radiusbase">
              <Table stickyHeader className={classes.table} aria-label="sticky table">
                <TableHead>
                  <TableRow>
                    <StyledTableCell align="left" style={{ width: '150px' }}>
                      {createGrnLocal.PRODUCT_ID}
                    </StyledTableCell>
                    <StyledTableCell align="left" style={{ width: '180px' }}>
                      {createGrnLocal.PRODUCT_NAME}
                    </StyledTableCell>
                    <StyledTableCell align="right" style={{ width: '120px' }}>
                      {createGrnLocal.UNIT_PRICE}
                    </StyledTableCell>
                    <StyledTableCell align="right" style={{ width: '150px' }}>
                      {createGrnLocal.SCANNED_BARCODE}
                    </StyledTableCell>
                    <StyledTableCell align="right" style={{ width: '130px' }}>
                      {createGrnLocal.QC_FAIL}
                    </StyledTableCell>
                    <StyledTableCell align="right" style={{ width: '130px' }}>
                      {createGrnLocal.TOTAL_PRICE}
                    </StyledTableCell>
                    <StyledTableCell align="right" style={{ width: '170px' }}>
                      {createGrnLocal.TOTAL_PRICE_WITH_TAXES}
                    </StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {grnSummary?.pids?.map((product) => {
                    const { description, pid, price, total_failed, total_passed, priceWithTax } =
                      product;
                    const totalScanned = total_failed + total_passed;
                    const totalPrice = totalScanned * price;
                    const totalPriceWithTax = totalScanned * priceWithTax;

                    return (
                      <StyledTableRow key={pid}>
                        <StyledTableCell align="left">{pid}</StyledTableCell>
                        <StyledTableCell align="left">{description}</StyledTableCell>
                        <StyledTableCell align="right">
                          {convertedCurrency}
                          {roundUptoFixedDigits(price)}
                        </StyledTableCell>
                        <StyledTableCell align="right">{totalScanned}</StyledTableCell>
                        <StyledTableCell align="right">{total_failed}</StyledTableCell>
                        <StyledTableCell align="right">
                          {convertedCurrency}
                          {roundUptoFixedDigits(totalPrice)}
                        </StyledTableCell>
                        <StyledTableCell align="right">
                          {convertedCurrency}
                          {roundUptoFixedDigits(totalPriceWithTax)}
                        </StyledTableCell>
                      </StyledTableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>

            <Box
              display="flex"
              justifyContent="space-between"
              className=" fs16 fw-bold border-grey5-radiusbase"
            >
              <Box m={2} width="45%" textAlign="center">
                {createGrnLocal.TOTAL}
              </Box>

              <Box pr={2} pt={2} width="150px" textAlign="right" className="border-left-dd">
                {' '}
                {grnSummary?.total_scanned}{' '}
              </Box>
              <Box pr={1} pt={2} width="130px" textAlign="right" className="border-left-dd">
                {' '}
                {grnSummary?.total_failed}{' '}
              </Box>

              <Box pt={2} width="150px" textAlign="right" className="border-left-dd">
                {' '}
                {convertedCurrency}
                {grnSummary &&
                  roundUptoFixedDigits(
                    grnSummary.total_received_amount + grnSummary.total_rejected_amount
                  )}{' '}
              </Box>
              <Box pt={2} pr={1} width="190px" textAlign="right" className="border-left-dd">
                {' '}
                {convertedCurrency}
                {roundUptoFixedDigits(grnSummary?.total_tax_price || 0)}{' '}
              </Box>
            </Box>
          </div>
        </>
      )}
    </LkModal>
  );
};
export default GrnSummaryModal;
