import * as action from '../actionTypes/facility';

// save facility (create / draft)
export function saveFacilityLoad(data) {
  return { type: action.FACILITY_SAVE_LOAD, payload: data };
}

export function saveFacilitySuccess(data) {
  return { type: action.FACILITY_SAVE_SUCCESS, payload: data };
}

export function saveFacilityFail(data) {
  return { type: action.FACILITY_SAVE_FAIL, payload: data };
}

export function saveFacilityReset(data) {
  return { type: action.FACILITY_SAVE_RESET, payload: data };
}

// get facility list
export function getFacilityListLoad(data) {
  return { type: action.FACILITY_LIST_LOAD, payload: data };
}

export function getFacilityListSuccess(data) {
  return { type: action.FACILITY_LIST_SUCCESS, payload: data };
}

export function getFacilityListFail(data) {
  return { type: action.FACILITY_LIST_FAIL, payload: data };
}

export function getFacilityListReset(data) {
  return { type: action.FACILITY_LIST_RESET, payload: data };
}

// get tax details
export function getTaxDeatilsLoad(data) {
  return { type: action.GET_TAX_DETAILS_LOAD, payload: data };
}

export function getTaxDeatilsSuccess(data) {
  return { type: action.GET_TAX_DETAILS_SUCCESS, payload: data };
}

export function getTaxDeatilsFail(data) {
  return { type: action.GET_TAX_DETAILS_FAIL, payload: data };
}

export function getTaxDeatilsReset(data) {
  return { type: action.GET_TAX_DETAILS_RESET, payload: data };
}

// get pincode details
export function getPincodeDeatilsLoad(data) {
  return { type: action.GET_PINCODE_DETAILS_LOAD, payload: data };
}

export function getPincodeDeatilsSuccess(data) {
  return { type: action.GET_PINCODE_DETAILS_SUCCESS, payload: data };
}

export function getPincodeDeatilsFail(data) {
  return { type: action.GET_PINCODE_DETAILS_FAIL, payload: data };
}

export function getPincodeDeatilsReset(data) {
  return { type: action.GET_PINCODE_DETAILS_RESET, payload: data };
}

// UPLOAD LOGO
export function getUploadLogoLoad(data) {
  return { type: action.UPLOAD_LOGO_LOAD, payload: data };
}

export function getUploadLogoSuccess(data) {
  return { type: action.UPLOAD_LOGO_SUCCESS, payload: data };
}

export function getUploadLogoFail(data) {
  return { type: action.UPLOAD_LOGO_FAIL, payload: data };
}

export function getUploadLogoReset(data) {
  return { type: action.UPLOAD_LOGO_RESET, payload: data };
}

// FACILITY TYPE

export function getFacilityTypeLoad() {
  return { type: action.FACILITY_TYPE_LOAD };
}

export function getFacilityTypeSuccess(data) {
  return { type: action.FACILITY_TYPE_SUCCESS, payload: data };
}

export function getFacilityTypeFail() {
  return { type: action.FACILITY_TYPE_FAIL };
}

// FACILITY DETAILS

export function getFacilityDetailsLoad(data) {
  return { type: action.FACILITY_DETAILS_LOAD, payload: data };
}

export function getFacilityDetailsSuccess(data) {
  return { type: action.FACILITY_DETAILS_SUCCESS, payload: data };
}

export function getFacilityDetailsFail(data) {
  return { type: action.FACILITY_DETAILS_FAIL, payload: data };
}

export function getFacilityDetailsReset(data) {
  return { type: action.FACILITY_DETAILS_RESET, payload: data };
}

// LAYOUT SCHEMA LIST AUTOSUGGESTION

export function getLayoutSchemaListLoad(data) {
  return { type: action.LAYOUT_SCHEMAS_LOAD, payload: data };
}

export function getLayoutSchemaListSuccess(data) {
  return { type: action.LAYOUT_SCHEMAS_SUCCESS, payload: data };
}

export function getLayoutSchemaListFail(data) {
  return { type: action.LAYOUT_SCHEMAS_FAIL, payload: data };
}

export function getLayoutSchemaListReset(data) {
  return { type: action.LAYOUT_SCHEMAS_RESET, payload: data };
}

// ENTITY LIST

export function getEntityListLoad(data) {
  return { type: action.GET_ENTITY_LIST_LOAD, payload: data };
}

export function getEntityListSuccess(data) {
  return { type: action.GET_ENTITY_LIST_SUCCESS, payload: data };
}

export function getEntityListFail(data) {
  return { type: action.GET_ENTITY_LIST_FAIL, payload: data };
}

export function getEntityListReset(data) {
  return { type: action.GET_ENTITY_LIST_RESET, payload: data };
}

// ENTITY details LIST

export function getEntityDetailsListLoad(data) {
  return { type: action.GET_ENTITY_DETAILS_LIST_LOAD, payload: data };
}

export function getEntityDetailsListSuccess(data) {
  return { type: action.GET_ENTITY_DETAILS_LIST_SUCCESS, payload: data };
}

export function getEntityDetailsListFail(data) {
  return { type: action.GET_ENTITY_DETAILS_LIST_FAIL, payload: data };
}

export function getEntityDetailsListReset(data) {
  return { type: action.GET_ENTITY_DETAILS_LIST_RESET, payload: data };
}
// get countryStateList

export function getStateCountryListLoad(data) {
  return { type: action.GET_COUNTRY_STATE_LIST_LOAD, payload: data };
}

export function getStateCountryListSuccess(data) {
  return { type: action.GET_COUNTRY_STATE_LIST_SUCCESS, payload: data };
}

export function getStateCountryListFail(data) {
  return { type: action.GET_COUNTRY_STATE_LIST_FAIL, payload: data };
}

export function getStateCountryListReset(data) {
  return { type: action.GET_COUNTRY_STATE_LIST_RESET, payload: data };
}

// download facility entity

export function getdownloadFacilityEntityLoad(data) {
  return { type: action.GET_DOWNLOAD_ENTITY_FILE_LOAD, payload: data };
}

export function getdownloadFacilityEntitySuccess(data) {
  return { type: action.GET_DOWNLOAD_ENTITY_FILE_SUCCESS, payload: data };
}

export function getdownloadFacilityEntityFail(data) {
  return { type: action.GET_DOWNLOAD_ENTITY_FILE_FAIL, payload: data };
}

export function getdownloadFacilityEntityReset(data) {
  return { type: action.GET_DOWNLOAD_ENTITY_FILE_RESET, payload: data };
}

// upload facility layout csv

export function uploadFacilityEntityLoad(data) {
  return { type: action.UPLOAD_FACILITY_ENTITY_LOAD, payload: data };
}

export function uploadFacilityEntitySuccess(data) {
  return { type: action.UPLOAD_FACILITY_ENTITY_SUCCESS, payload: data };
}

export function uploadFacilityEntityFail(data) {
  return { type: action.UPLOAD_FACILITY_ENTITY_FAIL, payload: data };
}

export function uploadFacilityEntityReset(data) {
  return { type: action.UPLOAD_FACILITY_ENTITY_RESET, payload: data };
}

// Export entity as csv

export function exportEntityAsCsvLoad(data) {
  return { type: action.EXPORT_ENTITY_AS_CSV_LOAD, payload: data };
}

export function exportEntityAsCsvSuccess(data) {
  return { type: action.EXPORT_ENTITY_AS_CSV_SUCCESS, payload: data };
}

export function exportEntityAsCsvFail(data) {
  return { type: action.EXPORT_ENTITY_AS_CSV_FAIL, payload: data };
}

export function exportEntityAsCsvReset(data) {
  return { type: action.EXPORT_ENTITY_AS_CSV_RESET, payload: data };
}

// bulk entity as csv

export function bulkEntityAsCsvLoad(data) {
  return { type: action.BULK_ENTITY_AS_CSV_LOAD, payload: data };
}

export function bulkEntityAsCsvSuccess(data) {
  return { type: action.BULK_ENTITY_AS_CSV_SUCCESS, payload: data };
}

export function bulkEntityAsCsvFail(data) {
  return { type: action.BULK_ENTITY_AS_CSV_FAIL, payload: data };
}

export function bulkEntityAsCsvReset(data) {
  return { type: action.BULK_ENTITY_AS_CSV_RESET, payload: data };
}

// edit entity

export function editEntityLoad(data) {
  return { type: action.EDIT_ENTITY_LOAD, payload: data };
}

export function editEntitySuccess(data) {
  return { type: action.EDIT_ENTITY_SUCCESS, payload: data };
}

export function editEntityFail(data) {
  return { type: action.EDIT_ENTITY_FAIL, payload: data };
}

export function editEntityReset(data) {
  return { type: action.EDIT_ENTITY_RESET, payload: data };
}

// delete entity

export function deleteEntityLoad(data) {
  return { type: action.DELETE_ENTITY_LOAD, payload: data };
}

export function deleteEntitySuccess(data) {
  return { type: action.DELETE_ENTITY_SUCCESS, payload: data };
}

export function deleteEntityFail(data) {
  return { type: action.DELETE_ENTITY_FAIL, payload: data };
}

export function deleteEntityReset(data) {
  return { type: action.DELETE_ENTITY_RESET, payload: data };
}

// Legal owner list

export function LegalOwnerListLoad(data) {
  return { type: action.LEGAL_OWNER_LIST_LOAD, payload: data };
}

export function legalOwnerListSuccess(data) {
  return { type: action.LEGAL_OWNER_LIST_SUCCESS, payload: data };
}

export function legalOwnerListFail() {
  return { type: action.LEGAL_OWNER_LIST_FAIL };
}
