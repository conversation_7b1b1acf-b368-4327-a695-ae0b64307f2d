{"container": {"padding": "10px", "display": "flex", "flex-direction": "column", "height": "100%"}, "powerInfoBorder": {"border": "1px solid gray", "borderRadius": "5px"}, "borderTopSpace": {"borderTop": "1px solid gray", "justifyContent": "space-between", "display": "flex"}, "widthPower": {"maxWidth": "70px", "padding": "5px"}, "seperator": {"width": "1px", "backgroundColor": "gray"}, "header": {"lineHeight": "14px", "textTransform": "uppercase", "fontSize": "12px", "fontWeight": "700", "textAlign": "center"}, "description": {"lineHeight": "20px", "fontSize": "14px", "fontWeight": "400", "textAlign": "center", "marginTop": "16px"}, "lensInfo": {"display": "flex", "justifyContent": "space-between", "borderBottom": "1px solid #ddd", "padding": "12px 16px 12px 16px", "fontSize": "12px", "fontWeight": "400", "lineHeight": "14px"}, "marginTop5": {"marginTop": "5px"}, "powerFontSize": {"fontSize": "135%"}, "powerFontSizeSmall": {"fontSize": "115%"}, "flagContainer": {"display": "flex", "justifyContent": "space-between", "color": "#DD0000", "marginBottom": "25px"}, "flagWrapper": {"display": "flex"}, "flagIcon": {"width": "20px", "height": "20px"}, "flagStatus": {"fontSize": "14px", "lineHeight": "20px", "fontWeight": "500"}, "qcLensoMeterHeading": {"display": "flex", "justifyContent": "center", "fontSize": "14px"}, "qcLensoMeterPower": {"color": "#00008B"}}