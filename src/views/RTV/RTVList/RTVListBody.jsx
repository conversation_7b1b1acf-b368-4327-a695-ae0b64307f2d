import Box from '@mui/material/Box';
import React, { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';
import { genericDateFormatted } from 'utils/helpers';
import SystemUpdateAltIcon from '@mui/icons-material/SystemUpdateAlt';
import { Button, IconButton } from '@mui/material';
import { RTV_LIST } from 'redux/reducers/RTVlist.slice';
import usePermissions from 'common/usePermissions';
import { LOGIN } from 'redux/reducers/login';
import 'views/PurchaseOrder/purchaseOrder.scss';
import { RTV_STATUS_MAPPING } from 'config/rtvStatusConfig';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import RTVsubmitModal from '../RTVDetails/RTVsubmitModal';

const initialSortingData = {
  id: 'created_at',
  order: 'DESC'
};

const defaultModalData = {
  open: false,
  status: '',
  gatepass_num: ''
};

const RTVListBody = ({
  fetchMoreData,
  selectedFilterList,
  onChangeFilterList,
  CONTENT,
  approvalRejectFun,
  deleteSelectedFilter,
  resetFilters,
  exportFun
}) => {
  const [tableHeight] = useWindowResize(window.innerHeight - 280);
  const [reason, setReason] = useState('');
  const [openReasonModal, setopenReasonModal] = useState({ ...defaultModalData });

  const { data, isLoading, totalCount } = useSelector((state) => state[RTV_LIST].list);
  const isExportLoading = useSelector((state) => state[RTV_LIST].exportLoading);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const { isPOSuperVisor } = usePermissions(userDetail?.permission);

  const submitForApproval = () => {
    approvalRejectFun(openReasonModal.status, openReasonModal.gatepass_num, reason);
    setopenReasonModal({ ...defaultModalData });
    setReason('');
  };

  const headerConfig = [
    {
      name: CONTENT.GATEPASS,
      key: 'GATEPASS',
      style: { minWidth: 130, maxWidth: 130 },
      formatBody: useCallback(
        ({ gatepass_num }) => (
          <Link to={`/RTVgatepass/details/${gatepass_num}`}>{gatepass_num}</Link>
        ),
        []
      ),
      filterData: {
        type: 'input',
        columnName: 'gatepass_num',
        selectedFilterList
      }
    },
    {
      name: CONTENT.VENDOR_CODE,
      key: 'vendor_code',
      filterData: {
        type: 'input',
        columnName: 'vendor_code',
        selectedFilterList
      }
    },
    {
      name: CONTENT.FACILITY,
      key: 'facility_code',
      style: { minWidth: 100, maxWidth: 100 },
      filterData: {
        type: 'input',
        columnName: 'facility_code',
        selectedFilterList
      }
    },
    {
      name: CONTENT.GATEPASS_QTY,
      key: 'total_quantity',
      style: { minWidth: 100, maxWidth: 100 }
    },

    {
      name: CONTENT.CREATED_DATE,
      key: 'created_at',
      supportSort: true,
      style: { minWidth: 140, maxWidth: 140 },
      formatBody: useCallback(
        ({ created_at }) => (
          <Box className="fw-bold">{genericDateFormatted(created_at, 'utcTOlocalDate')}</Box>
        ),
        []
      )
    },
    {
      name: CONTENT.CREATED_BY,
      key: 'created_by',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.APPROVAL_DATE,
      key: 'approved_at',
      supportSort: true,
      style: { minWidth: 100, maxWidth: 100 },
      formatBody: useCallback(
        ({ approved_at }) => (
          <Box className="fw-bold">{genericDateFormatted(approved_at, 'utcTOlocalDate')}</Box>
        ),
        []
      )
    },
    {
      name: CONTENT.APPROVED_BY,
      key: 'approved_by',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.RTV_STATUS,
      key: 'reason_for_rtv',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      align: 'left',
      marginLeft: '-40px',
      style: { minWidth: '160px', maxWidth: '160px' },
      formatBody: useCallback(
        ({ status, gatepass_num }, index) => (
          <div className="po-list-row" data-cy={`status-container-${index}`}>
            <div
              className={`chip-status ${RTV_STATUS_MAPPING[status]?.type} ${
                status === 'PENDING_APPROVAL' && isPOSuperVisor && 'hideStatusChip'
              }`}
            >
              {RTV_STATUS_MAPPING[status]?.text || status}
              {RTV_STATUS_MAPPING[status]?.img && (
                <img
                  className="mr-l10"
                  src={`${import.meta.env.VITE_STATIC_SERVER}${RTV_STATUS_MAPPING[status]?.img}`}
                  alt="info"
                />
              )}
            </div>
            {status === 'PENDING_APPROVAL' && isPOSuperVisor && (
              <div className="po-supervisor-actions">
                <Box
                  component="img"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/CheckFilled.svg`}
                  data-cy={`approve-btn-${index}`}
                  onClick={() =>
                    setopenReasonModal({ status: 'APPROVED', gatepass_num, open: true })
                  }
                />
                <Box
                  component="img"
                  className="mr-l16"
                  data-cy={`reject-btn-${index}`}
                  onClick={() =>
                    setopenReasonModal({ status: 'REJECTED', gatepass_num, open: true })
                  }
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/CrossFilledLarge.svg`}
                />
              </div>
            )}
          </div>
        ),
        []
      )
    }
  ];

  const primaryBtnTxtValue = {
    APPROVED: CONTENT.YES_APPROVED,
    REJECTED: CONTENT.YES_REJECTED
  };

  const titleMapping = {
    APPROVED: CONTENT.APPROVE_GATEPASS,
    REJECTED: CONTENT.REJECT_GATEPASS
  };

  return (
    <Box className="border-radius-8 border-dd pd-t4 po-dashboard">
      <Box className="display-flex justify-content-space-between align-items-center pd-8">
        <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />
        <Box>
          <Button
            variant="outlined"
            color="primary"
            size="medium"
            className="mr-r10 border-radius-8"
            disabled={!selectedFilterList.length}
            onClick={() => resetFilters()}
            data-cy="reset-btn"
          >
            {CONTENT.RESET}
          </Button>
          <IconButton
            size="large"
            color="primary"
            onClick={exportFun}
            data-cy="export-rtv-list"
            disabled={isExportLoading || !data.length}
          >
            <SystemUpdateAltIcon />
          </IconButton>
        </Box>
      </Box>
      <LkTable
        rowSize={60}
        tableData={data}
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        totalRowsCount={totalCount}
        dataRequestFunction={fetchMoreData}
        headerConfig={headerConfig}
        initialSortBy={initialSortingData}
        setFilters={onChangeFilterList}
      />
      <RTVsubmitModal
        openReasonModal={openReasonModal.open}
        setopenReasonModal={() => setopenReasonModal({ open: false, reason: '' })}
        submitForApproval={submitForApproval}
        hideReasonInput={openReasonModal.status === 'APPROVED'}
        CONTENT={CONTENT}
        reason={reason}
        setReason={setReason}
        title={titleMapping[openReasonModal.status]}
        primaryBtnTxt={primaryBtnTxtValue[openReasonModal.status]}
      />
    </Box>
  );
};

export default RTVListBody;
