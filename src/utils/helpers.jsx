/* eslint-disable no-restricted-syntax */
/* eslint-disable guard-for-in */
/* eslint-disable consistent-return */
/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
/* eslint-disable func-names */
import React from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { daysOfTheWeek } from 'config/daysOfTheWeek';
import errorConstant from '../constant/error';
// eslint-disable-next-line import/no-cycle
import { dynamicRegexValidation, isEmail } from './validation';
import currencySymbols from './currency';
import localStorageHelper from './localStorageHelper';

dayjs.extend(utc);
dayjs.extend(localizedFormat);
dayjs.extend(advancedFormat);

export const sleep = (ms = 0) =>
  new Promise((resolve) => {
    setTimeout(resolve, ms);
  });

/** Method to Truncate string by given length */
export const truncateString = (text, length) => {
  if (typeof text !== 'string') {
    text = String(text);
  }
  return text?.substring(0, length) ? `${text.substring(0, length)}...` : '';
};

export const getNumericChars = (str) => {
  if (str) {
    return str.replace(/\D/g, '');
  }
  return str;
};

export const capitalise = (text) => {
  if (typeof text !== 'string') {
    return text;
  }
  const result = text.toLowerCase();
  return result.charAt(0).toUpperCase() + result.slice(1);
};

export const arrayBufferToJson = (res) => {
  const arrayBuffer = res;
  const uint8Array = new Uint8Array(arrayBuffer);
  const jsonString = new TextDecoder().decode(uint8Array);
  return JSON.parse(jsonString);
};

export const composeValidators =
  (...validators) =>
    (value) =>
      validators.reduce((error, validator) => error || validator(value), undefined);

export const convertToQueryString = (params, allowNull) =>
  Object.keys(params).reduce((m, k) => {
    if (params[k] != null || allowNull) {
      m += `${k}=${params[k]}&`;
    }
    return m;
  }, '');

export const validateDecimalPlaces = (value = '', noOfDecimalAllowed = 0) => {
  const regex = new RegExp(`^(\\d+(\\.\\d{0,${noOfDecimalAllowed}})?)?$`);
  return regex.test(value);
};

export const isDigitContains = (value = '') => {
  const regex = /^\d*$/;
  return regex.test(value);
};

export const isDigitDecimal = (value = '') => {
  const regex = /^\d*\.?\d*$/;
  return regex.test(value);
};

export function formatDate(time) {
  let formattedString = '';
  const date = new Date(time).getDate();
  const year = new Date(time).getFullYear();
  const month = new Date(time).getMonth();
  formattedString = `${date}/${month}/${year}`;
  return formattedString;
}

export function formatDateDashedSeparated(time) {
  let formattedString = '';
  let date = new Date(time).getDate();
  if (date < 10) {
    date = `0${date}`;
  }
  const year = new Date(time).getFullYear();
  let month = new Date(time).getMonth() + 1;
  if (month < 10) {
    month = `0${month}`;
  }
  formattedString = `${year}-${month}-${date}`;
  return formattedString;
}

export function convertDateFormat(date, formatName) {
  try {
    /*
    output format:Feb 3, 2020 2:07 PM
    */
    if (formatName === 'shortDateTime') {
      return dayjs(date).format('lll');
    }
    /*
    output format:3rd Feb 2020
    */
    if (formatName === 'abbDate') {
      return dayjs(date).format('Do MMM YYYY');
    }
    /*
    output format:Feb 20, 2020
    */
    if (formatName === 'shortDate') {
      return dayjs(date).format('ll');
    }
    /*
    output format:20/02/2020
    */
    if (formatName === 'date') {
      return dayjs(date).format('DD/MM/YYYY');
    }
    if (formatName === 'yyyy-mm-dd') {
      return dayjs(date).format('YYYY-MM-DD');
    }

    if (formatName === 'yyyy-mm-dd hh:mm:ss') {
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    }
    if (formatName === 'utcDateTime') {
      return dayjs.utc(date).format('YYYY-MM-DD HH:mm:ss');
    }
    /*
    output format:20/02/2020
    */
    if (formatName === 'time') {
      return dayjs(date).format('LT');
    }
    if (formatName === 'export') {
      return `${dayjs(date).format('YYYYMMDD')}_${dayjs(date).format('hhmmss')}`;
    }
    if (formatName === 'utcTOlocalDate') {
      return dayjs.utc(date).local().format('DD/MM/YYYY');
    }
    if (formatName === 'utcTOlocalTime') {
      return dayjs.utc(date).local().format('HH:mm');
    }

    /*
    output format:03 Aug 23 4:51 PM
    */
    if (formatName === 'DD MMM YY h:mm A') {
      return dayjs(date).format('DD MMM YY h:mm A');
    }
    /*
    output format:Feb 20, 2020
    */
    if (formatName === 'shortDateutcTOLocal') {
      return dayjs.utc(date).local().format('ll');
    }

    // Sunday , Monday ..
    if (formatName === 'dayOfTheWeek') {
      return daysOfTheWeek[dayjs(date).day()];
    }
    return date;
  } catch (e) {
    return date;
  }
}

export function getInitials(name = '') {
  return name
    .replace(/\s+/, ' ')
    .split(' ')
    .slice(0, 2)
    .map((v) => v?.[0]?.toUpperCase())
    .join('');
}

export const genericDateFormatted = (date) => {
  if (date) {
    const formattedDate = convertDateFormat(date, 'utcTOlocalDate');
    const formattedTime = convertDateFormat(date, 'utcTOlocalTime');
    return `${formattedDate} ${formattedTime}`;
  }
  return '-';
};

export const genericDateWithoutTimeFormat = (date) => {
  if (date) {
    const formattedDate = convertDateFormat(date, 'utcTOlocalDate');
    return formattedDate;
  }
  return '-';
};

export function parseQuery(queryString) {
  const query = {};
  if (!queryString) {
    return query;
  }
  const pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
  pairs.forEach((p) => {
    const pair = p.split('=');
    query[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
  });
  return query;
}

export function getApplicableTaxes(taxType) {
  switch (taxType) {
    case 'INTL':
      return ['igst'];
    case 'INTERSTATE':
      return ['igst'];
    case 'INTRASTATE':
      return ['cgst', 'sgst'];
    case 'UT':
      return ['cgst', 'ugst'];
    default:
      return [];
  }
}

export function getTaxType(taxType = []) {
  return taxType.map((tax) => {
    switch (tax) {
      case 'FT':
        return 'flat';
      case 'IGST':
        return 'igst';
      case 'SGST':
        return 'sgst';
      case 'UGST':
        return 'ugst';
      case 'CGST':
        return 'cgst';
      default:
        return '';
    }
  });
}
export function extractApiErrorMessage(error) {
  return error?.response?.data?.meta
    ? error.response.data.meta.displayMessage
    : errorConstant.SERVER_NOT_REACHABLE;
}

export function debounce(func, wait) {
  let timeout;
  return function (...args) {
    const context = this;
    const later = function () {
      timeout = null;
      func.apply(context, args);
    };
    window.clearTimeout(timeout);
    timeout = window.setTimeout(later, wait);
  };
}

export const getQueryParam = (query = window.location.search.replace('?', ''), param) => {
  const result = {};
  // eslint-disable-next-line no-shadow
  query.split('&').forEach((param) => {
    const item = param.split(/=(.+)/);
    result[item[0]] = decodeURIComponent(item[1]);
  });
  return param ? result[param] : result;
};

const isEmpty = (value) => value === undefined || value === null || value === '';

export const validateEmail = (value) => {
  let error = null;
  if (
    (!isEmpty(value) && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value)) ||
    isEmpty(value)
  ) {
    error = 'Please enter a valid Email';
  }
  return error;
};

export const redirectToUrl = (url) => url.search.replace('?', '');

export const getAllQueryParam = (queryString) => {
  const params = new URLSearchParams(queryString);
  const data = [];
  for (const param of params) {
    data.push({ key: param[0], value: param[1] });
  }
  return data;
};

export const getTwoYearDateFromNow = () => {
  const date = new Date();
  let getMonth = date.getMonth();
  let getDate = date.getDate();
  if (getMonth < 10) {
    getMonth = `${0}${getMonth}`;
  }
  if (getDate < 10) {
    getDate = `${0}${getDate}`;
  }
  const twoYearFromNow = `${date.getFullYear() + 2}-${getMonth}-${getDate}`;
  return twoYearFromNow;
};

export const roundUptoDigits = (number, digits = 2) => {
  if (number) {
    const factor = 10 ** digits;
    return Math.round(Number(number) * factor) / factor;
  }
  return number;
};

export const roundUptoFixedDigits = (number, digits = 2) => {
  if ((number || number === 0) && typeof number === 'number') {
    return number.toFixed(digits);
  }
  return number;
};

export const listToCSVStringWithCommas = (data, labels, keys) => {
  const escapeCommas = (value) => {
    if (typeof value === 'string' && value.includes(',')) {
      return `"${value.replace(/"/g, '""')}"`; // Escape double quotes within strings
    }
    return value;
  };

  return [labels, ...data.map((item) => keys.map((eachKey) => escapeCommas(item[eachKey])))]
    .map((e) => e.join(','))
    .join('\n');
};

export const listToCSVString = (data, labels, keys) =>
  [labels, ...data.map((item) => keys.map((eachKey) => item[eachKey]))]
    .map((e) => e.join(','))
    .join('\n');

export const fileDownload = (blobData, name, format = 'csv') => {
  const blob = new Blob([blobData]);
  const fileUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = fileUrl;
  link.setAttribute('download', `${name}.${format}`);
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(fileUrl);
};

export const regextest = (barcode, pattern) => {
  const { start, end } = pattern;

  if (!/^[a-zA-Z0-9]+$/.test(barcode)) {
    return false;
  }

  const split_string = start.split(/(\d+)(?!.*\d)/); // splits only on last set of digits
  const endSplitString = end.split(/(\d+)(?!.*\d)/);

  const barcodeSplitString = barcode.split(/(\d+)(?!.*\d)/);
  const stringLength = split_string[0].length;
  const numberLength = split_string[1].length;
  const stringToGoIntoTheRegex = `\\b\\d{${numberLength}}\\b`;
  const regex = new RegExp(stringToGoIntoTheRegex, 'g');
  const regTest = regex.test(barcode.slice(stringLength));
  const result = barcode.startsWith(split_string[0]);
  const minMaxCheck =
    barcodeSplitString[1] >= split_string[1] && barcodeSplitString[1] <= endSplitString[1];
  return regTest && result && minMaxCheck;
};

export const getDatesFromFiltersString = (string) => {
  const result = {};
  if (string === 'TODAY') {
    result.start = `${convertDateFormat(new Date(), 'yyyy-mm-dd')} 00:00:00`;
    result.end = `${convertDateFormat(new Date(), 'yyyy-mm-dd')} 23:59:59`;
  }
  const splitString = string.split(' ');
  if (splitString.length === 3) {
    result.start = `${dayjs()
      .subtract(splitString[1] - 1, 'd')
      .format('YYYY-MM-DD')} 00:00:00`;
    result.end = `${convertDateFormat(new Date(), 'yyyy-mm-dd')} 23:59:59`;
  }
  return result;
};

export const getDatesDifference = (date_1, date_2) => {
  const difference = date_1.getTime() - date_2.getTime();
  const TotalDays = Math.ceil(difference / (1000 * 3600 * 24));
  return TotalDays;
};

const isToday = (date) => {
  const currentDate = new Date();
  return date.toDateString() === currentDate.toDateString();
};

export const convertDatesToString = (date_1, date_2, format = 'shortDate') => {
  if (!date_1 && !date_2) {
    return 'ALL PERIOD';
  }

  const days = Math.abs(getDatesDifference(date_1, date_2));
  let value = '';
  const isDateIsToday = isToday(date_2);

  if (days === 0 && isDateIsToday) {
    value = 'TODAY';
  } else if (days === 6 && isDateIsToday) {
    value = 'LAST 7 DAYS';
  } else if (days === 29 && isDateIsToday) {
    value = 'LAST 30 DAYS';
  } else {
    value = `${convertDateFormat(date_1, format)} - ${convertDateFormat(date_2, format)}`;
  }

  return value;
};
export const generatePayloadForSearchAPI = (
  result,
  payload = {},
  mapSearchFilterKey = {},
  matchIngKey = ''
) => {
  result.forEach(({ key, value }) => {
    // this code is for handling GRN tables
    if ((matchIngKey && (key.startsWith(matchIngKey) || key.startsWith('poNum'))) || !matchIngKey) {
      const k = mapSearchFilterKey[key] || key;
      if (
        key === 'UPDATED_CMS' ||
        key === 'CREATED_CMS' ||
        key === 'APPROVED' ||
        key === 'Date_Range' ||
        key === 'CREATED' ||
        key === 'Invoice Date' ||
        key === 'CLOSED' ||
        key === 'COMPLETED' ||
        key === 'Last_Updated' ||
        key === 'GRN_CREATED' ||
        key === 'Invoice_COMPLETED' ||
        key === 'Invoice_Invoice Date' ||
        key === 'Invoice_CREATED' ||
        key === 'DebitNote_INVOICE_DATE' ||
        key === 'DebitNote_CREATED' ||
        key === 'UPDATED' ||
        key === 'Order Date'
      ) {
        if (value.includes(' - ')) {
          const temp = value.split(' - ');
          if (temp[0].includes(':')) {
            // if time is already present
            payload[`${k}from`] = convertDateFormat(temp[0], 'yyyy-mm-dd hh:mm:ss');
            payload[`${k}to`] = convertDateFormat(temp[1], 'yyyy-mm-dd hh:mm:ss');
          } else {
            payload[`${k}from`] = `${convertDateFormat(temp[0], 'yyyy-mm-dd')} 00:00:00`;
            payload[`${k}to`] = `${convertDateFormat(temp[1], 'yyyy-mm-dd')} 23:59:59`;
          }
        } else if (value !== 'ALL PERIOD') {
          // eslint-disable-next-line no-shadow
          const result = getDatesFromFiltersString(value);
          payload[`${k}from`] = result.start;
          payload[`${k}to`] = result.end;
        }
      } else if (!payload[k]) {
        payload[k] = value;
      } else {
        const t = payload[k];
        if (typeof t === 'string') {
          payload[k] = [t, value];
        } else {
          payload[k] = [...t, value];
        }
      }
    }
  });

  return payload;
};

export const taxregexValidation = (taxDetailsSuccess, taxData) => {
  let result = true;
  const taxDetailsSuccessValues = Object.values(taxDetailsSuccess);
  const invaliddataTemp = {};
  Object.keys(taxData).forEach((t, index) => {
    if (taxData[t] && !dynamicRegexValidation(taxDetailsSuccessValues[index], taxData[t])) {
      invaliddataTemp[t] = 'Please Enter Valid';
      result = false;
    } else {
      invaliddataTemp[t] = '';
    }
  });
  return { result, invaliddataTemp };
};

export const allContactDataFilled = (contactDetailsData) => {
  const validateEmailId1 = isEmail(contactDetailsData.primary_contact.email);
  const validateEmailId2 = isEmail(contactDetailsData.secondary_contact.email);
  const invaliddataTemp = {};
  let result = true;
  if (validateEmailId1) {
    invaliddataTemp.email1 = 'Please enter valid Email';
  }
  if (validateEmailId2) {
    invaliddataTemp.email2 = 'Please enter valid Email';
  }
  if (!validateEmailId1 && !validateEmailId2) {
    result = true;
  } else {
    result = false;
  }
  return {
    result,
    invaliddataTemp
  };
};

export const diffBetweenTwoDates = (date1, date2 = new Date()) => {
  date1 = new Date(date1);
  const Difference_In_Time = date2.getTime() - date1.getTime();
  const Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
  return Difference_In_Days;
};

export const getQueryParamsVersion = (location, defaultPath) => {
  const version = getQueryParam(location.search.replace('?', ''), 'version');
  return version === 'v1' ? `${defaultPath}?version=v1` : defaultPath;
};

/** Product Title in uppercase and Colour code in Blue color highlighted */
export const getProductTitleBy_Value = (param) => {
  const { title, isCapital, doNotShowColorCode } = param;
  // eslint-disable-next-line no-useless-escape
  const titleForLocalOperations = title.replace(/(?!-\d+(?:\.\d+)?)-/g, ' ');
  const regex = /^[cC]\d+$/;
  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {titleForLocalOperations?.split(' ').map((val, index) => {
        if (val?.trim()?.length) {
          if (regex.test(val.toLowerCase())) {
            if (doNotShowColorCode) {
              return '';
            }
            return <span key={index} style={{ color: '#00bac6' }}>{`${val.toUpperCase()} `}</span>;
          }
          return <span key={index}>{`${isCapital ? val.toUpperCase() : val} `}</span>;
        }
        return '';
      })}
    </>
  );
};

export const getProductTitleBy_Brand_Model = (param) => {
  const { product, isCapital, doNotShowColorCode, isModelNumberInNextLine } = param;
  return (
    <>
      <span key="product_brand">{isCapital ? product.brand.toUpperCase() : product.brand} </span>
      {isModelNumberInNextLine ? (
        <div key="product_model_number">{`(${
          isCapital ? product.size.toUpperCase() : product.size
        }) `}</div>
      ) : (
        <span key="product_model_number">
          {isCapital ? product.size.toUpperCase() : product.size}{' '}
        </span>
      )}
      {!doNotShowColorCode ? getProductColorFromTitle(product.value) : ''}
    </>
  );
};

/** To get highlighted product Color code extracted from title */
export const getProductColorFromTitle = (title) => {
  const titleForLocalOperations = title ? title.replace(/-/g, ' ') : undefined;
  if (titleForLocalOperations) {
    // eslint-disable-next-line array-callback-return
    return titleForLocalOperations.split(' ').map((val) => {
      if (val?.trim()?.length && /^c\d+$/i.test(val)) {
        return <span key={val} style={{ color: '#00bac6' }}>{`${val.toUpperCase()} `}</span>;
      }
    });
  }
  return '';
};

/** ----------------------- START : Print Blob PDF ----------------------- */
let count = 0;
let intervalRef;
export const printBlobPdf = (blobData, onAfterPrint) => {
  const blob = new Blob([blobData], { type: 'application/pdf' });
  const objectUrl = URL.createObjectURL(blob);

  const oldIframe = document.getElementById(`blob-src${count - 1}`);
  if (oldIframe && oldIframe.parentNode) {
    oldIframe.parentNode.removeChild(oldIframe);
  }
  const iframe = document.createElement('iframe');
  // eslint-disable-next-line no-plusplus
  const id = `blob-src${count++}`;

  iframe.setAttribute('src', objectUrl);
  iframe.setAttribute('id', id);
  iframe.setAttribute('height', '0');
  iframe.setAttribute('width', '0');
  document.body.appendChild(iframe);
  const frame = document.getElementById(id);
  frame.onload = function () {
    if (!frame) {
      //			    alert("Error: Can't find printing frame.");
      return;
    }
    try {
      const frame2 = frame.contentWindow;
      frame2.focus();
      frame2.print();
      /** Interval is here to make a use of AfterPrint */
      intervalRef = setInterval(() => {
        // eslint-disable-next-line max-len
        /** Whenever focus again comes from print modal to document means main screen, it will call AfterPrint funciton and clear interval */
        /** Interval timing should be at least 500 ms */
        if (document.hasFocus()) {
          if (onAfterPrint) {
            onAfterPrint();
          }
          // eslint-disable-next-line no-use-before-define
          stopInterval();
        }
      }, 500);
    } catch (err) {
      // eslint-disable-next-line no-alert
      alert('Inline printing is disabled. Please try opening in Chrome'); // toast
      window.open(objectUrl).print();
    }
  };
};
function stopInterval() {
  clearInterval(intervalRef);
}
/** ----------------------- END : Print Blob PDF ----------------------- */

export const numberWithCommas = (x) => {
  if (x >= 0) {
    return x.toLocaleString('en-IN');
  }
  return '-';
};

export const numberOnly = (evt, allowDecimal) => {
  const charCode = evt.which ? evt.which : evt.keyCode;
  if (allowDecimal && charCode === 46) {
    return true;
  }
  if (charCode > 31 && (charCode < 48 || charCode > 57)) {
    evt.preventDefault();
  }
};

export const getDateDifference = (date1, date2, type = 'minute') => {
  const tempDate1 = dayjs(date1);
  const tempDate2 = dayjs(date2);
  return tempDate1.diff(tempDate2, type);
};

export const verifyPartialOrders = (qcData, returnCount = true) => {
  const verifyOrder = {
    haveHoldedOrder: false,
    haveunHoldedOrder: false,
    haveFailedOrder: false
  };
  qcData.forEach((item) => {
    if (item.status === 'QCFailed') {
      verifyOrder.haveFailedOrder = true;
    } else if (item.status === 'QCUnhold') {
      verifyOrder.haveunHoldedOrder = true;
    } else if (item.status === 'QC_HOLD' || item.status === 'QCHold') {
      verifyOrder.haveHoldedOrder = true;
    }
  });

  let orderCount = 0;
  Object.keys(verifyOrder).forEach((key) => {
    if (verifyOrder[key]) {
      orderCount += 1;
    }
  });
  if (returnCount) {
    return orderCount;
  }
  return verifyOrder;
};

export const mergeContentValues = (stringVal, ...values) => {
  if (values.length > 1) {
    values.forEach((value, index) => {
      stringVal = stringVal.replace(`%dynamicValue${index + 1}%`, value);
    });
  } else {
    stringVal = stringVal.replace('%dynamicValue%', values);
  }
  return stringVal;
};

export const valueOrDefault = (val) => val ?? '-';

export const getCurrencySumbol = (currency) => currencySymbols[currency || 'INR'] || `${currency} `;

// Numbers to Words
export const numberToWords = (number) => {
  const amtWithoutPaisa = converToWords(number.toString().split('.')[0]);
  const amtWithPaisa = converToWords(number.toString().split('.')[1]);
  if (amtWithPaisa) {
    return `${amtWithoutPaisa} and ${amtWithPaisa} paise only`;
  }
  return `${amtWithoutPaisa} only`;
};

function converToWords(num) {
  const thousands = ['', 'thousand', 'million', 'billion', 'trillion'];

  if (num === 0) {
    return 'zero';
  }
  const parts = [];
  let i = 0;
  while (num > 0) {
    const part = num % 1000;
    if (part !== 0) {
      const words = convertThreeDigit(part);
      parts.unshift(`${words} ${thousands[i]}`);
    }
    num = Math.floor(num / 1000);
    i += 1;
  }
  return parts.join(' ').trim();
}

function convertThreeDigit(num) {
  const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
  const teens = [
    '',
    'eleven',
    'twelve',
    'thirteen',
    'fourteen',
    'fifteen',
    'sixteen',
    'seventeen',
    'eighteen',
    'nineteen'
  ];
  const tens = [
    '',
    'ten',
    'twenty',
    'thirty',
    'forty',
    'fifty',
    'sixty',
    'seventy',
    'eighty',
    'ninety'
  ];

  let result = '';
  const hundreds = Math.floor(num / 100);
  if (hundreds !== 0) {
    result += `${ones[hundreds]} hundred `;
  }
  const remainder = num % 100;
  if (remainder >= 11 && remainder <= 19) {
    result += teens[remainder - 10];
  } else {
    const tensDigit = Math.floor(remainder / 10);
    result += tens[tensDigit];
    const onesDigit = remainder % 10;
    if (onesDigit !== 0) {
      result += ` ${ones[onesDigit]}`;
    }
  }
  return result.trim();
}

export const getNumberByPercentage = (total, percentage) => total * (percentage / 100);

export const getRandomValues = () => {
  //  Uint8Array constructor with a length of 1:
  const typedArray = new Uint8Array(1);
  const randomValue = crypto.getRandomValues(typedArray)[0];
  return randomValue;
};

export const csvFileReader = (file, callback) => {
  if (file && file.type === 'text/csv') {
    const reader = new FileReader();

    reader.onload = function (e) {
      const text = e.target.result;
      const rows = text
        .split('\n')
        .map((row) => row.split(','))
        .filter((row) => row.length > 1);

      callback(rows);
    };

    reader.readAsText(file);
  }
};

export const getTraySeriesOnFacility = (data) => {
  const { data: customData = [], defaultTraySeriesList = [] } = data;
  const facilityCode = localStorageHelper.getItem('facility-code');
  const trayList =
    customData.find((element) => element.facilityCode === facilityCode)?.traySeriesList ||
    defaultTraySeriesList;
  return trayList;
};

export const getFilterValue = (key, filterValue, priorityKey) =>
  key === priorityKey && filterValue === 1 ? 'P1' : filterValue;

export const getTimestampUnqiueId = () => {
  const typedArray = new Uint16Array(1);
  const randomValue = (crypto.getRandomValues(typedArray)[0] % 9000) + 1000;
  return `${Date.now()}-${randomValue}`;
};

export const getStoredShipments = (STORAGE_KEY) => {
  const shipmentsStr = localStorage.getItem(STORAGE_KEY);
  return shipmentsStr ? JSON.parse(shipmentsStr) : {};
};

export const getBarcodeFromURL = (barcode) => {
  try {
    const url = new URL(barcode);
    return url.searchParams.get('barcode') ?? barcode?.trim();
  } catch (err) {
    return barcode?.trim();
  }
};

export const checkCLProductExpiry = (productData, facilityCode, clExpiryCheckFacilityList) => {
  if (!productData || !facilityCode || !clExpiryCheckFacilityList) {
    return { isExpired: false, daysToExpiry: null };
  }

  const { product } = productData;
  const { classification } = product;

  // Check if it's a CL product
  const CL_CLASSIFICATIONS = [11354, 19429, 19153]; // Contact Lens classifications
  if (!CL_CLASSIFICATIONS.includes(classification)) {
    return { isExpired: false, daysToExpiry: null };
  }

  // Get expiry date from shipment headers
  const expiryDate = productData?.product?.order?.expiryDate;
  if (!expiryDate) {
    return { isExpired: false, daysToExpiry: null };
  }

  // Calculate days to expiry
  const today = new Date();
  const expiry = new Date(expiryDate);
  const timeDiff = expiry.getTime() - today.getTime();
  const daysToExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));

  // Get facility threshold
  const facilityThreshold = clExpiryCheckFacilityList[facilityCode];
  if (!facilityThreshold) {
    return { isExpired: false, daysToExpiry };
  }

  // Check if product is expired or nearing expiry
  const isExpired = daysToExpiry <= facilityThreshold;

  return { isExpired, daysToExpiry };
};
