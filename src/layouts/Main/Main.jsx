/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, Outlet, useNavigate } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';

import localStorageHelper from 'utils/localStorageHelper';
import { toastOpen } from 'redux/actionCreators/toast';
import { logoutUserReset, LOGIN, logoutUserSuccess } from 'redux/reducers/login';
import { isTokenExpired, isEmpty } from 'utils/validation';
import { LOCALISATION, languagepackLoad } from 'redux/reducers/localisation';
import { getConsulKeyValue, selectFlatNavigationData } from 'redux/reducers/consul';
import { getFacilities, getFacilitiesReset } from '../../redux/reducers/settings';
import Spinner from '../../components/Spinner/Spinner';
import { Topbar } from './components';
import TopbarRightElement from './components/Topbar/TopbarRightElement';
import useIdleTimer from '../../useIdleTimer';

const useStyles = makeStyles()((theme) => ({
  root: {
    paddingTop: 56,
    height: '100%',
    [theme.breakpoints.up('sm')]: {
      paddingTop: 64
    }
  },
  shiftContent: {
    // paddingLeft: 64
  },
  backdrop: {
    zIndex: 1201,
    color: '#fff'
  },
  content: {
    height: '100%'
  }
}));

const objPayload = {
  type: 'facilities',
  pageRequest: { pageNumber: 0, pageSize: 6000, sortKey: 'updated_at', sortOrder: 'DESC' },
  facility_status: 'ACTIVE'
};

const loading = () => <Spinner className="display-grid-center" />;

const Main = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();

  useIdleTimer();
  const { isLoading: localeDataLoading, localeData } = useSelector((state) => state[LOCALISATION]);
  const { logoutSuccess } = useSelector((state) => state[LOGIN].logout);
  const { userDetail } = useSelector((state) => state[LOGIN].user);

  const {
    selectedFacility,
    facilitiesObj: { facilities }
  } = useSelector((state) => state.settings);

  const { pageTitle, hide: hideTopBar } = useSelector((state) => state.settings.globalTopBar);

  const { navigationDataLoading } = useSelector(selectFlatNavigationData);

  useEffect(() => {
    if (!Object.keys(localeData).length) {
      dispatch(languagepackLoad());
    }
  }, [localeData]);

  useEffect(() => {
    if (location.pathname !== '/changePassword' && !isEmpty(userDetail)) {
      dispatch(getConsulKeyValue(['navigation']));
      dispatch(getConsulKeyValue(['idleSessionTimer']));
      dispatch(getFacilities(objPayload));
    }
  }, [userDetail]);

  useEffect(() => {
    const checkUserSessionExpired = () => {
      if (isTokenExpired(userDetail?.exp, location.pathname !== '/changePassword')) {
        dispatch(logoutUserSuccess({ message: 'Please Login , Session Expired', type: 'error' }));
      }
    };
    document.addEventListener('click', checkUserSessionExpired);
    return () => document.removeEventListener('click', checkUserSessionExpired);
  }, [userDetail, location]);

  useEffect(() => {
    if (logoutSuccess) {
      localStorageHelper.removeItem('facilityList');
      dispatch(logoutUserReset());
      dispatch(getFacilitiesReset());
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: logoutSuccess.message,
          severity: logoutSuccess.type
        })
      );
      navigate('/login', { state: { from: location } });
    }
  }, [logoutSuccess]);

  const canLoadRoutes =
    (facilities &&
      userDetail &&
      selectedFacility &&
      !navigationDataLoading &&
      !localeDataLoading) ||
    location.pathname === '/changePassword';

  return canLoadRoutes ? (
    <div className={classes.root} key={selectedFacility}>
      <main className={classes.content}>
        {!hideTopBar ? (
          <Topbar>
            {pageTitle}
            <TopbarRightElement />
          </Topbar>
        ) : null}
        <React.Suspense fallback={loading()}>
          <Outlet />
        </React.Suspense>
      </main>
    </div>
  ) : (
    loading()
  );
};

export default Main;
