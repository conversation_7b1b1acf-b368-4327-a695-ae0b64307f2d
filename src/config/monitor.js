export const MONITOR_TYPES = {
  TOTAL_ORDERS: 'TOTAL_ORDERS',
  FULFILLABLE: 'FULFILLABLE',
  UNFULFILLABLE_LENS: 'UNFULFILLABLE_LENS',
  UNFULFILLABLE_FRAME: 'UNF<PERSON>LF<PERSON><PERSON>BLE_FRAME',
  UNFULFILLABLE_ORDERS: 'UNFULFILLABLE_ORDERS',
  FULFILLABLE_ORDERS: 'FU<PERSON><PERSON><PERSON>BLE_ORDERS',
  JIT_ORDERS: 'JIT_ORDERS'
};

export const MONITOR_FR_TYPES = {
  TOTAL: 'TOTAL_ORDERS',
  FULFILLABLE: 'FULFILLABLE',
  UNFULFILLABLE_LENS: 'UNFULFILLABLE_LENS',
  UNFULFILLABLE_FRAME: 'UNFULFILLABLE_FRAME'
};

export const MONITOR_HEADER_CARD_TITLES = {
  TOTAL_ORDERS: 'TOTAL ORDERS',
  FULFILLABLE: 'FULF<PERSON><PERSON>BLE ORDERS',
  UNFULFILLABLE_LENS: 'UNFULFILLABLE LENS',
  UNFULFILLABLE_FRAME: 'UNFULFILLABLE FRAME'
};

export const MONITOR_DEFAULT_COLUMNS = {
  TAG_NAME: 'Fr Tag',
  TOTAL: 'TOTAL'
};

export const FILTERS = {
  BINARYSWITCH: [
    { label: 'JIT Status', key: 'JIT', option1: 'JIT', option2: 'Non-JIT' },
    { label: 'VSM Status', key: 'VSM Hold', option1: 'Hold', option2: 'Unhold' },
    { label: 'Destination', key: 'Ship to Customer', option1: 'Customer', option2: 'Store' },
    { label: 'ASRS', key: 'ASRS', option1: 'ASRS', option2: 'NON_ASRS' },
    {
      label: 'Order Type',
      key: 'Super Order',
      option1: 'Super Order',
      option2: 'Non Super Order'
    },
    {
      label: 'Lens Only Order',
      key: 'Lens Only',
      option1: 'Lens Only',
      option2: 'Non Lens Only'
    },
    { label: 'Last Piece Order', key: 'TLP', option1: 'LP', option2: 'Non LP' },
    {
      label: 'Market Place Order',
      key: 'Market Place',
      option1: 'Market Place',
      option2: 'Non Market place'
    },
    {
      label: 'International Order',
      key: 'International',
      option1: 'International',
      option2: 'Non-International'
    },
    {
      label: 'OwnDays Order',
      key: 'OwnDays',
      option1: 'OwnDays',
      option2: 'Non-OwnDays'
    }
  ],
  BINARY: [
    'JIT',
    'Exchange',
    'PNC',
    'PFU',
    'TLP',
    'VSM Hold',
    'Ship to Customer',
    'ASRS',
    'NON_ASRS'
  ],
  AGEING: [
    {
      value: 0,
      label: '0'
    },
    {
      value: 24,
      label: '24h'
    },
    {
      value: 48,
      label: '2d'
    },
    {
      value: 72,
      label: '3d'
    },
    {
      value: 96,
      label: '4d'
    },
    {
      value: 120,
      label: '5d'
    },
    {
      value: 144,
      label: '6d'
    },
    {
      value: 168,
      label: '>6d'
    }
  ],
  QC_STATUS: ['Hold', 'Fail', 'Pass'],
  PICKING_PRIORITY: [0, 1, 2, 3, 10, 100],
  COUNTRY: [
    {
      key: 'IN',
      label: 'India'
    },
    {
      key: 'SG',
      label: 'Singapore'
    },
    {
      key: 'AE',
      label: 'UAE'
    },
    {
      key: 'US',
      label: 'US'
    }
  ],
  CHANNEL: ['John Jacobs', 'Customer', 'Store', 'Web', 'Marketplace', 'B2B'],
  PACKAGE: ['BLU cut', 'Anti Reflective', 'Shiny Shiny', 'Dhooop', 'Smudge Proof', 'All in One'],
  DATE_RANGE: { from: '', to: '' },
  HOLDED_ORDERS: [
    'payment_mismatch',
    'pincode_mismatch',
    'payment_not_captured',
    'power_follow_up',
    'pincode_not_servicable'
  ],
  ITEM_TYPE: ['FRAME', 'SUNGLASS', 'ACCESSORIES', 'CONTACT_LENS', 'CONTACT_LENS_SOLUTION']
};

export const DEFAULT_FILTERS = {
  BINARY: {},
  AGEING: {
    startValue: 0,
    endValue: '-1'
  },
  UPDATE_AGEING: {
    startValue: 0,
    endValue: '-1'
  },
  QC_STATUS: [],
  PICKING_PRIORITY: [],
  COUNTRY: [],
  CHANNEL: [],
  PACKAGE: [],
  DATE_RANGE: { from: '', to: '' },
  HOLDED_ORDERS: '',
  ITEM_TYPE: []
};

export const AGING_VALUE_MAPPING = {
  168: '-1'
};

export const FILTER_CHIPS_NAME = {
  BINARY: 'Binary Flags',
  AGEING: 'Order Ageing',
  UPDATE_AGEING: 'Updated Since',
  DATE_RANGE: 'Order Date Range',
  QC_STATUS: 'QC Status',
  PICKING_PRIORITY: 'Picking Priority',
  COUNTRY: 'Country',
  CHANNEL: 'Order Channel Type',
  PACKAGE: 'Lens Package',
  HOLDED_ORDERS: 'Holded Orders',
  CATEGORY: 'Category',
  ITEM_TYPE: 'Item Type'
};

export const BINARY_VALUES = {
  isJit: 'JIT',
  isTrueLastPiece: 'TLP',
  shipToCustomer: 'Ship to Customer',
  paymentNotCaptured: 'PNC',
  exchangeFlag: 'Exchange',
  powerFollowUpFlag: 'PFU',
  vsmHoldFlag: 'VSM Hold',
  Destination: 'Destination',
  VSM_Status: 'VSM_Status',
  JIT_Status: 'JIT_Status',
  isAsrsOrder: 'ASRS',
  isNonAsrsOrder: 'NON_ASRS',
  isSuperOrder: 'Super Order',
  isLensOnlyOrder: 'Lens Only',
  isMarketPlaceOrder: 'Market Place',
  isInternationalOrder: 'International',
  isOwndaysOrder: 'OwnDays'
};

export const COUNTRY_VALUES = {
  IN: 'India',
  SG: 'Singapore',
  US: 'US',
  AE: 'UAE'
};

export const BINARY_SWITCH_MAPPING = {
  JIT: 'Non-JIT',
  'VSM Hold': 'VSM Unhold',
  'Ship to Customer': 'Ship to Store',
  ASRS: 'NON_ASRS',
  'Super Order': 'Non Super Order',
  'Lens Only': 'Non Lens Only',
  'Market Place': 'Non Market',
  TLP: 'Non TLP',
  International: 'Non-International',
  OwnDays: 'Non-OwnDays'
};

export const MONITOR_V3_FR_TYPES = {
  TOTAL_ORDERS: 'TOTAL_ORDERS',
  FULFILLABLE_ORDERS: 'FULFILLABLE ORDERS',
  UNFULFILLABLE_ORDERS: 'UNFULFILLABLE ORDERS',
  JIT_ORDERS: 'JIT ORDERS'
};

export const DEFAULT_MONITOR_V3_TAGS = ['FR1', 'FR2', 'FR0_EG', 'FR0_SG', 'OTHERS'];

export const TABLE_INFO_API_CONFIG = [
  {
    key: 'JIT',
    filterKey: 'JIT',
    filterValue: true
  },
  {
    key: 'Non JIT',
    filterKey: 'JIT',
    filterValue: false
  },
  {
    key: 'ASRS',
    filterKey: 'ASRS',
    filterValue: true
  },
  {
    key: 'Non ASRS',
    filterKey: 'ASRS',
    filterValue: false
  },
  {
    key: 'STS',
    filterKey: 'Ship to Customer',
    filterValue: false
  },
  {
    key: 'STC',
    filterKey: 'Ship to Customer',
    filterValue: true
  }
];

export const CATEGORY_LABEL_MAPPING = {
  current: '1 Day',
  critical: '2 Day',
  severe: '>2 Day'
};

export const TABS_IMAGES = {
  FR0_SG: { src: 'fr0.svg', width: 37 },
  FR0_EG: { src: 'fr0_reading.svg', width: 37 },
  FR1: { src: 'fr1_2.svg', width: 37 },
  FR2: { src: 'fr2_powerSun.svg', width: 37 },
  OTHERS: { src: 'boxes.svg', width: 30 },
  FR0: { src: 'fr0.svg', width: 37 },
  default: { src: 'fr0.svg', width: 37 }
};

export const TOTAL_ORDERS_TYPES = [
  {
    name: 'FULFILLABLE ORDERS',
    filters: {
      isFulfillable: 0
    }
  },
  {
    name: 'UNFULFILLABLE ORDERS',
    filters: {
      isFulfillable: 1
    }
  }
];

export const defaultTotalOrderPayload = (filters = {}) => ({
  monitorPanelFilters: {
    binaryFilter: {},
    singleSelectFilters: {
      ...filters
    },
    monitorPanelRangeFilters: {
      ageingSinceCreated: {
        startValue: 0,
        endValue: '-1'
      },
      ageingSinceLastUpdate: {
        startValue: 0,
        endValue: '-1'
      },
      date: {
        startValue: '',
        endValue: ''
      }
    },
    multiSelectFilters: {
      pickingPriority: [],
      qcStatus: [],
      itemType: []
    }
  }
});
