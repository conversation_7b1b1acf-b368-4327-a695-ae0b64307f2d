{"global": {"NEXS": "NexS", "NEXS_PANEL": "NexS", "ENGLISH": "English", "HINDI": "Hindi", "CHINESE": "Chinese"}, "PAGE_TITLE": {"PO_PAGE_TITLE": "Purchase Orders", "BARCODE_HISTORY": "Barcode History", "FITTING_PANEL": "Fitting Panel", "SUPERVISOR_PICKING_PANEL": "Supervisor Panel - Picking", "INVENTORY_DASHBOARD": "Inventory Dashboard", "CONSOLIDATIONS_SUPERVISOR_PANEL": "Consolidation Supervisor Panel", "GRN": "GRN", "GRN_DETAIL": "GRN Detail", "GRN_AND_PID_INFORMATION": "GRN & PID Information", "INVOICES": "Invoices", "VENDOR_ITEM_MASTER": "<PERSON><PERSON><PERSON>", "DEBIT_NOTE_LIST": "Debit Note List", "VIEW_DEBIT_NOTE": "View Debit Note", "FACILITY_CONSTRAINTS": "Facility Constraints", "FACILITY": "Facility", "LAYOUT": "Layout", "PUTAWAY": "Putaway", "EXCEPTION_MANAGEMENT_SYSTEM": "Exception Management System", "BULK_INVENTORY_TRANSFER": "Bulk Inventory Transfer", "STORE_PACKING_PANEL": "Store Packing Panel", "MEI_EDGING": "MEI - Edging", "CARTON_SHIPMENT_MAPPING": "Carton Shipment Mapping", "DASHBOARD": "Dashboard", "INVENTORY": "Inventory", "E_INVOICE_MASTER_DASHBOARD": "E-Invoice Master Dashboard", "REFURBISHMENT_PANEL": "Refurbishment Panel", "CREATE_PUTAWAY": "Create Putaway", "PICKING": "Picking", "PICKING_DETAILS": "Picking Details", "MONITOR_PANEL": "Monitor Panel", "EXPORT_CONFIGURATION": "Export Configurations", "D365_LIST": "D365 List", "LOCATION_INVENTORY_DETAILS": "Location Inventory Details", "USER_MANAGEMENT": "User Management", "IQC": "IQC", "LAB_LENS_STICKER_PRINTING": "Lab Lens Sticker Printing", "MANIFEST_SHIPPING_METHOD": "Manifest Shipping Method", "COURIER_CUTOFF": "Courier Cutoff", "BAG_SHIPMENTS": "Bag Shipments", "ADHOC_ASRS_PICKING": "Adhoc ASRS Picking", "JOB_CARD_PRINTING": "Job Card Printing", "TRAY_REASSIGNMENT": "Tray Reassignment", "MANIFEST_DETAILS": "Manifest Details", "MANIFEST": "Manifest", "PACKING_PANEL": "Packing Panel", "QUALITITY_CHECK_PANEL": "Quality Check Panel", "PRICE_CONTROL_USER_ACCESS": "Price Control User Access", "INVENTORY_CONTROL": "Inventory Control", "STORE_CONSOLIDATION": "Store Consolidation", "START_RETIRE": "Start Retire", "REACTIVE_TRANSFER": "Reactive Transfer", "JIT_ORDER_SEARCH": "JIT Order Search", "HAND_EDGING": "Hand Edging", "MEI_BLOCKING": "MEI Blocking", "EDGING_QC": "Edging QC", "INVOICE_POSTING": "Invoice Posting", "CUSTOMER_DASHBOARD": "Customer Dashboard", "ORDER_DASHBOARD": "Order Dashboard", "CREATE_NEW_ORDER": "Create New Order", "DO_DASHBOARD": "DO Dashboard", "DISTRIBUTOR_ORDER_SHIPMENT_LIST": "Distributor Order Shipments Listing", "DISTRIBUTOR_ORDER_SHIPMENT_DETAILS": "Distributor Order Shipments Details", "VENDOR_SHIPMENT": "Vendor Shipment", "STOCK_TAKE": "Stock Take", "BULK_ORDER_PICKING": "Bulk Order Picking Panel", "JIT_STOCKIN": "JIT Stockin", "ADD_BARCODES": "Add Barcodes", "AUTO_GRN_SUMMARY": "GRN Summary", "WAREHOUSE_RECIEVING": "Warehouse Receiving", "ASRS_MANUAL_SYNC": "ASRS Manual Sync"}, "VENDOR_ITEM_MASTER": {"APPROVE": "APPROVE", "CONFIRM": "CONFIRM", "LISTING": "Listing", "CONFIRM_LOWECASE": "Confirm", "PACKAGE_NAME": "Package Name", "ITEM_NAME": "Item Name", "CATEGORY": "Category", "BRAND": "Brand", "PID": "PID", "PREVIOUS_PRICE": "Previous Price", "UPDATED_PRICE": "Updated Price", "BULK_ACTION": "Bulk Action", "APPROVE_LOWERCASE": "Approve", "REJECT": "Reject", "ITEMS_SELECTED": "Items selected", "UNIT_PRICE": "Unit Price", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "STATUS": "Status", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "EXPORT": "EXPORT", "REASON_FOR_REJECTION": "Reason for Rejection", "DO_YOU_WANT_TO": "Do you want to", "PRICE_CHANGE": "Price Change", "REJECT_UPPERCASE": "REJECT", "YES": "Yes", "CANCEL": "CANCEL", "PLEASE_ENTER_REASONS": "Please enter Reason(s)", "PRICE_ADDED": "Price Added", "PRICE_UPDATED": "Price Updated", "CONFIRMED": "Confirmed", "SELECT_CATEGORY_TO_CONTINUE": "Select Category to continue", "PRODUCT_ID": "Product ID", "PROPOSED_PRICE": "Proposed Price", "VENDOR_NAME_CODE": "Vendor Name / Code", "PID_INFO": "PID Info", "LENS_ID": "LensID", "PARENT_SKU": "Parent SKU", "CURRENT_PRICE": "Current Price", "COLOR": "Color", "SPH_CYL_AXIS_AP_BC": "SPH / CYL / AXIS / AP / BC", "IS_ENABLE": "Is Enable", "RESET": "Reset", "BULK_APPROVE_REJECT": "Bulk approve / reject", "ADD_UPDATE_PRICE": "ADD/UPDATE PRICE", "PLEASE_SELECT_CSV_TO_IMPORT_VIM": "Please select the CSV file to import VIM details", "LEGAL_OWNER": "Legal  Owner"}, "PO": {"PO": "PO", "PO_DETAILS": "PO Details", "PURCHASE_ORDERS": "Purchase Orders", "SEARCH_PO_HERE": "Search PO here", "APPROVE_PO": "APPROVE PO", "REJECT_PO": "REJECT PO", "SEND_FOR_APPROVAL": "SEND FOR APPROVAL", "CREATE_MULTIPLE_PO": "Create Multiple PO", "CREATE_PURCHASE_ORDER": "Create Purchase Order", "RESET": "Reset", "EXPORT": "EXPORT", "SELECTED": "Selected", "IGST": "IGST", "CGST": "CGST", "SGST": "SGST", "UGST": "UGST", "CESS": "Cess", "TYPE": "Type", "NONE": "None", "YES": "Yes", "YES_UPPERCASE": "YES", "GOOD": "Good", "BAD": "Bad", "DATE": "Date", "NO": "No", "AIR": "AIR", "SEA": "SEA", "LAND": "LAND", "DUTY": "Duty", "AMEND": "Amend", "HOLD": "Hold", "UNHOLD": "Unhold", "CANCEL": "Cancel", "CANCEL_UPPERCASE": "CANCEL", "CREATE": "Create", "SUBTOTAL": "Subtotal", "VENDOR": "<PERSON><PERSON><PERSON>", "TOTAL": "Total", "DONE": "Done", "NO_DATA": "No Data", "DONE_UPPERCASE": "DONE", "ACCEPTED": "Accepted", "REJECTED": "Rejected", "PENDING": "Pending", "ADD_PID": "Add PID", "IMPORT_CSV": "Import Csv", "OVERHEADS": "Overheads", "TOTAL_ORDER": "Total Order", "TOTAL_AMOUNT": "Total Amount", "TAXES_DUTY": "Taxes + Duty", "ATTACHEMENTS": "Attachments", "DISPLAY_ORDER": "Display Order", "REFRENCE_DETAILS": "Reference Details", "BULK_ORDER": "Bulk Order", "JIT_ORDER": "JIT Order", "SHIPMENT_TYPE": "Shipment Type", "VENDOR_DETAILS": "<PERSON><PERSON><PERSON>", "ENTER_VENDOR": "<PERSON><PERSON>", "AGREEMENT": "Agreement", "BASIC_DETAILS": "Basic Details", "EXPIRY_DATE": "Expiry Date", "SHIPPING_DATE": "Shipping Date", "CLOSE_PO": "Close PO", "SEASON": "Season", "PID": "PID", "ITEM_DETAILS": "<PERSON><PERSON>", "QUANTITY": "Quantity", "TAXABLE_VALUE": "Taxable Value", "UNIT_PRICE": "Unit Price", "VENDOR_COST": "<PERSON><PERSON><PERSON>", "TOTAL_DUTY": "Total Duty", "ENTER_REASON": "Enter Reason", "CHARACHTERS_LEFT": "characters left", "REASON_FOR_HOLD": "Reason For Hold", "REASON_FOR_UNHOLD": "Reason For Unhold", "REASON_FOR_AMEND": "Reason For Amend?", "PLEASE_ENTER_REASON_FOR_HOLDING_THE_PO": "Please enter your reason for holding the PO", "PLEASE_ENTER_REASON_FOR_UNHOLDING_THE_PO": "Please enter your reason for unholding the PO", "ADDITIONAL_COMMENTS": "Additional Comments", "APPROVE_PURCHASE_ORDERS": "Approve Purchase Orders", "REJECT_PURCHASE_ORDERS": "Reject Purchase Orders", "PROCUREMENT_TYPE": "Procurement Type", "SHORT_CLOSE_PO": "Short-Close PO?", "SHORT_CLOSE_PO_UPPERCASE": "SHORT CLOSE PO", "TOTAL_OVERHEADS": "Total Overheads", "CHA_CHARGES": "CHA Charges", "LOGISTICS_CHARGES": "Logistics Charges", "INSURANCE_CHARGES": "Insurance Charges", "FORWADER_CHARGES": "Forwarder Charges", "TOTAL_TAX_ON_SALES": "Total Tax on Sales", "BULK_APPROVE_OPTIONAL": "Bulk Approve (Optional)", "PROCUREMENT_TYPE_OPTIONAL": "Procurement Type (Optional)", "CLOSE_PO_WITHOUT_RECEIVING": "Closing PO without Receiving?", "PO_WILL_BE_DISCARDED": "PO will be discarded", "PLEASE_ENTER_REASON_FOR_AMENDING_PO": "Please enter your reason for amending the PO", "ITEM_DESC_NOT_AVAILABLE_FOR_THIS_PID": "Item Description not available for this PID", "ARE_YOU_SURE_YOU_WANT_TO_REJECT": "Are you sure you want to reject", "ARE_YOU_SURE_YOU_WANT_TO_APPROVE": "Are you sure you want to approve", "ARE_YOU_SURE_YOU_WANT_TO_CLOSE_THIS_PO": "Are you sure you want to close this PO?", "PO_HAS_PENDING_QTY_TO_RECEIVE": "PO has pending quantity to receive", "ARE_YOU_SURE_YOU_WANT_TO_SHORT_CLOSE_PO": "Are you sure you want to short-close po?", "PO_PROCESSING_STATE_CHECK_STATUS": "PO is in Processing state, Click Check Status to get the updated Status", "OVERWRITE_EXISTING_ROWS_IN_PO": "This will overwrite existing rows in PO", "CREATE_COPY_OF_PO": "Do you want to create a copy of this Purchase Order?", "CHECK_STATUS": "Check Status", "UPLOAD_FAILED": "Upload Failed", "RECEIVED": "Received", "SUBMITTED_ON": "Submitted On", "APPROVED_ON": "Approved On", "CREATED_BY": "Created By", "APPROVED_BY": "Approved By", "FACILITY_INFO": "Facility Info", "PO_VALUE": "PO Value", "PO_STATUS": "PO Status", "CLONE_PO": "CLONE PO", "VENDOR_CODE": "Vendor Code", "AMENDED_TO": "Amended To", "SHIPMENT_MODE": "Shipment Mode", "FROM_PARTY": "From party", "CREATED_ON": "Created On", "AMENDED_FROM": "Amended From", "BILLING_ADDRESS": "Billing Address", "SHIPPING_ADDRESS": "Shipping Address", "SAVE_AND_CLOSE": "SAVE AND CLOSE", "EDIT": "EDIT", "SUBMIT_AMEND": "SUBMIT AMEND", "SUBMIT_FOR_APPROVAL": "SUBMIT FOR APPROVAL", "START_RECEIVING": "Start Receiving", "CREATE_INVOICE": "CREATE INVOICE", "CLONE": "<PERSON><PERSON>", "CLOSE": "Close", "PRINT": "Print", "PRODUCTS": "Products", "INVOICE": "Invoice", "GRN": "GRN", "DEBIT_NOTE": "Debit Note", "SUBMIT": "SUBMIT", "FOC": "FOC"}, "INVOICE": {"INVOICES": "Invoices", "ARE_YOU_SURE": "Are you sure", "YES": "Yes", "NO": "NO", "EDIT": "Edit", "PRINT": "Print", "PENDING": "Pending", "ACCEPTED": "Accepted", "REJECTED": "Rejected", "CLOSE_INVOICE": "Close Invoice", "START_RECEIVING": "Start Receiving", "VIEW_DEBIT_NOTE": "View Debit Note", "YOU_WANT_TO_DELETE_THIS_INVOICE": "You want to delete this invoice", "VENDOR_INV_NO": "Vendor Inv. No.", "STATUS": "Status", "RECIEVED": "Recieved", "PO": "PO", "CREATE": "Create", "QC_PASS": "QC Pass", "QC_FAIL": "QC Fail", "REJECT_STOCK": "REJECT STOCK", "APPROVE_STOCK": "APPROVE STOCK", "SAVE_AND_CLOSE": "SAVE AND CLOSE", "SAMPLE_SIZE": "Sample Size", "PENDING_QUANTITY": "Pending Quantity", "TOTAL_QUANTITY": "Total Quantity", "VENDOR_INV_DATE": "Vendor Inv. Date", "VENDOR_CODE": "Vendor Code", "PURCHASE_ORDER": "Purchase Order", "CREATED_ON": "Created On", "COMPLETED_ON": "Completed On", "CREATED_BY": "Created By", "QC_DONE_TOTAL_SAMPLE_SIZE": "QC Done/Total Sample Size", "QC_PASS_QC_DONE": "QC pass/QC Done", "INVOICE_REF_NUM": "Invoice Ref Num", "SEARCH_INVOICE_HERE": "Search Invoice here", "CREATE_INVOICE": "Create Invoice", "SELECT_VENDOR": "Select Vendor", "VENDOR_NAME": "Vendor Name", "SELECT_PO": "Select PO", "ENTER_INVOICE_NUMBER": "Enter Invoice Number", "PO_NUM": "PO Num", "INVOICE_DATE": "Invoice Date", "SHOW_ADDITIONAL_FIELDS": "Show additional fields", "OK": "OK", "RESET": "Reset", "EXPORT_LOWERCASE": "Export", "EXPORT": "EXPORT", "TOTAL": "Total", "TYPE": "Type", "VENDOR": "<PERSON><PERSON><PERSON>", "VENDOR_INVOICE": "Vendor Invoice", "VENDOR_INVOICE_DATE": "Vendor Invoice Date", "ADD_PID": "ADD PID", "CONFIRM": "CONFIRM", "BULK_QC_FAIL": "BULK QC FAIL", "COMPLETE": "COMPLETE", "CLEAR_ALL": "Clear All", "CANCEL": "Cancel", "CANCEL_LOWERCASE": "Cancel", "DONE": "Done", "DISCARD": "DISCARD", "CLOSE_GRNS": "Close GRNs", "SNO": "SNo.", "PID": "PID", "PID1": "PID1", "ITEM_DETAILS": "<PERSON><PERSON>", "QUANTITY": "Quantity", "UNIT_PRICE": "Unit Price", "ITEM_DESCRIPTION": "Item Description", "TAXABLE_AMOUNT": "Taxable Amount", "CGST": "CGST", "IGST": "IGST", "SGST": "SGST", "UGST": "UGST", "CESS": "Cess", "FLAT": "Flat", "SHORT_QTY": "Short Qty", "TOTAL_PRICE": "Total Price", "ENTER_PID": "Enter PID", "NO_GRN": "No Grn", "GRN_LOADING": "Grn loading", "GRNS_ADDED": "GRNs Added", "VENDOR_INVOICE_NO": "Vendor Invoice No.", "INVOICE_REF_NO": "Invoice Ref No.", "NEXS_INVOICE_NO": "NexS Invoice No.", "DEBIT_INVOICE_NO": "Debit Invoice No.", "DUPLICATE_PIDS": "Duplicate PID's", "QC_FAIL_REASON": "QC Fail Reason", "QC_FAIL_BULK_QTY": "QC Fail Bulk Qty", "NO_PID_FOR_BULK_QC_FAIL": "No Pids For Bulk Qc Fail", "QTY_CANNOT_EXCEED_SHORT_QTY": "Qty cannot exceed short qty", "VENDOR_INVOICE_NUMBER": "Vendor Invoice Number", "TOTAL_INVOICE_QUANTITY": "Total Invoice Quantity", "TOTAL_INVOICE_VALUE": "Total Invoice Value", "Add_NEW_VENDOR_INVOICE": "Add NEW VENDOR INVOICE", "ADD_PRODUCT_BY_DESCRIPTION": "ADD PRODUCT BY DESCRIPTION", "OVERWRITE_EXISTING_ROWS_INVOICE": "This will overwrite existing rows in Invoice", "IMPORT_PROCESSED_SUCCESSFULLY": "Import Processed Successfully", "FILE_HAS_BEEN_IMPORTED_SUCCESSFULLY": "The file has been imported successfully", "ERROR_OPEN_GRN_FOUND": "Error - open GRN(s) found", "OPEN_GRN_FOUND_PLEASE_CLOSE_BEFORE_EDITING_INVOICE": "Open GRN found. Please close before editing invoice.", "CLICK_TO_SEE_OPEN_GRNS": "<PERSON>lick to See Open Grn(s)", "PLEASE_CORRECTLY_FILL_TAX_FIELDS": "Please correctly fill tax fields", "CANNOT_OPEN_EDIT_MODE": "Cannot Open Edit Mode", "CANNOT_OPEN_EDIT_MODE_NETWORK_ERROR": "Cannot Open Edit Mode, Network Error", "BATCH_NUMBER": "Batch Number"}, "QC": {"QC_PANEL": "Quality Check Panel", "QC_CHECK_PANEL": "Quality Check Panel", "SCAN_PRODUCT_BARCODE": "Scan Product Barcode", "SCAN_BARCODE_TO_START_QC": "Scan tray barcode to start QC", "PRINT_AT_PACKING": "Print at packing", "QC_COMPLETE": "QC Completed Successfully!", "QC_ON_HOLD": "Order is on Hold!", "QC_FAIL": "Order Failed!", "OC_PENDING": "Order is Pending!", "HOLD": "HOLD", "UNHOLD": "UNHOLD", "FAIL": "FAIL", "YES": "YES", "FAILED": "FAILED", "CANCEL": "CANCEL", "SUBMIT": "SUBMIT", "LENS_ID": "Lens ID", "INDEX_ID": "Index ID", "PRODUCT": "Product", "JIT": "JIT", "NON_JIT": "Non-JIT", "BARCODE": "Barcode", "PID": "PID", "TOTAL_PRODUCTS": "Total Products", "QC_PASSED": "QC Passed", "ACCESSORIES": "Accessories", "ON_HOLD": "On Hold", "ON_UNHOLD": "On UnHold", "FAIL_LOWERCASE": "Fail", "S_NO": "S.No.", "ACCESSORY_NAME": "Accessory Name", "IMAGE": "Image", "QC_ALREADY_DONE_FOR_THIS_SHIPMENT": "QC already done via Unicom for this shipment.", "YOU_CAN_PRINT_SHIPMENT_FROM_THIS_PAGE": "You can print shipment/invoice from this page.", "PARTIAL_OPERATIONS_CANT_BE_PERFORMED": "Partial Operations can't be performed either mark all FAIL or UNHOLD", "QC_COMPLETE_OF_ALL_ITEMS_TEXT": "Below is the summary of QC for all products in this order", "SCAN_LENS": "<PERSON>an <PERSON>ame/Lens", "SCAN_LENS_BARCODE": "Scan Lens Barcode", "SCAN_FRAME": "<PERSON>an <PERSON>ame/Lens", "SCAN_TO_COMPLETE_QC": "<PERSON><PERSON> to Complete QC", "SCAN_NEXT_PRODUCT": "Scan Next Product", "SCAN_NEXT_PRODUCT_BARCODE": "Scan Next Product Barcode", "REASON_FOR_HOLD": "Reason For Hold", "ENTER_REASON_FOR_HOLDING": "Please enter your reason for holding the product", "BARCODE_SCANNED_SUCCESSFULLY": "Barcode Scanned Successfully!", "PLEASE_SCANNED_ALL_BARCODES": "Please Scan All Barcodes!", "BARCODE_ALREADY_SCANNED": "Barcode already scanned!", "INVALID_BARCODE_SCANNED": "Invalid barcode scanned", "ORDER_ORIGIN": "Order Origin", "ORDER_ID": "Order ID", "FACILITY_CODE": "Facility Code", "ORDER_SOURCE": "Order Source", "SHIPMENT_ID": "Shipment ID", "ORDER_TYPE": "Order Type", "DO_NOT_PRINT_INVOICE": "Do Not Print Invoice", "CHANNEL": "Channel", "HIGH_VALUE_ORDER": "High Value Order", "CUT_LENS": "CUT LENS", "UNCUT_LENS": "UNCUT LENS", "PRIMARY_REASON": "Primary Reason", "SECONDARY_REASON": "Secondary Reason", "TERTIARY_REASON": "Tertiary Reason", "SELECT": "Select", "UNCLASSIFIED": "Unclassified", "ARE_YOU_SURE": "Are you sure?", "QC_PENDING": "Order is Pending!", "SCAN_FITTING_ID": "Scan Fitting ID", "ARE_YOU_SURE_TO_UNHOLD_ITEM": "Are you sure you want to unhold this item?", "ARE_YOU_SURE_TO_FAIL_ITEM": "Are you sure you want to mark this item as fail?", "SYSTEM_ERROR_OCCURRED": "System Error Occurred. Please try again.", "COATING": "Coating", "HAND_EDGER_USED": "Hand Edger used", "EXPECTED_DELIVERY_DATE": "Estimated Delivery Date"}, "PACKING": {"P_PANEL": "Packing Panel", "P_PANEL_MSLP": "Packing Panel (MSLP)", "SCAN_PRODUCT_BARCODE": "Scan Product Barcode", "SCANNED_QTY": "<PERSON><PERSON><PERSON>ty:", "SCANNED_ITEMS": "Scanned Items:", "TOTAL_QTY": "Total Qty", "TOTAL_ITEMS": "Total Items", "PID": "PID", "FRAME_TYPE": "Frame Type", "QTY": "Qty", "FRAME": "<PERSON>ame", "LENS": "Lens", "BARCODE": "Barcode", "YES": "YES", "AWB": "AWB", "SUBMIT": "SUBMIT", "CANCEL": "CANCEL", "HOLD": "HOLD", "UNHOLD": "UNHOLD", "S_NO": "S.No.", "IMAGE": "Image", "BRAND": "Brand", "CATEGORY": "Category", "COLOUR": "Colour", "MODEL": "Model", "QUANTITY": "Quantity", "SCAN_AGAIN_TO_COMPLETE": "<PERSON><PERSON> Again to Complete", "RE_SCAN_PRODUCT_BARCODE": "Re-scan Product Barcode", "BARCODE_ALREADY_SCANNED": "Barcode already scanned!", "INVALID_BARCODE_SCANNED": "Invalid barcode scanned", "LOYALTY_SERVICES": "Loyalty Services", "PACKING_COMPLETE": "Packing Done Successfully!", "PACKING_COMPLETE_OF_ALL_ITEMS_TEXT": "Below is the summary of all items in this order.", "SCAN_BARCODE": "Scan Barcode", "RE_SCAN_BARCODE": "Re-scan barcode", "INVALID_CODE_SCANNED": "Invalid code scanned", "ADD_REASON_FOR_HOLD": "Add Reason for Hold", "ARE_YOU_SURE": "Are you sure?", "ARE_YOU_SURE_TO_UNHOLD": "Are you sure you want to unhold?", "ENTER_REASON_FOR_HOLDING_ORDER": "Please enter your reason for holding the order", "ORDER_ON_HOLD": "This order is on hold. Reason:", "SCAN_PRODUCT_BARCODE_SHIPMENT_ID": "Scan Product Barcode/Shipment ID", "ERR_GIVE_TO_MANUAL_PACKER": "Please, give it to manual packer", "SYSTEM_ERROR_OCCURRED": "System Error Occurred. Please try again.", "NOT_VALID_PACKER": "LoggedIn user is not a valid packer, Please assign packing mode to user"}, "FITTING": {"FITTING_PANEL": "Fitting Panel", "QC_COMPLETE": "QC Completed Successfully!", "QC_FAIL": "Order Failed!", "QC_ON_HOLD": "Order is on Hold!", "QC_PENDING": "Order is Pending!", "SCAN_FITTING_ID": "Scan Fitting ID", "LEFT_LENS": "Left Lens", "RIGHT_LENS": "RIGHT Lens", "ORDER_ORIGIN": "Order Origin", "ORDER_PLACED_ON": "Order Placed On", "SHIPMENT_ID": "Shipment ID", "FITTING_ID": "Fitting ID", "ORDER_TYPE": "Order Type", "JIT_NON_JIT": "JIT/NON-JIT", "PACKAGE": "Package", "CONSOLIDATION": "Consolidation", "S_NO": "S.No.", "STATUS": "Status", "IMAGE": "Image", "UNHOLD": "UNHOLD", "HOLD": "HOLD", "FAIL": "FAIL", "FRAME": "<PERSON>ame", "LENS": "Lens", "NO": "NO", "YES": "YES", "SELECT": "Select", "PRIORITY": "Priority", "SECONDARY_REASON": "Secondary Reason", "TERTIARY_REASON": "Tertiary Reason", "SCAN_BARCODE_TO_START_FITTING": "Scan Barcode to Start Fitting", "SCAN_AGAIN_TO_COMPLETE_FITTING": "<PERSON><PERSON> Again to Complete Fitting", "SCAN_NEXT_BARCODE_TO_START_FITTING": "Scan Next Barcode to Start Fitting", "EITHER_HOLD_OR_SELECT_ALL_REASON_TO_FAIL": "Either Unhold or Select all the reasons to Fail", "SELECT_ALL_THE_REASON_AND_HOLD": "Select all the reasons & then hold", "FITTING_COMPLETED_SUCCESSFULLY": "Fitting Completed Successfully!", "REMOVE_ENGRAVING": "Remove Engraving", "FITTING_ALERT_MESSAGE": "For shipments which contain multiple items and damaged items, scan next item to automatically put it in Hold", "QC_COMPLETE_OF_ALL_ITEMS_TEXT": "Below is the summary of QC for all products in this order", "HAND_EDGER_USED": "Hand Edger used", "EXPECTED_DELIVERY_DATE": "Estimated Delivery Date"}, "JOBCARDPRINTING": {"SCAN_TRAY_ID": "<PERSON>an T<PERSON>"}, "SHIPMENTPRINTING": {"SCAN_PRODUCT_BARCODE_SHIPMENT_ID": "Scan Product Barcode/Shipment ID", "SCAN_MONO_CARTON": "<PERSON>an Mon<PERSON>", "AUTOBAGPRINTING": "Autobag Shipment", "MONOCARTON": "Mono Carton", "PACK_SLIP": "Pack Slip", "SCAN_SHIPMENT_ID": "Scan Tray Id / Carton Barcode", "PLEASE_CONTACT_SUPERVISOR": "Please contact the Supervisor to reprint."}, "EXPORT_REPORT": {"EXPORT_CONFIGURATION": "Export Configurations", "INVENTORY_REPORT": "Inventory Report", "SALE_ORDER_REPORT": "Sale Order Report", "ASRS_DISCREPANCY_REPORT": "ASRS Discrepancy Report ", "PO_ITEM_REPORT": "PO Item Report", "WHICH_REPORT_YOU_WANT_TO_EXPORT": "Which Report You want to Export?", "SELECT_REPORT_TYPE": "Select Report Type", "GENERATE_REPORT": "GENERATE REPORT", "FILTERS_FOR_THE_REPORT": "Filters for the report", "SELECT_ALL": "Select All", "CREATED_AT": "Created At", "WHICH_COLUMN_YOU_WANT_TO_ADD_IN_REPORT": "Which columns do you want to add in the report?", "YOU_CAN_ADD_UPTO": "You can add upto %dynamicValue% PIDs", "YOU_CAN_ADD_UPTO_PEOPLE": "You can add upto %dynamicValue% people", "SEND_COPY_OF_REPORT_TO": "Send the copy of report to", "PLEASE_REMOVE_DUPLICATE_EMAILS": "Please remove duplicate Emails", "PEOPLE": "people", "RECEIVER_EMAIL": "Receiver <PERSON><PERSON>", "YOU_CANNOT_ADD_MORE_THAN_FIVE_PEOPLE": "You can't add more than 5 people", "SEE_MORE": "...See more", "REPORT_ID": "Report Id", "REPORT_NAME": "Report Name", "REPORT_STATUS": "Report Status", "CREATED_BY": "Created By", "SEND_TO": "<PERSON><PERSON>", "DOWNLOAD_REPORT": "DOWNLOAD REPORT", "REPORT_HISTORY": "REPORT HISTORY"}, "TOP_BAR_RIGHT": {"CHANGE_PASSWORD": "Change Password", "LOGOUT": "Logout", "SELECT_FACILITY": "Select Facility", "ENTER_BARCODE": "Enter Barcode", "ENTER_PRODUCT": "Enter Product", "SHIPMENT_OR_INCREMENT_ID": "Shipment Id/ Increament Id", "ENTER_LOCATION": "Enter Location", "NEXS": "NexS"}, "E_INVOICE": {"E_INVOICE_MASTER_DASHBOARD": "E-Invoice Master Dashboard", "SUCCESS": "Success", "IN_PROGRESS": "In Progress", "FAILED": "Failed", "E_INVOICES": "E-invoices", "EXPORT": "EXPORT", "SEARCH_INV_OR_INC_OR_SHIP_ID": "Search with Inv no, Increment ID, Shipment ID", "GENERATE_E_INVOICE": "generate e-invoice", "SELECT_ALL_SHIPMENT": "select all shipment", "INVOICE_NO": "Invoice No.", "SHIPMENT_ID": "Shipment ID", "INCREMENT_ID": "Increment ID", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "STATUS": "Status", "MESSAGE": "Message", "IRN_NUMBER": "IRN Number", "ACTION": "Action", "GENERATE": "generate"}, "D365": {"D365_LIST": "D365 List", "ALL_PERIOD": "All Period", "ALL_PERIOD_UPPERCASE": "ALL PERIOD", "TODAY": "Today", "TODAY_UPPERCASE": "TODAY", "LAST_THREE_DAYS": "Last 3 Days", "LAST_THREE_DAYS_UPPERCASE": "LAST 3 DAYS", "LAST_FIVE_DAYS": "Last 5 Days", "LAST_FIVE_DAYS_UPPERCASE": "LAST 5 DAYS", "LAST_TEN_DAYS": "Last 10 Days", "LAST_TEN_DAYS_UPPERCASE": "LAST 10 DAYS", "LAST_FIFTEEN_DAYS": "Last 15 Days", "LAST_FIFTEEN_DAYS_UPPERCASE": "LAST 15 DAYS", "LAST_NINTY_DAYS": "Last 90 Days", "LAST_NINTY_DAYS_UPPERCASE": "LAST 90 DAYS", "CUSTOM_RANGE": "Custom Range", "SOURCE": "Source", "DESTINATION": "Destination", "EVENTS_SENT": "Events Sent", "SUCCESSFUL_EVENTS": "Successful Events", "FAILURE_EVENTS": "Failure Events", "PURCHASE_INVOICE": "Purchase Invoice", "GRN": "GRN", "PURCHASE_ORDER": "Purchase Order", "SEARCH_GRN_HERE": "Search GRN here...", "SEARCH_PO_HERE": "Search PO here...", "SEARCH_INVOICE_HERE": "Search invoice here...", "SEARCH_WITH_REASON": "Search with Reason", "RETRY": "Retry", "ID": "ID", "UPDATED_AT": "Updated At", "CREATED_AT": "Created At", "STATUS": "Status", "D365_RESPONSE": "D365 Response", "OWNER": "Owner", "UPDATED_BY": "Updated By", "RETRY_COUNT": "Retry Count", "DETAIL": "Detail"}, "EMS_PUTAWAY": {"EXCEPTION_MANAGEMENT_SYSTEM": "Exception Management System", "ENTER_DETAILS": "Enter Details", "SCAN_BARCODE": "Scan barcode", "SEARCH_BARCODE_HERE": "Search Barcode here...", "MATCH_NOT_FOUND": "Match Not Found", "NO_DATA_FOUND": "No Data Found", "Scanned": "Scanned", "Passed": "Passed", "Failed": "Failed", "Successful": "Successful"}, "REFURBISHMENT": {"REFURBISHMENT_PANEL": "Refurbishment Panel", "PENDING_REFURBISHMENT": "Pending Refurbishment", "REFURB_COMPLETED": "Refurb Completed", "PENDING_GOOD_PUTAWAY": "Pending Good Putaway", "SERCH_BARCODE_HERE_PLACEHOLDER": "Search Barcode here...", "SEARCH_BARCODE_HERE": "Search Barcode here", "BARCODE": "Barcode", "PID": "Pid", "LOCATION": "Location", "UPDATED_AT": "Updated At", "PID_UPPERCASE": "PID", "STATUS": "Status", "UPDATED_BY": "Updated By", "ACTION": "Action", "CREATE_PUTAWAY": "Create putaway", "IN_REFURB": "In Refurb", "MARK_BAD": "MARK BAD", "MARK_GOOD": "MARK GOOD"}, "CYCLE_COUNT": {"INVENTORY": "Inventory", "TODAY": "Today", "TODAY_UPPERCASE": "TODAY", "LAST_SEVEN_DAYS": "Last 7 Days", "LAST_SEVEN_UPPERCASE": "LAST 7 DAYS", "LAST_THIRTY_DAYS": "Last 30 Days", "LAST_THIRTY_DAYS_UPPERCASE": "LAST 30 DAYS", "CUSTOM_RANGE": "Custom Range", "FILTER": "Filter", "SORT_DATE_BY": "Sort Date By", "BINS_SCANNED": "Bins Scanned", "UPDATE_LOCATION": "Update Location", "VALID_BARCODE": "Valid Barcodes", "INVALID_BARCODES": "Invalid Barcodes", "NOT_FOUND": "Not Found", "SUMMARY_OF_SCANNING": "Summary of Scanning", "LOCATION": "Location", "UPDATED_BY": "Updated By", "UPDATED_AT": "Updated At", "TOTAL_BARCODES": "Total Barcodes", "LOCATION_UPDATE": "Location Update", "EXPORT": "Export", "NO_OF_ITEMS_SCANNED": "NO. OF ITEMS SCANNED", "NO_OF_BOXES_SCANNED": "NO. OF BOXES SCANNED", "ITEM_BARCODES": "Item Barcodes", "BOX_BARCODES": "Box Barcodes", "STATUS": "Status"}, "MANIFEST": {"TITLE": "Manifest", "MANIFEST_ID": "manifestId", "SHIPPING_PROVIDER": "Shipping Provider", "GENERATED_BY": "Generated By", "CHANNEL": "Channel", "STATUS": "Status", "SHIPPING_METHOD": "Shipping Method", "NO_OF_ITEMS": "No. of Items", "CREATED": "Created", "BAG_ID": "Bag ID", "SCAN_BARCODE": "Scan Barcode", "CREATE_MANIFEST": "C<PERSON> Manifest", "ORDER_TYPE": "Order Type", "SHIPMENT_COUNT": "SHIPMENT COUNT", "MANIFEST_DETAILS": "Manifest Details", "NEXS_COUNT": "NEXS COUNT", "BAG_COUNT": "BAG COUNT", "RETRY": "Retry", "GO_BACK": "Go Back", "BACK": "Back", "BAG_STATUS": "Bag Status", "MANIFEST_NUMBER": "Manifest Number", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "CLOSED_AT": "Closed At", "CLOSED_BY": "Closed By", "AWB_ORDER_SHIPMENT_PLACEHOLDER": "Enter AWB / Order No / Shipment ID", "AWB_OR_SHIPMENT": "AWB/Shipment", "ENTER_AWB_OR_SHIPMENT": "Enter AWB/Shipment", "CLOSE_MANIFEST": "Close Manifest", "PRINT_SUMMARY": "Print Summary", "PRINT_PDF": "Print PDF", "PRINT_CSV": "Print CSV", "PRINT_INVOICES": "Print Invoices", "ARE_YOU_SURE": "Are you sure?", "CANCEL": "Cancel", "CONFIRM": "Confirm", "S_NO": "S. No.", "CODE": "Code", "INVOICE": "Invoice", "ORDER": "Order", "PACKAGE_DETAILS": "Package Details", "TRACKING_NUMBER": "Tracking Number", "NO_OF_PRODUCTS": "No of Products", "WEIGHT_GM": "Weight(gm)", "AMOUNT": "Amount", "PAYMENT_METHOD": "Payment Method", "ADDED_AT": "Added At", "ACTIONS": "Actions", "CLOSED_SUCCESSFULLY": "closed successfully", "ALREADY_EXISTS_IN_MANIFEST": "already exists in the manifest.", "MANIFEST_NOT_FOUND": "Manifest not found. Please refresh the page.", "PLEASE_SCAN_VALID_PKG": "Please scan a valid package.", "NO_DATA": "No data...", "CREATE": "Create"}, "MONOCARTONPRINTING": {"TITLE": "Carton Shipment Mapping", "SCAN_TRAY_BARCODE": "Scan Tray Barcode", "SCAN_CARTON_BARCODE": "Scan Carton barcode", "SUBMIT": "SUBMIT", "CARTON_MAPPED_TO_SHIPMENT": "Carton mapped to shipment", "SUCCESSFULLY": "successfully", "SOMETHING_WENT_WRONG": "Something went wrong"}, "ADHOCASRSPICKING": {"ASRS_PICKING_HEADER": "Adhoc ASRS Picking", "PID": "PID", "PID_DESC": "PID Description", "QTY": "Quantity", "AVAILABLE_QTY": "Available Quantity", "ENTER_PID": "ENTER PID", "ADD_PID": "ADD PID", "DONE": "DONE", "PID_ALREADY_ADDED": "PID Already Added !", "CHOOSE_DIFF_PID": "Please choose different PID.", "ENTER_QTY": "Please Enter Quantity", "GREATER_THAN_QTY_AVAILABLE": "Total Count is Grater than Quantity Available", "GREATER_THAN_MAX_QTY": "Total Count is Grater than MAX_QUANTITY", "REQUEST_ID": "Request ID", "PID_COUNT": "PID Count", "PICKED_QTY": "Picked <PERSON><PERSON>", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "CREATED_BY": "Created By", "UPDATED_BY": "Updated By", "STATUS": "Status", "IN_PICKING": "IN_PICKING", "DISPATCHED": "DISPATCHED", "CREATED": "CREATED", "RECEIVED": "RECEIVED", "SEND": "Send", "TRANSFER_ID": "Transfer Id", "NUM_OF_PIDS": "No. of PIDs", "NUM_OF_ITEMS": "No. of ITEMS", "NA": "NA", "SEARCH": "Search", "PRODUCT_ID": "Product ID", "REQ_QTY": "<PERSON><PERSON><PERSON>.", "QTY_SCANNED": "<PERSON><PERSON><PERSON>", "AVL_QTY": "Avail. Qty.", "UPDATED_ON": "Updated On", "EXPORT": "EXPORT", "TOTAL_ITEMS_SCANNED": "Total items scanned", "NO_OF_BOXES_SCANNED": "No. of boxes scanned", "REPLACE": "Replace", "OLD_BARCODE": "Old Barcode", "SCAN_NEW_BARCODE": "Scan new barcode", "REPLACE_BARCODE_OF": "Replace Barcode Of", "CANCEL": "CANCEL", "REPLACE_CAPS": "REPLACE"}, "PICKING": {"PICKING_TITLE": "Picking", "PICKLIST_CODE": "PickList Code", "GENERATED_BY": "Generated By", "TOTAL_QUANTITY": "Total Quantity", "PICKED_ITEMS": "Picked Items", "PENDING_QUANTITY": "Pending Quantity", "SKIPPED_QUANTITY": "Skipped Quantity", "STATUS": "Status", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "PICKER_ID": "PICKER ID", "TOTAL_QTY_UPPERCASE": "TOTAL QTY", "PICKED_ITEMS_UPPERCASE": "PICKED ITEMS", "SKIPPED": "SKIPPED", "PENDING_ITEMS": "PENDING ITEMS", "PICK_LIST": "Picklist", "SEARCH_PLACE_HOLDER": "Search ..", "PRODUCT_ID": "Product Id", "PICKED_TIME": "Picked Time", "PICKING_TIME": "Picking Time", "TOTAL_QTY": "Total Qty", "BARCODES_PICKED": "Barcodes Picked", "PENDING_QTY": "Pending Qty", "SKIPPED_QTY": "Skipped <PERSON><PERSON>", "CREATED": "Created", "LAST_UPDATED": "Last Updated", "COMPLETED": "Completed", "PICK_LIST_ID": "PickList Id", "ORDER_ID": "Order Id", "SHIPMENT_ID": "Shipment Id", "ITEM_ID": "Item Id", "PRODUCT_NAME": "Product Name", "SPH": "SPH", "CYL": "CYL", "PRODUCT_DESC": "Product Description", "PICK_CREATED_DATE": "Pick Create Date", "PICKER_NAME": "Picker Name", "LOCATION": "Location", "AISLE": "Aisle"}, "TRAYREASSIGNMENT": {"TITLE": "Tray Reassignment", "SCAN_BARCODE_TO_REASSIGN": "Scan Barcode to Reassign", "SCAN_BARCODE": "Scan Barcode", "FITTING_ID": "Fitting ID", "SHIPPING_ID": "Shipping ID", "NA": "NA", "SOMETHING_WENT_WRONG": "Something went wrong !", "TRAY_VALIDATION_SUCC": "Tray Validation Successful", "SELECT_ITEMS_WANT_TO_ADD_TO_NEW_TRAY": "Please select the items that you want to add to new tray", "SCAN_VALID_TRAY": "Please Scan a valid Tray ID", "SCAN_NEW_TRAY_ID": "Scan New Tray ID", "REASSIGN": "REASSIGN", "SELECT": "Select", "ORDER_ITEM_ID": "Order Item ID.", "PID": "PID", "STATUS": "Status", "BARCODE": "Barcode", "TRAY_ID": "Tray ID", "TRAY_LOCATION": "Tray Location", "TYPE": "Type", "UPDATED_AT": "Updated At", "HIDE_HISTORY": "Hide History", "SHOW_HISTORY": "Show History", "INCREMENT_ID": "Increment ID", "SHIPPING_PKG_ID": "Shipping Package ID", "TRAY_REASSIGNED": "<PERSON><PERSON> Reassigned", "SCANNED_WRONG_BARCODE": "Scanned wrong barcode", "BARCODE_CANNOT_BE_EMPTY": "Barcode can not be empty", "CONFIRM_REASSIGNMENT": "Confirm Reassignment", "SCAN_ITEMS_YOU_WANT_TO_ADD_TO_NEW_TRAY": "Please scan the items that you want to add to new tray", "SCAN_ITEM_BARCODE": "Scan Item Barcode", "CANCEL": "cancel", "ERROR": "Error"}, "BAGSHIPMENT": {"BAG_SHIPMENTS": "Bag Shipments", "HISTORY": "HISTORY", "LISTING": "LISTING", "MANIFEST_SHIPPING_METHOD": "Manifest Shipping Method", "BAG_ID": "Bag ID", "MANIFEST_ID": "Manifest ID", "SHIPMENT_COUNT": "Shipment Count", "PRIORITY": "Priority", "DROP_LOCATION_TYPE": "Drop Location Type", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "CREATED_BY": "Created By", "UPDATED_BY": "Updated By", "BAG_STATUS": "Bag Status", "RELEASE_BAG": "Release Bag", "UNDER_MANIFEST_SHIPPING_METHOD": "Do you want to release bags under Manifest Shipping Method", "RELEASE_BAG_FROM_MANIFEST": "Do you want release the bag (%dynamicValue1%) from Manifest Shipping Method (%dynamicValue2%)?", "BAG_COUNT": "Bag Count", "ORDER_NUMBER": "Order Number", "SHIPPING_PKG_ID": "Shipping Package Id", "AWB_NUMBER": "AWB Number", "INVOICE_NO": "Invoice Number", "EXPORT": "Export"}, "CREATE_GRN": {"NEXS_GRN": "NexS GRN", "REMOVE": "Remove", "CREATED_BY": "Created by", "ITEMS": "Items", "GRN": "GRN", "DATE": "Date", "INVOICE": "Invoice", "PO": "PO", "VENDOR": "<PERSON><PERSON><PERSON>", "VENDOR_INVOICE_NUMBER": "Vendor Invoice Number", "QC_FAIL_REASONS": "QC Fail Reasons", "NO_PRODUCTS_FOUND_FOR_THIS_GRN": "No Products found for this GRN", "PLEASE_ADD_PRODUCTS": "Please add Products", "DONE": "DONE", "ERROR": "Error", "ADD_PID": "ADD PID", "PRODUCT_LIST": "Product List", "PID": "PID", "SPH": "SPH", "CYL": "CYL", "AP": "AP", "ED": "ED", "MODEL_NUMBER": "Model Number", "COLOR_CODE": "Color Code", "MRP": "MRP", "INDEX": "Index", "PD": "PD", "AXIS": "Axis", "AXIS_TYPE": "Axis-Type", "UNIT_COST_PRICE": "Unit Cost Price", "UNIT_TAXES": "Unit Taxes", "TOTAL_COST": "Total Cost", "TOTAL_ITEMS": "Total Items", "MIN_TO_SCAN": "Min. to <PERSON>an", "SCANNED": "Scanned", "QC_PASS": "QC Pass", "QC_FAIL": "QC Fail", "OTHERS": "Others", "ENTER_DETAILS": "Enter Details", "QC_DETAILS": "QC Details", "SCAN_ITEM_BOX_CODE": "Scan Item / Box Code", "SCAN_ITEM": "<PERSON><PERSON>", "EXPIRY_DETAILS": "Expiry Details", "SCAN_QR_CODE": "Scan QR Code", "SCANNED_ITEMS_WILL_COME_HERE": "Scanned items will come here", "SCAN_BOX_CODE_TO_GET_STARTED": "Scan Box Code to get started", "SCAN_ITEM_CODE_TO_GET_STARTED": "Scan Item Code to get started", "SEARCH_BARCODE_HERE": "Search Barcode here...", "qc_passed": "QC Passed", "qc_failed": "QC Fail", "others": "Others", "PID_SUCCESSFULLY_ADDED": "PID Successfully Added!", "PLEASE_ENTER_AN_POSITIVE_INTEGER": "Please Enter an Positive Integer", "PLEASE_ENTER_AN_INTEGER_VALUE": "Please Enter an Integer Value", "ADD_A_NEW_PID": "Add a New PID", "PLEASE_SCAN_PID_SKU_GTIN_OF_THE_ITEM_BELOW": "Please scan PID / SKU / GTIN of the item below", "SEARCH_BY_DESCRIPTION": "Search by description", "SCAN_PID_SKU_GTIN": "Scan PID / SKU / GTIN", "PUTAWAY_MOVED_TO_PENDING": "Putaway moved to pending", "NO_DATA_FOUND": "No Data Found", "QTY": "QTY", "PUTAWAY_NUM": "PUTAWAY NUM", "VIEW_DETAILS": "VIEW DETAILS", "MARK_AS_PENDING": "mark as pending", "MINUTES_LEFT": "Minutes Left", "QUANTITY_UPDATED_SUCCESSFULLY_ADDED": "Quantity Updated Successfully Added!", "PLEASE_PROVIDE_POSITIVE_NUMBER": "Please Provide Positive number", "FINISHED_SCAN_MORE": "Finished, <PERSON><PERSON> more?", "IF_YOU_WISH_TO_SCAN_MORE_ITEMS_OF_SAME_PID,ENTER_ADDITIONAL_QUANTITY": "If you wish to scan more items of same PID, enter additional quantity", "ENTER_ADDITIONAL_QUANTITY": "Enter additional quantity", "MIN_QUANTITY_TO_SCAN": "<PERSON> qunatity to scan", "ADD_NEW_PID": "ADD NEW PID", "ADD_QUANTITY": "ADD QUANTITY", "GRN_SAVED_SUCCESSFULLY": "GRN Saved Successfully", "SUCCESS": "Success", "OK": "OK", "BOX_CODE": "Box Code", "TOTAL_QUANTITY": "Total Quantity", "SAMPLING_QUANTITY": "Sampling Quantity", "STATUS": "Status", "BOXES": "Boxes", "UNICOMM_GRN": "Unicomm GRN", "TOTAL_PRICE_WITH_TAXES": "Total price with taxes ", "CONFIRM": "CONFIRM", "PRODUCT_ID": "Product Id", "PRODUCT_NAME": "Product Name", "UNIT_PRICE": "Unit Price", "SCANNED_BARCODE": "Scanned Barcode", "TOTAL_PRICE": "Total Price", "TOTAL": "Total", "BLOCKED": "BLOCKED", "ONGOING": "ONGOING", "START_GRN": "Start GRN", "CREATE_GRN": "Create GRN", "ENTER_VENDOR": "<PERSON><PERSON>", "ENTER_PO": "Enter Purchase Order", "SELECT_INVOICE": "Select Invoice", "BATCH_NUMBER": "Batch Number"}, "MANIFEST_SHIPPING_METHOD": {"TITLE": "Manifest Shipping Method", "MODAL_INFO": "Please select CSV file to Import Shipping Provider, Order Type, Shipping Method, Courier Code, Manifest Shipping Method and Facility Code.", "SHIPPING_METHOD": "Shipping Method", "ADD_OR_UPDATE": "ADD/UPDATE", "SHIPPING_PROVIDER": "Shipping Provider", "ORDER_TYPE": "Order Type", "PAYMENT_METHOD": "Payment Method", "COURIER": "Courier", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "CREATED_BY": "Created By", "UPDATED_BY": "Updated By", "FACILITY_CODE": "Facility code"}, "PUTAWAY": {"TITLE": "Putaway", "PUTAWAY_LISTING": "Putaway Listing", "ALL": "all", "PENDING": "pending", "SEARCH_PLACE_HOLDER": "Search ..", "RESET": "RESET", "PUTAWAY_NO": "Putaway No.", "TYPE": "Type", "STATUS": "Status", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "CREATED_BY": "Created By", "TOTAL_QTY": "Total Qty", "ITEM_NOT_FOUND": "Item Not Found Qty", "LOCATION_NOT_FOUND": "Location Not Found Qty", "PENDING_QTY": "Pending Qty", "COMPLETED_UPPERCASE": "COMPLETED", "PENDING_UPPERCASE": "PENDING", "STATUS_UPPERCASE": "STATUS", "SOURCE": "SOURCE", "DESTINATION": "DESTINATION", "CREATED_BY_UPPER": "CREATED BY", "UPDATED_BY": "UPDATED BY", "QTY": "QTY", "TOTAL_QTY_UPPERCASE": "TOTAL QTY", "MORE_INFO": "More Info", "ITEM_BARCODE": "Item Barcode", "PID": "PID", "PID_DESC": "PID Description", "LOCATION": "Location", "INVENTORY_CONDITION": "Inventory Condition", "ERROR": "Error", "SCANNED": "Scanned", "SUCCESSFUL": "Successful", "FAILED": "Failed", "PUTAWAY_REFERENCE_ID": "Putaway Reference Id "}, "GRN_LIST": {"GRN_AND_PID_INFORMATION": "GRN & PID Information", "SEARCH_GRN_HERE": "Search GRN here...", "CREATE_GRN": "Create GRN", "GRN_LISTING": "GRN ListING", "RESET": "RESET", "EXPORT": "EXPORT", "GRN_NO": "GRN Number", "GRN_STATUS": "GRN Status", "UNICOMM_GRN": "Unicomm GRN", "PO_NO": "PO No.", "VENDOR_INV_NO": "Vendor Inv. No.", "VENDOR_NAME": "Vendor Name", "CREATED_BY": "Created By", "ASSIGNED_TO": "Assigned To", "CREATED_ON": "Created On", "TOTAL_QTY": "Total Qty", "QC_COMPLETE": "QC Complete", "UNBLOCK_PRODUCTS": "Unblock Products", "REASSIGN_GRN": "Reassign GRN", "ERROR": "Error"}, "GRN_DETAIL": {"TITLE": "GRN Detail", "GRN_DETAILS": "GRN Details", "GRN_NO": "GRN Number", "PO_NO": "PO Number", "STATUS": "Status", "PUTAWAY": "Putaway", "CREATED_BY": "Created By", "BILL_OF_ENTRY_AMOUNT": "Bill Of Entry Amount", "INVOIVE_REF_NO": "Invoice Ref No", "BILL_OF_ENTRY_DATE": "Bill Of Entry Date", "BILL_OF_ENTRY_NO": "Bill Of Entry Number", "ASSIGNED_TO": "Assigned To", "CREATED_ON": "Created On", "RECEIVED_UNITS": "Received Units", "RECEIVED_AMOUNTS": "Received Amounts", "REJECTED_UNITS": "Rejected Units", "REJECTED_AMOUNT": "Rejected Amount", "TOTAL_SCANNED": "Total Scanned", "ACCEPTED": "Accepted", "REJECTED": "Rejected", "PIDS": "PIDs", "VENDOR_INV_NO": "Vendor Invoice No.", "VENDOR_INV_DATE": "Vendor Invoice Date", "B2B_INV_DATE": "B2B Invoice Date", "HANDOVER_BY": "Handover By", "SEND_TO_PARTY": "Send to Party", "BLOCKED_PIDS": "Blocked PIDs", "CANCEL": "Cancel", "SAVE_AND_CLOSE": "SAVE AND CLOSE", "NO_DATA_FOUND": "No Data Found", "USER_NOT_HAVE_PERMISSION": "User Does not have permission for path", "VENDOR": "<PERSON><PERSON><PERSON>", "UNICOMM_GRN": "Unicomm GRN", "VENDOR_INV_NUMBER": "Vendor Invoice Number", "GRN": "GRN", "PO": "PO", "CREATED": "Created", "PENDING": "Pending", "CLOSED": "Closed", "START_IQC": "Start IQC", "START_RECEIVING": "Start Receiving", "REASSIGN_GRN": "Re-Assign GRN", "PRINT": "Print", "BARCODE_DETAIL": "Barcode Detail", "PRODUCTS": "Products", "IQC": "IQC", "PENDING_QC": "Pending QC", "QC_PASS": "QC Pass", "QC_FAIL": "QC Fail", "PID": "PID", "ITEM_DETAILS": "<PERSON><PERSON>", "PID_STATUS": "PID Status", "VENDOR_SKU": "Vendor SKU", "BATH_CODE": "Batch Code", "RECEIVED": "Received", "PRICE": "Price", "ADDITIONAL_COST": "Additional Cost", "BOX_ID": "Box Id", "PRODUCT_DESCRIPTION": "Product Description", "QUANTITY": "Quantity", "CTRL_P": "Ctrl+P", "CTRL_E": "Ctrl+E"}, "PRINT_CONFIRM_MODAL": {"PRINT_OPTIONS": "Print Options", "SELECT_FROM_OPTIONS_BELOW": "Please select from various options below:", "SELECT_PRINT_OPTIONS": "Select Print Options", "PRINT": "PRINT"}, "MEI": {"SELECT_LOCATION": "Select Location", "ADD_BARCDOE": "Add Barcode", "SCAN_ITEM_BARCODE": "Scan Item Barcode", "INVALID_BARCODE": "<PERSON><PERSON><PERSON>"}, "HAND_EDGING": {"ORDER_ORIGIN": "Order Origin", "ORDER_ID": "Order ID", "SHIPMENT_ID": "Shipment ID", "CHANNEL": "Channel", "SCAN_AGAIN_CMPLT_EDGING": "<PERSON><PERSON> Again to Complete Edging", "SCAN_NEXT_BRCODE_STRT_EDGING": "Scan Next Barcode to Start Edging", "SRCH_PLACEHOLDER": "Scan Fitting ID / Tray ID", "SCAN_BRCODE_TO_STRT_EDGING": "Scan Barcode to Start Edging", "EDGING_CMPLTED_SUCCESSFULLY": "Edging Completed Successfully!", "RIGHT": "RIGHT", "LEFT": "LEFT", "LENS": "LENS", "SCAN_BARCODE_TO_MEI_BLOCKING": "Scan Barcode to MEI Block", "SCAN_AGAIN_BLOCK_BLOCKING": "<PERSON><PERSON> again to complete the blocking", "SUCCESSFULLY_BLOCKED": "Successfully Blocked", "PLEASE_ENTER_ALREADY_SCNNED_VAL": "Please enter a value that has already been scanned"}, "MEI_BLOCKING": {"ORDER_ORIGIN": "Order Origin", "ORDER_ID": "Order ID", "SHIPMENT_ID": "Shipment ID", "CHANNEL": "Channel", "SCAN_AGAIN_CMPLT_EDGING": "<PERSON><PERSON> Again to Complete Edging", "SCAN_NEXT_BRCODE_STRT_EDGING": "Scan Next Barcode to Start Edging", "SRCH_PLACEHOLDER": "Scan Fitting ID / Tray ID", "SCAN_BRCODE_TO_STRT_EDGING": "Scan Barcode to Start Edging", "EDGING_CMPLTED_SUCCESSFULLY": "Edging Completed Successfully!", "RIGHT": "RIGHT", "LEFT": "LEFT", "LENS": "LENS", "SCAN_BARCODE_TO_MEI_BLOCKING": "Scan Barcode to MEI Block", "SCAN_AGAIN_BLOCK_BLOCKING": "<PERSON><PERSON> again to complete the blocking", "SUCCESSFULLY_BLOCKED": "Successfully Blocked", "PLEASE_ENTER_ALREADY_SCNNED_VAL": "Please enter a value that has already been scanned"}, "INVENTORY_DASHBOARD": {"FACILITY_CODE": "Facility Code", "AVAILABLE": "Available", "ALLOCATED": "Allocated", "PENDING_PUTAWAY": "Pending Putaway", "OTHER": "Other", "NOT_FOUND": "Not Found", "TOTAL": "Total", "SELECT_INVENTORY_TYPE": "Select Inventory Type", "PID": "PID", "IMG": "img", "SUPPORTED_POWER_RANGE": "Supported Power Range", "SPH": "SPH", "CYL": "CYL", "AXIS": "AXIS", "PD": "PD", "PRODUCT_TYPE": "Product Type", "BRAND": "Brand", "FRAME_TYPE": "Frame Type", "MODEL_NO": "Model No", "MATERIAL": "Material", "BASE_CURVE": "Base Curve", "PACKAGING": "Packaging", "DIAMETER": "Diameter", "INDEX": "Index", "COATING": "Coating", "WATER_CONTENT": "Water Content", "GENDER": "Gender", "WEIGHT": "Weight", "FRAME_WIDTH": "<PERSON><PERSON>", "HEIGHT": "Height", "COLLECTION": "Collection", "PRESCREPTION_TYPE": "Prescreption Type", "FULFILLABLE": "Fulfillable", "UNFULFILLABLE": "Unfulfillable", "ORDER_TYPE": "Order Type", "BARCODE": "Barcode", "CONDITION": "Condition", "AVAILABILITY": "Availability", "LOCATION": "Location", "STATUS": "Status", "PARENT_LOCATION": "Parent Location", "ACTIVITY_ID": "Activity ID", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By"}, "TRAY_RELEASE": {"HISTORY": "HISTORY", "LISTING": "LISTING", "SEARCH_PLACE_HOLDER": "Search ..", "RELEASE_TRAYS": "Release Trays", "RELEASE_TRAY": "Release Tray", "TRAY_ID": "Tray Id", "STORE_CODE": "Store Code", "SOFT_COURIER": "Soft Courier", "PRIORITY": "Priority", "NO_OF_SIPMENTS": "No of shipments", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "SELECTED_TRAY_ID": "Selected Tray ID", "REMOVE_TRAY": "Remove If you don't want to release tray", "CONFIRM": "CONFIRM", "CANCEL": "CANCEL", "SHIPPING_PKG_ID": "Shipping Package Id", "STATUS": "Status", "TRAY_NOT_RELEASED": "Trays not released", "SELECTED_TRAYS_SUCSFULLY_RELEASED": "Selected Trays successfully released", "CANNOT_RELEASE_MORE_THAN_FIFTEEN": "You cannot release more than 15 trays at a time."}, "REACTIVE_TRANSFER": {"WANT_TO_INITIATE_TRANSFER": "Are you sure you want to initiate transfer?", "ITEMS_SELECTED": "Items selected", "CONFIRM_TRANSFER": "Confirm Transfer", "INCREMENT_ID": "Increment ID", "SHIPPING_PACKAGE_ID": "Shipping Package ID", "PRODUCT_ID": "Product ID", "UW_ITEM_ID": "uw Item ID", "SOURCE_WAREHOUSE": "Source Warehouse", "AVL_QTY": "Available Qty.", "CATEGORY": "Category", "CREATE_TRANSFER": "Create Transfer", "CANCEL": "Cancel", "PENDING_TRANSFERS": "Pending Transfers", "OPEN_TRANSFER": "Open transfer", "CLOSE_TRANSFER": "Close transfer", "SEARCH_ORDER_ID": "Search Order ID", "RESET": "RESET", "EXPORT": "EXPORT", "ORDER_DATE": "Order Date", "UPDATED_AT": "Updated At", "TRANSFER_ITEM_ID": "Transfer Item ID", "STATUS": "Status"}, "START_RETIRE": {"SEARCH_BARCODE_HERE": "Search Barcode here..", "SINGLE_PID": "SINGLE PID", "BULK_UPLOAD": "BULK UPLOAD", "SACN_VALID": "Please scan valid", "SINGLE_PID_UPLOAD": "Single PID Upload", "SCAN_BARCODE": "Scan Barcode", "SOURCE_PID": "Source PID", "DESTINATION_PID": "Destination PID", "SCAN_DESTINATION_PID": "Scan Destination PID", "START_RETIRE_ID": "Start Retire ID", "BARCODE": "Barcode", "UNIT_PRICE": "Unit Price", "DATE": "Date", "REQESTED_BY": "Requested by", "STATUS": "Status", "SELECT_CSV_FILE_IMPORT_PID_DETLS": "Please select the CSV file to import PID details", "INVALID_CONTENT": "Invalid Content"}, "COURIER_CUTOFF": {"SEARCH_PLACE_HOLDER": "Search Manifest Shipping Method", "UPLOAD_CUTOFF": "upload cutoff", "MANIFEST_SHIPPING_METHOD": "Manifest Shipping Method", "DAYS_OF_WEEK": "Days of Week", "CUTOFF_TIME": "Cutoff Time", "BUFFER_TIME": "Buffer Time", "LAST_CUTOFF_TIME": "Last Cutoff Time", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "CREATED_BY": "Created By", "UPDATED_BY": "Updated By", "CUTOFF_TIME_STATUS": "Cutoff Time Status", "FACILITY": "Facility", "INVALID_CONTENT": "Invalid Content", "MODAL_SUBTITLE": "Please select the CSV file to import Courier Cutoff details", "SAMPLE_FILE_NAME": "manifest_shipping_method"}, "FILE_UPLOAD": {"CSV_FILE_IMPORT": "CSV File Import", "IMPORT_PROCESSED_SUCCESSFULLY": "Import Processed Successfully", "FILE_IMPORTED_SUCCESSFULLY": "The file has been imported successfully", "PROBLEMS_WITH_CSV_FILE": "Problems with CSV File", "IMPORT_UNSUCCESSFUL": "The import was unsuccessful. Please download the error report below", "UPLOAD_FILE": "Uploading File", "WAIT_WHILE_UPLOAD_UR_CSV_FILE": "Please wait while we upload your CSV file", "SAMPLE_CSV_FILE": "Sample CSV file", "DOWNLOAD": "Download", "UPLOAD_FILE_UPPERCASE": "UPLOAD FILE", "CANCEL": "CANCEL", "IMPORT": "IMPORT", "OK": "OK", "DOWNLOAD_ERR_REPORT": "Download Error Report"}, "DEBIT_NOTE": {"SEARCH_DEBIT_NOTES": "Search Debit Notes here...", "DEBIT_NOTE_NO": "Debit Note No.", "CREATED_ON": "Created On", "NEXS_INV_NO": "Nexs Inv. No", "VENDOR_INV_NO": "Vendor Inv. No", "INVOICE_DATE": "Invoice Date", "PO_NO": "PO No.", "VENDOR_NAME": "Vendor Name", "CREATED_BY": "Created By", "AMOUNT": "Amount", "RESET": "Reset", "EXPORT": "EXPORT", "DEBIT_NOTE": "Debit Note", "DEBIT_NOTE_DATE": "Debit Note Date", "VENDOR": "<PERSON><PERSON><PERSON>", "PO": "PO", "TOTAL": "Total", "PRINT": "Print", "CTRL_P": "Ctrl+P", "QTY": "Qty", "OUT_OF_PO": "Out of PO", "PRICE_MISMATCH": "Price Mismatch", "QC_FAIL": "QC Fail", "OTHER_RETURN": "Other Return", "TOTAL_WITH_TAX": "Total with Tax", "STATUS": "Status", "TYPE": "Type", "VENDOR_INVOICE_NO": "Vendor Invoice No.", "NXS_INVOICE": "Nexs Invoice", "VENDOR_INV_DATE": "Vendor Invoice Date", "PURCHASE_ORDER": "Purchase Order", "VENDOR_CODE": "Vendor Code", "TOTAL_QTY": "Total Quantity", "TOTAL_PRICE": "Total Price", "TOTAL_ORDER": "Total Order", "ACCEPTED": "Accepted", "REJECTED": "Rejected", "PENDING": "Pending", "DETAILS": "Details", "PID": "PID", "ITEM_DETAILS": "<PERSON><PERSON>", "QUANTITY": "Quantity", "UNIT_PRICE": "Unit Price", "SHORT_QTY": "Short Qty", "OUT_OF_PO_QTY": "Out of PO Qty", "BULK_QC_FAIL_QTY": "Bulk QC Fail Qty ", "GRN_REJECTED": "GRN Rejected", "QC_FAIL_REASON": "QC Fail Reason", "CGST": "CGST", "IGST": "IGST", "SGST": "SGST", "UGST": "UGST", "CESS": "Cess", "SUB_TOTAL": "Subtotal", "DELIVERY_CHALLAN": "Delivery <PERSON>"}, "TRANSFER": {"CREATE_TRANSFER": "Create Transfer", "TRANSFER_DESTINATION": "Transfer Destination", "ADD_PID": "ADD PID", "IMPORT_CSV": "IMPORT CSV", "RECEIVE_TRANSFER": "Receive Transfer", "SCAN_TRANSFER_CODE": "Scan Transfer Code", "SELECT_DESTINATION": "Select Destination", "TRANSFER_SOURCE": "Transfer Source", "TRANSFERS_DETAILS": "Transfers Details", "SEARCH_TRANSFERS_HERE": "Search Transfers here..", "ALL_DESTINATIONS": "All Destinations", "RESET": "RESET", "TRANSFER_ID": "Transfer ID", "AWB_NUMBER": "AWB Number", "PUTAWAY_NUMBER": "Putaway Number", "PID_COUNT": "PID Count", "NO_OF_ITEMS": "No.of Items", "SOURCE": "Source", "DESTINATION": "Destination", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "CREATED_BY": "Created By", "STATUS": "Status", "SEND": "Send", "SOURCE_LOWER": "source", "SOURCE_TRANSFER_CODE": "Source Transfer Code", "PUTAWAY_NO": "Putaway Number", "NO_OF_PIDS": "No. of PIDs", "NO_OF_ITEMS_DETAILS": "No. of ITEMS", "STATUS_DETAILS": "status", "CLOSE_TRANSFER": "CLOSE TRANSFER", "SEARCH": "Search", "GENERATE_AWB": "Generate AWB", "EDIT_TRANSFER": "Edit TRANSFER", "START_SCANNING": "START SCANNING", "CANCEL": "CANCEL", "DONE": "DONE", "PID": "PID", "PID_DESCRIPTION": "PID Description", "QUANTITY": "Quantity", "AVAILABLE_QTY": "Available Quantity", "PID_ALREADY_ADDED": "PID Already Added !", "PLEASE_CHOOSE_DIFF_PID": "Please choose different PID.", "ENTER_PID": "ENTER PID", "PLEASE_ENTER_QTY": "Please Enter Quantity", "PRODUCT_ID": "Product ID", "REQ_QTY": "<PERSON><PERSON><PERSON>.", "QTY_SCANNED": "<PERSON><PERSON><PERSON>", "AVAIL_QTY": "Avail. Qty.", "UPDATED_ON": "Updated On", "PLEASE_SELECT_FACILITY": "Please select facility", "TRANSFER_ID_SCAN": "Transfer Id", "SCANNING": "Scanning", "PID_SCANNED": "PID SCANNED", "TOTAL_ITEMS_ADDED": "Total items Added", "SCANNING_COMPLETE": "SCANNING COMPLETE", "PID_SCAN": "PID:", "SCANNED": "Scanned", "PID_DESC": "PID Description", "AVL_QTY": "Available Quantity", "ENTER_QUANTITY": "Please Enter Quantity", "ENTER_PID_FIRST": "Please Enter PID First", "NO_DATA": "No Data...", "SELECT_COURIER_TO_CREATE_AWB": "Please select the courier to create AWB", "SHIPPING_PROVIDER": "Shipping Provider", "CREATE": "create", "CREATED": "CREATED", "IN_PICKING": "IN_PICKING", "DISPATCHED": "DISPATCHED", "RECEIVED": "RECEIVED", "EXPORT": "EXPORT", "TOTAL_ITEMS_SCANNED": "Total items scanned", "NO_OF_BOXES_SCANNED": "No. of boxes scanned", "REPLACE": "Replace", "PUTAWAY_MODAL_TITLE": "Please click on respective Putaway Number to view details"}, "PRODUCTS_SCANNER_TEXTFEILD": {"ERROR": "Error!", "BOX_ALREADY_SCANNED": "Box(%dynamicValue%) already scanned", "ONLY_BOX_SCAN_ALLOWED": "Only box scanning is allowed", "SCAN_BOX_BFR_SCNG_ITEM": "Please scan box before scanning the item:", "CANT_ADD_BOXES_FR_TRANSFER": "You can't add boxes for this transfer:", "DUPLICATE_BARCODE_SCANNED": "Duplicate Barcode scanned", "SCAN_BARCODE": "Scan Barcode", "ENTER_DETAILS": "Enter Details", "SCAN_BOXES_ONLY": "Scan Boxes only"}, "SCANNED_ITEMS": {"SCANNED": "Scanned", "SUCCESS": "Success", "FAILED": "Failed"}, "FILE_UPLOAD_CONFIRMATION": {"FILE_UPLOAD": "File Upload", "ERR_IN_CSV_FILE": "Error In CSV file. Please try Again", "PRODUCT_ID_IN_CSV_FILE_MSG": "Change %dynamicValue% to product_id in CSV file", "QTY_IN_CSV_FILE_MSG": "Change %dynamicValue% to quantity in CSV file", "INVALID_PRODUCT_ID_OR_QTY": "Invalid Product Id/Quantity", "MODAL_SUBTITLE": "Please wait while we upload your CSV file", "UPLOAD_CSV": "Upload Csv", "CANCEL": "CANCEL", "IMPORT": "IMPORT"}, "CREATE_TRANSFER_CONFIRMATION": {"PID": "PID", "QUANTITY": "Quantity", "MODAL_TITLE": "Create Transfer Request", "MODAL_SUBTITLE": "Kindly confirm list of inventory that needs to be transferred", "CANCEL": "CANCEL", "CREATE_TRANSFER_REQUEST": "CREATE TRANSFER REQUEST"}, "CSV_IMPORT_MODAL": {"CSV_FILE_IMPORT": "CSV File Import", "SELECT_CSV_FILE_IMPORT_PID_DTLS": "Please select the CSV file to import PID details", "WILL_OVERWRITE_EXISTING_ROWS_IN_PO": "This will overwrite existing rows in PO", "CANCEL": "CANCEL", "UPLOAD_FILE": "UPLOAD FILE", "SAMPLE_CSV_FILE": "Sample CSV file", "DOWNLOAD": "Download", "PID_SIX": "PID (06)", "QUANTITY": "Quantity (304)"}, "LAYOUT_PAGE": {"ALL": "ALL", "ACTIVE": "ACTIVE", "LINKED": "LINKED", "DRAFT": "DRAFT", "SEARCH_LAYOUT": "Search Layout", "CREATE_NEW_LAYOUT_SCHEME": "CREATE NEW LAYOUT SCHEMA", "NAME": "Name", "LAST_MODIFIED": "Last Modified", "STATUS": "Status", "DESCRIPTION": "Description", "NAME_YOUR_LAYOUT_SCHEMA": "Name Your New Layout Schema", "GIVE_YOUR_LAYOUT_SCHEMA": "Give your Layout <PERSON><PERSON>a a name to start with", "CONTINUE": "CONTINUE", "LAYOUT_NAME_NOT_BE": "Layout name should not be", "LAST_MODIFIED_UPPERCASE": "LAST MODIFIED", "CLOSE_TAB": "CLOSE Tab", "CANCEL": "CANCEL", "SAVE_AS_DRAFT": "SAVE AS DRAFT", "SUBMIT": "SUBMIT", "NEW_PHSICAL_ENTITY": "NEW PHSICAL ENTITY", "DISCOVER_TYPES_OF_REGEX": "Discover the types of Regular Expressions", "READ_MORE": "READ MORE", "DEFINE_PHYSICAL_LAYER_ENTITY": "Define Phsyical Layer Entity", "LAYER_NAME": "Layer Name", "ID_FORMAT": "Id format", "CUSTOM_FIELDS": "Custom Fields", "CUSTOM_FIELD_NAME": "Custom Field Name", "CUSTOM_FIELD_ID_FORMAT": "Custom Field Id Format", "MARK_AS_MANDATORY": "<PERSON> as <PERSON><PERSON><PERSON>", "ADD_NEW_CUSTOM_FIELD": "Add new custom field", "DONE": "DONE", "ADD": "ADD", "REGULAR_EXPRESSION": "Regular Expression", "FOR_MORE_INFORMATION": "For more information", "VISIT": "visit", "TARGET_FORMAT": "Target Format", "EXPRESSION": "Expression", "MATCHES": "Matches", "DOSENOT_MATCH": "Doesn’t Match", "FACILITY": "Facility", "ADD_PHYSICAL_ENTITY": "ADD PHYSICAL ENTITY", "NO_DATA_EXIST_FOR_LAYOUT_SCMA": "No Data exist for layout schema", "PHYSICAL_ENTITY": "PHYSICAL ENTITY", "COPY_CODE": "Copy Code", "CODE_COPIED": "Code Copied"}, "FACILITY": {"SEARCH_FACILITY": "Search Facility", "FETCHING_UPDATED_DATA": "Fetching Updated Data....", "ACTIVE": "Active", "ALL": "All", "CREATE_NEW_FACILITY": "Create New Facility", "CREATE_A_NEW_FACILITY": "Create a New Facility", "FACILITY_CODE": "Facility Code", "SAVE_FACILITY_AS_DRAFT": "Save Facility as Draft", "SAVE_AS_DRAFT": "Save as draft", "LINK_LAYOUT_SCHEMA_TEMPLATE": "Link Layout <PERSON><PERSON>a <PERSON>late", "GENERAL_DETAILS": "General Details", "TAX_DETAILS": "Tax Details", "CONTACT_DETAILS": "Contact Details", "LAYOUT_SCHEMA": "Layout <PERSON>hema", "ADD_LOGO": "Add Logo", "DISPLAY_NAME": "Display Name", "LEGAL_NAME": "Legal Name", "FACILITY_TYPE": "Facility Type", "SELECT_FACILITY": "Select Facility", "BILLING_ADDRESS": "Billing Address", "SELECT_COUNTRY": "Select Country", "PINCODE": "Pin Code", "CITY": "City", "STATE": "state", "ADDRESS_LINE_ONE": "Address Line 1", "ADDRESS_LINE_TWO": "Address Line 2", "SHIPPING_ADDRESS_SAME_AS_BILLNG_ADDRESS": "Shipping Address same as Billing Address", "SHIPPING_ADDRESS": "Shipping Address", "REGISTER_NAME": "Registered Name", "GST_NO": "GST No", "PAN_NO": "PAN No", "TIN_NO": "TIN No", "PRIMARY_CONTACT": "Primary Contact", "NAME": "Name", "PHONE_NUMBER": "Phone Number", "MOBILE_NUMBER": "Mobile Number", "EMAIL_ID": "Email ID", "PLEASE_ENTER_VALID_EMAIL": "Please enter a valid Email", "SECONDARY_CONTACT": "Secondary Contact", "CREATE_NEW_LAYOUT_SCHEME": "CREATE NEW LAYOUT SCHEMA", "SELECT_FROM_EXISTING_SCHEMA": "Select from Existing <PERSON><PERSON><PERSON>", "VIEW": "VIEW", "UNKNOWN_STEP": "Unknown step", "BACK": "Back", "SAVE_AS_DRAFT_UPPERCASE": "SAVE AS DRAFT", "CREATE": "Create", "NEXT": "NEXT", "RESET": "RESET", "CODE": "Code", "TYPE": "Type", "STATUS": "Status", "PARTY_NAME": "Party Name", "UPDATED_AT": "Updated At", "CREATED_AT": "Created At", "DRAFT_NAME": "Draft Name", "SAVE_THE_CHANGES": "Save the changes", "SAVE_THE_CHANGES_WARNING_MSG": "This page contains unsaved changes that will be lost if you exit without saving", "DISCARD": "Discard", "SAVE_AND_PROCEED": "SAVE & PROCEED", "ACTIVE_UPPERCASE": "ACTIVE", "CREATED": "CREATED", "LAST_MODIFIED": "LAST MODIFIED", "ACTIVATE_FACILITY": "Activate Facility", "SAVE": "Save", "FACILITY_ENTITIES": "Facility Entities", "LAYOUT_SCHEMA_EDITED_WARNING": "Only Layout Schema which are linked to a facility can be edited.", "FACILITY": "FACILITY", "LINKED_TO": "LINKED TO", "LAYOUT_SCHEMA_TEMPLATE": "LAYOUT SCHEMA TEMPLATE", "NAME_YOUR_LAYOUT_SCHEMA": "Name Your Layout Schema", "LINK_AND_PROCEED": "LINK & PROCEED", "LAYOUT_SCHEMA_NOT_LINKED": "Layout <PERSON><PERSON>a Not Linked", "NEW_FACILITY_ENTITY": "New Facility Entity", "SEARCH_FACILITY_ENTITIES": "Search Facility Entities", "EXPORT_AS_CSV": "EXPORT as csv", "BULK_EDIT": "Bulk edit", "PRINT_BARCODE": "Print Barcode", "ADD_NEW_ENTITY": "Add New Entity", "GENERATE_SAMPLE_UPLOA_FILE": "Generate Sample Upload File", "ENTITY_TYPE": "Entity Type", "SELECT_FROM_LIST_OF_ENTITY_TYPE": "Select from List of Entity Types", "PARENT_ENTITY": "Parent Entity", "DOWNLOAD_FILE": "DOWNLOAD FILE", "UPLOAD_ENTITY_FILE": "Upload Entity File", "UPLOAD_FILE": "UPLOAD FILE", "FORMATS_WHICH_ARE_SUPPORTED": "All .csv, .xlsx, and .xls file types are supported", "ENTITY": "entity", "DOWNLOAD_ERR_MESSAGE": "Download Error Message", "ENTITY_NAME": "Entity Name", "COUNT": "Count", "INVENTORY": "Inventory", "FILE_IS_SUCCESSFULLY_UPLOADED": "File is successfully uploaded", "ERROR": "Error", "PLEASE_CHECK_DOWNLOAD_FILE": "Please check download file", "BULK_EDIT_MODAL": "Bulk Edit", "ID": "ID", "BARCODE": "Barcode", "PARENT_ID": "Parent Id", "ACTIONS": "Actions", "EDIT": "Edit", "SUCCESS": "Success", "MANDATORY_FIELDS": "Mandatory fields", "CUSTOM_FIELDS": "Custom Fields", "CUSTOM_FIELD_NAME": "Custom Field Name", "VALUE": "value", "SAVE_CHANGES": "SAVE CHANGES", "ENTITY_SUCCESSFULLY_DELETED": "Entity is successfully deleted", "LEGAL_OWNER": "Legal Owner", "SELECT_LEGAL_OWNER": "Select Legal Owner"}, "EDGING_QC": {"ORDER_ORIGIN": "Order Origin", "ORDER_ID": "Order ID", "SHIPMENT_ID": "Shipment ID", "CHANNEL": "Channel", "SCAN_AGAIN_COMPLETE_EDGING": "<PERSON><PERSON> Again to Complete Edging QC", "SCAN_NEXT_BRCODE_STRT_EDGING": "Scan Next Barcode to Start Edging", "SRCH_PLACEHOLDER": "Scan Tray ID / Barcode", "SCAN_BRCODE_TO_STRT_EDGING": "Scan Barcode to Start Edging QC", "EDGING_CMPLTED_SUCCESSFULLY": "Edging Completed Successfully!", "INVALID_BARCODE_SCANNED": "Invalid barcode scanned", "PARTIAL_OPERATIONS_CANT_BE_PERFORMED": "Partial Operations can't be performed either mark all FAIL or UNHOLD"}, "CONSTRAINTS": {"CREATE_NEW_RULE": "Create New Rule", "ENABLED_ONLY": "ENABLED Only", "SEARCH": "Search", "STATUS": "Status", "PREIORITY": "Priority", "RULE_ID": "Rule ID", "RULE_NAME": "Rule Name", "CONDITION": "Condition", "FACILITY_CODE": "Facility Code", "CREATED_ON": "Created On", "UPDATED_ON": "Updated On", "ENABLED": "Enabled", "DISABLE": "Disable", "PENDING": "Pending", "DRAFT": "Draft", "ENABLE": "Enable", "CHANGE_PREIORITY": "Change Priority", "EDIT_RULE": "Edit Rule", "DESTINATION_TYPE": "Destination Type", "DESTINATION": "Destination", "SCAN_METHOD": "<PERSON>an Method", "LOCATION_SCAN": "Location Scan", "ITEM_SCAN_TYPE": "<PERSON><PERSON>", "APPROVAL_NEEDED": "Approval needed", "GENERAL_DETAILS": "General Details", "FACILITIES_LINKED": "Facilities Linked", "CREATE_RULE": "Create Rule", "VALIDATE": "Validate", "EVALUATION_METHOD": "Evaluation Method", "SEQUENTIAL": "Sequential", "ANY": "Any", "SELECT_DROPLOCATION": "Select Droplocation", "ADD_CONDITION": "ADD CONDITION", "DELETE": "Delete", "SELECT_RULE_TYPE": "Select Rule Type", "DESCRIPTION": "Description", "ENTER_FACILITY": "Enter Facility", "ADD_NEW_RULE": "Add New Rule", "CANCEL": "CANCEL", "SAVE_AS_DRAFT": "SAVE AS DRAFT", "DUPLICATE": "DUPLICATE", "SUBMIT": "SUBMIT"}, "ADD_INVENTORY": {"INVALID_CONTENT": "Invalid Content", "PLEASE_SELECT_THE_CSV_FILE_TO_IMPORT_PID_DETAILS": "Please select the CSV file to import PID details", "SEARCH_PID_HERE": "Search PID here...", "SINGLE_PID": "SINGLE PID", "BULK_UPLOAD": "BULK UPLOAD", "PLEASE_SCAN_VALID": "Please scan valid", "SINGLE_PID_UPLOAD": "Single PID Upload", "SCAN_BARCODE": "Scan Barcode", "BARCODE": "Barcode", "DESTINATION_PID": "Destination PID", "PID": "PID", "SCAN_DESTINATION_PID": "Scan Destination PID", "ADD_INVENTORY_ID": "Add Inventory ID", "PRODUCT_ID": "Product ID", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "REQUESTED_BY": "Requested by", "STATUS": "Status", "FAILED_REASON": "Failed Reason"}, "SUPERVISOR_PICKING": {"ASSIGNMENT": "ASSIGNMENT", "SKIPPED_LIST": "SKIPPED LIST", "DISCARD": "DISCARD", "MOVE_TO_DISCARD_UPLOAD": "Move to Discard Upload", "MOVE_TO_MANUAL_UPLOAD": "Move to Manual Upload", "MOVE_TO_MANUAL_FROM_ASRS": "MOVE TO MANUAL", "PICKER_ALLOCATION": "PICKER ALLOCATION", "PICKING_CONFIG": "PICKING CONFIG.", "SEARCH_BY_NAME": "Search By Name ..", "ASSIGN": "Assign", "VIEW_BY": "View By", "SELECT": "Select", "NAME": "Name", "#LOCATIONS": "#Locations", "CATEGORY": "Category", "LOCATIONS": "Locations", "LOCATION": "Location", "ADD": "Add", "ASSIGN_CATEGORY": "Assign Category", "ASSIGN_LOCATION": "Assign Location", "SELECTED": "Selected", "SELECT_LOCATION": "Select location", "ALL": "All", "CLEAR_ALL": "Clear All", "CUT_OFF_HOURS": "Cut Off hours", "CANCEL": "Cancel", "SAVE": "Save", "SELECT_CATEGORY": "Select Category", "SKIPPED": "SKIPPED", "HOLD": "HOLD", "ASSIGN_PICKER": "Assign <PERSON>", "ARE_YOU_SURE_WANT_TO_ASSIGN": "Are you sure you want to assign", "THE_FOLLOWING_PIDS": "the following PIDs?", "YES_ASSIGN": "YES, ASSIGN", "SEARCH": "Search ..", "NOT_FOUND": "Not Found", "RESET": "RESET", "PID": "PID", "ORDER_ID": "Order ID", "WAREHOUSE_QUANTITY": "Warehouse Quantity", "BIN_QUANTITY": "Bin Quantity", "SKIPPED_BY": "Skipped By", "SKIPPED_DATE": "Skipped Date", "SKIPPED_REASON": "Skipped Reason", "CHANNEL": "Channel", "STOCKOUT": "StockOut", "SCAN_ITEM_BARCODE": "Scan Item Barcode", "SELECT_RE_PICK_STATUS": "Select RePick Status", "SORT_BY_DATE": "Sort By Date", "EXPORT": "Export", "UPLOAD_FAST_ZONE_PID": "Upload Fast Zone PID's"}, "JIT_ORDER_SEARCH": {"SCAN_FITTING_ID_TRAY_ID": "Scan Fitting ID / Tray ID", "UW_ITEM_ID": "UW Item ID", "PID": "PID", "BLANK_PID": "Blank PID", "BARCODE": "Barcode", "STATUS": "Status", "RXU_STATUS": "RXU Status", "ORDER_TYPE": "Order Type", "LENS_TYPE": "Lens Type", "JIT_STATUS": "JIT Status", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "FITTING_ID": "Fitting ID", "SHIPPING_PACKAGE_ID": "Shipping Package ID", "MARK_DAMAGED": "<PERSON>", "PRODUCTION_DONE": "Production Done", "ARE_YOU_SURE_YOU_WANT_TO_MARK_ITEM": "Are you sure you want to mark the item(s)", "AS_DAMAGED": "as damaged?", "SUBMIT": "Submit", "CANCEL": "Cancel", "SELECT": "Select"}, "STORE_PACKING": {"SHIPMENT_ID": "Shipment ID", "STATUS": "Status", "AWB_NO": "AWB No", "SCAN_TIME": "<PERSON>an <PERSON>", "ORDER_DATE": "Order Date", "LAST_UPDATED": "Last Updated", "ACTIONS": "Actions", "SHIPMENT_CHANGE_TITLE": "Shipment belongs to %dynamicValue1%. Adding it here will change the courier to %dynamicValue2% .", "DO_YOU_STILL_WANT_TO_ADD_SHIPMENT_TO_THE_CURRENT_STORE_PACKING_LIST": "Do you still want to add shipment to the current store packing list?", "ADDRESS": "ADDRESS", "SCAN_SHIPMENT_ID": "Scan Shipment ID", "ALLOW_MULTI_SELECT": "Allow Multi-Select", "ADD_ANY_WAY": "Add Any Way", "CANCEL": "Cancel", "SHIPMENTS_SCANNED": "SHIPMENTS SCANNED", "ORDER_ORIGIN": "Order Origin", "STORE_NAME": "Store Name", "COURIER": "Courier", "CHANGE": "Change", "TO_PRINT_SHIPPING_LABEL_SCAN_ALL_NON_CANCELLED_SHIPMENT": "To print shipping label, Please scan all non-cancelled shipments", "PRINT_SHIPPING_LABEL": "PRINT SHIPPING LABEL", "MARK_ALL_PACKED": "Mark All Packed?", "ARE_YOU_SURE_YOU_WANT_TO_MARK_ALL_SHIPMENT_PACKED": "Are you sure you want to mark all shipments as packed?", "YES_PRINT_LABEL": "Yes, Print Label", "CANCEL_UPPERCASE": "CANCEL", "COURIER_CHANGE_SUBTITLE": "%dynamicValue1% Shipments have %dynamicValue2% as Assigned Courier Partner. This action will change Courier for all Shipments. Proceed anyway?", "PROCEED_ANYWAY": "as Assigned Courier Partner. This action will change Courier for all Shipments. Proceed anyway?", "CHANGE_COURIER_PARTNER": "Change Courier Partner", "YES": "Yes", "NO": "No"}, "CONSOLIDATIONS_SUPERVISOR_PANEL": {"SELECT_TYPE": "Select Type", "SEARCH": "Search", "PIGEON_HOLE_NO": "Pigeon Hole No", "STATUS": "Status", "SHIPMENT_ID": "Shipment ID", "ITEMS_ADDED": "Items Added", "FITTING_ID": "Fitting ID", "STORE_CODE": "Store Code", "IMAGE": "Image", "PID": "PID", "BARCODE": "Barcode", "TRAY_NUMBER": "Tray Number", "SHIPPING_ID": "Shipping Id"}, "PRICE_CONTROL": {"PLEASE_ENTER_VALID_EMAIL": "Please Enter valid Email", "ADD_LEVELS": "Add levels", "MANAGE_ROLES_AND_PERMISSIONS": "Manage Roles & Permissions", "APPLY": "Apply", "ROLES": "Roles", "CLASSIFICATION": "Classification", "BRAND": "Brand", "LEGAL_ENTITY": "Legal Entity", "CONFIRMER": "Confirmer", "APPROVER": "Approver", "LEGAL_OWNER": "Legal Owner", "ACTION": "Action", "EMP_CODE": "EMP Code", "EMP_NAME": "EMP Name", "EMP_E_MAIL": "EMP E-Mail", "ROLE": "Role", "UPDATED_AT": "Updated At"}, "INVENTORY": {"PID": "PID", "TOTAL_INVENTORY": "Total Inventory", "GRN": "GRN", "ALLOCATED": "Allocated", "AVAILABLE": "Available", "RESERVED": "Reserved", "BLOCKED": "Blocked", "PENDING_PUTAWAY": "Pending PutAway", "NOT_FOUND": "Not Found", "SUPPORTED_POWER_RANGE": "Supported Power Range", "SPH": "SPH", "CYL": "CYL", "AXIS": "AXIS", "PD": "PD", "PRODUCT_TYPE": "Product Type", "BRAND": "Brand", "FRAME_TYPE": "Frame Type", "MODEL_NO": "Model No", "MATERIAL": "Material", "BASE_CURVE": "Base Curve", "PACKAGING": "Packaging", "DIAMETER": "Diameter", "INDEX": "Index", "COATING": "Coating", "WATER_CONTENT": "Water Content", "GENDER": "Gender", "WEIGHT": "Weight", "FRAME_WIDTH": "<PERSON><PERSON>", "HEIGHT": "Height", "COLLECTION": "Collection", "PRESCREPTION_TYPE": "Prescreption Type", "BARCODE": "Barcode", "AVAILABILITY": "Availability", "STATUS": "Status", "CONDITION": "Condition", "LOCATION": "Location", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "SELECT_INVENTORY_TYPE": "Select Inventory Type", "GOOD": "Good", "BAD": "Bad", "SEARCH_WITH_BARCODE": "Search with Barcode", "EXPORT_TABLE": "EXPORT TABLE"}, "BARCODE_HISTORY": {"BARCODE": "Barcode", "CURRENT_STATUS": "Current Status", "PID": "PID", "SRCH_PLACEHOLDER": "Search ..", "RESET": "Reset", "OPERATION": "Operation", "CONDITION": "Condition", "AVAILABILITY": "Availability", "ACTIVITY_ID": "Activity ID", "STATUS": "Status", "LOCATION": "Location", "FACILITY_CODE": "Facility Code", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "PARENT_LOCATION": "Parent Location", "OPERATION_ID": "Operation ID", "MARK_AS_BAD_INVENTORY": "<PERSON> as <PERSON> Inventory"}, "ORDER_DETAILS": {"ORDER": "Order", "ORDER_ORIGIN": "Order Origin", "SOURCE": "Source", "CATEGORY": "Category", "DESTINATION_TYPE": "Destination Type", "ORDER_DATE": "Order Date", "ITEMS_DELIVERED": "Items Delivered", "PRIORITY": "Priority", "UPDATED_AT": "Updated At", "STATUS": "Status", "Customer": "Customer", "Payment": "Payment", "Address & Refund": "Address & Refund", "Store": "Store", "Customer Name": "Customer Name", "Email ID": "Email ID", "Mobile Number": "Mobile Number", "LK Customer ID": "LK Customer ID", "Order Amount": "Order Amount", "Pay. Method": "Pay. Method", "Amount": "Amount", "Billing Address": "Billing Address", "Shipping Address": "Shipping Address", "Franchisee Code": "Franchisee Code", "CS Comm.": "CS Comm.", "Merchant Key": "Merchant Key", "Customer Comm": "Customer <PERSON>mm", "Store Id": "Store Id", "VSM Status": "VSM Status", "Sale Order Self": "Sale Order Self", "Team": "Team", "OVERVIEW": "OVERVIEW", "ITEMS": "ITEMS", "SHIPMENTS": "SHIPMENTS", "INVOICES": "INVOICES", "VSM_NOT_REACHABLE": "vsm not reachable. pls try after sometime", "PRINTING_INVOICE": "Printing Invoice", "PRINTING_SHIPPING_LABEL": "Printing Shipping Label", "SOMETHING_WENT_WRONG": "something went wrong", "SHIPMENT_ID": "Shipment ID", "AWB_NUMBER": "AWB No", "FACILITY": "Facility", "NO_OF_ITEMS": "No of Items", "NEXS_STATUS": "NexS Status", "VSM_STATUS": "VSM Status", "ACTIONS": "Actions", "PID": "PID", "ORDER_ITEM_ID": "Order Item Id", "LOCATION": "Location", "BARCODE": "Barcode", "PRINT_SHIPPING_LABEL": "Print Shipping Label", "PRINT_INVOICE": "Print Invoice", "UPDATED_BY": "Updated By", "ACTIVE_ID": "Activity Id", "DETAILS": "Details", "ACTION": "Action", "VIEW_INVOICE": "VIEW INVOICE", "INVOICE_NUMBER": "Invoice Number", "CREATED_AT": "Created At", "SP_WITHOUT_TAX": "SP w/o Tax", "DISCOUNT": "Discount", "TAX": "Tax", "TOTAL": "Total", "TIMING_ARE_SHOWN_IN_LOCAL_TIMEZONE": "Timings are shown in Local timezone", "FTAT": "FTAT", "CHANNEL": "Channel", "search": "Search ..", "PLEASE_CONTACT_SUPERVISOR": "Please Contact Supervisor", "MOVE_TO_JIT": "Move To JIT"}, "INVOICE_POSTING": {"INVOICE_NO": "Invoice Number", "PO_NO": "PO Number", "INVOICE_QUANTITY": "Invoice Quantity", "INVOICE_AMOUNT": "Invoice Amount", "BOE": "BOE", "CREATED_AT": "Created At", "CREATED_BY": "Created By", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "INVOICE_STATUS": "Invoice Status", "INVOICE_POSTING_STATUS": "Invoice Posting Status", "ERROR": "Error", "ACTION": "Action", "SEARCH_BY_INVOICE_NO": "Search by Invoice No.", "ADD_NEW_INVOICE": "Add new invoice", "DOWNLOAD_CSV": "Download CSV", "EDIT_INVOICE": "Edit Invoice", "CANCEL": "Cancel", "POST": "Post", "SAVE_AND_UPDATE_INVOICE": "SAVE AND UPDATE INVOICE", "PLEASE_APPLY_FILTER": "Please apply filters"}, "FINANCE_DO": {"CONFIRM_APPROVAL": "Confirm Approval", "CONFIRM_REJECTION": "Confirm Rejection", "PLEASE_CONFIRM_IF_YOU_WANT_TO": "Please confirm if you want to %dynamicValue%", "OKAY_APPROVE": "OKAY, APPROVE", "REJECT": "REJECT", "CANCEL": "CANCEL", "APPROVE": "approve", "REJECT_LOWERCASE": "reject", "DO_NUMBER": "DO Number", "WITH": "with", "VALUE": "Value %dynamicValue%", "RAISED_AGAINST_PO_BY_ENTITY": "raised against", "ENTER_REASIN_FOR_REJECTION": "Enter reason for rejection...", "SEARCH_BY_DO_NUMBER": "Search by DO Number...", "CONFIRMED": "Confirmed", "REJECTED": "Rejected", "APPROVED": "approved", "REJECTED_LOWERCASE": "rejected", "ORDER_FOR": "Order %dynamicValue% for", "PLEASE_ENTER_REASON_FOR_REJECTION": "Please enter reason for rejection.", "S_NO": "S.No", "PO_NUMBER": "PO Number", "ENTITY": "Entity", "SO_VALUE": "SO Value", "EMPLOYEE_ID": " Employee ID", "STATUS": "Status", "ACTION": "Action", "APPROVED_UPPERCASE": "Approved", "APPROVE_UPPERCASE": "Approve", "REJECT_UPPERCASE": "Reject", "CREATED": "Created", "CREATED_AT": "Created At", "SEARCH_STATUS_CANNOT_BE_EMPTY": "Search status cannot be empty", "PLEASE_APPLY_FILTER": "Please apply filter to get data less than 2000", "NO_DATA_TO_DOWNLOAD": "Sorry, there is no data to download", "DO_TYPE": "DO Type"}, "DO_ORDER_MANAGEMENT": {"SEARCH_BY": "Search By", "SEARCH_BY_DO_NUMBER": "Search by DO Number...", "SEARCH_STATUS_CANNOT_BE_EMPTY": "Search status cannot be empty", "PLEASE_APPLY_FILTER": "Please apply filter to get data less than 2000", "INVOICE_NUMBER": "Invoice Number", "PO_NUMBER": "PO Number", "AWB_NUMBER": "AWB Number", "CREATE_NEW_ORDER": "CREATE NEW ORDER", "DOWNLOAD_CSV": "DOWNLOAD CSV", "ENTITY": "Entity", "SO_VALUE": "SO Value", "PLACED_BY": "Placed By", "STATUS": "Status", "CREATED_AT": "Created At", "ACTION": "Action", "INVOICE_VALUE": "Invoice Value", "INVOICE_NO": "Invoice No", "CARRIER": "Carrier", "AWB": "AWB", "FACILITY": "Facility", "ENTER_ORDER_DETAILS": "Enter Order Details", "SELECT_AND_VALIDATE_CSV": "Select & Validate CSV", "PLACE_NEW_ORDER": "Place New Order", "SELECT_CUSTOMER": "Select Customer", "THIS_FIELD_IS_REQUIRED": "this field is required", "SELECT_FACILITY": "Select Facility", "ENTER_PO_NUMBER": "Enter PO Number", "ALL_FIELDS_ARE_REQUIRED": "All fields are required", "CONFIRM": "CONFIRM", "PO": "PO", "CHANGE_DETAILS": "CHANGE DETAILS", "PID": "PID", "QUANTITY": "Quantity", "PRICE": "Price", "DROP_HERE_OR_SELECT_CSV": "Drop here or Select CSV file", "REQUIRED": "Required", "CHANGE": "CHANGE", "VALIDATE_CSV": "VALIDATE CSV", "CHANGE_CSV": "CHANGE CSV", "ALL_COLUMN_FIELDS_IN_CSV_REQUIRED": "All column fields in CSV file are required", "THERE_SHULD_BE_NO_TYPING_ERR": "There should be no typing error", "DATA_TYPE_SHOULD_BE_CORRECT": "Data type of every field in CSV should be correct", "ONLY_SINGLE_FILE_ALLOWED": "Only a single .csv file is allowed", "MAXIMUM_FILE_SIZE_FIVE_MB": "Maximum file size allowed is 5 MB", "CSV_FILE_SUCCESSFULLY_VALIDATED": "CSV FILE SUCCESSFULLY VALIDATED", "ORDER_CREATED_SUCCESSFULLY": "Order created successfully", "DO_NUMBER": "DO Number", "TOTAL_SO_VALUE": "Total SO Value", "TOTAL_PID": "Total PIDs", "TOTAL_QTY": "Total Quantity", "CREATE_ORDER": "CREATE ORDER", "CANCEL_ORDER": "CANCEL ORDER", "CANCEL_ORDER_SMALL": "Cancel Order", "MODAL_INFO_MESSAGE": "Please confirm if you want to cancel the order", "SO_NUMBER": "SO Number", "RAISED_AGAINST": "raised against", "BY": "by", "GO_BACK": "GO BACK", "ORDER_CANCELLED": "Order Cancelled", "ORDER_CANCELLED_FOR_SO_NO": "Order cancelled for SO Number", "ERRORS_FOUND_IN_CSV": "Errors found in CSV file", "UNIT_PRICE_WITH_TAX": "Unit Price With Tax", "SAMPLE_CSV_FILE": "Sample CSV file", "DOWNLOAD": "Download", "DO_TYPE": " DO Type"}, "DISTRIBUTOR_ORDER_SHIPMENT_LIST": {"SHIPPING_PACKAGE_ID": "Shipping Package ID", "INCREMENT_ID": "Increment ID", "DO_NUMBER": "DO Number", "ACTION": "Action", "DO_VALUE": "DO Value", "CUSTOMER": "Customer", "PICKED": "Picked", "AWB_NUMBER": "AWB Number", "CARRIER": "Carrier", "INVOICE_NUMBER": "Invoice Number", "CREATED_AT": "Created At", "INVOICE_VALUE": "Invoice Value", "STATUS": "Status", "FULFIL_TYPE": "Fulfil Type", "DOWNLOAD_CSV": "download csv", "SEARCH_BY_SHIPPING_PACKAGE_ID": "Search by shipping package ID", "RESET": "RESET"}, "DISTRIBUTOR_ORDER_SHIPMENT_DETAILS": {"PRODUCT_ID": "Product ID", "REQUIRED_QTY": "Required <PERSON><PERSON>.", "QTY_SCANNED": "<PERSON><PERSON><PERSON>", "CATEGORY": "Category", "PID_DESC": "PID Description", "UPDATED_ON": "Updated On", "UPDATED_BY": "Updated By", "SEARCH": "Search ..", "CLOSE_PICKING": "CLOSE PICKING", "PRINT_INVOICE": "PRINT INVOICE", "PRINT_SHIPMENT_LABEL": "PRINT SHIPMENT LABEL", "START_SCANNING": "START SCANNING", "CONFIRM": "CONFIRM", "GO_BACK_TO_SCANNING": "GO BACK TO SCANNING", "CANCEL": "CANCEL", "FEW_ITEMS_ARE_PENDING": "A few items are still pending for picking. Please complete the picking.", "NUMBER_OF_BOXES_USED": "Number of Boxes Used", "SELECT_THE_NUMBER_OF_PACKING_BOXES": "Select the Number of packing boxes", "": ""}, "CUSTOMER": {"CUSTOMER_NAME": "Customer Name", "CODE": "Code", "BILLING_ADDRESS": "Billing Address", "SHIPPING_ADDRESS": "Shipping Address", "CREATED_BY": "Created By", "LAST_UPDATED_ON": "Last updated on", "ACTIONS": "Actions", "EDIT_CUSTOMER": "Edit Customer", "CREATE_NEW_CUSTOMER": "Create New Customer", "SEARCH_BY_CUSTOMER_NAME": "Search by Customer Name", "SEARCH_BY_CUSTOMER_NAME_CANNOT_BE_EMPTY": "Search by Customer Name cannot be empty", "CONFIRM": "Confirm", "CANCEL": "Cancel", "SAVE_CHANGES": "Save Changes", "GO_BACK": "Go Back", "CREATE_NEW_CUSTOMER_UPPERCASE": "CREATE NEW CUSTOMER", "ACCOUNTING_DETAILS": "Accounting Details", "CUSTOMER_CONTACT": "Customer Contact", "GENERAL_DETAILS": "General Details", "PLEASE_FILL_ALL_THE_REQUIRED_FIELDS": "Please fill all the required fields....", "EDIT_CUSTOMER_DETAILS": "Edit Customer Details?", "PLEASE_CONFIRM_IF_YOU_WANT_TO_UPDATE_CUSTOMER": "Please confirm if you want to save the changes made to the customer %dynamicValue%", "PLEASE_CONFIRM_IF_YOU_WANT_TO_CREATE_CUSTOMER": "Please confirm if you want to update the customer"}, "COMMON_COMPONENTS": {"THE_SELECTED_DATE_RANGE_MUST_BE_LESS_THAN": "The selected date range must be less than %dynamicValue%  days"}, "VENDOR_SHIPMENT_LIST": {"SEARCH_VENDOR_SHIPMENT_HERE": "Search Vendor Shipment", "CREATE_PO_SHIPMENT": "create PO shipment", "RESET": "RESET", "EXPORT": "EXPORT", "VENDOR_SHIPMENT": "Vendor Shipment", "STATUS": "Status", "QUANTITY": "Quantity", "PO": "PO", "VENDOR_INV_NO": "Vendor Inv. No.", "ETD": "Expected Dispatch Date", "EAD": "Expected Arrival Date", "BOE_DATE": "Bill Of Entry Date", "AD_DATE": "Actual Dispatch Date", "CC_DATE": "Custom Clearance Date", "WH_LANDED_DATE": "WH Landed Date", "CREATED_BY": "Created By", "CREATED_AT": "Created At", "FACILITY": "Facility", "FACILITY_CODE": "Facility Code", "PLEASE_APPLY_FILTER": "Please apply filter to get data less than 100"}, "VENDOR_SHIPMENT_DETAILS_LIST": {"PO_SHIPMENT": "PO Shipment", "SHIPMENT_EXPECTED_DISPATCH_DATE": "Shipment Expected dispatch date", "VENDOR": "<PERSON><PERSON><PERSON>", "PO": "PO", "TOTAL": "Total", "SNO": "SNo.", "PID": "PID", "ITEM_DETAILS": "<PERSON><PERSON>", "QUANTITY": "Quantity", "EDIT": "EDIT", "ADD_PID": "ADD PID"}, "CREATE_VENDOR_SHIPMENT": {"CREATE_SHIPMENT": "Create Shipment", "CREATE": "CREATE", "SELECT_VENDOR": "Select Vendor", "SELECT_PO": "PO Number", "ENTER_INVOICE_NUMBER": "Enter Invoice Number", "INVOICE_DATE": "Invoice Date", "SHIPMENT_QTY": "Shipment QTY", "EXPECTED_TIME_OF_DISPATCH": "Expected Time of Dispatch", "EXPECTED_TIME_OF_ARRIVAL": "Expected Time of Arrival", "UPDATE_SHIPMENT": "Update Shipment", "PLEASE_ENTER_INVOICE_DATE": "Please Enter Invoice Date", "ENTER_INV_DATE": "Enter Invoice Date", "ADDITIONAL_FIELDS": "Additional Fields", "OPTIONAL": "optional", "UPDATE": "UPDATE", "PLEASE_ENTER_INVOICE_NUMBER": "Please Enter Invoice Number", "PLEASE_FILL_ALL_THE_REQUIRED_FIELDS": "Please fill all the required fields", "ENTER_PO_NUMBER": "Enter PO Number", "ENTER_SHIPMENT_QTY": "Enter Shipment Qty", "SELECT_EXPECTED_TIME_OF_DISPATCH": "Select Expected Time Of Dispatch", "SELECT_EXPECTED_TIME_OF_ARRIVAL": "Select Expected Time Of Arrival", "ACTUAL_DISPATCH_DATE": "Actual Dispatch Date", "BILL_OF_ENTRY_DATE": "Bill Of Entry Date", "CUSTOMER_CLEARANCE_DATE": "Customer Clearance Date", "WAREHOUSE_LANDING_DATE": "Warehouse Landing Date", "SHIPMENT_VALUE": "Shipment Value", "MODE_OF_TRANSPORT": "Mode Of Transport"}, "VENDOR_SHIPMENT_SUMMARY": {"PO_SHIPMENT": "PO Shipment", "CREATION_DATE": "Creation Date", "PO": "PO", "PID_ALREADY_ADDED": "PID already added", "PLEASE_CHOOSE_DIFF_PID": "please choose different pid", "PLEASE_ENTER_QTY": "Please enter quantity", "SNO": "SNo.", "PID": "PID", "PID_DESCRIPTION": "PID DESCRIPTION", "QUANTITY": "QUANTITY", "MATCH_QTY_WARNING": "Please ensure the quantity matches what you provided during the update", "IMPORT_CSV": "IMPORT CSV", "CANCEL": "CANCEL", "DONE": "DONE", "ADD_PID": "ADD PID", "FILE_UPLOAD_WARN_MESSAGE": "This will overwrite existing rows in Shipment", "VENDOR": "<PERSON><PERSON><PERSON>", "ENTER_PID": "ENTER PID", "RELOAD_WARN_MSG": "Changes will be lost if you reload."}, "STOCK_TAKE": {"CREATE_STOCK_TAKE": "Create Stock Take", "CREATE_CYCLE_TAKE": "Create Cycle Take", "ADD_AUDIT_CYCLE_NAME": "Add Audit Cycle Name", "PLEASE_ENTER_AUDIT_CYCLE_NAME": "Please enter audit cycle name", "CYCLE_ID": "Cycle ID", "CYCLE_NAME": "Cycle Name", "FACILITY_CODE": "Facility Code", "CREATED_BY": "Created By", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "ACTIONS": "Actions", "ACTION": "Action", "UPLOAD_ITMES": "Upload Items", "SCAN_BARCODE": "Scan Barcode", "SCAN_LOCATION": "Scan Location", "CREATE": "Create", "MESSAGE": "Message", "STATUS": "Status", "BARCODE": "Barcode", "BARCODE_ALREADY_SCANNED": "Barcode already scanned!", "DOWNLOAD_SAMPLE_CSV": "Download Sample CSV", "CANCEL": "Cancel", "STOCK_TAKE_ID": "Stock Take ID", "TOTAL_COUNT": "Total Count", "CYCLE_NAME_MUST_NOT_BE_BLANK": "Cycle name must not be blank", "STOCK_TAKE": "Stock Take", "CYCLE_IS_CLOSED": "Cycle is Closed", "CLOSE_CYCLE": "Close Cycle", "CLOSE": "Close", "CYCLE_COUNT_STATUS": "Cycle Count Status", "ARE_YOU_SURE_YOU_WANT_TO_CLOSE_CYCLE": "Are you sure you want to close %dynamicValue% cycle?", "STOCK_TAKE_MUST_BE_ENABLED": "Stock Take is not enabled for the selected Facility."}, "BULK_ORDER_PICKING": {"SCAN_SHIPMENT_ID_OR_FITTING_ID": "Scan Shipment ID / Fitting ID", "COUNTRY": "Country", "ORDER_PLACED_ON": "Order Placed On", "UNICOM_SHIPPING_STATUS": "Unicom Shipping Status", "CHANNEL": "Channel", "PRODUCT_IMAGE": "Product Image", "PRODUCT_NAME": "Product Name", "SKU": "SKU", "GO_BACK": "Go Back", "SHIPPING_ID_OR_FITTING_ID": "Shipment ID / Fitting ID", "SCAN_BARCODES_FOR_PICKING": "Scan Barcodes for Picking", "NOT_PICKED": "Not Picked", "PICKED": "Picked", "PRODUCT_ID": "Product Id", "QUANTITY": "Quantity"}, "RTV_GATEPASS": {"SELECT_VENDOR": "Select Vendor", "GATEPASS": "Gatepass", "VENDOR_CODE": "Vendor Code", "FACILITY": "Facility", "GATEPASS_QTY": "Gatepass QTY", "CREATED_DATE": "Created date", "APPROVAL_DATE": "Approval date", "APPROVED_BY": "Approved by", "ADD_PID": "ADD PID", "RESET": "RESET", "TRANSFER_ID": "Transfer ID", "AWB_NUMBER": "AWB Number", "PUTAWAY_NUMBER": "Putaway Number", "PID_COUNT": "PID Count", "NO_OF_ITEMS": "No.of Items", "SOURCE": "Source", "DESTINATION": "Destination", "CREATED_AT": "Created At", "UPDATED_AT": "Updated At", "UPDATED_BY": "Updated By", "CREATED_BY": "Created By", "STATUS": "Status", "SEND": "Send", "SOURCE_LOWER": "source", "SOURCE_TRANSFER_CODE": "Source Transfer Code", "PUTAWAY_NO": "Putaway Number", "NO_OF_PIDS": "No. of PIDs", "NO_OF_ITEMS_DETAILS": "No. of ITEMS", "STATUS_DETAILS": "status", "SUBMIT_FOR_APPROVAL": "SUBMIT FOR APPROVAL", "SEARCH": "Search", "GENERATE_AWB": "Generate AWB", "EDIT_TRANSFER": "Edit TRANSFER", "START_SCANNING": "START SCANNING", "CANCEL": "CANCEL", "DONE": "DONE", "PID": "PID", "PID_DESCRIPTION": "PID Description", "QUANTITY": "Quantity", "AVAILABLE_QTY": "Available Quantity", "PID_ALREADY_ADDED": "PID Already Added !", "PLEASE_CHOOSE_DIFF_PID": "Please choose different PID.", "ENTER_PID": "ENTER PID", "PLEASE_ENTER_QTY": "Please Enter Quantity", "PRODUCT_ID": "Product ID", "REQ_QTY": "<PERSON><PERSON><PERSON>.", "QTY_SCANNED": "<PERSON><PERSON><PERSON>", "AVAIL_QTY": "Avail. Qty.", "UPDATED_ON": "Updated On", "PLEASE_SELECT_FACILITY": "Please select facility", "GATEPASS_ID": "Gatepass ID", "SCANNING": "Scanning", "PID_SCANNED": "PID SCANNED", "TOTAL_ITEMS_ADDED": "Total items Added", "SCANNING_COMPLETE": "SCANNING COMPLETE", "PID_SCAN": "PID:", "SCANNED": "Scanned", "PID_DESC": "PID Description", "AVL_QTY": "Available Quantity", "ENTER_QUANTITY": "Please Enter Quantity", "ENTER_PID_FIRST": "Please Enter PID First", "NO_DATA": "No Data...", "SELECT_COURIER_TO_CREATE_AWB": "Please select the courier to create AWB", "SHIPPING_PROVIDER": "Shipping Provider", "CREATE": "create", "CREATED": "CREATED", "IN_PICKING": "IN_PICKING", "DISPATCHED": "DISPATCHED", "RECEIVED": "RECEIVED", "EXPORT": "EXPORT", "TOTAL_ITEMS_SCANNED": "Total items scanned", "NO_OF_BOXES_SCANNED": "No. of boxes scanned", "REPLACE": "Replace", "PUTAWAY_MODAL_TITLE": "Please click on respective Putaway Number to view details", "CREATE_RTV": "CREATE RTV", "COUNT": "Count", "VENDOR": "<PERSON><PERSON><PERSON>", "YES_SUBMIT": "YES, SUBMIT", "YES_REJECTED": "YES, REJECT", "SUBMIT_GATEPASS_FOR_APPROVAL": "Submit gatepass for approval", "PLEASE_ENTER_REASON_FOR_SUBMITING_FOR_APPROVAL": "Please enter reason for submitting for approval", "ENTER_REASON": "Enter Reason", "APPROVE_GATE_PASS": "Approve gatepass", "YES_DISCARDED": "YES, DISCARD", "YES_APPROVED": "YES, APPROVE", "DISCARD_GATEPASS": "Discard gatepass", "APPROVE_GATEPASS": "Approve gatepass", "REJECT_GATEPASS": "Reject gatepass", "BARCODE": "Barcode", "PRINT": "Print", "CTRL_P": "Ctrl+P", "DEBIT_NOTE": "Debit Note", "DELIVERY_CHALLAN": "Delivery <PERSON>", "DISCARD": "DISCARD", "RTV_STATUS": "RTV Status"}, "AUTO_GRN": {"SCAN_BARCODE": "Scan Barcode", "PRODUCT_DETAILS": "Product Details", "POWER_DETAILS": "Power Details", "GRN_SUMMARY": "GRN SUMMARY", "BULK_GRN": "BULK GRN", "UNICOM": "UNICOM", "NEXS": "NEXS", "GRN_NUMBER": "GRN Number", "BULK_GRN_NUMBER": "Bulk GRN Number", "PO": "PO", "BARCODE_SCANNED": "Barcode Scanned", "TOTAL_AMOUNT": "Total Amount", "PO_NUMBER_UNICOM": "PO NUMBER - Unicom", "PO_NUMBER_NEXS": "PO NUMBER - NEXS", "VENDOR_NAME": "Vendor Name", "INVOICE_DATE": "Invoice Date", "JIT_GRN": "JIT GRN", "BARCODE_ADDED_TO_GRN": "Barcode Added to GRN", "SOMETHING_WENT_WRONG": "Something went wrong", "ORDER_ID": "Order ID", "BARCODE": "Barcode", "INVOICE_NUMBER": "Invoice Number", "START_RECEIVING": "Start Receiving", "PO_NUMBER": "PO Number", "QUANTITY": "Quantity", "ACTION": "Action", "RECEIVED_AT_WAREHOUSE": "Recieved At Warehouse", "ENTER_DETAILS": "Enter Details", "ENTER_GRN_NUMBER": "ENTER GRN NUMBER", "PID": "PID", "UNIT_PRICE": "Unit Price", "SPH": "SPH", "CYL": "CYL", "AP": "AP", "PD": "PD", "ED": "ED", "PO_GRN_CREATED_SUCCESSFULLY": "PO and GRN successfully created for invoice: %dynamicValue%", "USER_DOES_NOT_HAVE_PERMISSION": "User does not have permission", "VENDOR_NOT_ALLOWED": "%dynamicValue% Vendor not allowed"}, "DO_RETURN_REVERSE": {"MATCH_NOT_FOUND": "Match Not Found", "NO_DATA_FOUND": "No Data Found", "Scanned": "Scanned", "Passed": "Passed", "Failed": "Failed", "Successful": "Successful", "ENTER_DETAILS": "Enter Details", "SCAN_BARCODE": "Scan Barcode", "PLEASE_ENTER_BARCODE": "Please eneter barcode"}, "ASRS_MANUAL_SYNC": {"PICKING_CATEGORY": "Picking Category", "PENDING_ORDER_COUNT": "Pending Order Count", "ASRS_ELIGIBLE_ORDERS": "ASRS Eligble Orders", "ENTER_ORDER_COUNT_TO_SYNC": "Enter Order Count To Sync", "ACTION": "Action", "ASRS_ORDER_COUNT_VALIDATION": "Please enter a value less than or equal to the ASRS eligible orders count.", "ASRS_ORDER_COUNT_VALIDATION_SYNC": "Please enter an Order Sync value less than or equal to the"}}