import React from 'react';
import config from '../../../config';
import { listData } from './data/AddInventory.json';
import AddInventory from '..';
import { ADD_INVENTORY as CONTENT } from '../../../../public/localisation/lang_en.json';

describe('Add Inventory Component', () => {
  beforeEach(() => {
    cy.intercept('POST', `${config.addInventory.list}**`, {
      statusCode: 200,
      body: listData
    }).as('addInventoryList');
    cy.mount(<AddInventory />);
  });

  it('Should Render Add Inventory Component Correctly', () => {
    cy.getByCy('add-inventory-container').should('exist');
    cy.get('#globalSearchId').should('have.attr', 'placeholder', CONTENT.SEARCH_PID_HERE);
    cy.getByCy('singlePidBtn').should('have.text', CONTENT.SINGLE_PID);
    cy.getByCy('bulkUploadBtn').should('have.text', CONTENT.BULK_UPLOAD);
    cy.get('#globalSearchId').type('1234');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(1000);
    cy.get('#globalSearchId').clear();
  });

  it('Should Work Filters Correctly', () => {
    cy.get('[data-testid="CancelIcon"]').click();
    cy.get('#globalSearchId').type('1234');
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(1000);
    cy.getByCy('status-filter-icon').click();
    cy.getByCy('IN_PROGRESS').click();
  });

  it('should work single pid and bulk upload functionality correctly', () => {
    cy.intercept('POST', `${config.addInventory.singleUpload}**`, {
      statusCode: 200,
      body: ''
    }).as('singleUploadPid');

    cy.intercept('POST', `${config.addInventory.bulkUpload}**`, {
      statusCode: 200,
      body: ''
    }).as('bulkUploadSuccess');

    cy.getByCy('singlePidBtn').click();
    cy.getByCy('close-modal').click();
    cy.getByCy('singlePidBtn').click();
    cy.getByCy('modal-title').find('div').should('have.text', CONTENT.SINGLE_PID_UPLOAD);
    cy.get('#scanBarcode').should('have.attr', 'placeholder', CONTENT.SCAN_BARCODE);
    cy.get('#destinationPid').should('have.attr', 'placeholder', CONTENT.SCAN_DESTINATION_PID);
    cy.get('#scanBarcode').type('test1245{enter}');
    cy.get('#destinationPid').type('test{enter}');
    cy.getByCy('bulkUploadBtn').click();
    cy.getByCy('close-modal').click();
    cy.getByCy('bulkUploadBtn').click();
    cy.getByCy('upload-file').selectFile('cypress/addInventory/sample_add_inventory_upload.csv');
    cy.getByCy('modal-title').should('have.text', 'Import Processed Successfully');
    cy.getByCy('upload-success-ok-button').click();
  });

  it('should work correctly if api\'s failed', () => {
    cy.intercept('POST', `${config.addInventory.list}**`, {
      statusCode: 500,
      body: 'Internal Server Error'
    }).as('addInventoryListError');

    cy.intercept('POST', `${config.addInventory.singleUpload}**`, {
      statusCode: 500,
      body: 'Internal Server Error'
    }).as('singleUploadPidError');

    cy.intercept('POST', `${config.addInventory.bulkUpload}**`, {
      statusCode: 200,
      body: [
        ['barcode', 'pid', 'reason'],
        [12345, 1285511, 'Barcode', 'already exists data']
      ]
    }).as('bulkUploadFileError');

    cy.mount(<AddInventory />);
    cy.getByCy('singlePidBtn').click();
    cy.get('#scanBarcode').type('test1245{enter}');
    cy.get('#destinationPid').type('test{enter}');
    cy.getByCy('close-modal').click();
    cy.getByCy('bulkUploadBtn').click();
    cy.getByCy('upload-file').selectFile('cypress/addInventory/sample_add_inventory_upload.csv');
    cy.getByCy('download-error-report').click();
    cy.getByCy('ok_button').click();

    cy.intercept('POST', `${config.addInventory.bulkUpload}**`, {
      statusCode: 500,
      body: 'Internal Server Error'
    }).as('bulkUploadError');
    cy.getByCy('bulkUploadBtn').click();
    cy.getByCy('upload-file').selectFile('cypress/addInventory/sample_add_inventory_upload.csv');
    cy.getByCy('cancel-file-selection').click();
    cy.getByCy('close-modal').click();
    cy.getByCy('singlePidBtn').click();
    cy.get('#scanBarcode').type('{enter}');
  });
});
