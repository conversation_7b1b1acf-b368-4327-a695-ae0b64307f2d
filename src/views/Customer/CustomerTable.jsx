import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Box } from '@mui/material';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import MoreItem from 'components/common/MoreItem';

import useWindowResize from 'common/useWindowResize';
import { CUSTOMER, updateCustomerLoad } from 'redux/reducers/customer';
import LkToolTip from 'components/ToolTip/ToolTip';
import { LOCALISATION } from 'redux/reducers/localisation';
import Spinner from 'components/Spinner/Spinner';
import styled from 'styled-components';

const MenuBox = styled(Box)`
  cursor: 'pointer';
  padding: '14px 15px';
`;

const CustomerTable = ({ pageSize, page, fetchMoreListItems }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [tableHeight] = useWindowResize(window.innerHeight - 255);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);
  const { isLoading, data, totalCount } = useSelector((state) => state[CUSTOMER].customerList);
  const { isLoading: UpdateStateLoading } = useSelector(
    (state) => state[CUSTOMER].isCreatedOrUpdated
  );

  const handleEnableDisable = (actionData) => {
    if (actionData?.customerEnabled) {
      dispatch(updateCustomerLoad({ ...actionData, customerEnabled: false }));
    } else if (!actionData?.customerEnabled) {
      dispatch(updateCustomerLoad({ ...actionData, customerEnabled: true }));
    }
  };

  const actionOptions = (actionData) => {
    const { id, customerEnabled } = actionData;
    if (UpdateStateLoading) {
      return (
        <MenuBox sx={{ width: '115px', textAlign: 'center', height: '50px', margin: 'auto' }}>
          <Spinner />
        </MenuBox>
      );
    }

    return (
      <>
        <MenuBox
          onClick={() => navigate(`/customer/edit/${id}`)}
          sx={{ cursor: 'pointer', padding: '14px 15px' }}
        >
          {CONTENT.EDIT_CUSTOMER}
        </MenuBox>
        <hr />
        <MenuBox
          onClick={() => handleEnableDisable(actionData)}
          sx={{ cursor: 'pointer', padding: '14px 15px' }}
        >
          {customerEnabled ? 'Disable' : 'Enable'}
        </MenuBox>
      </>
    );
  };

  const addressTemplate = (address) => {
    if (address.length === 0) {
      return <>-</>;
    }
    const addressDetails = address[0];
    const renderAddress = `${addressDetails?.addressLine1},${addressDetails?.addressLine2} 
    ${addressDetails?.city} ${addressDetails?.state} ${addressDetails?.country} 
    ${addressDetails?.pincode}`;
    return (
      <LkToolTip
        placement="bottom-end"
        title={<Box p={1}>{renderAddress} </Box>}
        className="dtoolTip"
      >
        <div className="ellipsis-vertical">{renderAddress}</div>
      </LkToolTip>
    );
  };

  const getWarnedRows = useMemo(() => {
    const result = [];
    data.forEach((item) => {
      if (!item.customerEnabled) {
        result.push(item.id);
      }
    });
    return result;
  }, [data]);

  const headerConfig = [
    {
      key: 'name',
      name: CONTENT.CUSTOMER_NAME
    },
    {
      key: 'code',
      name: CONTENT.CODE
    },
    {
      key: 'billingAddress',
      name: CONTENT.BILLING_ADDRESS,
      formatBody: useCallback(({ customerAddressDetails }) => {
        const billingAddress = customerAddressDetails.filter(
          (addresss) => addresss.addressType === 'BILLING'
        );
        return addressTemplate(billingAddress);
      }, []),
      style: { minWidth: 180, maxWidth: 180 }
    },
    {
      key: 'shippingAddress',
      name: CONTENT.SHIPPING_ADDRESS,
      formatBody: useCallback(({ customerAddressDetails }) => {
        const shippingAddress = customerAddressDetails.filter(
          (addresss) => addresss.addressType === 'SHIPPING'
        );
        return addressTemplate(shippingAddress);
      }, []),
      style: { minWidth: 180, maxWidth: 180 }
    },
    {
      key: 'createdBy',
      name: CONTENT.CREATED_BY,
      style: { minWidth: 80, maxWidth: 80 }
    },
    {
      key: 'updatedAt',
      name: CONTENT.LAST_UPDATED_ON
    },
    {
      key: 'actions',
      name: CONTENT.ACTIONS,
      formatBody: useCallback(
        (actionData) => <MoreItem itemsToShow={actionOptions(actionData)} />,
        [UpdateStateLoading]
      ),
      style: { minWidth: 50, maxWidth: 50 }
    }
  ];

  return (
    <LkTable
      tableHeight={tableHeight}
      isDataFetching={isLoading}
      headerConfig={headerConfig}
      tableData={data}
      totalRowsCount={totalCount}
      pageLimit={pageSize}
      pageNumber={page}
      rowSize={80}
      warnedRows={getWarnedRows}
      rowKey="id"
      dataRequestFunction={fetchMoreListItems}
    />
  );
};

export default CustomerTable;
