import React from 'react';
import Slider from 'react-slick';
import LkModal from '../Modal/Modal';
import './carouselModal.scss';
// import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
// import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

const settings = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1
};

const CarouselModal = (props) => {
  const { open, openModal, currentSlide, data, pid } = props;
  return (
    <LkModal
      upperHeading={`PID - ${pid}`}
      open={open}
      showActionButton={false}
      handleClose={() => openModal(false)}
    >
      <div
        className="slider-main carousel-comp-slider 
      carousel-modal-container pd-l40 pd-r40 mr-b40"
      >
        <Slider {...settings} initialSlide={currentSlide}>
          {data?.map((imgpath) => (
            <div key={imgpath} className="image-container">
              <img src={imgpath} alt="img" />
            </div>
          ))}
        </Slider>
      </div>
    </LkModal>
  );
};
export default CarouselModal;
