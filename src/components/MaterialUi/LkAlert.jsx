import React, { useState } from 'react';
import { Snackbar } from '@mui/material';
import Alert from '@mui/material/Alert';
import { withStyles } from 'tss-react/mui';

const handleClose = (props, setOpen) => {
  setOpen(false);
  props.onClose && props.onClose();
};

const StyledAlert = withStyles(Alert, () => ({
  root: {
    fontSize: '14px'
  },
}));

const LkAlert = props => {
  const [open, setOpen] = useState(true);
  const { message, severity } = props;
  return (
    <Snackbar
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      open={open} autoHideDuration={2600}
      onClose={() => handleClose(props,setOpen)}
    >
      <StyledAlert severity={severity || 'info'} variant="filled" >
        {message}
      </StyledAlert>
    </Snackbar>
  )
};

export default LkAlert;
