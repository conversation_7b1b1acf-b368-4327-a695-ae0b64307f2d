import React from 'react';

const EdgingQC = React.lazy(() => import('./views/EdgingQC'));
const DistributorOrderShipmentList = React.lazy(() =>
  import('./views/DistributorOrderShipmentList')
);
const DistributorOrderShipmentDetails = React.lazy(() =>
  import('./views/DistributorOrderShipmentDetails')
);

const Dashboard = React.lazy(() => import('./views/Dashboard/Dashboard'));
const GrnCreation = React.lazy(() => import('./views/Grn/GrnCreation'));
const GrnHome = React.lazy(() => import('./views/Grn/GrnHome'));
const GrnDetails = React.lazy(() => import('./views/Grn/GrnDetails'));
const GrnListing = React.lazy(() => import('./views/Grn/GrnListing'));
const VendorMaster = React.lazy(() => import('./views/VendorMaster/VendorMaster'));
const DebitNoteDetail = React.lazy(() => import('./views/DebitNote/DebitNoteDetail'));
const DebitNoteList = React.lazy(() => import('./views/DebitNote/DebitNote'));
const ChangePassword = React.lazy(() => import('./views/Login/ChangePassword'));
const FacilityDetails = React.lazy(() => import('./views/Facility/FacilityDetails'));
const HistoryMain = React.lazy(() => import('./views/BarcodeHistoryPage'));
const FacilityListing = React.lazy(() => import('./views/Facility/FacilityList/FacilityListTab'));
const InwardQc = React.lazy(() => import('./views/InwardQC'));
const UserManagement = React.lazy(() => import('./views/UserManagement'));
const PutawayCreation = React.lazy(() => import('./views/PutawayCreation'));
const BulkInventory = React.lazy(() => import('./views/BulkInventory'));
const BulkInventoryTransferDetails = React.lazy(() =>
  import(
    './views/BulkInventory/TransferSend/BulkInventoryTransferDetails/BulkInventoryTransferDetails'
  )
);
const ScanTransfer = React.lazy(() =>
  import('./views/BulkInventory/TransferSend/ScanTransfer/ScanTransfer')
);
const SuperVisorPicking = React.lazy(() => import('./views/SupervisorPanel/Picking'));
const Fitting = React.lazy(() => import('./views/Fitting'));
const StorePacking = React.lazy(() => import('./views/StorePacking'));
const MEIEdging = React.lazy(() => import('./views/MEIEdging'));
const CartonShipmentMapping = React.lazy(() => import('./views/CartonShipmentMapping'));
const Monitor = React.lazy(() => import('./views/MonitorV3'));
const Inventory = React.lazy(() => import('./views/Inventory'));
const Consolidation = React.lazy(() => import('./views/Consolidation'));
const CycleCount = React.lazy(() => import('./views/CycleCount'));
const Order = React.lazy(() => import('./views/Order'));
const Refurb = React.lazy(() => import('./views/Refurb'));
const CreatePutaway = React.lazy(() => import('./views/Putaway/CreatePutaway'));
const ShipmentPrinting = React.lazy(() => import('./views/ShipmentPrinting'));
const PickingList = React.lazy(() => import('./views/Picking/PickingList'));
const PickingDetailsTable = React.lazy(() =>
  import('./views/Picking/PickingListDetails/PickingDetailsTable')
);
const ExportReports = React.lazy(() => import('./views/ReportingTool'));
const D365List = React.lazy(() => import('./views/D365'));
const D365Details = React.lazy(() => import('./views/D365/D365Details'));
const LensLabStickerPrinting = React.lazy(() => import('./views/LensLabStickerPrinting'));
const JobCardPrinting = React.lazy(() => import('./views/JobCardPrinting'));
const ManifestShippingMethod = React.lazy(() => import('./views/ManifestShippingMethod'));
const ManifestCourierCutoff = React.lazy(() => import('./views/ManifestCourierCutOff'));
const BagShipments = React.lazy(() => import('./views/BagShipments'));
const AddInventory = React.lazy(() => import('./views/AddInventory'));
const AdhocASRSPicking = React.lazy(() => import('./views/AdhocASRSPicking'));
const AdhocASRSPickingDetails = React.lazy(() =>
  import('./views/AdhocASRSPicking/AdhocASRSPickingDetails')
);
const EInvoiceList = React.lazy(() => import('./views/E-Invoice/E-InvoiceList'));
const ASRSDiscrepancy = React.lazy(() => import('./views/ASRSDiscrepancy'));
const TrayReassignment = React.lazy(() => import('./views/TrayReassignment/TrayReassignment'));
const ManifestDetails = React.lazy(() => import('./views/Manifest/ManifestDetails'));
const Manifest = React.lazy(() => import('./views/Manifest/Manifest'));
const QualityCheck = React.lazy(() => import('./views/QualityCheck/QualityCheck'));
const QCSummary = React.lazy(() => import('./views/QualityCheck/QCSummary'));
const PackingPanel = React.lazy(() => import('./views/Packing/PackingPanel'));
const PackingSummary = React.lazy(() => import('./views/Packing/PackingSummary'));
const StartRetire = React.lazy(() => import('./views/StartRetire'));
const PriceControl = React.lazy(() => import('./views/PriceControl'));
const StoreConsolidation = React.lazy(() =>
  import('./views/StoreConsolidation/StoreConsolidation')
);
const ReactiveTransfer = React.lazy(() => import('./views/ReactiveTransfer'));
const JitOrderSearch = React.lazy(() => import('./views/JIT/OrderSearch'));
const MEIHandEdging = React.lazy(() => import('./views/MEIHandEdging'));
const MEIBlocking = React.lazy(() => import('./views/MEIBlocking'));
const OrderDashboard = React.lazy(() =>
  import('./views/DistributorOrders/OrderDashboard/OrderDashboard')
);
const InvoicePosting = React.lazy(() => import('./views/InvoicePosting'));
const CreateOrder = React.lazy(() =>
  import('./views/DistributorOrders/OrderDashboard/CreateOrder')
);
const FinanceDashboard = React.lazy(() => import('./views/Finance'));
const PickingScan = React.lazy(() => import('./views/PickingScan/PickingScan'));
const PutawayDetail = React.lazy(() => import('./views/Putaway/PutawayDetails/PutawayDeatil'));
const PutawayListTabs = React.lazy(() => import('./views/Putaway/PutawayList/PutawayListTabs'));
const PurchaseOrderList = React.lazy(() => import('./views/PurchaseOrder/PurchaseOrder'));
const PurchaseOrderDetail = React.lazy(() =>
  import('./views/PurchaseOrder/PurchaseOrderDetail/PurchaseOrderDetail')
);
const CreatePO = React.lazy(() => import('./views/PurchaseOrder/CreatePOHome'));
const InvoiceDashboard = React.lazy(() => import('./views/Receiving/InvoiceDashboard'));
const InvoiceView = React.lazy(() => import('./views/Receiving/invoiceView/InvoiceView'));
const InvoiceReference = React.lazy(() => import('./views/Receiving/InvoiceReference'));
const ConstraintTabs = React.lazy(() => import('./views/Constraint/ConstraintTabs'));
const ConstraintCreate = React.lazy(() =>
  import('./views/Constraint/ConstraintCreate/ConstraintCreate')
);
const LayoutListTab = React.lazy(() => import('./views/Facility/Layout/LayoutList/LayoutListTab'));
const LayoutCreation = React.lazy(() =>
  import('./views/Facility/Layout/LayoutCreation/LayoutCreation')
);
const CustomerDashboard = React.lazy(() => import('./views/Customer/CustomerDashboard'));
const CustomerFormsMain = React.lazy(() => import('./views/Customer/CustomerFormsMain'));
const VendorShipmentCreateSummary = React.lazy(() =>
  import('./views/VendorShipment/VendorShipmentDetails/create/VendorShipmentCreateSummary')
);
const VendorShipmentList = React.lazy(() => import('./views/VendorShipment/VendorShipmentList'));
const VendorShipmentDetailsList = React.lazy(() =>
  import('./views/VendorShipment/VendorShipmentDetails')
);
const CreateVendorShipment = React.lazy(() =>
  import('./views/VendorShipment/VendorShipmentDetails/create/CreateVendorShipment')
);
const StockTakeList = React.lazy(() => import('./views/StockTakeList'));

const stockTakeBarcodeScan = React.lazy(() => import('./views/stockTakeBarcodeScan'));

const BulkOrderPicking = React.lazy(() => import('./views/BulkOrderPicking'));
const BulkOrderDetails = React.lazy(() => import('./views/BulkOrderPicking/BulkOrderDetails'));
const RTVgatepassList = React.lazy(() => import('views/RTV/RTVList'));
const RTVgatepassDetailsList = React.lazy(() => import('views/RTV/RTVDetails'));
const RTVScanTransfer = React.lazy(() => import('./views/RTV/RTVScan'));
const AddBarcode = React.lazy(() => import('./views/AutoGrn/LensLab/AddBarcode'));
const WarehouseRecieving = React.lazy(() => import('./views/AutoGrn/WareHouseRecieving'));
const StartRecieving = React.lazy(() => import('./views/AutoGrn/LensLab/StartRecieving'));
const AutoGrnSummary = React.lazy(() => import('./views/AutoGrn/LensLab/AutoGrnSummary'));
const DOReturnReverse = React.lazy(() => import('./views/DOReturnReverse'));
const ASRSManualSyncList = React.lazy(() => import('./views/ASRSManualSync/ASRSManualSyncList'));

const routes = [
  {
    path: '/po/list',
    exact: false,
    component: PurchaseOrderList,
    id: 'ProcurementPurchaseOrder',
    title: 'Purchase Orders'
  },
  {
    path: '/po/detail',
    exact: false,
    component: PurchaseOrderDetail,
    id: 'ProcurementPurchaseOrder',
    title: 'PO_PAGE_TITLE'
  },
  {
    path: '/po/create',
    exact: false,
    component: CreatePO,
    id: 'ProcurementPurchaseOrder',
    title: 'PO_PAGE_TITLE'
  },
  {
    path: '/historyPage/:barcodeId',
    exact: false,
    component: HistoryMain,
    id: 'barcodeHistoryPage',
    title: 'BARCODE_HISTORY'
  },
  {
    path: '/supervisor/picking/:tab/:innerTab',
    exact: true,
    component: SuperVisorPicking,
    id: 'supervisorPicking',
    title: 'SUPERVISOR_PICKING_PANEL'
  },
  {
    path: '/fitting',
    exact: true,
    component: Fitting,
    id: 'OrderFitting',
    title: 'FITTING_PANEL'
  },
  {
    path: '/inventory/:productId',
    exact: true,
    component: Inventory,
    id: 'inventory',
    title: 'INVENTORY_DASHBOARD'
  },
  {
    path: '/supervisor/consolidation/:type?',
    exact: true,
    component: Consolidation,
    id: 'supervisorConsolidation',
    title: 'CONSOLIDATIONS_SUPERVISOR_PANEL'
  },
  {
    path: '/grn/create',
    exact: true,
    component: GrnCreation,
    id: 'ProcurementGRNCreate',
    title: ''
  },
  {
    path: '/grn/home',
    exact: true,
    component: GrnHome,
    id: 'ProcurementGRNHome',
    title: 'GRN'
  },
  {
    path: '/grn/details/:grnCode/:iqc?',
    exact: false,
    component: GrnDetails,
    id: 'ProcurementGRNnDetails',
    title: 'GRN_DETAIL'
  },
  {
    path: '/grn/listing',
    exact: true,
    component: GrnListing,
    id: 'ProcurementGRNListing',
    title: 'GRN & PID Information'
  },
  {
    path: '/invoice/view/:summary?',
    exact: false,
    component: InvoiceView,
    id: 'ProcurementVendorInvoice',
    title: 'Invoices'
  },
  {
    path: '/invoice/dashboard',
    exact: false,
    component: InvoiceDashboard,
    id: 'ProcurementVendorInvoice',
    title: 'Invoices'
  },
  {
    path: '/invoice/create',
    exact: false,
    component: InvoiceReference,
    id: 'ProcurementVendorInvoice',
    title: 'INVOICES'
  },
  {
    path: '/invoice/edit',
    exact: false,
    component: InvoiceReference,
    id: 'ProcurementVendorInvoice',
    title: 'INVOICES'
  },
  {
    path: '/vendor-master/dashboard',
    exact: true,
    component: VendorMaster,
    id: 'ProcurementVendorItemMaster',
    title: 'VENDOR_ITEM_MASTER'
  },
  {
    path: '/debitnote/list',
    exact: true,
    component: DebitNoteList,
    id: 'ProcurementDebitNoteList',
    title: 'DEBIT_NOTE_LIST'
  },
  {
    path: '/debitnote/detail',
    exact: true,
    component: DebitNoteDetail,
    id: 'ProcurementDebitNoteDetail',
    title: 'VIEW_DEBIT_NOTE'
  },
  {
    path: '/changePassword',
    exact: false,
    component: ChangePassword,
    id: 'changePassword',
    title: '',
    hideGlobalTopBar: true
  },
  {
    path: '/constraint/list',
    exact: false,
    component: ConstraintTabs,
    id: 'constraint',
    title: 'FACILITY_CONSTRAINTS'
  },
  {
    path: '/constraint/create',
    exact: false,
    component: ConstraintCreate,
    id: 'constraint',
    title: 'FACILITY_CONSTRAINTS'
  },
  {
    path: '/facilitys/list',
    exact: false,
    component: FacilityListing,
    id: 'FacilityFacilities',
    title: 'FACILITY'
  },
  {
    path: '/facilitys/details/:facilityId',
    exact: false,
    component: FacilityDetails,
    id: 'FacilityFacilitiedetails',
    hideGlobalTopBar: true
  },
  {
    path: '/layoutpage/list',
    exact: false,
    component: LayoutListTab,
    id: 'FacilityLayoutPage',
    title: 'Layout'
  },
  {
    path: '/layoutpage/create',
    exact: false,
    component: LayoutCreation,
    id: 'FacilityLayoutPage',
    title: 'Layout'
  },
  {
    path: '/putawayPage/list',
    exact: false,
    component: PutawayListTabs,
    id: 'PutawayListTabs',
    title: 'PUTAWAY'
  },
  {
    path: '/putawayPage/details/:putawayId',
    exact: false,
    component: PutawayDetail,
    id: 'inventoryPutaway',
    title: 'PUTAWAY'
  },
  {
    path: '/putawayCreation',
    exact: false,
    component: PutawayCreation,
    id: 'emsPutawayCreation',
    title: 'EXCEPTION_MANAGEMENT_SYSTEM'
  },
  {
    path: '/bulkInventory',
    exact: true,
    component: BulkInventory,
    id: 'inventoryTransfers',
    title: 'BULK_INVENTORY_TRANSFER'
  },
  {
    path: '/bulkInventory/:transferId',
    exact: true,
    component: BulkInventoryTransferDetails,
    id: 'inventoryTransfersDetails',
    title: 'BULK_INVENTORY_TRANSFER'
  },
  {
    path: '/bulkInventory/:transferId/scan',
    exact: true,
    component: ScanTransfer,
    id: 'inventoryTransferScan',
    title: 'BULK_INVENTORY_TRANSFER'
  },
  {
    path: '/storePacking',
    exact: true,
    component: StorePacking,
    id: 'OrderStorePacking',
    title: 'STORE_PACKING_PANEL'
  },
  {
    path: '/MEIEdging',
    exact: true,
    component: MEIEdging,
    id: 'OrderMEIEdging',
    title: 'MEI_EDGING'
  },
  {
    path: '/cartonShipmentMapping',
    exact: true,
    component: CartonShipmentMapping,
    id: 'OrderCartonShipmentMapping',
    title: 'CARTON_SHIPMENT_MAPPING'
  },
  {
    path: '/dashboard',
    exact: true,
    component: Dashboard,
    id: 'dashboard',
    title: 'DASHBOARD'
  },
  {
    path: '/cycleCount',
    exact: true,
    component: CycleCount,
    id: 'inventoryCycleCount',
    title: 'INVENTORY'
  },
  {
    path: '/order/:orderId/:tab',
    exact: true,
    component: Order,
    id: 'orderDetails',
    hideGlobalTopBar: true
  },
  {
    path: '/E-Invoice/list',
    exact: true,
    component: EInvoiceList,
    id: 'eInvoice',
    title: 'E_INVOICE_MASTER_DASHBOARD'
  },
  {
    path: '/refurb/:type?',
    exact: true,
    component: Refurb,
    id: 'inventoryRefurbishment',
    title: 'REFURBISHMENT_PANEL'
    // reducers: ['refurb'],
    // sagas: ['refurb'],
  },
  {
    path: '/createPutaway/:putawayType',
    exact: true,
    component: CreatePutaway,
    id: 'createPutaway',
    title: 'CREATE_PUTAWAY'
  },
  {
    path: '/shipment-printing/:type?',
    exact: true,
    component: ShipmentPrinting,
    id: 'shipmentPrinting',
    hideGlobalTopBar: true
  },
  {
    path: '/picking',
    exact: true,
    component: PickingList,
    id: 'OrderPicking',
    title: 'PICKING'
  },
  {
    path: '/picking/details/:pickingId',
    exact: true,
    component: PickingDetailsTable,
    id: 'OrderPickingDetails',
    title: 'PICKING_DETAILS'
  },
  {
    path: '/monitor',
    exact: true,
    component: Monitor,
    id: 'monitor',
    title: 'MONITOR_PANEL'
  },
  {
    path: '/reporting/:type?',
    exact: false,
    component: ExportReports,
    id: 'exportReport',
    title: 'EXPORT_CONFIGURATION'
    // sagas: ['reportingTool'],
    // reducers: ['reportingTool'],
  },
  {
    path: '/D365/List',
    exact: true,
    component: D365List,
    id: 'ReportsD365List',
    title: 'D365_LIST'
  },
  {
    path: '/D365/details/:entityType',
    exact: true,
    component: D365Details,
    id: 'ReportsD365Details',
    hideGlobalTopBar: true
  },
  {
    path: '/asrsDescrepancy/:locationId',
    exact: true,
    component: ASRSDiscrepancy,
    id: 'ASRSDiscrepancy',
    title: 'LOCATION_INVENTORY_DETAILS'
  },
  {
    path: '/usermanagement/:roles',
    exact: true,
    component: UserManagement,
    id: 'userManagement',
    title: 'USER_MANAGEMENT'
  },
  {
    path: '/IQC',
    exact: true,
    component: InwardQc,
    id: 'IQC',
    title: 'IQC'
  },
  {
    path: '/lensLabSticker',
    exact: true,
    component: LensLabStickerPrinting,
    id: 'OrderLabLensStickerPrinting',
    title: 'LAB_LENS_STICKER_PRINTING'
  },
  {
    path: '/manifestShippingMethod',
    exact: true,
    component: ManifestShippingMethod,
    id: 'DCManifestShippingMethod',
    title: 'MANIFEST_SHIPPING_METHOD'
  },
  {
    path: '/manifestCourierCutoff',
    exact: true,
    component: ManifestCourierCutoff,
    id: 'DCCourierCutOff',
    title: 'COURIER_CUTOFF'
  },
  {
    path: '/bagShipments/:manifestId?',
    exact: true,
    component: BagShipments,
    id: 'DCBagShipments',
    title: 'BAG_SHIPMENTS'
  },
  {
    path: '/adhocASRSPicking',
    exact: true,
    component: AdhocASRSPicking,
    id: 'OrderASRSPicking',
    title: 'ADHOC_ASRS_PICKING'
  },
  {
    path: '/adhocASRSPickingDetails/:transferId',
    exact: true,
    component: AdhocASRSPickingDetails,
    id: 'OrderASRSPicking',
    title: 'ADHOC_ASRS_PICKING'
  },
  {
    path: '/jobCard',
    exact: true,
    component: JobCardPrinting,
    id: 'OrderJobCardPrinting',
    title: 'JOB_CARD_PRINTING'
  },

  {
    path: '/trayReassignment/:trayId?',
    exact: true,
    component: TrayReassignment,
    id: 'OrderTrayReassignment',
    title: 'TRAY_REASSIGNMENT'
  },
  {
    path: '/manifest-detail/:manifestId?',
    exact: true,
    component: ManifestDetails,
    id: 'OrderManifestDetails',
    title: 'MANIFEST_DETAILS'
  },
  {
    path: '/manifest',
    exact: true,
    component: Manifest,
    id: 'OrderManifest',
    title: 'MANIFEST'
  },
  {
    path: '/packing-summary',
    exact: true,
    component: PackingSummary,
    id: 'OrderPacking',
    title: 'PACKING_PANEL'
  },
  {
    path: '/qc',
    exact: true,
    component: QualityCheck,
    id: 'OrderOrderQC',
    title: 'QUALITITY_CHECK_PANEL'
  },
  {
    path: '/packing',
    exact: true,
    component: PackingPanel,
    id: 'OrderPacking',
    hideGlobalTopBar: true
  },
  {
    path: '/qc-summary',
    exact: true,
    name: 'QCSummary',
    component: QCSummary,
    id: 'OrderOrderQC',
    title: 'QUALITITY_CHECK_PANEL'
  },
  {
    path: '/priceControl',
    exact: true,
    name: 'Price Control',
    id: 'priceControl',
    component: PriceControl,
    title: 'PRICE_CONTROL_USER_ACCESS'
  },
  {
    path: '/addInventory',
    exact: true,
    component: AddInventory,
    id: 'AddInventory',
    title: 'INVENTORY_CONTROL'
  },
  {
    path: '/trayRelease',
    exact: true,
    component: StoreConsolidation,
    id: 'AutomationTrayRelease',
    title: 'STORE_CONSOLIDATION'
  },
  {
    path: '/trayRelease/history',
    exact: true,
    component: StoreConsolidation,
    id: 'AutomationTrayRelease',
    title: 'STORE_CONSOLIDATION'
  },
  {
    path: '/startRetire',
    exact: true,
    id: 'startRetire',
    component: StartRetire,
    title: 'START_RETIRE'
  },
  {
    path: '/reactiveTransfer',
    exact: true,
    component: ReactiveTransfer,
    id: 'reactiveTransfer',
    title: 'REACTIVE_TRANSFER'
  },
  {
    path: '/autojit/ordersearch/:barcode?',
    exact: true,
    component: JitOrderSearch,
    id: 'JitOrderSearch',
    title: 'JIT_ORDER_SEARCH'
  },
  {
    path: '/MEIHandEdging/:type',
    exact: true,
    component: MEIBlocking,
    id: 'MEIBlocking',
    title: 'MEI_BLOCKING'
  },
  {
    path: '/MEIHandEdging',
    exact: true,
    component: MEIHandEdging,
    id: 'MEIHandEdging',
    title: 'HAND_EDGING'
  },
  {
    path: '/edgingQC',
    exact: true,
    component: EdgingQC,
    id: 'edgingQC',
    title: 'EDGING_QC'
  },
  {
    path: '/invoicePosting',
    exact: true,
    component: InvoicePosting,
    id: 'invoicePosting',
    title: 'INVOICE_POSTING'
  },
  {
    path: '/marketPlace/distributorOrders/list',
    exact: true,
    component: OrderDashboard,
    id: 'marketPlaceDOOrderList',
    title: 'ORDER_DASHBOARD'
  },
  {
    path: '/marketPlace/distributorOrders/create',
    exact: true,
    component: CreateOrder,
    id: 'marketPlaceDOOrderCreate',
    title: 'CREATE_NEW_ORDER'
  },
  {
    path: '/finance/distributorOrders',
    exact: true,
    component: FinanceDashboard,
    id: 'financeDOOrdersDashboard',
    title: 'DO_DASHBOARD'
  },
  {
    path: '/PickingScan/:type/:shippingPackageID/:summaryId',
    exact: true,
    component: PickingScan,
    id: 'inventoryTransferScan',
    title: ''
  },
  {
    path: '/distributorOrder/Shipment/List',
    exact: true,
    component: DistributorOrderShipmentList,
    id: 'doShipmentList',
    title: 'DISTRIBUTOR_ORDER_SHIPMENT_LIST'
  },
  {
    path: '/distributorOrder/Shipment/Details/:shippingPackageID',
    exact: true,
    component: DistributorOrderShipmentDetails,
    id: 'doShipmentDetailsList',
    title: 'DISTRIBUTOR_ORDER_SHIPMENT_DETAILS'
  },
  {
    path: '/customer/list',
    exact: true,
    component: CustomerDashboard,
    id: 'customerList',
    title: 'CUSTOMER_DASHBOARD'
  },
  {
    path: '/customer/:action/:id?',
    exact: true,
    component: CustomerFormsMain,
    id: 'customerDashboard',
    title: 'Customer Forms'
  },
  {
    path: '/vendorShipment/list',
    exact: true,
    component: VendorShipmentList,
    id: 'vendorShipmentList',
    title: 'VENDOR_SHIPMENT'
  },
  {
    path: '/vendorShipment/details/:id',
    exact: true,
    component: VendorShipmentDetailsList,
    id: 'vendorShipmentDetailsList',
    title: 'VENDOR_SHIPMENT'
  },
  {
    path: '/vendorShipment/:type/:shipmentId?',
    exact: true,
    component: CreateVendorShipment,
    id: 'createOrEditVendorShipment',
    title: 'VENDOR_SHIPMENT'
  },
  {
    path: '/vendorShipmentDetails/:shipmentId',
    exact: true,
    component: VendorShipmentCreateSummary,
    id: 'POShipment',
    title: 'PO_SHIPMENT'
  },
  {
    path: '/stockTake/list',
    exact: true,
    component: StockTakeList,
    id: 'stockTakeList',
    title: 'STOCK_TAKE'
  },
  {
    path: 'stockTake/scan/:stockTakeId',
    exact: true,
    component: stockTakeBarcodeScan,
    id: 'stockTakeBarcodeScan',
    title: 'STOCK_TAKE'
  },
  {
    path: '/bulk-order-picking',
    exact: true,
    component: BulkOrderPicking,
    id: 'bulkOrderPicking',
    title: 'BULK_ORDER_PICKING'
  },
  {
    path: '/bulk-order-summary/:shipmentId',
    component: BulkOrderDetails,
    id: 'bulkOrderPickingSummary',
    title: 'DO_RETURN'
  },
  {
    path: '/RTVgatepass/list',
    component: RTVgatepassList,
    id: 'RTVgatepassList',
    title: 'RTV_GATEPASS_LIST'
  },
  {
    path: '/RTVgatepass/details/:gatepassNum',
    component: RTVgatepassDetailsList,
    id: 'RTVgatepassList',
    title: 'RTV_GATEPASS_LIST'
  },
  {
    path: '/RTVgatepass/scan/:gatepassNum',
    component: RTVScanTransfer,
    id: 'RTVgatepassList',
    title: 'RTV_GATEPASS_LIST'
  },
  {
    path: '/autogrn/startReceiving',
    component: StartRecieving,
    id: 'lenslab',
    title: 'JIT_STOCKIN'
  },
  {
    path: '/autogrn/addBarcodes',
    component: AddBarcode,
    id: 'addBarcode',
    title: 'ADD_BARCODES'
  },
  {
    path: '/autogrn/grnsummary',
    component: AutoGrnSummary,
    id: 'autoGrnSummary',
    title: 'AUTO_GRN_SUMMARY'
  },
  {
    path: '/autogrn/warehouseReceiving',
    component: WarehouseRecieving,
    id: 'warehouseReceiving',
    title: 'WAREHOUSE_RECIEVING'
  },
  {
    path: '/distributorOrders/:doType',
    exact: false,
    component: DOReturnReverse,
    id: 'doReturn',
    title: 'DO_RETURN'
  },
  {
    path: '/asrsManualSync',
    component: ASRSManualSyncList,
    id: 'ASRSManualSync',
    title: 'ASRS_MANUAL_SYNC'
  }
];

export default routes;
