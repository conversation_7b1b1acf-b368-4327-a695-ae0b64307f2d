import { call, put, all, takeLatest, takeEvery } from 'redux-saga/effects';
import { fileDownload } from 'utils/helpers';
import {
  changeTransferStatus<PERSON>pi,
  deleteAbarcode<PERSON>pi,
  fetchPidData<PERSON><PERSON>,
  editCreateTransfertApi,
  downloadCsvFile<PERSON>pi,
  uploadTransferCsvApi,
  getTransferDetailsApi,
  getBarcodesForTid<PERSON>pi,
  createdTransferListApi,
  scanBarcodeApi,
  downloadTransferSummaryApi,
  downloadTransferDetailsApi,
  deleteBoxFromTransferApi,
  validateTransferBoxApi,
  transferPrintInvoiceTemplateApi,
  replaceFaultyBarcodeApi,
  generateAWBnumberApi,
  transferPrintShipping<PERSON>abel<PERSON>pi,
  validateBox<PERSON><PERSON>
} from '../../../apis/bulkInventory/bulkInventory';
import {
  changeTransferStatusSuccess,
  changeTransferStatusFail,
  deleteAbarcodeSuc<PERSON>,
  deleteAbarcodeF<PERSON>,
  createdTransferList<PERSON>uc<PERSON>,
  createdTransferListFail,
  scanBarcodeSuccess,
  scanBarcodeFail,
  fetchPidDataSuccess,
  fetchPidDataFail,
  editCreateTransferSuccess,
  editCreateTransferFail,
  downloadTransferCsvSuccess,
  downloadTransferCsvFail,
  uploadTransferCsvSuccess,
  uploadTransferCsvFail,
  transferDetailsSuccess,
  transferDetailsFail,
  getBarcodesForTidSuccess,
  getBarcodesForTidFail,
  downloadTransferSummarySuccess,
  downloadTransferSummaryFail,
  downloadTransferDetailsSuccess,
  downloadTransferDetailsFail,
  deleteBoxFromTransferSuccess,
  deleteBoxFromTransferFail,
  validateTransferBoxSuccess,
  transferPrintInvoiceSuccess,
  transferPrintInvoiceFail,
  replaceFaultyBarcodeFail,
  replaceFaultyBarcodeSuccess,
  generateAWBnumberSuccess,
  generateAWBnumberFail,
  transferPrintShippingLabelSuccess,
  transferPrintShippingLabelFail,
  validateBoxBarcodeSuccess,
  validateBoxBarcodeFail
} from '../../actionCreators/bulkInventory/bulkInventory';
import {
  CHANGE_TRANSFER_STATUS_LOAD,
  DELETE_A_BARCODE_LOAD,
  GET_CREATED_TRANSFER_LOAD,
  SCAN_BARCODE_LOAD,
  FETCH_PID_DATA_LOAD,
  EDIT_CREATE_TRANSFER_LOAD,
  DOWNLOAD_TRANSFER_CSV_LOAD,
  UPLOAD_CREATE_TRANSFER_LOAD,
  GET_TRANSFER_DETAILS_LOAD,
  GET_BARCODES_LOAD,
  DOWNLOAD_TRANSFER_SUMMARY_LOAD,
  DOWNLOAD_TRANSFER_DETAILS_LOAD,
  DELETE_BOX_TRANSFER_REQUEST,
  VALIDATE_BOX_TRANSFER_REQUEST,
  TRANSFERS_PRINT_INVOICE_LOAD,
  REPLACE_FAULTY_BARCODE_REQUEST,
  GENERATE_AWB_NUMBER_REQUEST,
  TRANSFER_PRINT_SHIPPING_LABEL_REQUEST,
  VALIDATE_BOX_BARCODE_REQUEST
} from '../../actionTypes/bulkInventory';
import { toastOpen } from '../../actionCreators/toast';
import { showToaster } from '../toast';

function* fetchPidData(action = { payload: {} }) {
  try {
    const response = yield call(fetchPidDataApi, action.payload);
    yield put(fetchPidDataSuccess(response.data));
  } catch (er) {
    yield put(fetchPidDataFail(action.payload));
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: er?.response?.data?.meta?.displayMessage || 'Product Details not found',
        subHeading: 'Please Enter Valid PID.',
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  }
}

function* editCreateTransferList(action = { payload: {} }) {
  try {
    const response = yield call(editCreateTransfertApi, action.payload);
    yield put(editCreateTransferSuccess(response.data.data));
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Transfer Updated',
        subHeading: response.data?.data.message || 'Transfer Updated',
        severity: 'success',
        autoHideDuration: 2000
      })
    );
  } catch (er) {
    yield put(editCreateTransferFail(er));
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Transfer Not Updated',
        subHeading:
          er?.response?.data?.meta?.display_message || 'Something went wrong please try again',
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  }
}
function* downloadSampleCsv(action = { payload: {} }) {
  try {
    const data = yield call(downloadCsvFileApi, action.payload);
    yield put(downloadTransferCsvSuccess(data));
  } catch (er) {
    yield put(downloadTransferCsvFail(er));
  }
}
function* uploadTransferCsv(action = { payload: {} }) {
  try {
    const data = yield call(uploadTransferCsvApi, action.payload);
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Transfer Created',
        severity: 'success',
        autoHideDuration: 2000
      })
    );
    yield put(uploadTransferCsvSuccess(data));
  } catch (er) {
    yield showToaster(er);
    yield put(uploadTransferCsvFail(er));
  }
}

function* getTransferDetails(action = { payload: {} }) {
  try {
    const response = yield call(getTransferDetailsApi, action.payload);
    yield put(transferDetailsSuccess(response.data, action.payload.checkBarcode));
  } catch (er) {
    yield put(transferDetailsFail(er?.response?.data));
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Error!',
        subHeading:
          er?.response?.data?.meta?.display_message || 'Something went wrong please try again',
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  }
}

function* getBarcodesForTid(action = { payload: {} }) {
  try {
    const response = yield call(getBarcodesForTidApi, action.payload);
    yield put(getBarcodesForTidSuccess(response.data));
  } catch (er) {
    yield put(getBarcodesForTidFail(er));
  }
}

// Create Transfer
function* getCreatedTransferList(action = { payload: {} }) {
  try {
    const response = yield call(createdTransferListApi, action.payload);
    yield put(createdTransferListSuccess(response.data));
  } catch (er) {
    yield put(createdTransferListFail(er));
  }
}

function* scanBarcodeList(action = { payload: {} }) {
  const { barcode, box_barcode } = action.payload;
  try {
    const response = yield call(scanBarcodeApi, action.payload);
    yield put(scanBarcodeSuccess(response.data));
  } catch (er) {
    yield put(scanBarcodeFail({ er, barcode, box_barcode }));
    yield showToaster(er);
  }
}

function* deleteAbarcode(action = { payload: {} }) {
  try {
    const response = yield call(deleteAbarcodeApi, action.payload);
    yield put(deleteAbarcodeSuccess(response.data));
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Success',
        subHeading: response.data.message || 'Successfully deleted barcode!!',
        severity: 'success',
        autoHideDuration: 2000
      })
    );
  } catch (er) {
    yield put(deleteAbarcodeFail(action.payload));
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Error!',
        subHeading:
          er?.response?.data?.meta?.display_message || 'Something went wrong please try again',
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  }
}

function* changeTransferStatus({ payload }) {
  try {
    yield call(changeTransferStatusApi, payload);
    yield put(
      changeTransferStatusSuccess({ status: payload.status, updatedAt: new Date().toUTCString() })
    );
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Success',
        subHeading: 'Transfer Status Changed!!',
        severity: 'success',
        autoHideDuration: 2000
      })
    );
  } catch (er) {
    yield put(changeTransferStatusFail());
    let defaultErrorMessage =
      er?.response?.data?.meta?.display_message || 'Something went wrong please try again';
    if (er.response.status === 417) {
      fileDownload(er.response.data, `${payload.transfer_code}-errors`);
      defaultErrorMessage = 'Error details downloaded, Please check';
    }
    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Error!',
        subHeading: defaultErrorMessage,
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  }
}

function* downloadTransferSummary(action = { payload: {} }) {
  try {
    const data = yield call(downloadTransferSummaryApi, action.payload);
    yield put(downloadTransferSummarySuccess(data));
  } catch (er) {
    yield put(downloadTransferSummaryFail(er));
  }
}

function* downloadTransferDetails({ payload }) {
  try {
    const response = yield call(downloadTransferDetailsApi, payload);
    yield put(downloadTransferDetailsSuccess(response.data));
  } catch (er) {
    yield put(downloadTransferDetailsFail(er));
  }
}

function* deleteBoxFromTransfer({ payload }) {
  try {
    const response = yield call(deleteBoxFromTransferApi, payload);
    if (response?.data?.data) {
      yield put(deleteBoxFromTransferSuccess(response?.data?.data));
      yield put(
        toastOpen({
          isToastOpen: true,
          heading: 'Success',
          subHeading: response?.data?.data.message || 'Successfully deleted !!',
          severity: 'success',
          autoHideDuration: 2000
        })
      );
    }
  } catch (error) {
    yield put(deleteBoxFromTransferFail(payload));
    yield showToaster(error);
  }
}

function* validateTransferBox({ payload }) {
  try {
    const response = yield all(
      payload.boxes.map((eachBox) =>
        call(validateTransferBoxApi, { box_barcode: eachBox, transfer_code: payload.transferId })
      )
    );
    yield all(
      response.map((eachResponse) => put(validateTransferBoxSuccess(eachResponse.data.data)))
    );
  } catch (error) {
    yield showToaster(error);
  }
}

function* transferPrintInvoiceTemplateSaga({ payload }) {
  try {
    const { data } = yield call(transferPrintInvoiceTemplateApi, payload);
    yield put(transferPrintInvoiceSuccess(data));
  } catch (error) {
    yield showToaster(error);
    yield put(transferPrintInvoiceFail());
  }
}

function* replaceFaultyBarcode({ payload }) {
  try {
    yield call(replaceFaultyBarcodeApi, payload);
    yield getBarcodesForTid({
      payload: { pId: payload.pid, transfer_id: payload.transferItemId }
    });
    yield put(replaceFaultyBarcodeSuccess());
  } catch (error) {
    yield put(replaceFaultyBarcodeFail());
    yield showToaster(error);
  }
}

function* generateAWBnumberSaga({ payload }) {
  try {
    const response = yield call(generateAWBnumberApi, payload);
    yield showToaster(null, 'success', 'AWB Number Created Successfully');
    yield put(generateAWBnumberSuccess(response));
  } catch (error) {
    yield put(generateAWBnumberFail());
    yield showToaster(error);
  }
}

function* transferPrintShippingLabelSaga({ payload }) {
  try {
    const response = yield call(transferPrintShippingLabelApi, payload);
    yield put(transferPrintShippingLabelSuccess(response));
  } catch (error) {
    yield put(transferPrintShippingLabelFail());
    yield showToaster(error);
  }
}

function* validateBoxSaga({ payload }) {
  try {
    yield call(validateBoxApi, payload);
    yield put(validateBoxBarcodeSuccess(payload));
  } catch (error) {
    yield put(validateBoxBarcodeFail());
    yield showToaster(error);
  }
}

export function* runBulkInventoryCalls() {
  yield all([
    takeLatest(FETCH_PID_DATA_LOAD, fetchPidData),
    takeLatest(EDIT_CREATE_TRANSFER_LOAD, editCreateTransferList),
    takeLatest(DOWNLOAD_TRANSFER_CSV_LOAD, downloadSampleCsv),
    takeLatest(UPLOAD_CREATE_TRANSFER_LOAD, uploadTransferCsv),
    takeLatest(GET_TRANSFER_DETAILS_LOAD, getTransferDetails),
    takeLatest(GET_BARCODES_LOAD, getBarcodesForTid),
    takeEvery(GET_CREATED_TRANSFER_LOAD, getCreatedTransferList),
    takeEvery(SCAN_BARCODE_LOAD, scanBarcodeList),
    takeLatest(DELETE_A_BARCODE_LOAD, deleteAbarcode),
    takeLatest(CHANGE_TRANSFER_STATUS_LOAD, changeTransferStatus),
    takeLatest(DOWNLOAD_TRANSFER_SUMMARY_LOAD, downloadTransferSummary),
    takeLatest(DOWNLOAD_TRANSFER_DETAILS_LOAD, downloadTransferDetails),
    takeLatest(DELETE_BOX_TRANSFER_REQUEST, deleteBoxFromTransfer),
    takeLatest(VALIDATE_BOX_TRANSFER_REQUEST, validateTransferBox),
    takeLatest(TRANSFERS_PRINT_INVOICE_LOAD, transferPrintInvoiceTemplateSaga),
    takeLatest(REPLACE_FAULTY_BARCODE_REQUEST, replaceFaultyBarcode),
    takeLatest(GENERATE_AWB_NUMBER_REQUEST, generateAWBnumberSaga),
    takeLatest(TRANSFER_PRINT_SHIPPING_LABEL_REQUEST, transferPrintShippingLabelSaga),
    takeLatest(VALIDATE_BOX_BARCODE_REQUEST, validateBoxSaga)
  ]);
}
