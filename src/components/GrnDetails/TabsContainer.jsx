import React, { useEffect, useState } from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { withStyles, makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import GrnPutaway from 'views/Grn/GrnPutaway';
import IQCTable from 'components/InwardQC/IQCTable';

import { LOCALISATION } from 'redux/reducers/localisation';
import GRNTab from './GRNTab';
import './TabsContainer.scss';

const styles = makeStyles()((_, { isIqc, grnClosure }) => ({
  tableContainer: {
    padding: isIqc && grnClosure ? '24px 0 24px 24px' : 24,
    width: '100%',
    margin: '0 auto'
  },
  positionRelative: {
    position: 'relative'
  },
  tab1Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 0
  },
  tab2Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 160
  }
}));

const StyledTabs = withStyles(
  (props) => <Tabs {...props} TabIndicatorProps={{ children: <span /> }} />,
  {
    indicator: {
      backgroundColor: 'transparent'
    }
  }
);

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`simple-tabpanel-${index}`}
    aria-labelledby={`simple-tab-${index}`}
    {...other}
  >
    {value === index && <Box>{children}</Box>}
  </div>
);

const TabsContainer = ({ mainGrnSearch, grnCode, setIsIqc, isIqc, grnClosure, showIqc }) => {
  const { classes } = styles({ isIqc, grnClosure });
  const params = useParams();
  const navigate = useNavigate();
  const [value, setValue] = useState(0);
  const { getGrnPids } = useSelector((state) => state.grnDetails);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_DETAIL);

  const handleChange = (event, newValue) => {
    setValue(newValue);
    if (value === 0) {
      navigate(`/grn/details/${params.grnCode}/iqc`);
    } else {
      navigate(`/grn/details/${params.grnCode}`);
    }
    setIsIqc(Boolean(newValue));
  };

  useEffect(() => {
    if (isIqc) {
      setValue(1);
    } else {
      setValue(0);
    }
  }, [params.iqc]);

  return (
    <Box display="flex" className={classes.root}>
      <Box className={classes.tableContainer}>
        <StyledTabs value={value} onChange={handleChange}>
          <Tab
            label={`${CONTENT.PRODUCTS}  (${getGrnPids?.result?.data?.products.length || 0} )`}
            className={`${value === 0 ? 'tabselected' : ''} tabs`}
          />
          {showIqc && (
            <Tab label={CONTENT.IQC} className={`${value === 1 ? 'tabselected' : ''} tabs`} />
          )}
        </StyledTabs>
        <Box className={`border-grey5-radiusbase ${value === 0 && 'border-left-no-radius'}`}>
          <TabPanel value={value} index={0} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab1Absolute} />
            <GRNTab grnCode={grnCode} mainGrnSearch={mainGrnSearch} handleChange={handleChange} />
          </TabPanel>
          {showIqc && (
            <TabPanel value={value} index={1} className={classes.positionRelative}>
              <Box width={158} component="div" height={2} className={classes.tab2Absolute} />
              <IQCTable />
            </TabPanel>
          )}
        </Box>
      </Box>

      {isIqc && grnClosure && (
        <Box width="40px">
          <GrnPutaway />
        </Box>
      )}
    </Box>
  );
};

export default TabsContainer;
