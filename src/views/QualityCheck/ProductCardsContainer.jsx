import React, { useCallback, useEffect, useState, useRef } from 'react';
import { Box } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

import { setHoldUnholdVisit } from 'redux/actionCreators/order';
import constant from 'constant';
import localStorageHelper from 'utils/localStorageHelper';
import { fittingByStationCodeLoad } from 'redux/actionCreators/qc';
import { toastOpen } from 'redux/actionCreators/toast';
import { keys } from 'utils/lodash';
import config from '../../config';
import ProductCard from './ProductCard';
import LensCard from './LensCard';
import QCHoldModal from './QCHoldModal';
import QCUnholdModal from './QCUnholdModal';

import { Style_ProductCardsContainer } from './Style';

const styles = makeStyles()(() => ({ ...Style_ProductCardsContainer }));

const ProductCardsContainer = () => {
  const { classes } = styles();
  const dispatch = useDispatch();

  const { shipment, shipmentHeaders } = useSelector((state) => state.order);
  const { product, right, left } = shipment[0];
  const isBulkOrder = shipmentHeaders?.navChannel.toLowerCase().includes('bulk');

  const facilityCode = useSelector((state) => state.settings.selectedFacility);
  const qcLensPowerDetailsFacilityList = useSelector(
    (state) => state.consul.featureToggle.data?.qcLensPowerDetailsFacilityList ?? {}
  );

  const { isLoading: reScanFittingLoading } = useSelector((state) => state.qc.qcLensoMeterData);

  const [qcHoldModal, showQCHoldModal] = useState({ show: false, uwItemId: null });
  const [qcUnholdModal, showQCUnoldModal] = useState({ show: false, uwItemId: null });
  const [isFail, setIsFail] = useState(false);
  const stationCode = localStorageHelper.getItem(constant.WORKSTATION) ?? [];
  const [rightLensData, setRightLensData] = useState(null);
  const [leftLensData, setLeftLensData] = useState(null);

  const eventSourceRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const hasCalledFittingApi = useRef(false);

  const facilityUrl =
    keys(qcLensPowerDetailsFacilityList)?.length > 0
      ? qcLensPowerDetailsFacilityList?.[facilityCode]
      : '';
  // Selected and Product Facility Code should be same
  const isSameFacility = product.order.facilityCode === facilityCode;

  const isShowLensoMeterPowerDetails = !!facilityUrl && isSameFacility && !isBulkOrder;

  const qcHoldHandler = useCallback((show, uwItemId = null) => {
    showQCHoldModal({ show, uwItemId });
    if (!show) {
      dispatch(setHoldUnholdVisit(true));
    }
  }, []);

  const qcUnholdHandler = useCallback((show, uwItemId = null, isFailProp = false) => {
    setIsFail(isFailProp);
    showQCUnoldModal({ show, uwItemId });
    if (!show && !isFailProp) {
      dispatch(setHoldUnholdVisit(true));
    }
  }, []);

  const convertObjectValuesToString = (obj) =>
    Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [key, value != null ? String(value) : value])
    );

  const formatLensDataFromSSE = (data) => {
    if (!data) {
      return null;
    }
    return convertObjectValuesToString({ ...data });
  };

  // SSE connection setup
  useEffect(() => {
    const connectToEventSource = () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      const source = new EventSource(
        `${facilityUrl}${config.qcLensoMeter.qcLensometerEventsApi}/${stationCode}`
      );
      eventSourceRef.current = source;

      source.onmessage = (event) => {
        try {
          const response = JSON.parse(event.data);
          const isConnected =
            response.station && response.station.toUpperCase().includes(stationCode.toUpperCase());
          if (response.station) {
            if (isConnected && !hasCalledFittingApi.current) {
              hasCalledFittingApi.current = true;
              dispatch(
                fittingByStationCodeLoad({
                  url: facilityUrl ?? '',
                  stationCode,
                  fittingId: left.order.fittingId
                })
              );
            } else if (!isConnected) {
              dispatch(
                toastOpen({
                  isToastOpen: true,
                  heading: `Please map the workstation to the 
                    lensometer to view the lensometer power details.`,
                  severity: 'info',
                  autoHideDuration: 4000
                })
              );
            }
          }
          if (response?.data?.fittingId !== left?.order?.fittingId) {
            return;
          }

          const formattedData = formatLensDataFromSSE(response.data);

          if (response.type === 'RIGHT_LENS_DATA') {
            setRightLensData(formattedData);
          }
          if (response.type === 'LEFT_LENS_DATA') {
            setLeftLensData(formattedData);
          }
        } catch (err) {
          // eslint-disable-next-line no-console
          console.error('SSE parse error:', err);
        }
      };

      source.onerror = () => {
        source.close();
        reconnectTimeoutRef.current = setTimeout(connectToEventSource, 3000);
      };
    };

    if (stationCode && isShowLensoMeterPowerDetails && facilityUrl && isSameFacility) {
      connectToEventSource();
    }

    return () => {
      eventSourceRef.current?.close();
      clearTimeout(reconnectTimeoutRef.current);
      hasCalledFittingApi.current = false; // Reset on unmount
    };
  }, [stationCode, left?.order?.fittingId, isShowLensoMeterPowerDetails]);

  useEffect(() => {
    if (reScanFittingLoading) {
      setRightLensData(null);
      setLeftLensData(null);
    }
  }, [reScanFittingLoading]);

  return (
    <div className={classes.container} style={{ width: left && right ? '100%' : 'min-content' }}>
      <ExpandLessIcon className={classes.expandIcon} />
      <div className={classes.expandIconBelowWhite} />
      <Box className={classes.productCard}>
        <ProductCard
          product={product}
          qcHoldHandler={qcHoldHandler}
          qcUnholdHandler={qcUnholdHandler}
        />
      </Box>
      {right && (
        <Box className={classes.lensCard}>
          <LensCard
            eye="right"
            lens={right}
            qcHoldHandler={qcHoldHandler}
            qcUnholdHandler={qcUnholdHandler}
            qcLensoMeterData={rightLensData}
          />
        </Box>
      )}
      {left && (
        <Box className={classes.lensCard}>
          <LensCard
            eye="left"
            lens={left}
            qcHoldHandler={qcHoldHandler}
            qcUnholdHandler={qcUnholdHandler}
            qcLensoMeterData={leftLensData}
          />
        </Box>
      )}
      {qcHoldModal.show && (
        <QCHoldModal
          open={qcHoldModal.show}
          uwItemId={qcHoldModal.uwItemId}
          closedHandler={qcHoldHandler}
        />
      )}
      {qcUnholdModal.show && (
        <QCUnholdModal
          open={qcUnholdModal.show}
          isFail={isFail}
          uwItemId={qcUnholdModal.uwItemId}
          closedHandler={qcUnholdHandler}
        />
      )}
    </div>
  );
};

export default ProductCardsContainer;
