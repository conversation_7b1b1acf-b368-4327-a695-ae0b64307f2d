import {
  MON<PERSON><PERSON>_DEFAULT_COLUMNS,
  MONITOR_FR_TYPES,
  MON<PERSON>OR_TYPES,
  AGING_VALUE_MAPPING,
  FILTERS,
  FILTER_CHIPS_NAME,
  BINARY_SWITCH_MAPPING,
  CATEGORY_LABEL_MAPPING
} from 'config/monitor';
import { createSelector } from 'reselect';
import { invert, keys } from 'utils/lodash';
// eslint-disable-next-line import/no-extraneous-dependencies
import {
  MONITOR_NODE_CONFIG,
  MONITOR_EDGE_CONFIG
} from 'views/MonitorV3/MonitorBody/FlowChart/workflowConfig';

const monitorList = (state) => state.monitor.monitorList;
const jitMonitorList = (state) => state.monitor.jitMonitorList;
const selectedFilters = (state) => state.monitor.filters;
const totalOrdersData = (state) => state.monitor.totalOrdersList;

const REVERSE_VALUE = invert(AGING_VALUE_MAPPING);

const rangeLogic = (name, filters) => {
  const keyType = 'RANGE';
  const values = {
    startValue: '',
    endValue: ''
  };
  values.startValue =
    filters[name].startValue === '-1'
      ? Number(REVERSE_VALUE[filters[name].startValue])
      : filters[name].startValue;
  values.endValue =
    filters[name].endValue === '-1'
      ? Number(REVERSE_VALUE[filters[name].endValue])
      : filters[name].endValue;
  FILTERS.AGEING.forEach((aging) => {
    if (aging.value === values.startValue) {
      values.startValue = aging.label;
    }
    if (aging.value === values.endValue) {
      values.endValue = aging.label;
    }
  });
  return { values, keyType };
};

export const getSelectedFilters = createSelector(selectedFilters, (filterData) => {
  const filters = filterData.selected.data;
  const finalSelectedFilters = [];

  Object.keys(filters).forEach((name) => {
    let values;
    let type;

    switch (name) {
      case 'AGEING': {
        if (!filters[name].startValue && filters[name].endValue === '-1') {
          return;
        }
        const { values: value, keyType } = rangeLogic(name, filters);
        values = value;
        type = keyType;
        break;
      }

      case 'UPDATE_AGEING': {
        if (!filters[name].startValue && filters[name].endValue === '-1') {
          return;
        }
        const { values: value, keyType } = rangeLogic(name, filters);
        values = value;
        type = keyType;
        break;
      }

      case 'DATE_RANGE': {
        if (filters[name].from === '') {
          return;
        }
        type = 'DATE_RANGE';
        values = filters[name];
        break;
      }

      case 'BINARY': {
        values = Object.keys(filters[name]).filter((key) => filters[name][key] != null);
        values.forEach((key, index) => {
          // if filter value is false then change it with corresponding mapping value
          if (!filters[name][key]) {
            const newValue = BINARY_SWITCH_MAPPING[values[index]];
            values.splice(index, 1, newValue);
          }
        });

        if (!values.length) {
          return;
        }
        break;
      }

      case 'CATEGORY': {
        if (CATEGORY_LABEL_MAPPING[filters[name]]) {
          values = CATEGORY_LABEL_MAPPING[filters[name]];
        } else {
          return;
        }
        break;
      }

      default: {
        if (!filters[name].length) {
          return;
        }
        values = filters[name];
        break;
      }
    }

    const obj = {
      key: name,
      keyValue: FILTER_CHIPS_NAME[name],
      values,
      type
    };

    finalSelectedFilters.push(obj);
  });

  return finalSelectedFilters;
});

export const getMonitorDashboardData = createSelector(monitorList, ({ data, isLoading }) => {
  const newData = {
    ...data,
    [MONITOR_TYPES.TOTAL_ORDERS]: Array.isArray(data[MONITOR_TYPES.TOTAL_ORDERS])
      ? data[MONITOR_TYPES.TOTAL_ORDERS].reduce((filtered, each) => {
        const headerCount =
            data.headers[MONITOR_FR_TYPES[each[MONITOR_DEFAULT_COLUMNS.TAG_NAME]]];
        if (headerCount) {
          filtered.push({ ...each, [MONITOR_DEFAULT_COLUMNS.TOTAL]: headerCount });
        }
        return filtered;
      }, [])
      : [],
    [MONITOR_TYPES.FULFILLABLE]: data[MONITOR_TYPES.FULFILLABLE]?.map((each) => {
      if (each[MONITOR_DEFAULT_COLUMNS.TAG_NAME] === MONITOR_DEFAULT_COLUMNS.TOTAL) {
        const headerCount = data.headers[MONITOR_TYPES.FULFILLABLE];
        return { ...each, [MONITOR_DEFAULT_COLUMNS.TOTAL]: headerCount };
      }
      return each;
    }),
    [MONITOR_TYPES.UNFULFILLABLE_LENS]: data[MONITOR_TYPES.UNFULFILLABLE_LENS]?.map((each) => {
      if (each[MONITOR_DEFAULT_COLUMNS.TAG_NAME] === MONITOR_DEFAULT_COLUMNS.TOTAL) {
        const headerCount = data.headers[MONITOR_TYPES.UNFULFILLABLE_LENS];
        return { ...each, [MONITOR_DEFAULT_COLUMNS.TOTAL]: headerCount };
      }
      return each;
    }),
    [MONITOR_TYPES.UNFULFILLABLE_FRAME]: data[MONITOR_TYPES.UNFULFILLABLE_FRAME]?.map((each) => {
      if (each[MONITOR_DEFAULT_COLUMNS.TAG_NAME] === MONITOR_DEFAULT_COLUMNS.TOTAL) {
        const headerCount = data.headers[MONITOR_TYPES.UNFULFILLABLE_FRAME];
        return { ...each, [MONITOR_DEFAULT_COLUMNS.TOTAL]: headerCount };
      }
      return each;
    })
  };

  return {
    data: newData,
    isLoading
  };
});

export const getMonitorGraphSelector = createSelector(monitorList, ({ data, isLoading }) => {
  let newData = {};
  keys(MONITOR_TYPES).forEach((key) => {
    let dataItems = {};
    let eachColumnValue = {};
    data.standard[key]?.forEach((each, idx) => {
      if (each[MONITOR_DEFAULT_COLUMNS.TAG_NAME] !== MONITOR_DEFAULT_COLUMNS.TOTAL) {
        data.COLUMNS.forEach((column) => {
          eachColumnValue = {
            ...eachColumnValue,
            [column]: [
              each[column] || 0,
              data.critical[key][idx]?.[column] || 0,
              data.severe[key][idx]?.[column] || 0
            ]
          };
        });
        dataItems = {
          ...dataItems,
          [each[MONITOR_DEFAULT_COLUMNS.TAG_NAME]]: { ...eachColumnValue, total: each.TOTAL }
        };
      }
    });
    newData = { ...newData, [key]: dataItems };
  });

  return { ...newData, columns: data.COLUMNS, isLoading };
});

function findObject(data, orderType, columnData, severity) {
  return data?.[severity]?.[orderType]?.find((t) => t['Fr Tag'] === columnData['Fr Tag']) || {};
}

const configureNodesAndEdges = (data, orderType, categoryType) => {
  let newData = {};

  data?.[categoryType]?.[orderType].forEach((columnData) => {
    const frTag = columnData['Fr Tag'];
    if (frTag !== 'TOTAL') {
      const currentObj = findObject(data, orderType, columnData, 'current');
      const criticalObj = findObject(data, orderType, columnData, 'critical');
      const severeObj = findObject(data, orderType, columnData, 'severe');
      const totalObj = data.standard[orderType].find(
        (standardData) => frTag === standardData['Fr Tag']
      );

      const nodesConfig = MONITOR_NODE_CONFIG[frTag]
        ? MONITOR_NODE_CONFIG[frTag]
        : MONITOR_NODE_CONFIG.DEFAULT;
      const edgesConfig = MONITOR_EDGE_CONFIG[frTag]
        ? MONITOR_EDGE_CONFIG[frTag]
        : MONITOR_EDGE_CONFIG.DEFAULT;

      const newNodes = nodesConfig.map((eachNode) => ({
        ...eachNode,
        data: {
          ...eachNode.data,
          currentCount: currentObj[eachNode.data.key],
          criticalCount: criticalObj[eachNode.data.key],
          severeCount: severeObj[eachNode.data.key],
          totalCount: totalObj[eachNode.data.key]
        }
      }));

      const highestCount = Math.max(
        ...newNodes.map((eachNode) =>
          Math.max(
            eachNode.data.currentCount || 0,
            eachNode.data.criticalCount || 0,
            eachNode.data.severeCount || 0
          )
        )
      );

      newData = {
        ...newData,
        [columnData['Fr Tag']]: {
          nodes: newNodes.map((eachNode) => ({
            ...eachNode,
            data: { ...eachNode.data, highestCount }
          })),
          edges: edgesConfig,
          total: columnData.TOTAL
        }
      };
    }
  });
  return newData;
};

export const getMonitorFlowChartSelector = createSelector(
  monitorList,
  jitMonitorList,
  selectedFilters,
  (
    { data: monitorData, isLoading: monitorDataLoading },
    { data: jitMonitorData, isLoading: jitMonitorDataLoading },
    {
      selected: {
        data: { CATEGORY }
      }
    }
  ) => {
    const categoryType = CATEGORY || 'standard';

    const orderTypesData = {
      [MONITOR_TYPES.FULFILLABLE_ORDERS]: configureNodesAndEdges(
        monitorData,
        MONITOR_TYPES.FULFILLABLE_ORDERS,
        categoryType
      ),
      [MONITOR_TYPES.UNFULFILLABLE_ORDERS]: configureNodesAndEdges(
        monitorData,
        MONITOR_TYPES.UNFULFILLABLE_ORDERS,
        categoryType
      ),
      [MONITOR_TYPES.JIT_ORDERS]: configureNodesAndEdges(
        jitMonitorData,
        MONITOR_TYPES.JIT_ORDERS,
        categoryType
      )
    };
    return { orderTypesData, isLoading: monitorDataLoading || jitMonitorDataLoading };
  }
);

export const getMonitorHeaderSelector = createSelector(
  monitorList,
  jitMonitorList,
  ({ data: monitorData }, { data: jitMonitorData }) => ({
    monitorHeaders: { ...monitorData.headers, ...jitMonitorData.headers },
    reportAsOn: monitorData.reportAsOn,
    jitReportAsOn: jitMonitorData.reportAsOn,
    frTags: [...monitorData.FR_TAG, ...jitMonitorData.FR_TAG]
  })
);

export const getTotalOrdersListSelector = createSelector(totalOrdersData, ({ isLoading, data }) => {
  const { monitorPanelTagCountResponseList = [], frTagsList = [] } = data || {};
  let count = 0;

  const groupedByPriority = Object.values(
    monitorPanelTagCountResponseList.reduce((acc, item) => {
      const { pickingPriority, v3FrTag, totalCount } = item;
      count += totalCount;
      acc[pickingPriority] = { ...acc[pickingPriority], pickingPriority, [v3FrTag]: totalCount };
      return acc;
    }, {})
  );

  return { isLoading, data: groupedByPriority, headersList: frTagsList, count };
});
