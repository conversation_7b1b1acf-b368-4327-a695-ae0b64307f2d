import React, { Fragment } from 'react';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import LkToolTip from 'components/ToolTip/ToolTip';

const styles = makeStyles()(() => ({
  root: {
    display: 'flex',
    alignItems: 'center'
  },
  container: {
    flex: 1
  },
  divider: {
    width: '40px',
    background: '#3C3C3C8A',
    height: 1,
    transform: 'rotate(90deg)'
  },
  heading: {
    fontSize: 12,
    fontWeight: 400,
    color: '#3C3C3C8A',
    lineHeight: '14px'
  },
  value: {
    fontSize: 20,
    fontWeight: 500,
    color: '#3C3C3C8A',
    lineHeight: '32px',
    cursor: 'pointer'
  }
}));

const InfoBarItems = ({ items }) => {
  const { classes } = styles();
  return (
    <Box className={classes.root}>
      {items.map(({ header, value }, index) => (
        <Fragment key={header}>
          <Box className={classes.container}>
            <Box className={classes.heading}>{header}</Box>
            <LkToolTip
              placement="bottom-end"
              title={value && <Box p={1}>{value} </Box>}
              className="dtoolTip"
            >
              <Box className={`ellipsis-vertical-one ${classes.value}`}>{value ?? '--'}</Box>
            </LkToolTip>
          </Box>
          {items.length !== index + 1 && <Box className={classes.divider} />}
        </Fragment>
      ))}
    </Box>
  );
};

export default InfoBarItems;
