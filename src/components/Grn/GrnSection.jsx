import React, { useState, useEffect, useCallback } from 'react';
// import { useSelector } from 'react-redux';
import { useSelector } from 'react-redux';

import PIDInfo from './PIDInfo';
import GrnItemsInfo from './GrnItemsInfo';
import QcStatus from './QcStatus';

const boxCodeRegex = [];

const GrnSection = (props) => {
  const [searchedValue, setSearchedValue] = useState('');
  const {
    qcDetailsSuccess,
    qcDetailsError,
    isProceedToGreenChannel,
    updateQtyData,
    barCodeSeries
  } = useSelector((state) => state.grn);
  const {
    createGrnLocal,
    addPidModal,
    toggleModal,
    pidDetail = {
      result: {
        data: {
          estimated_qty: 0,
          lot_offset: 0,
          total_scanned: 0,
          qc_pass_count: 0,
          qc_fail_count: 0
        }
      }
    }
  } = props;
  const { status, pid } = pidDetail.result.data;
  const [pidStatus, setPidStatus] = useState(status);

  useEffect(() => {
    if (qcDetailsSuccess) {
      const tempStatus = qcDetailsSuccess.result.data.status;
      if (tempStatus !== 'PASSED') {
        setPidStatus(tempStatus);
      }
    }
  }, [qcDetailsSuccess, pid]);

  useEffect(() => {
    if (qcDetailsError) {
      const { data, status: statusCode } = qcDetailsError;
      if (statusCode === 406 && data.error_code === 'LK-GRN-008') {
        setPidStatus('FAILED');
      }
    }
  }, [qcDetailsError]);

  useEffect(() => {
    if (updateQtyData) {
      setPidStatus('PENDING');
    }
  }, [updateQtyData]);

  useEffect(() => {
    if (barCodeSeries) {
      barCodeSeries.forEach((t) => {
        if (t.type !== 'item') {
          const split_string = t.start.split(/(\d+)/);
          boxCodeRegex.push(split_string[0]);
        }
      });
    }
  }, [barCodeSeries]);

  useEffect(() => {
    if (isProceedToGreenChannel[pid]) {
      setPidStatus('PASSED');
    }
  }, [isProceedToGreenChannel, pid]);
  const { estimated_qty, lot_offset, total_scanned, qc_pass_count, qc_fail_count, min_to_scan } =
    pidDetail?.result?.data || {};

  const setSearchedValueFun = useCallback(
    (value) => {
      setSearchedValue(value);
    },
    [setSearchedValue]
  );
  return (
    <div className="display-flex">
      <PIDInfo pidStatus={pidStatus} pid={pid} createGrnLocal={createGrnLocal} />
      <div className="mr-l16 flex1">
        <GrnItemsInfo
          clearSearch={setSearchedValueFun}
          pidStatus={pidStatus}
          estimatedQty={estimated_qty}
          min_to_scan={min_to_scan}
          lot_offset={lot_offset}
          total_scanned={total_scanned}
          qc_pass_count={qc_pass_count}
          qc_fail_count={qc_fail_count}
          toggleModal={toggleModal}
          addPidModal={addPidModal}
          createGrnLocal={createGrnLocal}
        />
        <div className="bg-white  mr-t16 overflow-hidden">
          <QcStatus
            boxCodeRegex={boxCodeRegex}
            searchedValue={searchedValue}
            toggleAddMoreItemModal={toggleModal}
            searchFun={setSearchedValueFun}
            createGrnLocal={createGrnLocal}
          />
        </div>
      </div>
    </div>
  );
};
export default GrnSection;
