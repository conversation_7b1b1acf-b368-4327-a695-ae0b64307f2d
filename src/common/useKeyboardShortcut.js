import { useEffect, useCallback, useReducer } from "react";

const blacklistedTargets = ["INPUT", "TEXTAREA"];

const keysReducer = (state, action) => {
  switch (action.type) {
    case "set-key-down":
      return { ...state, [action.key]: true };
    case "set-key-up":
      return { ...state, [action.key]: false };
    default:
      return state;
  }
};

const useKeyboardShortcut = (shortcutKeys, callback, capture = true) => {
  if (!Array.isArray(shortcutKeys))
    throw new Error(
      "The first parameter to `useKeyboardShortcut` must be an ordered array of `KeyboardEvent.key` strings."
    );

  if (!shortcutKeys.length)
    throw new Error(
      "The first parameter to `useKeyboardShortcut` must contain atleast one `KeyboardEvent.key` string."
    );

  if (!callback || typeof callback !== "function")
    throw new Error(
      "The second parameter to `useKeyboardShortcut` must be a function that will be envoked when the keys are pressed."
    );

  const initalKeyMapping = shortcutKeys.reduce((currentKeys, key) => {
    currentKeys[key] = false;
    return currentKeys;
  }, {});

  const [keys, setKeys] = useReducer(keysReducer, initalKeyMapping);

  const keydownListener = useCallback(
    keydownEvent => {
      const { key, target, repeat, which } = keydownEvent;

      if (!key) return;
      if (repeat) return;
      if (blacklistedTargets.includes(target.tagName)) return;
      if (keys[which] === undefined) return;

      if (keys[which] === false)
        setKeys({ type: "set-key-down", key: which });
    },
    [keys]
  );

  const keyupListener = useCallback(
    keyupEvent => {
      const { key, target, which } = keyupEvent;
      if (!key) return;

      if (blacklistedTargets.includes(target.tagName)) return;
      if (keys[which] === undefined) return;

      if (keys[which] === true)
        setKeys({ type: "set-key-up", key: which });
    },
    [keys]
  );

  useEffect(() => {
    if (!Object.values(keys).filter(value => !value).length) {
      callback(keys);
    }
  }, [callback, keys]);

  useEffect(() => {
    window.addEventListener("keydown", keydownListener, true);
    return () => window.removeEventListener("keydown", keydownListener, capture);
  }, [keydownListener]);

  useEffect(() => {
    window.addEventListener("keyup", keyupListener, true);
    return () => window.removeEventListener("keyup", keyupListener, capture);
  }, [keyupListener]);
};

export default useKeyboardShortcut;
