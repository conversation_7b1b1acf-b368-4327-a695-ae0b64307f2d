// get barcode Item details
export const GET_BARCODE_ITEM_DETAILS_LOAD = 'GET_BARCODE_ITEM_DETAILS_LOAD';
export const GET_BARCODE_ITEM_DETAILS_SUCCESS = 'GET_BARCODE_ITEM_DETAILS_SUCCESS';
export const GET_BARCODE_ITEM_DETAILS_FAIL = 'GET_BARCODE_ITEM_DETAILS_FAIL';

// export BarcodeListTable
export const EXPORT_PRODUCT_BARCODE_LIST_LOAD = 'EXPORT_PRODUCT_BARCODE_LIST_LOAD';
export const EXPORT_PRODUCT_BARCODE_LIST_SUCCESS = 'EXPORT_PRODUCT_BARCODE_LIST_SUCCESS';
export const EXPORT_PRODUCT_BARCODE_LIST_FAIL = 'EXPORT_PRODUCT_BARCODE_LIST_FAIL';

// get PID details
export const GET_INVENTORY_PID_DETAILS_LOAD = 'GET_INVENTORY_PID_DETAILS_LOAD';
export const GET_INVENTORY_PID_DETAILS_SUCCESS = 'GET_INVENTORY_PID_DETAILS_SUCCESS';
export const GET_INVENTORY_PID_DETAILS_FAIL = 'GET_INVENTORY_PID_DETAILS_FAIL';

// get PID total count
export const GET_PID_TOTAL_COUNT_LOAD = 'GET_PID_TOTAL_COUNT_LOAD';
export const GET_PID_TOTAL_COUNT_SUCCESS = 'GET_PID_TOTAL_COUNT_SUCCESS';
export const GET_PID_TOTAL_COUNT_FAIL = 'GET_PID_TOTAL_COUNT_FAIL';
export const GET_PID_TOTAL_COUNT_RESET = 'GET_PID_TOTAL_COUNT_RESET';

// get INVENTORY warehouse list
export const GET_INVENTORY_WAREHOUSE_LIST_LOAD = 'GET_INVENTORY_WAREHOUSE_LIST_LOAD';
export const GET_INVENTORY_WAREHOUSE_LIST_SUCCESS = 'GET_INVENTORY_WAREHOUSE_LIST_SUCCESS';
export const GET_INVENTORY_WAREHOUSE_LIST_FAIL = 'GET_INVENTORY_WAREHOUSE_LIST_FAIL';
export const GET_INVENTORY_WAREHOUSE_LIST_RESET = 'GET_INVENTORY_WAREHOUSE_LIST_RESET';

// get INVENTORY warehouse details list
export const GET_WAREHOUSE_DETAILS_LIST_LOAD = 'GET_WAREHOUSE_DETAILS_LIST_LOAD';
export const GET_WAREHOUSE_DETAILS_LIST_SUCCESS = 'GET_WAREHOUSE_DETAILS_LIST_SUCCESS';
export const GET_WAREHOUSE_DETAILS_LIST_FAIL = 'GET_WAREHOUSE_DETAILS_LIST_FAIL';
export const GET_WAREHOUSE_DETAILS_LIST_RESET = 'GET_WAREHOUSE_DETAILS_LIST_RESET';

// get INVENTORY ORDER TYPE  list
export const GET_INVENTORY_ORDER_TYPE_LIST_LOAD = 'GET_INVENTORY_ORDER_TYPE_LIST_LOAD';
export const GET_INVENTORY_ORDER_TYPE_LIST_SUCCESS = 'GET_INVENTORY_ORDER_TYPE_LIST_SUCCESS';
export const GET_INVENTORY_ORDER_TYPE_LIST_FAIL = 'GET_INVENTORY_ORDER_TYPE_LIST_FAIL';
export const GET_INVENTORY_ORDER_TYPE_LIST_RESET = 'GET_INVENTORY_ORDER_TYPE_LIST_RESET';

// get INVENTORY BARCODE status  list
export const GET_INVENTORY_BARCODE_DETAILS_LIST_LOAD = 'GET_INVENTORY_BARCODE_DETAILS_LIST_LOAD';
export const GET_INVENTORY_BARCODE_DETAILS_LIST_SUCCESS =
  'GET_INVENTORY_BARCODE_DETAILS_LIST_SUCCESS';
export const GET_INVENTORY_BARCODE_DETAILS_LIST_FAIL = 'GET_INVENTORY_BARCODE_DETAILS_LIST_FAIL';
export const GET_INVENTORY_BARCODE_DETAILS_LIST_RESET = 'GET_INVENTORY_BARCODE_DETAILS_LIST_RESET';
