import * as action from 'redux/actionTypes/qc';

export function getQCHoldReasons(id) {
  return {
    type: action.QC_HOLD_REASONS_LOAD,
    id
  };
}

export function getQCHoldReasonsSuccess(payload) {
  return {
    type: action.QC_HOLD_REASONS_SUCCESS,
    payload
  };
}

export function getQCHoldReasonsFail(payload) {
  return {
    type: action.QC_HOLD_REASONS_FAIL,
    payload
  };
}

export function createQCData(items) {
  const data = items.map((item) => {
    let barcode;
    let shippingPackageId;
    let incrementId;
    let uwItemId;
    let isAccessories;
    let fittingId;
    let trayId = null;
    let reason = '';
    let status = 'QCPass';
    let barcodeStatus = '';
    if (item && item.isAccessories) {
      isAccessories = item.isAccessories;
    }
    if (item && item.order) {
      barcode = item.order.barcode;
      shippingPackageId = item.order.shippingPackageId;
      incrementId = item.order.incrementId;
      uwItemId = item.order.uwItemId;
      fittingId = item.order.fittingId;
      trayId = item.order.trayId;
      barcodeStatus = item.order.status;
      if (item.order.status !== 'IN_QC') {
        status = item.order.status;
      }
      if (item.order.status === 'QC_HOLD' || item.order.status === 'QC_FAILED') {
        reason = item?.qcStatus?.reasonName || '';
      }
    }
    return {
      barcode,
      damaged: false,
      incrementId,
      primaryReason: 'UNCLASSIFIED',
      qcStage: '',
      shelfCode: '',
      shippingPackageId,
      status,
      tertiaryReason: '',
      uwItemId,
      isScanningDone: false,
      isAccessories,
      fittingId,
      trayId,
      reason,
      reasonId: '',
      reasonName: '',
      secondaryReason: '',
      barcodeStatus
    };
  });
  return {
    type: action.CREATE_QC_DATA,
    payload: data
  };
}

export function createQCDataFitting(
  { scanned_order_item: scannedOrderItem, order },
  qcStatusList = []
) {
  const defaultQCData = {
    status: 'PendingCustomization',
    reasonId: '',
    reasonName: '',
    secondaryReason: '',
    incrementId: null
  };
  const primaryReason = 'UNCLASSIFIED';
  const frameQCData =
    qcStatusList.find(({ orderItemId }) => orderItemId === scannedOrderItem.frame_orderItemId) ||
    defaultQCData;
  const leftLensQCData =
    qcStatusList.find(
      ({ orderItemId }) => orderItemId === scannedOrderItem.left_lens_orderItemId
    ) || defaultQCData;
  const rightLensQCData =
    qcStatusList.find(
      ({ orderItemId }) => orderItemId === scannedOrderItem.right_lens_orderItemId
    ) || defaultQCData;

  const statusConvertor = (status) => {
    switch (status) {
      case 'FittingQCHold':
        return 'FITTING_QC_HOLD';
      case 'FittingQCUnhold':
        return 'FITTING_QC_UNHOLD';
      case 'QCFailed':
        return 'FITTING_QC_FAIL';
      default:
        return status;
    }
  };

  const data = [
    {
      barcode: scannedOrderItem.frame_barcode,
      fittingId: scannedOrderItem.fitting_id,
      shippingPackageId: scannedOrderItem.shipping_package_id,
      trayId: scannedOrderItem.frame_tray_id,
      status: frameQCData.status,
      qcStatus: statusConvertor(frameQCData.status),
      incrementId: order.increment_id,
      uwItemId: scannedOrderItem.frame_orderItemId,
      primaryReason,
      reasonId: frameQCData.reasonId,
      reason: frameQCData.reasonName,
      reasonName: frameQCData.reasonName,
      secondaryReason: frameQCData.secondaryReason,
      tertiaryReason: '',
      qcStage: '',
      shelfCode: '',
      damaged: false,
      isScanningDone: true,
      orderItemId: scannedOrderItem.frame_orderItemId
    },
    {
      barcode: scannedOrderItem.left_lens_barcode,
      fittingId: scannedOrderItem.fitting_id,
      shippingPackageId: scannedOrderItem.shipping_package_id,
      trayId: scannedOrderItem.left_lens_tray_id,
      status: leftLensQCData.status,
      qcStatus: statusConvertor(leftLensQCData.status),
      incrementId: order.increment_id,
      uwItemId: scannedOrderItem.left_lens_orderItemId,
      primaryReason,
      reasonId: leftLensQCData.reasonId,
      reason: leftLensQCData.reasonName,
      reasonName: leftLensQCData.reasonName,
      secondaryReason: leftLensQCData.secondaryReason,
      tertiaryReason: '',
      qcStage: '',
      shelfCode: '',
      damaged: false,
      isScanningDone: true,
      orderItemId: scannedOrderItem.left_lens_orderItemId
    },
    {
      barcode: scannedOrderItem.right_lens_barcode,
      fittingId: scannedOrderItem.fitting_id,
      shippingPackageId: scannedOrderItem.shipping_package_id,
      trayId: scannedOrderItem.right_lens_tray_id,
      status: rightLensQCData.status,
      qcStatus: statusConvertor(rightLensQCData.status),
      incrementId: order.increment_id,
      uwItemId: scannedOrderItem.right_lens_orderItemId,
      primaryReason,
      reasonId: rightLensQCData.reasonId,
      reason: rightLensQCData.reasonName,
      reasonName: rightLensQCData.reasonName,
      secondaryReason: rightLensQCData.secondaryReason,
      tertiaryReason: '',
      qcStage: '',
      shelfCode: '',
      damaged: false,
      isScanningDone: true,
      orderItemId: scannedOrderItem.right_lens_orderItemId
    }
  ];
  return {
    type: action.CREATE_QC_DATA,
    payload: data
  };
}

export function updateQCData(data, uwItemId) {
  return {
    type: action.UPDATE_QC_DATA,
    payload: {
      data,
      uwItemId
    }
  };
}

export function markQcDone(payload) {
  return {
    type: action.MARK_QC_DONE_LOAD,
    payload
  };
}

export function markQcDoneSuccess(payload) {
  return {
    type: action.MARK_QC_DONE_SUCCESS,
    payload
  };
}

export function markDamaged(payload) {
  return {
    type: action.MARK_PRODUCT_DAMAGED_REQUEST,
    payload
  };
}

export function markDamagedSuccess(payload) {
  return {
    type: action.MARK_PRODUCT_DAMAGED_SUCCESS,
    payload
  };
}

export function markDamagedFail(payload) {
  return {
    type: action.MARK_PRODUCT_DAMAGED_FAIL,
    payload
  };
}

export function markDamagedReset() {
  return { type: action.MARK_PRODUCT_DAMAGED_RESET };
}

export function markQcDoneFail(payload) {
  return {
    type: action.MARK_QC_DONE_FAIL,
    payload
  };
}

export function markQcDoneReset(payload) {
  return {
    type: action.MARK_QC_DONE_RESET,
    payload
  };
}
export function fittingByStationCodeLoad(payload) {
  return {
    type: action.FITTING_BY_STATION_CODE_LOAD,
    payload
  };
}

export function fittingByStationCodeSuccess(payload) {
  return {
    type: action.FITTING_BY_STATION_CODE_SUCCESS,
    payload
  };
}

export function fittingByStationCodeFail() {
  return {
    type: action.FITTING_BY_STATION_CODE_FAIL
  };
}

export function markFullFillableLoad(payload) {
  return {
    type: action.MARK_FULL_FILLABLE_LOAD,
    payload
  };
}
