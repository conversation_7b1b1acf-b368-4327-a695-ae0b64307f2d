import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, MenuItem, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PopOver from 'components/PopOver';
import LkInput from 'components/MaterialUi/LkInput';
import { makeStyles } from 'tss-react/mui';
import { useDispatch } from 'react-redux';
import { toastOpen } from 'redux/actionCreators/toast';
import { getBarcodeFromURL } from '../../../../utils/helpers';

const tootlTipIcon = () => (
  <Box className="bg-ee border-radius-6" p={0.3} display="flex" justifyContent="center">
    <SearchIcon data-cy="global-search" className="cursor-pointer" />
  </Box>
);

const Options = ['barcode', 'product', 'Sale Order', 'Location'];
const placeHolderMapping = {
  barcode: 'Enter Barcode',
  product: 'Enter Product',
  'Sale Order': 'Shipment Id/ Increament Id',
  Location: 'Enter Location'
};

const useStyles = makeStyles()(() => ({
  placeHolder: {
    '& input::placeholder': {
      fontSize: '13px'
    }
  }
}));

const specialCharacters = ['%'];

const TopbarSearch = ({ value, onChangeFun }) => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const [openToolTip, setOpenTooltip] = useState(false);
  const [selectedType, setselectedType] = useState(Options[2]);
  const dispatch = useDispatch();

  const openToastFun = (heading) =>
    dispatch(
      toastOpen({
        isToastOpen: true,
        heading,
        severity: 'error'
      })
    );

  const placeholder = useMemo(() => placeHolderMapping[selectedType], [selectedType]);

  const submitSearch = () => {
    const extractedValue = getBarcodeFromURL(value);
    if (extractedValue) {
      const isSpecialCharacterEntered = specialCharacters.some((specialCharacter) =>
        extractedValue.includes(specialCharacter)
      );
      if (isSpecialCharacterEntered) {
        openToastFun(`Please Scan valid ${selectedType}`);
      } else {
        setOpenTooltip(false);
        switch (selectedType) {
          case Options[0]:
            return navigate(`/historyPage/${extractedValue}`, {
              state: { fromTopBarSearch: true }
            });
          case Options[1]:
            return navigate(`/inventory/${extractedValue}`, { state: { fromTopBarSearch: true } });
          case Options[2]:
            return navigate(`/order/${extractedValue}/overview`, {
              state: { fromTopBarSearch: true }
            });
          default:
            return navigate(`/asrsDescrepancy/${extractedValue}`, {
              state: { fromTopBarSearch: true }
            });
        }
      }
    } else {
      openToastFun(`Please Scan valid ${selectedType}`);
    }
    return null;
  };

  useEffect(() => {
    if (openToolTip) {
      setTimeout(() => {
        const tempElement = document.getElementById('barcodeSearchId');
        if (tempElement) {
          tempElement.focus();
        }
      }, 200);
    }
  }, [openToolTip]);

  return (
    <PopOver
      mouseEvent="onMouseDown"
      paddingTop="0"
      width="420px"
      open={openToolTip}
      setOpen={(val) => setOpenTooltip(val)}
      icon={tootlTipIcon()}
      marginLeft="-380px"
    >
      <Box p={1} display="flex" justifyContent="center">
        <Box width="143px">
          <LkInput
            select
            fullWidth
            value={selectedType}
            onChange={(e) => setselectedType(e.target.value)}
          >
            {Options?.map((ap) => (
              <MenuItem key={ap} value={ap}>
                {ap}
              </MenuItem>
            ))}
          </LkInput>
        </Box>
        <Box width="220px" mr={1} ml={1}>
          <LkInput
            fullWidth
            className={classes.placeHolder}
            id="barcodeSearchId"
            variant="outlined"
            value={value}
            onChange={({ target }) => onChangeFun(target.value.trim())}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                </InputAdornment>
              )
            }}
            placeholder={placeholder}
            onKeyDown={(e) => e.keyCode === 13 && submitSearch()}
          />
        </Box>
        <Box
          className="bg-primary border-radius-6 cursor-pointer "
          onClick={submitSearch}
          p={0.3}
          pl={1}
          pr={1}
          display="flex"
          justifyContent="center"
          alignItems="center"
        >
          <SearchIcon className=" text-white" />
        </Box>
      </Box>
    </PopOver>
  );
};

export default TopbarSearch;
