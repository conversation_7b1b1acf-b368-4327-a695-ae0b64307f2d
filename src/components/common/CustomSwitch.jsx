import React from 'react';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';

const useStyles = makeStyles()((theme) => ({
  root: {
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: 100,
    padding: 2,
    display: 'flex',
    width: 'fit-content',
  },
  item: {
    fontSize: 16,
    color: '#666666',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: 26,
    width: 90,
    borderRadius: 100,
    cursor: 'pointer'
  },
  itemActive: {
    color: '#098084',
    background: '#B1EAEB',
    fontWeight: 500,
    transition: '.3s all'
  }
}));

const CustomSwitch = ({ options, handleChange, activeKey }) => {
  const { classes, cx } = useStyles();

  const returnItem = ({key, label}) => (
    <Box
      key={key}
      className={cx(classes.item, { [classes.itemActive]: activeKey === key })}
      onClick={() => handleChange(key)}
    >
      {label}
    </Box>
  );

  return (
    <div className={classes.root}>
      {
        options.map((eachOption) => returnItem(eachOption))
      }
    </div>
  );
};

export default CustomSwitch;
