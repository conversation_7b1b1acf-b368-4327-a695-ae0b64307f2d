import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { convertToQueryString } from '../utils/helpers';

export const getPurchaseOrderListApi = payload => {
    const f = convertToQueryString(payload);
    return tokenInstance().get(`${config.purchaseOrder.list}?${f}`)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const getPurchaseOrderDetailApi = payload => {
    const f = convertToQueryString(payload);
    return tokenInstance().get(`${config.purchaseOrder.detail}?${f}`)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const uploadItemsApi = ({ payload, processConfig }) => {
    return tokenInstance({ 'Content-Type': 'multipart/form-data' }).post(`${config.purchaseOrder.uploadItem}`, payload, processConfig)
        .then(response => ({ response }))
        .catch(error => ({ error }));
};

export const getUploadErrorCsvApi = fileName => {
    return tokenInstance().get(`${config.purchaseOrder.uploadItem}?file=${fileName}`)
        .then(response => {
            return new Blob([response.data], { type: 'application/csv' });
        })
        .then(blob => ({ blob }))
        .catch(error => ({ error }));
};

export const submitPoApi = payload => {
    return tokenInstance().post(`${config.purchaseOrder.submitPo}`, payload)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const generatePoNumberApi = () => tokenInstance().get(config.purchaseOrder.generatePoNumber)
    .then(response => ({ response: response?.data }))
    .catch(error => ({ error }));

export const submitApprovalApi = payload => {
    return tokenInstance().put(config.purchaseOrder.submitAprroval, payload)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const holdPoApi = payload => {
    return tokenInstance().put(config.purchaseOrder.hold, payload)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const amendPoApi = payload => {
    return tokenInstance().put(config.purchaseOrder.amend, payload)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const unHoldPoApi = payload => {
    return tokenInstance().put(config.purchaseOrder.unhold, payload)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const closePoApi = payload => {
    return tokenInstance().put(config.purchaseOrder.close, payload)
        .then(response => response.data)
        .catch(error => { throw error });
};

export const fetchProductDetailsApi = (payload = {}) => {
    return tokenInstance().post(config.catalog.productDetails, payload)
        .then(response => ({ response }))
        .catch(error => ({ error }));
};

export const getProductPriceWithTaxApi = (payload = {}) => {
    return tokenInstance().post(config.catalog.productPrice, payload.reqObj, 
    { params: payload.procurementType })
        .then(response => ({ response }))
        .catch(error => ({ error }));
};

export const exportPo = (payload = {}) => {
    const f = convertToQueryString(payload);
    return tokenInstance().get(`${config.purchaseOrder.export}?${f}`)
        .then(response => ({ response }))
        .catch(error => ({ error }));
};
export const processingStatusApi = (payload = '') => tokenInstance().get(`${config.purchaseOrder.processingStatus}?reqid=${payload}`)
    .then(response => ({ response }))
    .catch(error => ({ error }));

export const exportPoDetails = ({ po_num }) => {
    return tokenInstance().get(`${config.purchaseOrder.exportPoDetails}?po_num=${po_num}`)
        .then(response => ({ response }))
        .catch(error => ({ error }));
};

export const poApprovalApi = (payload) => {
    const { bulkApproval } = payload;
    const url = bulkApproval ? config.purchaseOrder.bulkSubmitforapproval : config.purchaseOrder.poApproval;
    if (bulkApproval) {
        delete payload.bulkApproval;
        delete payload.status;
    }
    return tokenInstance().put(url, payload)
        .then(response => ({ response }))
        .catch(error => { throw error });
};
