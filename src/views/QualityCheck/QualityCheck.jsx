import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';

import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { getOrderDetailsReset } from 'redux/actionCreators/order';
import useOrderHook from 'common/useOrderHook';
import QualityCheckBarcode from './QualityCheckBarcode';
import QualityCheckProcess from './QualityCheckProcess';

const useStyles = makeStyles()(() => ({
  root: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    minWidth: '1188px',
    height: 'calc(100vh - 64px)'
  },
  cardComponent: {
    borderRadius: 12,
    transition: '0.3s',
    boxShadow: '0 8px 16px rgba(0,0,0,0.05)'
  },
  header: {
    fontWeight: 700
  }
}));

const QualityCheck = () => {
  const location = useLocation();
  useOrderHook();
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const qcProcess = location?.state?.qcProcess;
  useEffect(() => () => dispatch(getOrderDetailsReset()), [dispatch]);

  return (
    <Box className={classes.root} alignItems={qcProcess ? 'normal' : 'center'}>
      <Box width={qcProcess ? '100%' : 'auto'} borderRadius="12px">
        <Card
          sx={{ pt: 6, pb: 6 }}
          style={{
            paddingTop: qcProcess ? '0px' : '',
            boxShadow: 'rgb(0 0 0 / 30%) 0px 8px 16px',
            overflow: 'auto'
          }}
        >
          {qcProcess ? (
            <QualityCheckProcess />
          ) : (
            <QualityCheckBarcode />
          )}
        </Card>
      </Box>
    </Box>
  );
};

export default QualityCheck;
