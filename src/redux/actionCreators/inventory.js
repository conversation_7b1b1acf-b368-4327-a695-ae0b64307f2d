import * as action from '../actionTypes/inventory';

// getItembarcode list
export const getItembarcodeDetailsLoad = (data) => ({
  type: action.GET_BARCODE_ITEM_DETAILS_LOAD,
  payload: data
});

export const getItembarcodeDetailsSuccess = (data) => ({
  type: action.GET_BARCODE_ITEM_DETAILS_SUCCESS,
  payload: data
});

export const getItembarcodeDetailsFail = () => ({ type: action.GET_BARCODE_ITEM_DETAILS_FAIL });

// get PID details
export const getPIDDetailsLoad = (data) => ({
  type: action.GET_INVENTORY_PID_DETAILS_LOAD,
  payload: data
});

export const getPIDDetailsSuccess = (data) => ({
  type: action.GET_INVENTORY_PID_DETAILS_SUCCESS,
  payload: data
});

export const getPIDDetailsFail = () => ({ type: action.GET_INVENTORY_PID_DETAILS_FAIL });

// export ProductBarcodeListTable
export const exportBarcodeListLoad = (data) => ({
  type: action.EXPORT_PRODUCT_BARCODE_LIST_LOAD,
  payload: data
});

export const exportBarcodeListSuccess = (data) => ({
  type: action.EXPORT_PRODUCT_BARCODE_LIST_SUCCESS,
  payload: data
});

export const exportBarcodeListFail = () => ({
  type: action.EXPORT_PRODUCT_BARCODE_LIST_FAIL
});

// get PID total count
export const getPidTotalCountLoad = (data) => ({
  type: action.GET_PID_TOTAL_COUNT_LOAD,
  payload: data
});

export const getPidTotalCountSuccess = (data) => ({
  type: action.GET_PID_TOTAL_COUNT_SUCCESS,
  payload: data
});

export const getPidTotalCountFail = () => ({
  type: action.GET_PID_TOTAL_COUNT_FAIL
});

export const getPidTotalCountReset = () => ({
  type: action.GET_PID_TOTAL_COUNT_RESET
});

// get Inventory ware house list

export const getInventoryWarehouseListLoad = (data) => ({
  type: action.GET_INVENTORY_WAREHOUSE_LIST_LOAD,
  payload: data
});

export const getInventoryWarehouseListSuccess = (data) => ({
  type: action.GET_INVENTORY_WAREHOUSE_LIST_SUCCESS,
  payload: data
});

export const getInventoryWarehouseListFail = () => ({
  type: action.GET_INVENTORY_WAREHOUSE_LIST_FAIL
});

export const getInventoryWarehouseListReset = () => ({
  type: action.GET_INVENTORY_WAREHOUSE_LIST_RESET
});

// get Inventory ware house details list

export const getWarehouseDetailsListLoad = (data) => ({
  type: action.GET_WAREHOUSE_DETAILS_LIST_LOAD,
  payload: data
});

export const getWarehouseDetailsListSuccess = (data) => ({
  type: action.GET_WAREHOUSE_DETAILS_LIST_SUCCESS,
  payload: data
});

export const getWarehouseDetailsListFail = () => ({
  type: action.GET_WAREHOUSE_DETAILS_LIST_FAIL
});

export const getWarehouseDetailsListReset = () => ({
  type: action.GET_WAREHOUSE_DETAILS_LIST_RESET
});

// get Inventory Order Type list

export const getInventoryOrderTypeListLoad = (data) => ({
  type: action.GET_INVENTORY_ORDER_TYPE_LIST_LOAD,
  payload: data
});

export const getInventoryOrderTypeListSuccess = (data) => ({
  type: action.GET_INVENTORY_ORDER_TYPE_LIST_SUCCESS,
  payload: data
});

export const getInventoryOrderTypeListFail = () => ({
  type: action.GET_INVENTORY_ORDER_TYPE_LIST_FAIL
});

export const getInventoryOrderTypeListReset = () => ({
  type: action.GET_INVENTORY_ORDER_TYPE_LIST_RESET
});

// get Inventory barcode list

export const getInventoryBarcodeDetailsListLoad = (data) => ({
  type: action.GET_INVENTORY_BARCODE_DETAILS_LIST_LOAD,
  payload: data
});

export const getInventoryBarcodeDetailsListSuccess = (data) => ({
  type: action.GET_INVENTORY_BARCODE_DETAILS_LIST_SUCCESS,
  payload: data
});

export const getInventoryBarcodeDetailsListFail = () => ({
  type: action.GET_INVENTORY_BARCODE_DETAILS_LIST_FAIL
});

export const getInventoryBarcodeDetailsListReset = () => ({
  type: action.GET_INVENTORY_BARCODE_DETAILS_LIST_RESET
});
