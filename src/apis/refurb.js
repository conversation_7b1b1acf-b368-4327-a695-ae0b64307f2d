import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getRefurbStatusCount = () => tokenInstance().get(config.refurb.statusCount);

export const getRefurbListAPI = ({ page, pageSize: size, orderByFields, status, searchFields }) =>
  tokenInstance().post(
    config.refurb.listData,
    { status, ...searchFields },
    {
      params: {
        page,
        size,
        sortBy: orderByFields.key,
        sortOrder: orderByFields.order,
        totalCountRequired: true
      }
    }
  );

export const markBarcodeOperationAPI = ({ itemBarcode: barcode, actionType: operation }) =>
  tokenInstance().put(config.refurb.markGoodOrBad, null, { params: { barcode, operation } });
