import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import RightDrawer from 'views/MonitorV3/RightDrawerTable/RightDrawer';
import { monitorDetailsFiltersReset, monitorFiltersDataLoad } from 'redux/actionCreators/monitor';
import MonitorBodyFlowChart from './FlowChart/MonitorBodyFlowChart';
import MonitorBodyFilters from './MonitorBodyFilters';
import MonitorBodyHeader from './components/MonitorBodyHeader';
import MonitorBodyTotalOrder from './MonitorBodyTotalOrder';

const DEFAULT_DETAILS_CONFIG = { category: null, frTag: null, status: null };

const MonitorBody = ({
  selectedTab,
  activeCategory,
  monitorHeaderHeight,
  totalOrderSelectedTab,
  totalOrderExportFun
}) => {
  const dispatch = useDispatch();
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [detailsConfig, setDetailsConfig] = useState(DEFAULT_DETAILS_CONFIG);
  const {
    monitorDetailsConfig,
    monitorList: { isLoading }
  } = useSelector((state) => state.monitor);

  useEffect(() => {
    dispatch(monitorFiltersDataLoad());
  }, []);

  const openModalFun = () => {
    setDetailsConfig(DEFAULT_DETAILS_CONFIG);
  };

  const configureDetails = (e, tagName, category) => {
    setDetailsConfig({
      ...detailsConfig,
      category,
      frTag: tagName,
      status: e.target.ariaValueNow
    });
  };

  useEffect(() => {
    if (monitorDetailsConfig) {
      setDetailsConfig({
        ...detailsConfig,
        category: selectedTab,
        frTag: activeCategory,
        ...monitorDetailsConfig
      });
      dispatch(monitorDetailsFiltersReset());
    }
  }, [monitorDetailsConfig]);

  const openFilterDrawerFun = useCallback(() => {
    setOpenFilterDrawer((oldState) => !oldState);
  }, []);

  if (totalOrderSelectedTab) {
    return <MonitorBodyTotalOrder totalOrderExportFun={totalOrderExportFun} />;
  }

  return (
    <>
      <MonitorBodyFilters
        openDrawer={openFilterDrawer}
        handleClose={openFilterDrawerFun}
        activeCategory={activeCategory}
      />
      <Box>
        <MonitorBodyHeader isFilterActive={openFilterDrawer} openFilters={openFilterDrawerFun} />
      </Box>
      <MonitorBodyFlowChart
        selectedTab={selectedTab}
        activeCategory={activeCategory}
        setDetailsConfig={setDetailsConfig}
        isLoading={isLoading}
        monitorHeaderHeight={monitorHeaderHeight}
      />
      {detailsConfig.category && (
        <RightDrawer
          openModalFun={openModalFun}
          detailsConfig={detailsConfig}
          setDetailsConfig={configureDetails}
        />
      )}
    </>
  );
};

export default MonitorBody;
