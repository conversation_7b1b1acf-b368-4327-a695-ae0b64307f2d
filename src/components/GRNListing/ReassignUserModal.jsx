import React, { useEffect, useCallback } from 'react';
import Avatar from '@mui/material/Avatar';
import { makeStyles } from 'tss-react/mui';
import LkSelect from 'components/MaterialUi/LkSelectBox';
import { MenuItem, Box } from '@mui/material';
import { useDispatch } from 'react-redux';
import LkModal from '../Modal/Modal';
import { reassignGRNLoad } from '../../redux/actionCreators/grnListing';
import { toastOpen } from '../../redux/actionCreators/toast';

const styles = makeStyles()(() => ({
  avatarSize: {
    width: 45,
    height: 45
  },
  primaryBtn: {
    width: 131,
    borderRadius: 8
  }
}));

const ReassignUserModal = ({
  open,
  closeModal,
  data,
  reassignGRNReset,
  reassignFail,
  reassignSuccess,
  value,
  setValue
}) => {
  const { classes } = styles();
  const dispatch = useDispatch();

  const { created_by, grn_code, invoice_num } = data;

  useEffect(() => {
    if (reassignSuccess) {
      closeModal(false);
    }
  }, [reassignSuccess, closeModal]);

  const submit = useCallback(() => {
    // need to change the payload once backend does changes
    const payload = {
      assigned_to: value,
      grn_code,
      user_id: 'string'
    };
    dispatch(reassignGRNLoad(payload));
  }, [dispatch, value, grn_code]);

  useEffect(() => {
    if (reassignFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: reassignFail.display_message,
          severity: 'error'
        })
      );
      dispatch(reassignGRNReset());

      closeModal(false);
    }
  }, [reassignFail, dispatch, closeModal, reassignGRNReset]);

  return (
    <LkModal
      open={open}
      title="Reassign User"
      subTitle="Please enter the details below to reassign user for a GRN"
      handleClose={() => closeModal(false)}
      primaryBtn={submit}
      primaryBtnText="confirm"
      modalWidth="520px"
      modalHeight="520px"
      primaryBtnClass={classes.primaryBtn}
    >
      <Box width={282} margin="0 auto" mt={4}>
        <LkSelect
          style={{ height: 40 }}
          label
          labelValue="Select Invoice Ref No"
          fullWidth
          value={0}
        >
          <MenuItem value={0}>{invoice_num}</MenuItem>
        </LkSelect>
      </Box>

      <Box width={282} margin="0 auto" mt={3}>
        <LkSelect
          style={{ height: 40 }}
          label
          labelValue="Select GRN"
          fullWidth
          value={0}
        >
          <MenuItem value={0}>{grn_code}</MenuItem>
        </LkSelect>
      </Box>

      <Box mt={4} display="flex" alignItems="center" justifyContent="center" width="100%">
        <Box>
          <Avatar
            alt="Remy Sharp"
            className={classes.avatarSize}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/avatar_3.png`}
          />
        </Box>
        <Box ml={1}>
          <Box className="fs10 text-66" pb={0.5}>
              Currently Assigned To
          </Box>
          <Box className="fs14 fw-bold text-primary" pb={0.5}>
            {created_by}
          </Box>
          <Box className="fs12 ">NOT FOUND</Box>
        </Box>
      </Box>

      <Box width={282} margin="0 auto" mt={4}>
        <LkSelect
          style={{ height: 40 }}
          label
          labelValue="Reassign To"
          fullWidth
          value={value}
          onChange={setValue}
        >
          <MenuItem value="not Found">User1</MenuItem>
        </LkSelect>
      </Box>
    </LkModal>
  );
};
export default React.memo(ReassignUserModal);
