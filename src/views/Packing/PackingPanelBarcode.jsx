import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';

import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import { makeStyles } from 'tss-react/mui';

import usePermissions from 'common/usePermissions';
import { LOGIN } from 'redux/reducers/login';
import { LOCALISATION } from 'redux/reducers/localisation';
import { getOrderDetails } from 'redux/actionCreators/order';
import { createQCData } from 'redux/actionCreators/qc';
import { toastOpen } from 'redux/actionCreators/toast';
import { isPrintShipmentDone } from 'redux/actionCreators/printShipment';

import LkInput from 'components/MaterialUi/LkInput';
import { Style_PackingPanelBarcode } from './Style';

const styles = makeStyles()((theme) => ({
  root: {
    background: 'white',
    borderRadius: theme.spacing(0.5)
  },
  ...Style_PackingPanelBarcode
}));

const PackingPanelBarcode = () => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const isLoading = useSelector((state) => state.loader.isLoading);
  const { permission } = useSelector((state) => state[LOGIN].user.userDetail);
  const { isQCSuperVisor, isPackingSuperVisor } = usePermissions(permission);
  const { packerData } = useSelector((state) => state.packing);
  const packingLocal = useSelector((state) => state[LOCALISATION].localeData.PACKING);
  const { orderDetails, products, lenses, accessories, orderBarcodeInfo, packingDetailStatus } =
    useSelector((state) => state.order);

  useEffect(() => {
    /** Give focus on input field */
    const barcodeEl = document.getElementById('barcodeId');
    if (barcodeEl) {
      barcodeEl.focus();
    }

    if (
      orderDetails?.response?.data?.error_message ||
      orderBarcodeInfo?.response?.data?.error_message ||
      orderDetails?.response?.data?.meta?.displayMessage ||
      orderDetails?.response?.data?.message
    ) {
      /** If any error occur, input field set to empty string and Toaster will show message. */
      const barcodeIdRef = document.getElementById('barcodeId');
      if (barcodeIdRef) {
        barcodeIdRef.value = '';
      }
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading:
            orderDetails?.response?.data?.error_message ||
            orderBarcodeInfo?.response?.data?.error_message ||
            orderDetails?.response?.data?.meta?.displayMessage ||
            orderDetails?.response?.data?.message ||
            packingLocal.SYSTEM_ERROR_OCCURRED,
          severity: 'error'
        })
      );
    } else if (orderBarcodeInfo && orderDetails && packerData) {
      // Checking condition for supervisor while opening the order.
      if (!packerData.packingMode) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: packingLocal.NOT_VALID_PACKER,
            severity: 'error'
          })
        );
      } else if (
        `${orderBarcodeInfo.printShipment}` === '1' &&
        packerData.packingMode &&
        packerData.packingMode.toLowerCase().split('_')[1] === 'auto'
      ) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: packingLocal.ERR_GIVE_TO_MANUAL_PACKER,
            severity: 'error'
          })
        );
      } else if (Object.keys(orderBarcodeInfo).length && orderBarcodeInfo.country && packerData) {
        /** If data is fetched, initializing qcData and change path to '/packing' */
        dispatch(createQCData(products.concat(lenses).concat(accessories)));
        const barcodeIdRef = document.getElementById('barcodeId');
        const isSuperVisor = isQCSuperVisor || isPackingSuperVisor;
        navigate(
          packingDetailStatus === 'DONE' && !isSuperVisor ? '/packing-summary' : '/packing',
          {
            state: {
              qcProcess: true,
              inputBarcode: barcodeIdRef && barcodeIdRef.value ? barcodeIdRef.value : ''
            }
          }
        );
      } else {
        const barcodeIdRef = document.getElementById('barcodeId');
        if (barcodeIdRef) {
          barcodeIdRef.value = '';
        }
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: packingLocal.SYSTEM_ERROR_OCCURRED,
            severity: 'error'
          })
        );
      }
    }
  }, [orderDetails, products, lenses, orderBarcodeInfo, packerData, packingDetailStatus]);

  /** OnClick method on Barcode Input */
  const fetchOrderDetailsFn = (ev) => {
    const barcode = ev.target.value;
    if (barcode && barcode.trim().length) {
      /** If Input value is valid */
      dispatch(getOrderDetails(barcode.trim()));
      // dispatch(getOrderByBarcode(barcode.trim()));
      dispatch(isPrintShipmentDone(undefined));
    } else {
      /** If Input value is empty */
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: packingLocal.INVALID_CODE_SCANNED,
          severity: 'error'
        })
      );

      const barcodeIdRef = document.getElementById('barcodeId');
      if (barcodeIdRef) {
        barcodeIdRef.value = '';
      }
    }
  };

  /** State to manage Is currently on Summary screen or first screen */
  let isOnSummaryScreen = false;
  if (location.pathname.includes('/packing-summary')) {
    isOnSummaryScreen = true;
  }

  return (
    <div className={classes.card}>
      {isOnSummaryScreen ? (
        ''
      ) : (
        <img
          className={classes.scanImg}
          alt="scan-img"
          src={`${import.meta.env.VITE_STATIC_SERVER}/images/packingScanner.svg`}
        />
      )}
      <div className={classes.label}>{packingLocal.SCAN_PRODUCT_BARCODE_SHIPMENT_ID}</div>
      <Box display="flex" justifyContent="center" padding="32px 48px 0px 48px" paddingBottom={0}>
        <Box className={classes.barcode}>
          <LkInput
            id="barcodeId"
            className={classes.root}
            variant="outlined"
            placeholder={packingLocal.SCAN_BARCODE}
            fullWidth
            size="small"
            onKeyUp={(e) => e.key === 'Enter' && fetchOrderDetailsFn(e)}
            disabled={isLoading}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <img
                    style={{ width: '25px' }}
                    alt="barcode-img"
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/barcodeScan.svg`}
                  />
                </InputAdornment>
              )
            }}
          />
        </Box>
      </Box>
    </div>
  );
};

export default PackingPanelBarcode;
