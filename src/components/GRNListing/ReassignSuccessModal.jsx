import React, { useCallback } from 'react';
import Avatar from '@mui/material/Avatar';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import LkModal from '../Modal/Modal';

const styles = makeStyles()(() => ({
  avatarSize: {
    width: 45,
    height: 45
  },
  primaryBtn: {
    width: 131,
    borderRadius: 8
  }
}));

const ReassignSuccessModal = ({ open, closeModal, assignTo }) => {
  const { classes } = styles();
  const handleCloseFun = useCallback(() => closeModal(false), [closeModal]);
  return (
    <LkModal
      open={open}
      title="Successfully Reassigned!"
      subTitle="You have successfully reassigned the user."
      handleClose={handleCloseFun}
      primaryBtn={handleCloseFun}
      primaryBtnText="ok"
      modalWidth="520px"
      primaryBtnClass={classes.primaryBtn}
    >
      <Box mt={5} display="flex" alignItems="center" justifyContent="center" width="100%">
        <Box>
          <Avatar
            alt="Remy Sharp"
            className={classes.avatarSize}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/avatar_3.png`}
          />
        </Box>
        <Box ml={1}>
          <Box className="fs10 text-66" pb={0.5}>
            Newly Assigned To
          </Box>
          <Box className="fs14 fw-bold text-primary" pb={0.5}>
            {assignTo}
          </Box>
          <Box className="fs12 ">NOT FOUND</Box>
        </Box>
      </Box>
    </LkModal>
  );
};
export default React.memo(ReassignSuccessModal);
