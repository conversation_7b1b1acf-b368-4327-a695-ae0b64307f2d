import { call, put, all, takeLatest, takeEvery } from 'redux-saga/effects';
import { sleep } from 'utils/helpers';
import * as actionCreations from '../actionCreators/supervisorPickingSkipped';
import * as actions from '../actionTypes/supervisorPickingSkipped';
import * as apis from '../../apis/supervisorPickingSkipped';
import { showErrorToaster, showToaster } from './toast';

function* getUsersDataSaga() {
  try {
    const response = yield call(apis.getUserDataAPI);
    yield put(actionCreations.supervisorPickingUsersSuccess(response.data));
  } catch (err) {
    yield put(actionCreations.supervisorPickingUsersFail(err));
  }
}

function* getCategoriesSaga() {
  try {
    const response = yield call(apis.getSupervisorCategories);
    yield put(actionCreations.supervisorPickingCategoriesSuccess(response.data));
  } catch (err) {
    yield put(actionCreations.supervisorPickingCategoriesFail(err));
  }
}

function* getSkippedDataSaga({ payload }) {
  try {
    const response = yield call(apis.getSkippedDataAPI, payload);
    yield put(actionCreations.supervisorPickingSkippedDataSuccess(response.data));
  } catch (err) {
    yield put(actionCreations.supervisorPickingSkippedDataFail(err));
  }
}

function* getHoldDataSaga() {
  try {
    const data = yield call(apis.getHoldDataAPI);
    yield put(actionCreations.supervisorPickingHoldDataSuccess(data.response));
  } catch (err) {
    yield put(actionCreations.supervisorPickingHoldDataFail(err));
  }
}

function* scanOrderPicking({ payload }) {
  try {
    const response = yield call(apis.orderStockOutApi, payload);
    if (response?.data?.data) {
      yield put(actionCreations.supervisorPickingScanSuccess({ ...payload }));
    } else {
      yield put(actionCreations.supervisorPickingScanFail({ ...response, ...payload }));
    }
  } catch (err) {
    yield put(actionCreations.supervisorPickingScanFail({ ...payload, ...err }));
  }
}

function* markNotFoundOrderItems({ payload }) {
  try {
    const response = yield call(apis.notFoundOrderItemsAPI, payload.requestBody);
    if (response?.data?.data) {
      yield put(actionCreations.supervisorPickingNotFoundSuccess({ ...response, ...payload }));
    } else {
      yield put(actionCreations.supervisorPickingNotFoundFail({ ...response, ...payload }));
    }
  } catch (err) {
    yield put(actionCreations.supervisorPickingNotFoundFail({ ...err, ...payload }));
  }
}

function* assignOrderItems({ payload }) {
  try {
    const response = yield call(apis.assignOrderItemsAPI, payload.requestBody);
    if (response?.data?.data) {
      yield put(actionCreations.supervisorPickingAssignSuccess({ ...response, ...payload }));
    } else {
      yield put(actionCreations.supervisorPickingAssignFail({ ...response, ...payload }));
    }
  } catch (err) {
    yield put(actionCreations.supervisorPickingAssignFail({ ...err, ...payload }));
  }
}

function* holdOrderItems({ payload }) {
  try {
    const data = yield call(apis.holdOrderItemsAPI, payload);
    yield sleep(2000);
    if (data.response) {
      // send Success message
      yield put(actionCreations.supervisorPickingHoldSuccess({ ...payload, ...data.response }));
    } else {
      yield put(
        actionCreations.supervisorPickingHoldFail({
          displayMessage: 'Sorry, Something went wrong please try again',
          ...payload
        })
      );
    }
  } catch (err) {
    yield put(actionCreations.supervisorPickingHoldFail({ ...payload, ...err }));
  }
}

function* unHoldOrderItems({ payload }) {
  try {
    const data = yield call(apis.holdOrderItemsAPI, payload);
    yield sleep(2000);
    if (data.response) {
      // send Success message
      yield put(actionCreations.supervisorPickingUnHoldSuccess({ ...payload, ...data.response }));
    } else {
      yield put(
        actionCreations.supervisorPickingUnHoldFail({
          displayMessage: 'Sorry, Something went wrong please try again',
          ...payload
        })
      );
    }
  } catch (err) {
    yield put(actionCreations.supervisorPickingUnHoldFail({ ...payload, ...err }));
  }
}

function* getExportSkippedDataSaga({ payload }) {
  try {
    const { data } = yield call(apis.getSkippedDataAPI, payload);
    yield put(actionCreations.getExportSkippedDataSuccess(data?.data?.supervisorPanelDetail));
  } catch (err) {
    yield put(actionCreations.getExportSkippedDataFail(err));
  }
}

function* getUploadPidDataSaga({ payload }) {
  try {
    const { data } = yield call(apis.getUploadPidDataApi, payload);
    if (data) {
      yield put(actionCreations.getUploadPidDataSuccess(data));
      yield showToaster('', 'success', 'Successfully Uploaded!');
    }
  } catch (err) {
    yield put(actionCreations.getUploadPidDataFail(err));
    yield showToaster(err);
  }
}

function* moveFromASRSToManualSaga({ payload }) {
  try {
    yield call(apis.moveFromASRSToManual, payload);

    yield put(actionCreations.moveFromASRSToManualSuccess());
    yield showToaster('', 'success', 'Successfully Moved!');
    yield put(actionCreations.moveFromASRSToManualReset());
  } catch (err) {
    yield put(actionCreations.moveFromASRSToManualFail());
    yield showToaster(err);
  }
}

function* bulkUploadDiscardedShipmentSaga({ payload }) {
  try {
    const { data } = yield call(apis.bulkUploadDiscardedShipmentAPI, payload);
    const failedShipments = data?.data?.failedShipments || [];
    const noOfFailedShipments = failedShipments.length;
    if (noOfFailedShipments > 0) {
      yield put(actionCreations.bulkUploadDiscardedShipmentFail(failedShipments));
      yield showErrorToaster(`Failed to discard ${noOfFailedShipments} shipments`);
    } else {
      yield put(actionCreations.bulkUploadDiscardedShipmentSuccess());
      yield showToaster('', 'success', 'Successfully Uploaded!');
    }
  } catch (err) {
    yield put(actionCreations.bulkUploadDiscardedShipmentFail(err));
    yield showToaster(err);
  }
}

export function* runSupervisorPickingSkippedCalls() {
  yield all([
    takeLatest(actions.GET_SUPERVISOR_PICKING_USERS_LOAD, getUsersDataSaga),
    takeLatest(actions.GET_SUPERVISOR_PICKING_CATAGORIES_LOAD, getCategoriesSaga),
    takeEvery(actions.GET_SUPERVISOR_PICKING_SKIPPED_DATA_LOAD, getSkippedDataSaga),
    takeLatest(actions.GET_SUPERVISOR_PICKING_HOLD_DATA_LOAD, getHoldDataSaga),
    takeLatest(actions.SUPERVISOR_PICKING_SCAN_REQUEST, scanOrderPicking),
    takeLatest(actions.SUPERVISOR_PICKING_NOT_FOUND_REQUEST, markNotFoundOrderItems),
    takeLatest(actions.SUPERVISOR_PICKING_HOLD_REQUEST, holdOrderItems),
    takeLatest(actions.SUPERVISOR_PICKING_UN_HOLD_REQUEST, unHoldOrderItems),
    takeLatest(actions.SUPERVISOR_PICKING_ASSIGN_REQUEST, assignOrderItems),
    takeLatest(actions.GET_EXPORT_SKIPPED_DATA_LOAD, getExportSkippedDataSaga),
    takeLatest(actions.GET_UPLOAD_PID_DATA_LOAD, getUploadPidDataSaga),
    takeLatest(actions.MOVE_FROM_ASRS_TO_MANUAL_LOAD, moveFromASRSToManualSaga),
    takeLatest(actions.BULK_UPLOAD_DISCARDED_SHIPMENT_LOAD, bulkUploadDiscardedShipmentSaga)
  ]);
}
