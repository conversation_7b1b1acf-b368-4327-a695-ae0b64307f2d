import React, { useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';

import LkInput from 'components/MaterialUi/LkInput';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import Spinner from 'components/Spinner/Spinner';
import { LOCALISATION } from 'redux/reducers/localisation';
import { toastOpen } from 'redux/actionCreators/toast';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },
  headerBtn: {
    borderRadius: '4px',
    marginRight: '20px',
    width: '180px'
  }
}));

const OrderDashboardHeader = ({
  deleteFilter,
  selectedFilters,
  submitFilter,
  downloadListData,
  downloadCSVDataLoading,
  totalCount
}) => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const searchDONumberRef = useRef('');

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.DO_ORDER_MANAGEMENT);

  const isDownloadCsvDisabled = useMemo(
    () => downloadCSVDataLoading || totalCount === 0,
    [downloadCSVDataLoading, totalCount]
  );

  const handleSearch = (e) => {
    const inputValue = searchDONumberRef.current.value?.trim();
    if (e.which === 13) {
      if (inputValue === '') {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.SEARCH_STATUS_CANNOT_BE_EMPTY,
            severity: 'error'
          })
        );
      }
      submitFilter([{ key: 'incrementId', value: inputValue }]);
      searchDONumberRef.current.value = '';
    }
    return null;
  };

  return (
    <Box data-cy="do-order-header-container" className={classes.container}>
      <Box className="display-flex">
        <Box display="flex">
          <Box width="280px" className="pd-l20" display="flex" alignItems="center">
            <LkInput
              id="search-by-do"
              fullWidth
              size="small"
              placeholder={`${CONTENT.SEARCH_BY_DO_NUMBER}`}
              inputRef={searchDONumberRef}
              onKeyPress={handleSearch}
              InputProps={{
                startAdornment: (
                  <img
                    className="image-container pd-r5"
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/search.svg`}
                    alt=""
                  />
                )
              }}
            />
          </Box>
          {selectedFilters.length > 0 ? (
            <FilterChipsV2 deleteData={deleteFilter} filters={selectedFilters} />
          ) : null}
        </Box>
      </Box>
      <Box className="display-flex">
        <Box>
          <Button
            data-cy="create-new-order-btn"
            onClick={() => navigate('/marketPlace/distributorOrders/create')}
            color="primary"
            className={classes.headerBtn}
            variant="contained"
          >
            {CONTENT.CREATE_NEW_ORDER}
          </Button>
        </Box>
        <Box>
          <Button
            data-cy="download-csv-btn"
            className={classes.headerBtn}
            onClick={downloadListData}
            color="primary"
            variant="outlined"
            disabled={isDownloadCsvDisabled}
          >
            {downloadCSVDataLoading ? <Spinner /> : CONTENT.DOWNLOAD_CSV}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default OrderDashboardHeader;
