import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Field, Form } from 'react-final-form';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import { makeStyles } from 'tss-react/mui';
import Autocomplete from '@mui/material/Autocomplete';

import Spinner from 'components/Spinner/Spinner';
import LkInput from 'components/MaterialUi/LkInput';
import LkCard from 'components/MaterialUi/LkCard';
import LkTypography from 'components/MaterialUi/LkTypography';
import FlexBox from 'components/core/FlexBox';
import { ChipableInput, UploadBox } from 'components/common';
import { formatDateDashedSeparated, debounce } from 'utils/helpers';
import { required } from 'utils/validation';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { searchVendorList } from 'redux/actionCreators/invoiceReferenceCreation';
import { generatePoNumLoad, resetCreatePo } from 'redux/actionCreators/purchaseOrder';
import CreatePO from './CreatePO';

const useStyles = makeStyles()((theme) => ({
  smallField: {
    width: theme.spacing(22.5)
  },
  bigField: {
    width: theme.spacing(35)
  },
  bottomBox: {
    width: 282,
    height: 122
  },
  buttonBox: {
    textAlign: 'center'
  },
  button: {
    width: 146
  },
  cancelButton: {
    width: 140,
    margin: '16px'
  },
  basicDetailsType: {
    gap: 20
  }
}));
// const createPoHomeSelector = createSelector(
//   state => ({
//       generatePoNumberLoading: state.purchaseOrder.generatePoNumberLoading,
//       generatePoNumber: state.purchaseOrder,
//       editPo: state.purchaseOrder.editPo
//     }),
//   po => po
// );
// const createVendorListSelector = createSelector(
//   state => state.invoiceReferenceCreation.vendorList,
//   vendorList => vendorList
// );
const mutatorsObj = {
  setVendor: (args, state, utils) => utils.changeValue(state, 'vendor', () => args[0])
};
const uploadBoxData = [
  { label: 'email.com' },
  { label: 'email.com' },
  { label: 'email.com' },
  { label: 'email.com' },
  { label: 'email.com' },
  { label: 'email.com' },
  { label: 'email.com' }
];
const CreatePOHome = () => {
  const navigate = useNavigate();
  const { classes } = useStyles();
  const PO = useSelector((state) => state[LOCALISATION].localeData.PO);
  const dispatch = useDispatch();
  const [page, setPage] = useState(0);
  const { generatePoNumberLoading, generatePoNumber, editPo, generatePoNumberFail } = useSelector(
    (state) => state.purchaseOrder
  );
  const [poNum, setPoNum] = useState(editPo?.data?.po_num);

  const procurementTypeValues = [
    { key: '', value: PO.NONE },
    { key: 'DISPLAY', value: PO.DISPLAY_ORDER },
    { key: 'BULK', value: PO.BULK_ORDER },
    { key: 'JIT', value: PO.JIT_ORDER },
    { key: 'FOC', value: PO.FOC }
  ];

  const gotoListing = () => {
    navigate('/po/list');
  };

  useEffect(() => {
    if (generatePoNumberFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: generatePoNumberFail,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [generatePoNumberFail]);

  const handleCancel = () => {
    // if(editPo){
    dispatch(resetCreatePo());
    gotoListing();
    // } else {
    //   dispatch(generatePoNumLoad());
    // }
    // setPage(0);
  };

  useEffect(() => {
    if (!editPo) {
      setPoNum(generatePoNumber);
    }
  }, [generatePoNumber]);

  useEffect(() => {
    if (!editPo) {
      dispatch(generatePoNumLoad());
    } else {
      // poNum = editPo.data.po_num;
      const initialData = {};
      initialData.procurementType =
        editPo.data.procurement_type && editPo.data.procurement_type.toUpperCase();
      initialData.vendor = {
        code: editPo.data.vendor_id,
        name: editPo.data.vendor_name,
        currency: editPo.data.currency,
        state_code: editPo.data.scm_vendor_state_code
      };
      initialData.expiryDate = editPo.data.expiry_date;
      initialData.shippingDate = editPo.data.shipping_date;
      initialData.season = editPo.data.season;
      initialData.status = editPo.type;
      initialData.reason = editPo.reason;
      initialData.data = editPo.data;
      initialData.referenceDetails = editPo?.data?.reference_number?.split(',');
      setPage(initialData);
    }
    return () => {
      dispatch(resetCreatePo());
    };
  }, []);

  const { vendorList } = useSelector((state) => state.invoiceReferenceCreation);

  const searchVendor = (value) => {
    dispatch(searchVendorList(value));
  };
  const delayedQuery = useCallback(
    debounce((q) => q.length > 0 && searchVendor(q), 500),
    []
  );

  if (generatePoNumberLoading) {
    return (
      <Box textAlign="center" pt={1}>
        {' '}
        <Spinner />
      </Box>
    );
  }

  return page === 0 ? (
    <Box mt={3}>
      <Form
        onSubmit={(e) => {
          setPage(e);
        }}
        mutators={mutatorsObj}
        render={({ handleSubmit, values, form }) => {
          const { mutators } = form;
          return (
            <form onSubmit={handleSubmit}>
              <LkCard width={850} pl={6.37} pr={6.37} margin="auto">
                <LkTypography mb={1} ta="center" variant="h3">
                  {PO.CREATE_PURCHASE_ORDER}
                </LkTypography>
                {poNum && (
                  <LkTypography mb={4} ta="center" variant="h6">
                    {PO.PO}: {poNum}
                  </LkTypography>
                )}
                <LkTypography mb={4} ta="center" variant="h5">
                  {PO.VENDOR_DETAILS}
                </LkTypography>
                <FlexBox justifyContent="space-between" mb={4.75}>
                  <Field name="vendor" validate={required}>
                    {({ meta }) => (
                      <Autocomplete
                        className={classes.bigField}
                        options={vendorList}
                        onChange={(event, newInputValue) => mutators.setVendor(newInputValue)}
                        onInputChange={(event, newInputValue) =>
                          newInputValue?.length > 2 && delayedQuery(newInputValue)
                        }
                        isOptionEqualToValue={(option) => option.code}
                        // error={meta.touched && meta.error ? meta.error : undefined }
                        getOptionLabel={(option) => option.code}
                        filterOptions={(options) => options}
                        renderInput={(params) => (
                          <LkInput
                            {...params}
                            className="input-box"
                            fullWidth
                            label={PO.ENTER_VENDOR}
                            error={meta.touched && meta.error}
                          />
                        )}
                      />
                    )}
                  </Field>
                  <Field name="agreement">
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.bigField}
                        label={PO.AGREEMENT}
                        variant="outlined"
                        disabled
                        {...input}
                        error={meta.error && meta.touched}
                        select
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        <MenuItem value="a">
                          <em>a</em>
                        </MenuItem>
                      </LkInput>
                    )}
                  </Field>
                </FlexBox>
                <Divider />
                <LkTypography mt={3.6} ta="center" variant="h5">
                  {PO.BASIC_DETAILS}
                </LkTypography>
                <FlexBox justifyContent="space-between" mb={4.5} mt={2}>
                  <Field name="expiryDate" validate={required}>
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.smallField}
                        label={PO.EXPIRY_DATE}
                        variant="outlined"
                        type="date"
                        {...input}
                        error={meta.error && meta.touched}
                        InputLabelProps={{
                          shrink: true
                        }}
                        inputProps={{
                          min: values.shippingDate
                            ? values.shippingDate
                            : formatDateDashedSeparated(Date.now())
                        }}
                        // mr={3}
                      />
                    )}
                  </Field>
                  <Field name="shippingDate" validate={required}>
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.smallField}
                        label={PO.SHIPPING_DATE}
                        variant="outlined"
                        type="date"
                        {...input}
                        error={meta.error && meta.touched}
                        InputLabelProps={{
                          shrink: true
                        }}
                        inputProps={{
                          min: formatDateDashedSeparated(Date.now()),
                          max: values.expiryDate ? values.expiryDate : null
                          // min: "2020-07-15"
                        }}
                        // mr={3}
                      />
                    )}
                  </Field>
                  <Field name="season">
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.smallField}
                        {...input}
                        error={meta.error && meta.touched}
                        label={PO.SEASON}
                        variant="outlined"
                      />
                    )}
                  </Field>
                </FlexBox>
                <FlexBox
                  className={classes.basicDetailsType}
                  justifyContent="space-between"
                  mb={4.5}
                >
                  <Field name="bulkApprove">
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.bigField}
                        label={PO.BULK_APPROVE_OPTIONAL}
                        variant="outlined"
                        {...input}
                        error={meta.error && meta.touched}
                        select
                      >
                        <MenuItem value="">{PO.NONE}</MenuItem>
                        <MenuItem value="Yes">{PO.YES}</MenuItem>
                        <MenuItem value="No">{PO.NO}</MenuItem>
                      </LkInput>
                    )}
                  </Field>
                  <Field name="procurementType">
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.bigField}
                        label={PO.PROCUREMENT_TYPE_OPTIONAL}
                        variant="outlined"
                        {...input}
                        error={meta.error && meta.touched}
                        select
                      >
                        {procurementTypeValues.map(({ key, value }) => (
                          <MenuItem key={value} value={key}>
                            {value}
                          </MenuItem>
                        ))}
                      </LkInput>
                    )}
                  </Field>
                  <Field name="shipment_type" validate={required}>
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.bigField}
                        label={PO.SHIPMENT_TYPE}
                        variant="outlined"
                        {...input}
                        error={meta.error && meta.touched}
                        select
                      >
                        <MenuItem value="AIR">{PO.AIR}</MenuItem>
                        <MenuItem value="SEA">{PO.SEA}</MenuItem>
                        <MenuItem value="LAND">{PO.LAND}</MenuItem>
                      </LkInput>
                    )}
                  </Field>
                </FlexBox>
                <FlexBox justifyContent="space-between" mb={4}>
                  <Field name="referenceDetails">
                    {({ input }) => (
                      <ChipableInput
                        label={PO.REFRENCE_DETAILS}
                        className={classes.bottomBox}
                        {...input}
                        // error={meta.error && meta.touched}
                        // data={[{label: 'email.com'},{label: 'email.com'},
                        // {label: 'email.com'},{label: 'email.com'},{label: 'email.com'}]}
                      />
                    )}
                  </Field>
                  <UploadBox
                    label={PO.ATTACHEMENTS}
                    className={classes.bottomBox}
                    data={uploadBoxData}
                  />
                </FlexBox>
                <Box margin="auto" width="350px" className={classes.buttonBox}>
                  <Button
                    className={classes.cancelButton}
                    disabled={generatePoNumberLoading}
                    onClick={gotoListing}
                    color="primary"
                    variant="outlined"
                  >
                    {PO.CANCEL}
                  </Button>
                  <Button
                    className={classes.button}
                    disabled={generatePoNumberLoading || !poNum}
                    type="submit"
                    color="primary"
                    variant="contained"
                  >
                    {PO.CREATE}
                  </Button>
                </Box>
              </LkCard>
            </form>
          );
        }}
      />
    </Box>
  ) : (
    <CreatePO handleCancel={handleCancel} value={page} poNum={poNum} />
  );
};

export default React.memo(CreatePOHome);
