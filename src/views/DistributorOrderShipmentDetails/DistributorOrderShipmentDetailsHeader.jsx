import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { CircularProgress, IconButton } from '@mui/material';
import SystemUpdateAltIcon from '@mui/icons-material/SystemUpdateAlt';

import { debounce } from 'utils/helpers';
import { isEmpty } from 'utils/lodash';

import LkInput from 'components/MaterialUi/LkInput';
import PrintShipment from 'components/common/PrintShipment';
import InfoBarItems from 'components/common/InfoBarItems';

import { CONSUL } from 'redux/reducers/consul';
import { LOGIN } from 'redux/reducers/login';
import {
  createPickingSummaryLoad,
  getDOShipmentInvoiceLoad,
  updateBoxItemLoad,
  updateBoxItemReset,
  updateDOShipmentDetailsListLoad,
  createPickingSummaryReset,
  DO_SHIPMENT_LIST
} from 'redux/reducers/doShipmentList';
import {
  getPrintShipmentDetailLoad,
  getPrintShipmentDetailReset
} from 'redux/actionCreators/printShipment';
import { getPickingScanSummaryLoad, PICKING_SCAN } from 'redux/reducers/pickingScan';

import usePermissions from 'common/usePermissions';
import useShipmentPrintingState from 'views/DistributorOrderShipmentDetails/useShipmentPrintState';

import UpdateBoxesModal from './UpdateBoxesModal';
import DistrubutorOrderClosePickingModal from './DistrubutorOrderClosePickingModal';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    background: '#FFFFFF',
    padding: 16,
    marginTop: 20
  },
  drawerContainer: {
    padding: '20px',
    display: 'flex',
    flexDirection: 'column',
    height: '94vh',
    justifyContent: 'space-between'
  },
  errorMessage: {
    fontSize: '12px',
    fontWeight: '700',
    letterSpacing: '0.25px',
    textAlign: 'center',
    color: '#e31a0d',
    margin: '10px 0'
  },
  inforBarItems: { flex: 1, marginLeft: '4vw' },
  invoiceBtn: { width: '135px' }
}));

const DistributorOrderShipmentDetailsHeader = ({
  onSearch,
  CONTENT,
  shippingPackageID,
  headerData,
  downloadCSVfun,
  disableDownloadFun,
  pidListData
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [openDrawer, setOpenDrawer] = useState(false);
  const [openBoxUpdateModal, setOpenBoxUpdateModal] = useState(false);
  const [boxCount, setBoxCount] = useState('');
  const [printShipmentDataState, setPrintShipmentDataState] = useState({});
  const isDisabled = useShipmentPrintingState(shippingPackageID);

  const {
    data: productList,
    assignedTo,
    isLoading: isProductLoading
  } = useSelector((state) => state[PICKING_SCAN].pickingScanSummary);
  const { data: createSummaryData, isLoading } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].createSummary
  );
  const { permission, empCode } = useSelector((state) => state[LOGIN].user.userDetail) || {};
  const { isPickingSuperVisor } = usePermissions(permission);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);
  const { isLoading: doInvoiceLoading } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].doShipmentInvoice
  );
  const { isLoading: boxUpdateLoading, isSuccess } = useSelector(
    (state) => state.doShipmentList.updateBoxItem
  );
  const { printShipmentData } = useSelector((state) => state.printShipment);
  const featureToggle = useSelector((state) => state[CONSUL].featureToggle.data);

  // supervisor can close picking even if all items are not picked
  const isAllItemsPicked = useMemo(() => {
    if (isPickingSuperVisor) {
      return true;
    }
    return !productList.filter((item) => item?.totalQuantity !== item?.pickedQuantity).length;
  }, [isPickingSuperVisor, productList]);

  const delayedQuery = useCallback(
    debounce(({ target: { value } }) => onSearch(value), 500),
    []
  );

  const createSummaryFun = () => dispatch(createPickingSummaryLoad({ shippingPackageID }));

  const toogleBoxModal = useCallback(() => setOpenBoxUpdateModal((prevState) => !prevState), []);

  const printShipmentLabel = () => {
    setPrintShipmentDataState({});
    dispatch(
      getPrintShipmentDetailLoad({
        shippingPackageId: shippingPackageID,
        disableOnError: true
      })
    );
  };

  const printDoShipmentInvoice = () => {
    const payload = {
      documentType: 'INVOICE',
      documentReferenceId: shippingPackageID,
      queryParams: {
        facilityCode,
        unicomOrderCode: headerData?.wmsOrderCode
      }
    };
    dispatch(getDOShipmentInvoiceLoad(payload));
  };

  const toogleDrawer = useCallback(() => setOpenDrawer((oldState) => !oldState), []);

  const returnActionButton = () => {
    const { status, channel } = headerData || {};
    if (featureToggle?.doPicking?.printStatus?.includes(status)) {
      return (
        <>
          <Button
            className={classes.invoiceBtn}
            onClick={printDoShipmentInvoice}
            color="primary"
            type="submit"
            variant="outlined"
            data-cy="print-invoice"
          >
            {doInvoiceLoading ? (
              <CircularProgress size="25px" color="inherit" />
            ) : (
              CONTENT.PRINT_INVOICE
            )}
          </Button>
          <Button
            onClick={printShipmentLabel}
            color="primary"
            type="submit"
            variant="outlined"
            className="mr-l10"
            data-cy="print-shipping-label"
            disabled={isDisabled}
          >
            {CONTENT.PRINT_SHIPMENT_LABEL}
          </Button>
        </>
      );
    }
    if (
      featureToggle?.doPicking?.startScanningStatus?.includes(status) &&
      !featureToggle?.doPicking?.channelsToExclude?.includes(channel)
    ) {
      return (
        <>
          <Button
            onClick={toogleDrawer}
            color="primary"
            type="submit"
            variant="outlined"
            className="mr-l10"
            data-cy="close-picking"
          >
            {CONTENT.CLOSE_PICKING}
          </Button>
          <Button
            color="primary"
            variant="contained"
            className="mr-l10"
            onClick={createSummaryFun}
            disabled={isLoading}
            data-cy="start-scanning"
            startIcon={
              <img
                width="12px"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/scan-icon.svg`}
                alt="validate"
              />
            }
          >
            {CONTENT.START_SCANNING}
          </Button>
        </>
      );
    }
    return null;
  };

  useEffect(() => {
    if (createSummaryData?.id) {
      navigate(`/PickingScan/distributor/${shippingPackageID}/${createSummaryData.id}`);
      dispatch(createPickingSummaryReset());
    }

    if (openDrawer) {
      dispatch(getPickingScanSummaryLoad({ shippingPackageID }));
    }

    if (printShipmentData) {
      dispatch(updateDOShipmentDetailsListLoad({ id: shippingPackageID }));
      setBoxCount(printShipmentData.boxCount || '');
      toogleBoxModal();
    }

    if (isSuccess) {
      setPrintShipmentDataState({ ...printShipmentData, boxCount });
      dispatch(updateBoxItemReset());
      toogleBoxModal();
    }
  }, [createSummaryData, openDrawer, printShipmentData, isSuccess, shippingPackageID]);

  useEffect(
    () => () => {
      dispatch(getPrintShipmentDetailReset());
    },
    []
  );
  const totalRequiredQty = useMemo(
    () => pidListData?.reduce((prev, curr) => prev + (curr.requiredQuantity ?? 0), 0),
    [pidListData]
  );

  const itemData = [
    { header: 'DO NUMBER', value: headerData?.doNumber },
    { header: 'DO VALUE', value: headerData?.doValue },
    { header: 'CUSTOMER', value: headerData?.customerName },
    { header: 'AWB NUMBER', value: headerData?.awbNumber },
    { header: 'CARRIER', value: headerData?.courier },
    { header: 'INVOICE NUMBER', value: headerData?.invoiceNumber },
    { header: 'TOTAL QUANTITY', value: totalRequiredQty }
  ];

  const goBackFun = () => navigate('/distributorOrder/Shipment/List');

  const disableGoBackToScanningBtn = useMemo(() => assignedTo !== empCode, [assignedTo, empCode]);

  const confirmBoxCountFun = () => {
    if (boxCount !== printShipmentData?.boxCount) {
      dispatch(updateBoxItemLoad([{ shippingPackageID, boxCount }]));
    } else {
      setPrintShipmentDataState(printShipmentData);
      dispatch(getPrintShipmentDetailReset());
      toogleBoxModal();
    }
  };

  return (
    <>
      <Box className="display-flex bg-white pd-16 ">
        <IconButton onClick={goBackFun} data-cy="arraw-back">
          <ArrowBackIcon fontSize="medium" />
        </IconButton>
        <Box className={classes.inforBarItems} data-cy="info-bar-items">
          <InfoBarItems items={itemData} />
        </Box>
      </Box>
      <Box className={classes.container}>
        <Box width={380}>
          <LkInput
            fullWidth
            onChange={delayedQuery}
            variant="outlined"
            id="DoShipmentDetailsglobalSearchId"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                </InputAdornment>
              )
            }}
            placeholder={CONTENT.SEARCH}
          />
        </Box>
        <Box className="display-flex">
          <IconButton
            color="primary"
            className="mr-r10"
            onClick={downloadCSVfun}
            disabled={disableDownloadFun}
            data-cy="export-csv"
          >
            <SystemUpdateAltIcon />
          </IconButton>
          {returnActionButton()}
        </Box>
      </Box>
      <DistrubutorOrderClosePickingModal
        isAllItemsPicked={isAllItemsPicked}
        CONTENT={CONTENT}
        openDrawer={openDrawer}
        handleCloseFun={toogleDrawer}
        classes={classes}
        productList={productList}
        disableGoBackToScanningBtn={disableGoBackToScanningBtn}
        shippingPackageID={shippingPackageID}
        createSummaryFun={createSummaryFun}
        isLoading={isProductLoading}
        printStatus={featureToggle?.doPicking?.printStatus}
      />

      <Box display="none">
        {!isEmpty(printShipmentDataState) && (
          <PrintShipment printShipmentData={printShipmentDataState} />
        )}
      </Box>

      <UpdateBoxesModal
        handleClose={toogleBoxModal}
        open={openBoxUpdateModal}
        CONTENT={CONTENT}
        onChange={setBoxCount}
        value={boxCount}
        handleSubmit={confirmBoxCountFun}
        isLoading={boxUpdateLoading}
      />
    </>
  );
};

export default DistributorOrderShipmentDetailsHeader;
