import * as action from '../actionTypes/monitor';

export const monitorDataRequest = (data) => ({ type: action.MONITOR_DATA_LOAD, payload: data });
export const monitorDataSuccess = (data) => ({ type: action.MONITOR_DATA_SUCCESS, payload: data });
export const monitorDataError = () => ({ type: action.MONITOR_DATA_ERROR });

export const monitorTagDetailsDataRequest = (data) => ({
  type: action.MONITOR_TAG_DETAILS_DATA_LOAD,
  payload: data
});
export const monitorTagDetailsDataSuccess = (data) => ({
  type: action.MONITOR_TAG_DETAILS_DATA_SUCCESS,
  payload: data
});
export const monitorTagDetailsDataError = () => ({ type: action.MONITOR_TAG_DETAILS_DATA_ERROR });

export const monitorTagDetailsExportDataRequest = (data) => ({
  type: action.MONITOR_TAG_DETAILS_EXPORT_DATA_LOAD,
  payload: data
});
export const monitorTagDetailsExportDataSuccess = (data) => ({
  type: action.MONITOR_TAG_DETAILS_EXPORT_DATA_SUCCESS,
  payload: data
});
export const monitorTagDetailsExportDataError = () => ({
  type: action.MONITOR_TAG_DETAILS_EXPORT_DATA_ERROR
});
export const monitorTagDetailsExportDataReset = () => ({
  type: action.MONITOR_TAG_DETAILS_EXPORT_DATA_RESET
});

// @TODO Change the structure
export const monitorFiltersDataLoad = () => ({ type: action.MONITOR_FILTERS_DATA_LOAD });
export const monitorFiltersDataSuccess = (data) => ({
  type: action.MONITOR_FILTERS_DATA_SUCCESS,
  payload: data
});
export const monitorFiltersUpdate = (data) => ({
  type: action.MONITOR_FILTERS_UPDATE,
  payload: data
});
export const monitorFiltersReset = () => ({ type: action.MONITOR_FILTERS_DATA_RESET });
export const monitorFiltersLoad = () => ({ type: action.MONITOR_FILTERS_DATA_ERROR });

// Filters Save
export const monitorFiltersSaveDataLoad = (data) => ({
  type: action.MONITOR_FILTERS_DATA_SAVE_LOAD,
  payload: data
});
export const monitorFiltersSaveDataSuccess = (data) => ({
  type: action.MONITOR_FILTERS_DATA_SAVE_SUCCESS,
  payload: data
});
export const monitorFiltersSaveDataFail = () => ({
  type: action.MONITOR_FILTERS_DATA_SAVE_ERROR
});

export const monitorFiltersSaveDataReset = () => ({
  type: action.MONITOR_FILTERS_DATA_SAVE_RESET
});

export const monitorDetailsFiltersUpdate = (data) => ({
  type: action.MONITOR_DETAILS_FILTERS_CONFIG_UPDATE,
  payload: data
});

export const monitorDetailsFiltersReset = () => ({
  type: action.MONITOR_DETAILS_FILTERS_CONFIG_RESET
});

export const monitorDataInfoTableRequest = (data) => ({
  type: action.MONITOR_DATA_INFO_TABLE_LOAD,
  payload: data
});

export const monitorDataInfoTableSuccess = (data) => ({
  type: action.MONITOR_DATA_INFO_TABLE_SUCCESS,
  payload: data
});

export const monitorDataInfoTableError = () => ({ type: action.MONITOR_DATA_INFO_TABLE_ERROR });

export const monitorJitDataInfoTableRequest = (data) => ({
  type: action.MONITOR_JIT_DATA_INFO_TABLE_LOAD,
  payload: data
});

export const monitorJitDataInfoTableSuccess = (data) => ({
  type: action.MONITOR_JIT_DATA_INFO_TABLE_SUCCESS,
  payload: data
});

export const monitorJitDataInfoTableError = () => ({
  type: action.MONITOR_JIT_DATA_INFO_TABLE_ERROR
});

// Total Order Count
export const monitorTotalOrderCountLoad = () => ({ type: action.MONITOR_TOTAL_ORDER_COUNT_LOAD });

export const monitorTotalOrderCountSuccess = (data) => ({
  type: action.MONITOR_TOTAL_ORDER_COUNT_SUCCESS,
  payload: data
});

export const monitorTotalOrderCountFail = () => ({
  type: action.MONITOR_TOTAL_ORDER_COUNT_ERROR
});

export const monitorTotalOrderCountReset = () => ({
  type: action.MONITOR_TOTAL_ORDER_COUNT_RESET
});

// Total Order list
export const monitorTotalOrderDataLoad = (data) => ({
  type: action.MONITOR_TOTAL_ORDER_DATA_LOAD,
  payload: data
});

export const monitorTotalOrderDataSuccess = (data) => ({
  type: action.MONITOR_TOTAL_ORDER_DATA_SUCCESS,
  payload: data
});

export const monitorTotalOrderDataFail = () => ({
  type: action.MONITOR_TOTAL_ORDER_DATA_ERROR
});

export const monitorTotalOrderDataReset = () => ({
  type: action.MONITOR_TOTAL_ORDER_DATA_RESET
});
