import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { omit } from 'utils/lodash';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { LOCALISATION } from 'redux/reducers/localisation';
import useFilterHook from 'components/common/useFilterHook';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import DistributorOrderShipmentListHeader from './DistributorOrderShipmentListHeader';
import DistributorOrderShipmentListBody from './DistributorOrderShipmentListBody';
import {
  DO_SHIPMENT_LIST,
  getDOShipmentListExportLoad,
  getDOShipmentListExportReset,
  getDOShipmentListLoad,
  printInvoiceLoad
} from '../../redux/reducers/doShipmentList';
import {
  convertDateFormat,
  fileDownload,
  generatePayloadForSearchAPI,
  getAllQueryParam,
  listToCSVString
} from '../../utils/helpers';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    height: 'calc(100vh - 65px)',
    background: '#F8F8F8'
  }
}));

const PAGE_SIZE = 35;

const requestPayload = {
  offset: 0,
  pageSize: PAGE_SIZE,
  sortKey: 'created_at',
  sortOrder: 'ASC'
};

const DistributorOrderShipmentList = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const location = useLocation();
  const initialReq = useRef(false);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter } = useFilterHook();
  const CONTENT = useSelector(
    (state) => state[LOCALISATION].localeData.DISTRIBUTOR_ORDER_SHIPMENT_LIST
  );
  const { data, isLoading, totalCount } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].shipmentList
  );
  const shipmentPrintInvoice = useSelector((state) => state[DO_SHIPMENT_LIST].shipmentPrintInvoice);
  const { data: exportData, isLoading: isExportLoading } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].exportShipmentList
  );
  const [requestBody, setRequestBody] = useState(requestPayload);

  const fetchMoreData = (sortOrder, sortKey, offset) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      offset,
      sortOrder,
      sortKey
    }));
  };

  const printInvoice = (shippingPackageId) => {
    dispatch(printInvoiceLoad({ shippingPackageId }));
  };

  const exportToCSV = (exportDataDetails) => {
    const columns = [CONTENT.SHIPPING_PACKAGE_ID, CONTENT.DO_NUMBER, CONTENT.STATUS];
    const exportKeys = ['shippingPackageId', 'incrementId', 'createdAt', 'status'];
    const csvString = listToCSVString(exportDataDetails, columns, exportKeys);
    fileDownload(csvString, `shipmentList-${convertDateFormat(new Date(), 'export')}`);
  };

  const onGlobalSearch = (value) => {
    if (value) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        offset: 0,
        shippingPackageId: value
      }));
    } else {
      setRequestBody((prevReqBody) => ({
        ...omit(prevReqBody, ['shippingPackageId'])
      }));
    }
  };

  useEffect(() => {
    initialReq.current = 0;
    onChangeFilterList([
      {
        key: 'CREATED',
        value: 'LAST 7 DAYS'
      }
    ]);
  }, []);

  useEffect(() => {
    if (initialReq.current >= 2) {
      dispatch(getDOShipmentListLoad(requestBody));
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current += 1;
    const result = getAllQueryParam(window.location.search);
    const payload = generatePayloadForSearchAPI(result, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => {
      const newState = {
        offset: 0,
        pageSize: PAGE_SIZE,
        sortKey: prevReqBody.sortKey,
        sortOrder: prevReqBody.sortOrder,
        ...payload
      };
      if (prevReqBody.shippingPackageId) {
        newState.shippingPackageId = prevReqBody.shippingPackageId;
      }
      return newState;
    });
  }, [location]);

  useEffect(() => {
    if (exportData.length) {
      exportToCSV(exportData);
      dispatch(getDOShipmentListExportReset());
    }
  }, [exportData]);

  const onDownloadFun = () => {
    if (data.length === totalCount) {
      exportToCSV(data);
    } else {
      dispatch(getDOShipmentListExportLoad({ ...requestBody, offset: 0, pageSize: totalCount }));
    }
  };

  return (
    <Box className={classes.root}>
      <DistributorOrderShipmentListHeader
        onSearch={onGlobalSearch}
        CONTENT={CONTENT}
        disableDownloadFun={!data.length || isExportLoading}
        onDownloadFun={onDownloadFun}
        deleteSelectedFilter={deleteSelectedFilter}
        selectedFilterList={selectedFilterList}
      />
      <DistributorOrderShipmentListBody
        pageLimit={PAGE_SIZE}
        pageNumber={requestBody.offset}
        fetchMoreListItems={fetchMoreData}
        CONTENT={CONTENT}
        data={data}
        isLoading={isLoading}
        totalCount={totalCount}
        onChangeFilterList={onChangeFilterList}
        selectedFilterList={selectedFilterList}
        printInvoice={printInvoice}
        shipmentPrintInvoice={shipmentPrintInvoice}
      />
    </Box>
  );
};

export default DistributorOrderShipmentList;
