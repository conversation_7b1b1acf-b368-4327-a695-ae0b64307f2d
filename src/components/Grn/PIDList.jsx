/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import Button from '@mui/material/Button';
import AddIcon from '@mui/icons-material/Add';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

import { getQcDataLoad, addPidReset } from '../../redux/actionCreators/grn';
import './PIDList.scss';

const PIDList = (props) => {
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);
  const { pidDetail, qcDetailsError, addPidData, isProceedToGreenChannel } = useSelector(
    (state) => state.grn
  );

  const pidStatusMapping = {
    FAILED: createGrnLocal.BLOCKED,
    PENDING: createGrnLocal.ONGOING,
    PASSED: (
      <img
        className="image-container"
        src={`${import.meta.env.VITE_STATIC_SERVER}/images/vectorSuccess.svg`}
        alt="img"
      />
    )
  };

  const [selectedPid, setSelectedPid] = useState();
  const [proceedToGreenChannel, setProceedToGreenChannel] = useState(isProceedToGreenChannel);
  const [isFailed, setIsFailed] = useState(false);
  const [blockedPidsList, setBlockedPidsList] = useState({});
  useEffect(() => {
    setProceedToGreenChannel(isProceedToGreenChannel);
  }, [isProceedToGreenChannel]);

  const dispatch = useDispatch();

  const { pidList, changePid, setOpenAddPidModal, toggleModal } = props;
  const pidData = pidList?.result?.data.grn_pids;

  useEffect(() => {
    if (pidList) {
      const temp = {};
      const { grn_pids } = pidList.result.data;
      if (grn_pids?.length > 0) {
        grn_pids.forEach(({ pid, status }) => {
          if (status === 'FAILED') {
            temp[pid] = true;
          }
        });
      }
      setBlockedPidsList(temp);
      if (!selectedPid) {
        // onload selecting the last pid which is in pending status
        let newPid = pidData[0]?.pid;
        const tempPidData = [...pidData];
        tempPidData.reverse();
        const index = tempPidData.findIndex((t) => t.status === 'PENDING');
        const findalIndex = tempPidData.length - 1 - index;
        if (index !== -1) {
          newPid = pidData[findalIndex].pid;
        }
        setSelectedPid(newPid);
        changePid(newPid);
      } else {
        changePid(selectedPid);
      }
    }
  }, [pidList]);

  useEffect(() => {
    if (qcDetailsError) {
      const { data, status } = qcDetailsError;
      if (status === 406 && data.error_code === 'LK-GRN-008') {
        setIsFailed(true);
      }
    }
  }, [qcDetailsError]);

  useEffect(() => {
    if (addPidData) {
      const newPid = addPidData.result.data?.grn_pid?.pid;
      setSelectedPid(newPid);
    }
  }, [addPidData]);

  const setPid = (pid) => {
    if (pid === selectedPid) {
      return;
    }
    setSelectedPid(pid);
    changePid(pid);
  };

  useEffect(() => {
    if (pidDetail) {
      const { data } = pidDetail.result;
      const { pid } = pidDetail.result.data;
      setSelectedPid(pid);
      if (data?.status === 'FAILED') {
        setIsFailed(true);
      } else {
        setIsFailed(false);
      }
      dispatch(getQcDataLoad(data));
    }
  }, [pidDetail, dispatch]);

  return (
    <div className="pid-section bg-f5">
      <div className="mr-l25 w-180 mr-t30">
        <Button
          variant="contained"
          color="primary"
          onClick={() => {
            dispatch(addPidReset());
            toggleModal(true, setOpenAddPidModal);
          }}
          startIcon={<AddIcon />}
        >
          {createGrnLocal.ADD_PID}
        </Button>
        <div className="mr-t25 text-20 fw-bold mr-b15">{createGrnLocal.PRODUCT_LIST}</div>
      </div>
      <div className="">
        {pidData?.map(({ pid, status }) => {
          const failedTemp = pid === selectedPid && isFailed && pidDetail;
          let label;
          if (failedTemp || blockedPidsList[pid]) {
            label = 'BLOCKED';
          } else if (proceedToGreenChannel[pid]) {
            label = pidStatusMapping.PASSED;
          } else if (!proceedToGreenChannel[pid]) {
            label = pidStatusMapping.PENDING;
          } else {
            label = pidStatusMapping[status];
          }
          return (
            <div
              key={pid}
              className={`display-flex justify-content-space-between align-items-center
                 cursor-pointer pd-16${pid === selectedPid ? ' bg-white' : ''}`}
              onClick={() => setPid(pid)}
            >
              <div className="text-dark_grey text-b3 fw-bold">{pid}</div>
              <div
                className={` text-b5 fw-bold text-uppercase ${
                  status === 'FAILED' || failedTemp ? 'text-red' : 'text-bright-yellow'
                }`}
              >
                {label}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default PIDList;
