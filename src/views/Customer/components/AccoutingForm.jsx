import React from 'react';
import { Box, Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const AccoutingForm = ({
  CustomerFormTitle,
  CustomerFieldContainer,
  CustomerTextField,
  formState,
  handleChange,
  errors
}) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);

  return (
    <Box>
      <CustomerFormTitle variant="h4">{CONTENT.ACCOUNTING_DETAILS}</CustomerFormTitle>
      <CustomerFieldContainer>
        <CustomerTextField
          name="gstin"
          data-cy="gstin"
          value={formState.gstin}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors?.gstin}
          helperText={errors?.gstin}
          placeholder="Enter GSTIN (optional)"
        />
        <CustomerTextField
          name="pan"
          value={formState.pan}
          onChange={handleChange}
          placeholder="Enter PAN (optional)"
        />
        <CustomerTextField
          name="tin"
          value={formState.tin}
          onChange={handleChange}
          placeholder="Enter TIN (optional)"
        />
        <CustomerTextField
          name="centralSaleTax"
          value={formState.centralSaleTax}
          onChange={handleChange}
          placeholder="Enter Centeral Sales Tax (optional)"
        />
        <CustomerTextField
          name="serviceTax"
          value={formState.serviceTax}
          onChange={handleChange}
          placeholder="Enter Service Tax (optional)"
        />
        <FormGroup
          sx={{
            display: 'inline-block'
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                name="taxExempted"
                onChange={handleChange}
                checked={formState.taxExempted}
              />
            }
            data-cy="taxExempted"
            label="Enter TAX Exempted (optional)"
          />
          <FormControlLabel
            control={
              <Checkbox
                name="registeredDealer"
                onChange={handleChange}
                checked={formState.registeredDealer}
              />
            }
            data-cy="registeredDealer"
            label="Enter Registered Dealer (optional)"
          />
          <FormControlLabel
            control={
              <Checkbox
                name="dualCompanyRetail"
                onChange={handleChange}
                checked={formState.dualCompanyRetail}
              />
            }
            data-cy="dualCompanyRetail"
            label="Dual Company Retail (optional)"
          />

          <FormControlLabel
            control={
              <Checkbox
                name="providesCForm"
                onChange={handleChange}
                checked={formState.providesCForm}
              />
            }
            data-cy="providesCForm"
            label="Provides C Form (optional)"
          />
        </FormGroup>
      </CustomerFieldContainer>
    </Box>
  );
};

export default AccoutingForm;
