import { Box, MenuItem, Button } from '@mui/material';
import React, { useState, useEffect } from 'react';
import LkModal from 'components/Modal/Modal';
import LkSelect from 'components/MaterialUi/LkSelectBox';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const PrintConfirmModal = ({
  open,
  handleClose,
  printFun,
  printOptions,
  changePrintfun,
  printValueTemp
}) => {
  const [printValue, setPrintValue] = useState(0);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.PRINT_CONFIRM_MODAL);

  const changePrintOption = (e) => {
    const { value } = e.target;
    setPrintValue(value);
  };
  useEffect(() => {
    if (printValueTemp) {
      setPrintValue(printValueTemp);
    }
  }, [printValueTemp]);

  return (
    <LkModal
      open={open}
      handleClose={handleClose}
      title={CONTENT.PRINT_OPTIONS}
      subTitle={CONTENT.SELECT_FROM_OPTIONS_BELOW}
      modalWidth="520px"
      modalHeight="334px"
      showActionButton={false}
    >
      <Box width="270px" margin="0 auto" mt={6}>
        <LkSelect
          style={{ height: 40 }}
          label={false}
          value={printValue}
          data-cy="select-print-options"
          fullWidth
          onChange={(e) => {
            if (changePrintfun) {
              changePrintfun(e);
            } else {
              changePrintOption(e);
            }
          }}
        >
          <MenuItem value={0}>{CONTENT.SELECT_PRINT_OPTIONS}</MenuItem>;
          {printOptions.map((print) => {
            const { key, value } = print;
            return (
              <MenuItem value={key} key={key} data-cy={`${value}`}>
                {value}
              </MenuItem>
            );
          })}
        </LkSelect>
      </Box>
      <div className="display-flex justify-content-center mr-b40 mr-t40">
        <Button
          onClick={() => printFun()}
          disabled={!printValue}
          style={{ borderRadius: 8 }}
          data-cy="confirm-print-btn"
          variant="contained"
          color="primary"
        >
          {CONTENT.PRINT}
        </Button>
      </div>
    </LkModal>
  );
};

export default PrintConfirmModal;
