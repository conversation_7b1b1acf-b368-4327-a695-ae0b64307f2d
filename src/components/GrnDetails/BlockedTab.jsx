import React, {  useState, useCallback } from 'react'
import { Box } from '@mui/material';
import BlockedTabActions from './BlockedTabActions';
import BlockedProductsTable from './BlockedProductsTable'
import { getBlockedsPidsLoad } from '../../redux/actionCreators/grnDetails';
import { useDispatch } from 'react-redux';
import { debounce } from 'utils/helpers';

const defaultValues = {
    manual_override_allowed: [],
    manual_override_rejected: []
}

const BlockedTab = ({  grnCode, mainGrnSearch }) => {
    const [selectBox, setSelectBox] = useState(0)
    const [inputBox, setInputBox] = useState("")
    const [actionValues, setActionValues] = useState(defaultValues)
    const dispatch = useDispatch();
    const handleSelectBox = ({ target: { value } }) => {
        setSelectBox(value)
        setInputBox("")
    }

    const debouceFun = ({ key, value }) => {
        value.length > 2 && dispatch(getBlockedsPidsLoad({ key, value, grnCode, }))
    }
    
    const delayedQuery = useCallback(debounce(q => { debouceFun(q) }, 500), []);


    const handleInputBox = (fromSelectFiled, e) => {
        const { value } = e.target
        if (fromSelectFiled && value === 0) return null
        else {
            setInputBox(value)
            delayedQuery({ key: selectBox, value: value })
        }
    }



    const overrideFun = (isAllowed, grnCode, pid) => {
        let tempState = { ...actionValues }
        const objName = isAllowed ? "manual_override_allowed" : "manual_override_rejected"
        let alreadyPresent = false
        if (isAllowed) {
            alreadyPresent = tempState.manual_override_rejected.some((t) => t.grn_code === grnCode && t.pid === pid)
        }
        else alreadyPresent = tempState.manual_override_allowed.some((t) => t.grn_code === grnCode && t.pid === pid)
        if (!alreadyPresent) {
            let findIndex = actionValues[objName].findIndex((data) => data.grn_code === grnCode && data.pid === pid)
            if (findIndex === -1) {
                let newData = { "grn_code": grnCode, "pid": pid }
                tempState[objName].push(newData)
            }
            else tempState[objName].splice(findIndex, 1)
        }

        setActionValues(tempState)
    }

    const clearSelection = useCallback(() => setActionValues({
        manual_override_allowed: [],
        manual_override_rejected: []
    }), [setActionValues])

    const reset = () =>{
        setSelectBox(0)
        setInputBox("")
        dispatch(getBlockedsPidsLoad({ grnCode:grnCode}))
    }
    return <div >
        <Box>
            <BlockedTabActions
                reset={reset}
                dispatch={dispatch}
                clearSelection={clearSelection}
                actionValues={actionValues}
                grnCode={grnCode}
                selectBox={selectBox}
                inputBox={inputBox}
                handleSelectBox={handleSelectBox}
                handleInputBox={handleInputBox}
            />
        </Box>
        <BlockedProductsTable
            actionValues={actionValues}
            overrideFun={overrideFun}
            keyValue={selectBox}
            value={inputBox}
            mainGrnSearch={mainGrnSearch}

        />
    </div>
}

export default BlockedTab