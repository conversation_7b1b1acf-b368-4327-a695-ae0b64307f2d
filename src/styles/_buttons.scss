@use 'sass:map';
@use './text' as *;
@use './variables' as *;
@use './mixins' as *;

.btn {
  font-size: $font-size-b1;
  line-height: 1.2;
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  border: 1px solid transparent;
  border-radius: $border-radius-base;
  padding: 8px 16px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background: $color-white;
  @include userSelectNone();

  &:not(:disabled):not(.disabled) {
    cursor: pointer;
  }

  &.focus,
  &:focus,
  &:hover {
    outline: 0;
  }

  &.btn-primary {
    font-weight: bold;
    box-shadow: 0px 8px 16px rgba(0, 186, 198, 0.15);
    color: $color-white;
    background-color: $color-primary;
    border: 1px solid $color-primary;

    &.btn-sm {
      font-size: $font-size-b2;
    }

    &.btn-xs {
      font-size: $font-size-b3;
    }

    &.focus,
    &:focus,
    &:hover {
      box-shadow: 0px 16px 32px rgba(0, 186, 198, 0.3);
    }
  }

  &.btn-default {
    font-weight: normal;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.05);
    color: $color-primary;
    background-color: $color-white;
    border: 1px solid $color-primary;

    &.btn-sm {
      font-size: $font-size-b2;
    }

    &.btn-xs {
      font-size: $font-size-b3;
    }

    &.focus,
    &:focus,
    &:hover {
      box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.1);
    }
  }

  &.btn-warning {
    font-weight: normal;
    box-shadow: 0px 8px 16px rgba(239, 78, 116, 0.15);
    color: $color-white;
    background-color: map.get($colors-secondary, dark_pink);
    border: 1px solid map.get($colors-secondary, dark_pink);

    &.btn-sm {
      font-size: $font-size-b2;
    }

    &.btn-xs {
      font-size: $font-size-b3;
    }

    &.focus,
    &:focus,
    &:hover {
      box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.1);
    }
  }

  &.btn-cta {
    font-size: $font-size-caption;
    font-weight: normal;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.05);
    border: 1px solid map.get($colors-grey, grey5);
    padding: 5px 10px;

    &.focus,
    &:focus,
    &:hover {
      box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.1);
    }
  }
}

.btn-link,
a {
  font-weight: bold;
  color: $color-primary;
  font-size: $font-size-b3;
  &.btn-link-sm {
    font-size: $font-size-b4;
  }
}
.lineHeight3 {
  line-height: 1.3;
}
