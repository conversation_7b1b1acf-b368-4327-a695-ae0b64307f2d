/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable max-len */
import { call, put, all, takeLatest, takeEvery, select } from 'redux-saga/effects';
import { newRelicNoticeError } from 'utils/newRelicHelper';
import { arrayBufferToJson } from 'utils/helpers';
import { NEWRELIC_TITLE } from 'utils/newRelicTitles';
import Reassign from 'components/OrderDetailAction/Reassign';
import { hideLoader, showLoader } from 'redux/actionCreators/loader';
import * as actionCreations from '../actionCreators/orderDetails';
import * as action from '../actionTypes/orderDetails';
import * as apis from '../../apis/orderDetails';
import { showToaster } from './toast';

// Helper function to extract error details
const fetchErrorDetails = (error) => {
  const { meta } = error?.response?.data || {};
  return {
    displayMessage: meta?.displayMessage,
    message: meta?.message
  };
};

const autoToManualErrorMEssage = [
  'The status combination BlankInTrayStatusConversion is not acceptable for conversion. Please process the order as it is',
  'The status combination InPickingStatusConversion is not acceptable for conversion. Please process the order as it is'
];

// Helper function to check if error message requires fallback
const shouldUseStockOutFallback = (errorMessage, FALLBACK_ERROR_MESSAGES) =>
  FALLBACK_ERROR_MESSAGES.some(
    (fallbackMsg) =>
      errorMessage?.displayMessage?.includes(fallbackMsg) ||
      errorMessage?.message?.includes(fallbackMsg)
  );

function* getHeaderDetailsSaga({ payload }) {
  try {
    const response = yield call(apis.orderDetailsHeaderAPI, payload);
    yield put(actionCreations.orderDetailsHeaderDataSuccess(response.data.data));
  } catch (error) {
    yield showToaster(error);
    yield put(actionCreations.orderDetailsHeaderDataFail());
  }
}

function* getOrderDetailsSaga({ payload }) {
  try {
    const response = yield call(apis.orderDetailsOverviewAPI, payload);
    yield put(actionCreations.orderDetailsOverviewDataSuccess(response.data));
  } catch (error) {
    yield put(actionCreations.orderDetailsOverviewDataFail());
    yield showToaster(error);
  }
}

function* getCourierListSaga({ payload }) {
  try {
    const response = yield call(apis.courierListApi, payload);
    yield put(actionCreations.courierListSuccess(response.data.data.result));
  } catch (error) {
    yield put(actionCreations.courierListfail());
    yield showToaster(error);
  }
}

function* getShipmentReassignSaga({ payload }) {
  try {
    const response = yield call(apis.shipmentReassignApi, payload);
    yield put(actionCreations.orderDetailsShipmentReassignSuccess(response.data));
    yield showToaster(
      null,
      'success',
      'Order has been sent for reassignment, please check after some time.'
    );
  } catch (error) {
    yield put(actionCreations.orderDetailsShipmentReassignFail());
    yield showToaster(error);
  }
}

function* getVsmStatusSaga(payload) {
  let vsmData = null;
  try {
    const { data } = yield call(apis.orderDetailsVMSStatusApi, payload);
    vsmData = data;
  } catch (error) {
    yield showToaster(error);
  }
  return vsmData;
}

export function* getItemsOrderListSaga({ payload }) {
  try {
    const vsmData = yield getVsmStatusSaga(payload);
    const response = yield call(apis.getItemsOrderList, payload);
    const { orderItemHeaderResponse } = response.data.data || {};
    const data = {
      ...payload,
      orderItemHeaderResponse,
      nexsOrderId: response.data.data.nexsOrderId,
      vsmData
    };
    yield put(actionCreations.orderDetailsShipmentItemsSuccess(data));

    const selectedFacility = yield select((state) => state.settings.selectedFacility);
    const featureToggle = yield select((state) => state.consul.featureToggle.data);

    const orderItemStauses = orderItemHeaderResponse?.orderItemResponses?.map(
      (item) => item?.status
    );
    const notFoundAllowedStatus = featureToggle?.notFound?.[selectedFacility]?.status;
    const isStatusAllowedForNotFound = orderItemStauses?.some((status) =>
      notFoundAllowedStatus?.includes(status)
    );

    if (isStatusAllowedForNotFound) {
      yield put(actionCreations.getNotFoundStatusLoad(payload?.shipmentId));
    }
  } catch (error) {
    yield showToaster(error);
    yield put(actionCreations.orderDetailsShipmentItemsFail(payload.shipmentId));
  }
}

function* orderStockOutFallbackSaga({ payload }) {
  try {
    const { shippingPackageId, incrementId } = payload;
    yield call(apis.orderStockOutFallbackApi, payload);
    yield put(actionCreations.orderDetailStockOutSuccess(true));
    yield showToaster(null, 'success', 'Barcode has been stocked out');
    yield call(getItemsOrderListSaga, {
      payload: { id: incrementId, shipmentId: shippingPackageId }
    });
  } catch (error) {
    const { shippingPackageId } = payload;
    const customAttributes = {
      shipmentId: shippingPackageId,
      apiPayload: JSON.stringify(payload)
    };
    newRelicNoticeError(NEWRELIC_TITLE.ORDER_STOCKOUT_FALLBACK, error, customAttributes);
    yield put(actionCreations.orderDetailStockOutFail());
    yield showToaster(error);
  }
}

function* orderStockOutSaga({ payload }) {
  try {
    const { id, barcode, shipmentId, incrementId } = payload;
    yield call(apis.orderStockOutApi, { id, barcode });
    yield put(actionCreations.orderDetailStockOutSuccess(true));
    yield showToaster(null, 'success', 'Barcode has been stocked out');
    yield call(getItemsOrderListSaga, { payload: { id: incrementId, shipmentId } });
  } catch (error) {
    const { shipmentId, id, barcode, enableFallback, FALLBACK_ERROR_MESSAGES, fallbackPayload } =
      payload;
    const customAttributes = {
      shipmentId,
      stockOutId: id,
      stockOutBarcode: barcode
    };
    newRelicNoticeError(NEWRELIC_TITLE.ORDER_STOCKOUT, error, customAttributes);
    const errorDetails = fetchErrorDetails(error);
    if (enableFallback && shouldUseStockOutFallback(errorDetails, FALLBACK_ERROR_MESSAGES)) {
      yield orderStockOutFallbackSaga({
        payload: fallbackPayload
      });
    } else {
      yield put(actionCreations.orderDetailStockOutFail());
      yield showToaster(error);
    }
  }
}

function* getOrderDetailsShipmentDataSaga({ payload }) {
  try {
    const response = yield call(apis.OrderDetailsShipmentAPI, payload);
    yield put(actionCreations.orderDetailsShipmentsDataSuccess(response.data));
  } catch (error) {
    yield put(actionCreations.orderDetailsShipmentsDataFail());
    yield showToaster(error);
  }
}

function* getOrderDetailsItemsDataSaga({ payload }) {
  try {
    const response = yield call(apis.orderDetailsItemsAPI, payload);
    yield put(actionCreations.orderDetailsItemsDataSuccess(response.data));
  } catch (error) {
    yield put(actionCreations.orderDetailsItemsDataFail());
    yield showToaster(error);
  }
}

function* getOrderDetailsPrintInvoiceDataSaga({ payload }) {
  try {
    const response = yield call(apis.OrderDetailsPrintInvoiceAPI, payload);
    const data = {
      shipmentId: payload,
      response
    };
    yield put(actionCreations.orderDetailsPrintInvoiceDataSuccess(data));
  } catch (error) {
    yield put(actionCreations.orderDetailsPrintInvoiceDataFail(payload));
    yield showToaster(
      error,
      'error',
      arrayBufferToJson(error?.response?.data)?.meta?.displayMessage,
      'Invoice not generated. Retry after 1 minute'
    );
  }
}

function* getOrderDetailsInvoiceDataSaga({ payload }) {
  try {
    const response = yield call(apis.OrderDetailsInvoiceDataAPI, payload);
    yield put(actionCreations.orderDetailsInvoiceDataSuccess(response.data));
  } catch (error) {
    yield showToaster(error, 'error', error?.response?.data);
    yield put(actionCreations.orderDetailsInvoiceDataFail());
  }
}

function* getOrderDetailHistorySaga({ payload }) {
  try {
    const { data } = yield call(apis.orderDetailsHistorysApi, payload);
    yield put(
      actionCreations.orderDetailsHistorySuccess({
        id: payload,
        data: data.data.orderActivityEvents
      })
    );
  } catch (error) {
    yield showToaster(error);
    yield put(actionCreations.orderDetailsHistoryFail(payload));
  }
}

function* autoToManualSaga({ payload }) {
  try {
    const { shippingPackageId, ...rest } = payload;
    const response = yield call(apis.autoToManualApi, rest);
    yield put(actionCreations.autoToManualSuccess(response.data));
    yield showToaster(null, 'success', 'Auto to manual JIT conversion completed');
  } catch (error) {
    const errorDetails = fetchErrorDetails(error);
    const { shippingPackageId, facilityCode } = payload;
    if (autoToManualErrorMEssage.some((msg) => errorDetails?.displayMessage?.includes(msg))) {
      yield showToaster(
        error,
        'error',
        '',
        <Reassign
          payload={{
            shippingPackageId,
            selectedFacility: facilityCode,
            reassignmentReason: 'Reassignment using auto to manual button'
          }}
        />,
        7000
      );
    } else {
      yield showToaster(error);
    }
    newRelicNoticeError(NEWRELIC_TITLE.ORDER_AUTO_TO_MANUAL, error, payload);
    yield put(actionCreations.autoToManualFail());
  }
}

function* autoReassignAutoToManualSaga({ payload }) {
  try {
    const response = yield call(apis.reassignAutoToManualApi, payload);
    yield put(actionCreations.autoToManualSuccess(response.data));
    yield showToaster(null, 'success', 'Reassigned Successfully');
  } catch (error) {
    yield showToaster(error);
    newRelicNoticeError(NEWRELIC_TITLE.ORDER_AUTO_REASSIGN, error, payload);
    yield put(actionCreations.autoToManualFail());
    yield put(actionCreations.autoToManualReset());
  }
}

function* getNotFoundStatusSaga({ payload }) {
  try {
    const response = yield call(apis.getNotFoundStatusAPI, payload);
    yield put(actionCreations.getNotFoundStatusSuccess(response.data));
  } catch (error) {
    yield showToaster(error);
    yield put(actionCreations.getNotFoundStatusFail());
  }
}

function* regularToJitOrderSaga({ payload }) {
  yield put(showLoader());
  const { shipmentId, incrementId } = payload;
  try {
    const { data } = yield call(apis.regularToJitOrderApi, shipmentId);
    yield showToaster('', 'success', data?.data?.displayMessage);
    yield call(getItemsOrderListSaga, { payload: { id: incrementId, shipmentId } });
    yield put(hideLoader());
  } catch (error) {
    yield showToaster(error);
    yield put(hideLoader());
  }
}

export default function* runOrderDetailsCalls() {
  yield all([
    takeLatest(action.ORDER_DETAILS_HEADER_LOAD, getHeaderDetailsSaga),
    takeLatest(action.ORDER_DETAILS_OVERVIEW_LOAD, getOrderDetailsSaga),
    takeLatest(action.ORDER_DETAILS_COURIER_LIST_LOAD, getCourierListSaga),
    takeLatest(action.ORDER_DETAILS_SHIPMENT_REASSIGN_LOAD, getShipmentReassignSaga),
    takeLatest(action.ORDER_STOCK_OUT_LOAD, orderStockOutSaga),
    takeLatest(action.ORDER_DETAILS_ITEMS_LOAD, getOrderDetailsItemsDataSaga),
    takeLatest(action.ORDER_DETAILS_SHIPMENTS_LOAD, getOrderDetailsShipmentDataSaga),
    takeEvery(action.ORDER_DETAILS_SHIPMENT_ITEMS_LOAD, getItemsOrderListSaga),
    takeLatest(action.ORDER_DETAILS_PRINT_INVOICE_LOAD, getOrderDetailsPrintInvoiceDataSaga),
    takeLatest(action.ORDER_DETAILS_INVOICE_DATA_LOAD, getOrderDetailsInvoiceDataSaga),
    takeLatest(action.ORDER_DETAILS_HISTORY_LOAD, getOrderDetailHistorySaga),
    takeLatest(action.ORDER_DETAILS_AUTO_TO_MANUAL_LOAD, autoToManualSaga),
    takeLatest(action.ORDER_DETAILS_AUTO_REASSIGN_LOAD, autoReassignAutoToManualSaga),
    takeLatest(actionCreations.getNotFoundStatusLoad.type, getNotFoundStatusSaga),
    takeLatest(actionCreations.regularToJitLoad.type, regularToJitOrderSaga)
  ]);
}
