// eslint-disable-next-line import/no-extraneous-dependencies
import 'cypress-file-upload';
import config from '../../src/config';

describe('Start Retire', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
    cy.navigate(['main-navigation', 'startRetire']);
  });

  afterEach(() => {
    cy.logout();
  });

  it('Visits the Start Retire Page from app bar and table is populated', () => {
    // TODO replace url when u add apis

    cy.fixture('startRetire/startRetireHeader').as('headers');

    cy.intercept('POST', config.startRetire.list).as('getStartRetireList');

    cy.get('@headers').then((headers) => {
      headers.forEach((header, index) => {
        cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
      });
    });

    cy.wait('@getStartRetireList').then(({ response }) => {
      const apiData = response?.body?.data?.responseList; // Capturing the API response body
      if (apiData.length) {
        cy.getByCy(`table-cell-${0}-0`).should('exist');

        // Your assertions for this item's data
        cy.getByCy(`table-cell-${0}-0`).should('contain.text', apiData[0].startRetireId);
      }
    });
  });

  it('File Upload', () => {
    const fileName = 'startRetire/sampleFileStartRetire.csv';

    cy.intercept('POST', config.startRetire.bulkUpload).as('uploadStartRetire');

    cy.getByCy('upload Start retire').click();
    cy.get('#upload-items-file').attachFile(fileName);

    cy.wait('@uploadStartRetire').then(({ response }) => {
      const apiData = response.body; // Capturing the API response body
      if (apiData?.meta?.displayMessage || apiData?.error) {
        // uploading failed error is thrown from API
        cy.getByCy('cancel-fileupload-modal').click();
      } else if (apiData) {
        // Downloading error file
        cy.findByText(/Problems with CSV File/i).should('exist');
        cy.getByCy('ok_button').click();
      } else {
        cy.findByText(/Import Processed Successfully/i).should('exist');
        cy.getByCy('upload-success-ok-button').click();
      }
    });
  });
});
