{"tableData": {"data": {"totalCount": 36, "content": [{"id": 1708, "createdBy": "144034", "createdAt": "2025-06-02 21:10:33", "updatedBy": "144034", "updatedAt": "2025-06-02 21:10:33", "incrementId": "1930751554", "junoOrderId": "51471224", "poNumber": "XYZ_123", "customer": {"id": 205, "createdBy": "144034", "createdAt": "2025-05-06 14:30:43", "updatedBy": "144034", "updatedAt": "2025-05-06 14:30:43", "name": "Cust-Banglore", "code": "Cu-1746522041277-2573", "email": "www.Cust-Banglore.com", "pan": "PAN123", "tin": "", "gstin": "29AAQFB1312C1ZU", "mobile": "09481882050", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "B2B", "orderItems": [{"id": 7024, "createdBy": "144034", "createdAt": "2025-06-02 21:10:33", "updatedBy": "144034", "updatedAt": "2025-06-02 21:10:36", "orderId": 1708, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 1000, "price": 583.0}]}, {"id": 1683, "createdBy": "144034", "createdAt": "2025-05-29 21:18:46", "updatedBy": "144034", "updatedAt": "2025-05-29 21:18:46", "incrementId": "1930750943", "junoOrderId": "51470616", "poNumber": "po-123", "customer": {"id": 205, "createdBy": "144034", "createdAt": "2025-05-06 14:30:43", "updatedBy": "144034", "updatedAt": "2025-05-06 14:30:43", "name": "Cust-Banglore", "code": "Cu-1746522041277-2573", "email": "www.Cust-Banglore.com", "pan": "PAN123", "tin": "", "gstin": "29AAQFB1312C1ZU", "mobile": "09481882050", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "B2B", "orderItems": [{"id": 6991, "createdBy": "144034", "createdAt": "2025-05-29 21:18:46", "updatedBy": "144034", "updatedAt": "2025-05-29 21:18:47", "orderId": 1683, "productId": 148248, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 583.66}]}, {"id": 1679, "createdBy": "144034", "createdAt": "2025-05-29 17:50:42", "updatedBy": "144034", "updatedAt": "2025-05-29 17:50:42", "incrementId": "1930750820", "junoOrderId": "51470491", "poNumber": "po-123", "customer": {"id": 108, "createdBy": "144034", "createdAt": "2024-10-23 19:44:19", "updatedBy": "144034", "updatedAt": "2024-10-23 19:48:22", "name": "sanity-customer", "code": "sanity-customer123", "email": "<EMAIL>", "pan": "PAN123", "tin": "123", "gstin": "GST1234", "mobile": "9481882050", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "B2B", "orderItems": [{"id": 6987, "createdBy": "144034", "createdAt": "2025-05-29 17:50:42", "updatedBy": "144034", "updatedAt": "2025-05-29 17:50:42", "orderId": 1679, "productId": 148248, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 583.66}]}, {"id": 1662, "createdBy": "144034", "createdAt": "2025-05-28 20:13:13", "updatedBy": "144034", "updatedAt": "2025-05-28 20:13:13", "incrementId": "1930750380", "junoOrderId": "51469992", "poNumber": "po-123", "customer": {"id": 151, "createdBy": "LSP06935", "createdAt": "2025-01-31 14:58:46", "updatedBy": "LSP06935", "updatedAt": "2025-03-24 15:04:59", "name": "OD Singapore_Plaza Singapura", "code": "OD_905_PS", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "", "mobile": "9173974229", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6968, "createdBy": "144034", "createdAt": "2025-05-28 20:13:13", "updatedBy": "144034", "updatedAt": "2025-05-28 20:13:14", "orderId": 1662, "productId": 148248, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 583.66}]}, {"id": 1661, "createdBy": "144034", "createdAt": "2025-05-28 20:11:49", "updatedBy": "144034", "updatedAt": "2025-05-28 20:11:49", "incrementId": "1930750350", "junoOrderId": "51470022", "poNumber": "po-123", "customer": {"id": 151, "createdBy": "LSP06935", "createdAt": "2025-01-31 14:58:46", "updatedBy": "LSP06935", "updatedAt": "2025-03-24 15:04:59", "name": "OD Singapore_Plaza Singapura", "code": "OD_905_PS", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "", "mobile": "9173974229", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6967, "createdBy": "144034", "createdAt": "2025-05-28 20:11:49", "updatedBy": "144034", "updatedAt": "2025-05-28 20:11:49", "orderId": 1661, "productId": 148248, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 583.66}]}, {"id": 1560, "createdBy": "144034", "createdAt": "2025-05-19 15:38:07", "updatedBy": "144034", "updatedAt": "2025-05-19 15:38:07", "incrementId": "1930746842", "junoOrderId": "51466470", "poNumber": "po-123", "customer": {"id": 1, "createdBy": "144034", "createdAt": "2024-07-09 19:38:37", "updatedBy": "144034", "updatedAt": "2024-10-23 18:01:24", "name": "<PERSON><PERSON>", "code": "TQ", "email": "<EMAIL>", "pan": "", "tin": "", "gstin": "09AAACB1534F1Z4", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": false, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": true, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6794, "createdBy": "144034", "createdAt": "2025-05-19 15:38:07", "updatedBy": "144034", "updatedAt": "2025-05-19 15:38:08", "orderId": 1560, "productId": 90079188, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 583.66}]}, {"id": 1503, "createdBy": "144034", "createdAt": "2025-05-13 21:16:35", "updatedBy": "144034", "updatedAt": "2025-05-13 21:16:35", "incrementId": "1930745510", "junoOrderId": "51465144", "poNumber": "po-8766", "customer": {"id": 203, "createdBy": "144034", "createdAt": "2025-04-27 13:58:20", "updatedBy": "144034", "updatedAt": "2025-04-27 13:58:20", "name": "Singapore Warehouse", "code": "Si-1745742499223-3479", "email": "<EMAIL>", "pan": "", "tin": "", "gstin": "", "mobile": "8130328613", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6685, "createdBy": "144034", "createdAt": "2025-05-13 21:16:35", "updatedBy": "144034", "updatedAt": "2025-05-13 21:17:16", "orderId": 1503, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 800.0}]}, {"id": 1495, "createdBy": "144034", "createdAt": "2025-05-13 17:20:11", "updatedBy": "144034", "updatedAt": "2025-05-13 17:20:11", "incrementId": "1930745429", "junoOrderId": "51465043", "poNumber": "po-3029", "customer": {"id": 202, "createdBy": "144034", "createdAt": "2025-04-27 13:37:58", "updatedBy": "144034", "updatedAt": "2025-04-27 13:37:58", "name": "Dubai Plant", "code": "Du-1745741277491-4645", "email": "<EMAIL>", "pan": "", "tin": "", "gstin": "", "mobile": "*********", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6677, "createdBy": "144034", "createdAt": "2025-05-13 17:20:11", "updatedBy": "144034", "updatedAt": "2025-05-13 17:20:15", "orderId": 1495, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 800.0}]}, {"id": 1471, "createdBy": "144034", "createdAt": "2025-05-08 08:00:02", "updatedBy": "144034", "updatedAt": "2025-05-08 08:00:02", "incrementId": "1930744069", "junoOrderId": "51463681", "poNumber": "po-123", "customer": {"id": 204, "createdBy": "144034", "createdAt": "2025-05-06 11:46:09", "updatedBy": "144034", "updatedAt": "2025-05-06 12:05:43", "name": "Cust-Haryana", "code": "Cu-1746512168590-6821", "email": "<EMAIL>", "pan": "PAN123", "tin": "", "gstin": "06ARLPK6315Q1Z8", "mobile": "9481882050", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6645, "createdBy": "144034", "createdAt": "2025-05-08 08:00:02", "updatedBy": "144034", "updatedAt": "2025-05-08 08:00:02", "orderId": 1471, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 250, "price": 583.66}]}, {"id": 1432, "createdBy": "144034", "createdAt": "2025-04-29 15:26:59", "updatedBy": "144034", "updatedAt": "2025-04-29 15:26:59", "incrementId": "1930742071", "junoOrderId": "51461741", "poNumber": "po-123", "customer": {"id": 83, "createdBy": "1233456", "createdAt": "2024-07-28 16:32:11", "updatedBy": "1233456", "updatedAt": "2025-05-05 19:00:23", "name": "Myntra21-bang", "code": "MYC21-bang", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AACCV7324B1ZO", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6588, "createdBy": "144034", "createdAt": "2025-04-29 15:26:59", "updatedBy": "144034", "updatedAt": "2025-04-29 15:27:00", "orderId": 1432, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 200.0}, {"id": 6589, "createdBy": "144034", "createdAt": "2025-04-29 15:26:59", "updatedBy": "144034", "updatedAt": "2025-04-29 15:27:00", "orderId": 1432, "productId": 131316, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 200.0}]}, {"id": 1411, "createdBy": "144034", "createdAt": "2025-04-28 19:11:27", "updatedBy": "144034", "updatedAt": "2025-04-28 19:11:27", "incrementId": "1930741829", "junoOrderId": "51461472", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6561, "createdBy": "144034", "createdAt": "2025-04-28 19:11:27", "updatedBy": "144034", "updatedAt": "2025-04-28 19:11:28", "orderId": 1411, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 200.0}]}, {"id": 1409, "createdBy": "144034", "createdAt": "2025-04-27 14:07:00", "updatedBy": "144034", "updatedAt": "2025-04-27 14:07:00", "incrementId": "1930741591", "junoOrderId": "51461243", "poNumber": "po-123", "customer": {"id": 203, "createdBy": "144034", "createdAt": "2025-04-27 13:58:20", "updatedBy": "144034", "updatedAt": "2025-04-27 13:58:20", "name": "Singapore Warehouse", "code": "Si-1745742499223-3479", "email": "<EMAIL>", "pan": "", "tin": "", "gstin": "", "mobile": "8130328613", "fax": null, "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": true, "registeredDealer": true, "dualCompanyRetail": true, "providesCForm": true, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6559, "createdBy": "144034", "createdAt": "2025-04-27 14:07:00", "updatedBy": "144034", "updatedAt": "2025-04-27 14:07:01", "orderId": 1409, "productId": 148246, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 2, "price": 200.0}]}, {"id": 1386, "createdBy": "LSP01355", "createdAt": "2025-04-15 12:35:42", "updatedBy": "LSP01355", "updatedAt": "2025-04-15 12:35:42", "incrementId": "1930738801", "junoOrderId": "51458463", "poNumber": "12222", "customer": {"id": 83, "createdBy": "1233456", "createdAt": "2024-07-28 16:32:11", "updatedBy": "1233456", "updatedAt": "2025-05-05 19:00:23", "name": "Myntra21-bang", "code": "MYC21-bang", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AACCV7324B1ZO", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6533, "createdBy": "LSP01355", "createdAt": "2025-04-15 12:35:42", "updatedBy": "LSP01355", "updatedAt": "2025-04-15 12:36:56", "orderId": 1386, "productId": 148248, "productName": "Test Product Name 1", "productType": "eyeglasses", "quantity": 1, "price": 200.0}]}, {"id": 1320, "createdBy": "144034", "createdAt": "2025-03-31 13:11:00", "updatedBy": "LSP06935", "updatedAt": "2025-03-31 13:11:58", "incrementId": "1930733814", "junoOrderId": "51453515", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6394, "createdBy": "144034", "createdAt": "2025-03-31 13:11:00", "updatedBy": "144034", "updatedAt": "2025-03-31 13:12:32", "orderId": 1320, "productId": 131932, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 50, "price": 100.55}]}, {"id": 1319, "createdBy": "144034", "createdAt": "2025-03-31 12:53:29", "updatedBy": "LSP06935", "updatedAt": "2025-03-31 13:02:11", "incrementId": "1930733830", "junoOrderId": "51453532", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6393, "createdBy": "144034", "createdAt": "2025-03-31 12:53:29", "updatedBy": "144034", "updatedAt": "2025-03-31 13:08:26", "orderId": 1319, "productId": 131932, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 1318, "createdBy": "144034", "createdAt": "2025-03-31 12:52:18", "updatedBy": "LSP06935", "updatedAt": "2025-03-31 13:09:59", "incrementId": "1930733829", "junoOrderId": "51453492", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6392, "createdBy": "144034", "createdAt": "2025-03-31 12:52:18", "updatedBy": "144034", "updatedAt": "2025-03-31 13:10:01", "orderId": 1318, "productId": 131932, "productName": "Test Product Name 1", "productType": "Contact lens-solution", "quantity": 50, "price": 100.55}]}, {"id": 1317, "createdBy": "144034", "createdAt": "2025-03-31 12:51:15", "updatedBy": "144034", "updatedAt": "2025-03-31 12:51:15", "incrementId": "1930733812", "junoOrderId": "51453531", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6391, "createdBy": "144034", "createdAt": "2025-03-31 12:51:15", "updatedBy": "144034", "updatedAt": "2025-03-31 12:51:15", "orderId": 1317, "productId": 131932, "productName": "Test Product Name 1", "productType": "Contact lens-solution", "quantity": 2, "price": 100.55}]}, {"id": 1246, "createdBy": "144034", "createdAt": "2025-03-21 00:54:57", "updatedBy": "144034", "updatedAt": "2025-03-21 00:54:57", "incrementId": "1930729889", "junoOrderId": "51449631", "poNumber": "123", "customer": {"id": 83, "createdBy": "1233456", "createdAt": "2024-07-28 16:32:11", "updatedBy": "1233456", "updatedAt": "2025-05-05 19:00:23", "name": "Myntra21-bang", "code": "MYC21-bang", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AACCV7324B1ZO", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6314, "createdBy": "144034", "createdAt": "2025-03-21 00:54:57", "updatedBy": "144034", "updatedAt": "2025-03-21 00:55:01", "orderId": 1246, "productId": 148246, "productName": "test", "productType": "EYEFRAME", "quantity": 2000, "price": 100.0}]}, {"id": 1215, "createdBy": "144034", "createdAt": "2025-03-18 17:05:58", "updatedBy": "144034", "updatedAt": "2025-03-18 17:05:58", "incrementId": "1930728389", "junoOrderId": "51448102", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6279, "createdBy": "144034", "createdAt": "2025-03-18 17:05:58", "updatedBy": "144034", "updatedAt": "2025-03-18 17:05:59", "orderId": 1215, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 1000, "price": 100.55}]}, {"id": 1204, "createdBy": "144034", "createdAt": "2025-03-17 15:06:58", "updatedBy": "144034", "updatedAt": "2025-03-17 15:06:58", "incrementId": "1930727789", "junoOrderId": "51447502", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6265, "createdBy": "144034", "createdAt": "2025-03-17 15:06:58", "updatedBy": "144034", "updatedAt": "2025-03-17 15:06:58", "orderId": 1204, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}, {"id": 6266, "createdBy": "144034", "createdAt": "2025-03-17 15:06:58", "updatedBy": "144034", "updatedAt": "2025-03-17 15:06:58", "orderId": 1204, "productId": 148248, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 1200, "createdBy": "144034", "createdAt": "2025-03-17 14:37:33", "updatedBy": "144034", "updatedAt": "2025-03-17 14:37:33", "incrementId": "1930727770", "junoOrderId": "51447492", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6259, "createdBy": "144034", "createdAt": "2025-03-17 14:37:33", "updatedBy": "144034", "updatedAt": "2025-03-17 14:37:34", "orderId": 1200, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 1194, "createdBy": "144034", "createdAt": "2025-03-13 11:21:21", "updatedBy": "144034", "updatedAt": "2025-03-13 11:21:21", "incrementId": "1930727229", "junoOrderId": "51446942", "poNumber": "Test-123-11", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6252, "createdBy": "144034", "createdAt": "2025-03-13 11:21:21", "updatedBy": "144034", "updatedAt": "2025-03-13 11:21:22", "orderId": 1194, "productId": 148246, "productName": "Test Product Name", "productType": "Eyeglasses", "quantity": 2000, "price": 100.0}]}, {"id": 1189, "createdBy": "144034", "createdAt": "2025-03-12 23:38:05", "updatedBy": "144034", "updatedAt": "2025-03-12 23:38:05", "incrementId": "1930727061", "junoOrderId": "51446831", "poNumber": "qknkjh111", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6247, "createdBy": "144034", "createdAt": "2025-03-12 23:38:05", "updatedBy": "144034", "updatedAt": "2025-03-12 23:38:05", "orderId": 1189, "productId": 148246, "productName": "Test Product Name", "productType": "Eyeglasses", "quantity": 3000, "price": 100.0}]}, {"id": 1104, "createdBy": "LSP07609", "createdAt": "2025-03-05 17:45:02", "updatedBy": "LSP07609", "updatedAt": "2025-03-05 17:45:02", "incrementId": "1930690580", "junoOrderId": "51410312", "poNumber": "po123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6160, "createdBy": "LSP07609", "createdAt": "2025-03-05 17:45:02", "updatedBy": "LSP07609", "updatedAt": "2025-03-05 17:45:12", "orderId": 1104, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 10, "price": 100.55}]}, {"id": 1097, "createdBy": "144034", "createdAt": "2025-03-05 13:25:53", "updatedBy": "144034", "updatedAt": "2025-03-05 13:25:53", "incrementId": "1930690389", "junoOrderId": "51410121", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6153, "createdBy": "144034", "createdAt": "2025-03-05 13:25:53", "updatedBy": "144034", "updatedAt": "2025-03-05 14:09:49", "orderId": 1097, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 1042, "createdBy": "144034", "createdAt": "2025-02-10 18:33:26", "updatedBy": "144034", "updatedAt": "2025-02-10 18:33:27", "incrementId": "1930675188", "junoOrderId": "51394918", "poNumber": "7565", "customer": {"id": 187, "createdBy": "LSP06935", "createdAt": "2025-01-31 14:58:46", "updatedBy": "LSP06935", "updatedAt": "2025-03-24 15:05:05", "name": "OD Singapore_ Le Quest", "code": "OD_1048_LQ", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "", "mobile": "91739742", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6098, "createdBy": "144034", "createdAt": "2025-02-10 18:33:26", "updatedBy": "144034", "updatedAt": "2025-02-10 18:33:26", "orderId": 1042, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 1041, "createdBy": "144034", "createdAt": "2025-02-10 18:31:23", "updatedBy": "144034", "updatedAt": "2025-02-10 18:31:25", "incrementId": "1930675181", "junoOrderId": "51394888", "poNumber": "7565", "customer": {"id": 186, "createdBy": "LSP06935", "createdAt": "2025-01-31 14:58:46", "updatedBy": "LSP06935", "updatedAt": "2025-03-24 15:05:05", "name": "OD Singapore_TOA PAYOH", "code": "OD_1043_TPY", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "", "mobile": "91739742", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6097, "createdBy": "144034", "createdAt": "2025-02-10 18:31:23", "updatedBy": "144034", "updatedAt": "2025-02-10 18:31:24", "orderId": 1041, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 1007, "createdBy": "144034", "createdAt": "2025-01-22 14:39:46", "updatedBy": "144034", "updatedAt": "2025-01-22 14:39:46", "incrementId": "1930667865", "junoOrderId": "51387627", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6058, "createdBy": "144034", "createdAt": "2025-01-22 14:39:46", "updatedBy": "144034", "updatedAt": "2025-01-22 14:39:46", "orderId": 1007, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 975, "createdBy": "144034", "createdAt": "2025-01-21 12:05:59", "updatedBy": "144034", "updatedAt": "2025-01-21 12:06:04", "incrementId": "1930667086", "junoOrderId": "51386843", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 6022, "createdBy": "144034", "createdAt": "2025-01-21 12:05:59", "updatedBy": "144034", "updatedAt": "2025-01-21 12:06:03", "orderId": 975, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}, {"id": 6023, "createdBy": "144034", "createdAt": "2025-01-21 12:05:59", "updatedBy": "144034", "updatedAt": "2025-01-21 12:06:03", "orderId": 975, "productId": 148248, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 926, "createdBy": "144034", "createdAt": "2025-01-09 13:19:54", "updatedBy": "144034", "updatedAt": "2025-01-09 13:19:58", "incrementId": "1930664089", "junoOrderId": "51383843", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 5968, "createdBy": "144034", "createdAt": "2025-01-09 13:19:54", "updatedBy": "144034", "updatedAt": "2025-01-09 13:19:58", "orderId": 926, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 3000.55}]}, {"id": 903, "createdBy": "144034", "createdAt": "2025-01-08 11:53:53", "updatedBy": "144034", "updatedAt": "2025-01-08 11:53:54", "incrementId": "1930663453", "junoOrderId": "51383291", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 5940, "createdBy": "144034", "createdAt": "2025-01-08 11:53:53", "updatedBy": "144034", "updatedAt": "2025-01-08 11:53:53", "orderId": 903, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 902, "createdBy": "144034", "createdAt": "2025-01-08 11:51:33", "updatedBy": "144034", "updatedAt": "2025-01-08 11:51:43", "incrementId": "1930663452", "junoOrderId": "51383276", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 5939, "createdBy": "144034", "createdAt": "2025-01-08 11:51:33", "updatedBy": "144034", "updatedAt": "2025-01-08 11:51:42", "orderId": 902, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 897, "createdBy": "144034", "createdAt": "2025-01-07 18:26:11", "updatedBy": "144034", "updatedAt": "2025-01-07 18:26:43", "incrementId": "1930662977", "junoOrderId": "51382787", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 5934, "createdBy": "144034", "createdAt": "2025-01-07 18:26:11", "updatedBy": "144034", "updatedAt": "2025-01-07 12:56:42", "orderId": 897, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 896, "createdBy": "144034", "createdAt": "2025-01-07 18:25:39", "updatedBy": "144034", "updatedAt": "2025-01-07 18:26:43", "incrementId": "1930662976", "junoOrderId": "51382785", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 5933, "createdBy": "144034", "createdAt": "2025-01-07 18:25:39", "updatedBy": "144034", "updatedAt": "2025-01-07 12:56:42", "orderId": 896, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}, {"id": 882, "createdBy": "144034", "createdAt": "2025-01-07 15:54:38", "updatedBy": "144034", "updatedAt": "2025-01-07 15:54:50", "incrementId": "1930662839", "junoOrderId": "51382631", "poNumber": "po-123", "customer": {"id": 84, "createdBy": "1233456", "createdAt": "2024-07-28 17:33:46", "updatedBy": "1233456", "updatedAt": "2025-05-07 16:45:41", "name": "Myntra21-<PERSON><PERSON>", "code": "MYC21-<PERSON><PERSON>", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AAECM9636P1C2", "mobile": "+91-8299784022", "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": true, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "NXS2", "status": "CREATED", "cancellationReason": null, "doType": "OTHERS", "orderItems": [{"id": 5919, "createdBy": "144034", "createdAt": "2025-01-07 15:54:38", "updatedBy": "144034", "updatedAt": "2025-01-07 10:24:50", "orderId": 882, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 2, "price": 100.55}]}]}, "meta": {"displayMessage": "Successfully Fetched", "code": "1002", "message": "Success"}}, "Shipment_Detail": {"data": [{"wmsOrderCode": "51267107", "wmsShippingPackageId": "SQNXS225000000014134", "facility": "QNXS2", "courierCode": "DELHIVERY_DK", "awbNumber": "5161510016306", "invoiceNumber": "INV2429A00001781", "manifestNumber": "NXS655152", "shippingAddress": {"id": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "addressType": null, "street": null, "region": null, "city": null, "country": null, "postcode": null, "stateCode": null, "countryCode": null, "firstName": null, "lastName": null, "email": null, "telephone": null, "fax": null}, "billingAddress": {"id": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "addressType": null, "street": null, "region": null, "city": null, "country": null, "postcode": null, "stateCode": null, "countryCode": null, "firstName": null, "lastName": null, "email": null, "telephone": null, "fax": null}, "orderItems": [], "shipmentMetaData": [], "totalPrice": 2006.0, "documentLink": "https://invoice-preprod.lenskart.com/INV2429A00001781.pdf"}, {"wmsOrderCode": "51267107-1", "wmsShippingPackageId": "SQNXS225000000004801", "facility": "QNXS2", "courierCode": "DELHIVERY_DK", "awbNumber": "5161510016307", "invoiceNumber": "INV2429A00001710", "shippingAddress": {"id": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "addressType": null, "street": null, "region": null, "city": null, "country": null, "postcode": null, "stateCode": null, "countryCode": null, "firstName": null, "lastName": null, "email": null, "telephone": null, "fax": null}, "billingAddress": {"id": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "addressType": null, "street": null, "region": null, "city": null, "country": null, "postcode": null, "stateCode": null, "countryCode": null, "firstName": null, "lastName": null, "email": null, "telephone": null, "fax": null}, "orderItems": [], "shipmentMetaData": [], "totalPrice": 588.82, "documentLink": "https://invoice-preprod.lenskart.com/INV2429A00001710.pdf"}], "meta": {"displayMessage": "Successfully Created", "code": "1000", "message": "Success"}}, "tableSearchData": {"data": {"totalCount": 1, "content": [{"id": 157, "createdBy": "115428", "createdAt": "2024-08-09 17:58:19", "updatedBy": "115428", "updatedAt": "2024-08-09 17:58:26", "incrementId": "1930608292", "junoOrderId": "51328105", "poNumber": "898989", "customer": {"id": 87, "createdBy": "1233456", "createdAt": "2024-07-31 11:04:57", "updatedBy": "1233456", "updatedAt": "2024-07-31 11:04:57", "name": "Myntra23", "code": "MYC23", "email": "<EMAIL>", "pan": null, "tin": "", "gstin": "06AACCV7324B1ZO", "mobile": null, "fax": "", "websiteUrl": "", "centralSaleTax": 0.0, "serviceTax": 0.0, "customerEnabled": false, "taxExempted": false, "registeredDealer": false, "dualCompanyRetail": false, "providesCForm": false, "customerAddressDetails": null}, "facility": "QNXS2", "status": "CREATED", "cancellationReason": null, "orderItems": [{"id": 4994, "createdBy": "115428", "createdAt": "2024-08-09 17:58:19", "updatedBy": "115428", "updatedAt": "2024-08-09 12:28:25", "orderId": 157, "productId": 148246, "productName": "Test Product Name 1", "productType": "Eyeglasses", "quantity": 20, "price": 100.55}, {"id": 4995, "createdBy": "115428", "createdAt": "2024-08-09 17:58:19", "updatedBy": "115428", "updatedAt": "2024-08-09 12:28:25", "orderId": 157, "productId": 151890, "productName": "Test Product Name 2", "productType": "Eyeglasses", "quantity": 10, "price": 201.0}]}]}, "meta": {"displayMessage": "Successfully Fetched", "code": "1002", "message": "Success"}}, "cancelOrderSuccessData": {"data": 146, "meta": {"displayMessage": "Successfully Created", "code": "1000", "message": "Success"}}}