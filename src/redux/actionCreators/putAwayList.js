import * as action from '../actionTypes/putAwayList';

// putAwayList list
export function putAwayListLoad(data) {
    return { type: action.GET_PUTAWAY_LIST_LOAD, payload: data };
}

export function putAwayListSuccess(data) {
    return { type: action.GET_PUTAWAY_LIST_SUCCESS, payload: data };
}

export function putAwayListFail(data) {
    return { type: action.GET_PUTAWAY_LIST_FAIL, payload: data };
}

export function putAwayListReset(data) {
    return { type: action.GET_PUTAWAY_LIST_RESET, payload: data };
}

// EXPORT PUTAWAYLIST 

export function exportPutawayListLoad(data) {
    return { type: action.EXPORT_PUTAWAYLIST_LOAD, payload: data };
}

export function exportPutawayListSuccess(data) {
    return { type: action.EXPORT_PUTAWAYLIST_SUCCESS, payload: data };
}

export function exportPutawayListFail(data) {
    return { type: action.EXPORT_PUTAWAYLIST_FAIL, payload: data };
}

export function exportPutawayListReset(data) {
    return { type: action.EXPORT_PUTAWAYLIST_RESET, payload: data };
}

// putaway filter suggestions 

export function getPutawayFilterSuggestionLoad(data){
    return {type : action.PUTAWAY_FILTER_SUGGESTIONS_LOAD, payload : data }
}

export function getPutawayFilterSuggestionSuccess(data){
    return {type : action.PUTAWAY_FILTER_SUGGESTIONS_SUCCESS, payload : data}
}

export function getPutawayFilterSuggestionFail(data) {
    return {type : action.PUTAWAY_FILTER_SUGGESTIONS_FAIL, payload : data }
}

export function getPutawayFilterSuggestionReset(data) {
    return {type : action.PUTAWAY_FILTER_SUGGESTIONS_RESET, payload : data }
}

