import { tokenInstance } from '../../utils/axiosInstance';
import config from '../../config';

export const fetchPidDataApi = (pid) => {
  const url = `${config.inventory.PIDdetails}?pid=${pid}`;
  return tokenInstance({ 'x-api-client': 'desktop' }).get(url);
};

export const editCreateTransfertApi = (payload) =>
  tokenInstance().post(config.bulkInventory.createTransfer, payload);
export const downloadCsvFileApi = () =>
  tokenInstance().get(config.bulkInventory.downloadTransferCsv);
export const uploadTransferCsvApi = (payload) =>
  tokenInstance().post(config.bulkInventory.uploadTransferCsv, payload);

// Transfer Details Page
export const getTransferDetailsApi = (payload) => {
  const { transfer_code, barcode_required, page_number, page_size } = payload;
  return tokenInstance()
    .get(config.bulkInventory.getTransferDetails, {
      params: { transfer_code, barcode_required, page_number, page_size }
    })
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getBarcodesForTidApi = (payload) => {
  const { transfer_id, pId } = payload;
  const url = `${config.bulkInventory.getBarcodes}?transfer_item_id=${transfer_id}&pId=${pId}`;
  return tokenInstance()
    .get(url)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const createdTransferListApi = (payload) =>
  tokenInstance()
    .post(config.bulkInventory.getAllTransfer, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

// Scan Barcodes
export const scanBarcodeApi = async (payload) => {
  const {
    barcode = '',
    box_barcode = '',
    unicom_transfer_code = '',
    transfer_code = '',
    enableBoxBarcode = false
  } = payload;
  return tokenInstance()
    .post(config.bulkInventory.scanBarcode, payload, {
      params: { barcode, box_barcode, unicom_transfer_code, transfer_code, enableBoxBarcode }
    })
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const deleteAbarcodeApi = (payload) => {
  const { barcode = '', transfer_code = '' } = payload;
  return tokenInstance()
    .put(config.bulkInventory.deleteAbarcode, payload, { params: { transfer_code, barcode } })
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const changeTransferStatusApi = ({ transfer_code = '', status = '' }) =>
  tokenInstance().put(config.bulkInventory.changeTransferStatus, null, {
    params: { transfer_code, status }
  });

export const downloadTransferSummaryApi = (payload) =>
  tokenInstance()
    .post(config.bulkInventory.downloadTransferSummary, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const downloadTransferDetailsApi = ({ transfer_code, product_id = null }) => {
  const params = {
    transfer_code
  };
  if (product_id) {
    params.barcode_required = true;
    params.product_id = product_id;
  }
  return tokenInstance().get(config.bulkInventory.downloadTransferDetails, { params });
};

export const deleteBoxFromTransferApi = ({ transfer_code, box_barcode }) =>
  tokenInstance().delete(config.bulkInventory.deleteBoxFromTransfer, {
    params: { box_barcode, transfer_code }
  });

// Scan Barcodes
export const validateTransferBoxApi = async ({ barcode = '', box_barcode = '', transfer_code }) =>
  tokenInstance().get(config.bulkInventory.validateTransferBox, {
    params: { barcode, box_barcode, transfer_code }
  });

export const replaceFaultyBarcodeApi = async ({ newBarcode, oldBarcode, transferCode }) =>
  tokenInstance().put(config.bulkInventory.replaceBarcode, {
    newBarcode,
    oldBarcode,
    transferCode
  });

// print RTV invoice template
export const transferPrintInvoiceTemplateApi = ({ transferCode }) =>
  tokenInstance().get(
    `${config.bulkInventory.transferPrintInvoiceTemplate}?transfer_code=${transferCode}`,
    { responseType: 'arraybuffer' }
  );

export const generateAWBnumberApi = ({ shippingProvider, transferCode }) =>
  tokenInstance().get(
    `${config.bulkInventory.generateAWBnumber}/${shippingProvider}/${transferCode}`
  );

export const transferPrintShippingLabelApi = ({ transferCode }) =>
  tokenInstance().get(`${config.bulkInventory.transferPrintShippingLabel}/${transferCode}`);

export const validateBoxApi = (barcode) =>
  tokenInstance().get(config.bulkInventory.validateBox, {
    params: { barcode }
  });
