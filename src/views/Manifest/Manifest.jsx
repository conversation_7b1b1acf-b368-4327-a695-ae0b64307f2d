import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import { Button, Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import FilterChips from 'components/FilterChip/FilterChip';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkInput from 'components/MaterialUi/LkInput';
import BarCodeWrapper from 'views/QualityCheck/BarCodeWrapper';
import {
  manifestListLoad,
  removeManifestLoad,
  resetRemoveManifest
} from '../../redux/actionCreators/manifest';
import useWindowResize from '../../common/useWindowResize';
import LkToolTip from '../../components/ToolTip/ToolTip';
import ShortCut from '../../components/shortCut/shortCut';
import { genericDateFormatted } from '../../utils/helpers';
import CreateManifest from './CreateManifest';
import './Manifest.scss';

const useStyles = makeStyles()((theme) => ({
  root: {
    background: '#fff',
    height: '90vh',
    padding: 20
  },
  table: {
    minWidth: 700,
    paddingBottom: 4
  },
  container: {
    maxHeight: 700
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'end',
    alignItems: 'center',
    marginBottom: '15px'
  },
  input: {
    background: 'white',
    borderRadius: theme.spacing(0.5)
  }
}));

const initialSortBy = { id: 'createdAt', order: 'DESC' };

const HEADER_MAPPING = {
  manifestId: 'manifestId',
  shippingProvider: 'Shipping Provider',
  createdBy: 'Generated By',
  channel: 'Channel',
  status: 'Status',
  shippingMethod: 'Shipping Method',
  items: 'No. of Items',
  created: 'Created'
};

const statusFilterList = ['CLOSED', 'CREATED', 'DISCARDED'];
const channelFilterList = ['STC', 'STS'];
const shippingFilterList = ['Standard-Prepaid', 'Standard-COD', 'Any'];

const PAGE_SIZE = 50;
const defaultObj = {
  payload: {
    page: 0,
    size: PAGE_SIZE,
    sort: 'createdAt,desc'
  }
};
const objPayload = { ...defaultObj };

const KEY_MAP = {
  manifestId: 'manifestId',
  shippingProvider: 'shippingProvider',
  createdBy: 'Generated By',
  channel: 'Channel',
  status: 'status',
  shippingMethod: 'Shipping Method',
  items: 'count',
  created: 'createdAt'
};

const Manifest = () => {
  const navigate = useNavigate();
  const { manifestList, manifestLoading, manifestRemoved } = useSelector((state) => state.manifest);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.MANIFEST);
  const { classes } = useStyles();
  const [createManifestModal, openCreateModal] = useState(false);
  const [tableHeight] = useWindowResize(window.innerHeight - 160);
  const dispatch = useDispatch();
  const [appliedFilter, setappliedFilter] = useState([]);

  const removeManifest = (id) => {
    dispatch(removeManifestLoad(id));
  };
  const headerConfig = [
    {
      name: CONTENT.MANIFEST_ID,
      key: 'manifestId',
      align: 'left',
      supportFilter: true,
      style: { minWidth: '150px', maxWidth: '150px' },
      filterData: {
        type: 'input',
        columnName: 'manifestId'
      },
      formatBody: useCallback(
        ({ manifestId }) => (
          <a
            href={`${window.location.origin}/manifest-detail/${manifestId}`}
            className="text-primary fw-bold "
          >
            {manifestId}
          </a>
        ),
        []
      )
    },
    {
      name: CONTENT.CHANNEL,
      key: 'channel',
      align: 'left',
      supportFilter: true,
      filterData: {
        type: 'singleSelect',
        columnName: 'channel',
        listData: channelFilterList,
        selectedFilterList: appliedFilter
      },
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: CONTENT.SHIPPING_PROVIDER,
      key: 'shippingProvider',
      align: 'left',
      supportSort: true,
      columnName: KEY_MAP.shippingProvider,
      filterData: {
        type: 'input',
        columnName: 'shippingProvider'
      },
      supportFilter: true,
      style: { minWidth: '180px', maxWidth: '180' }
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      align: 'left',
      supportSort: true,
      columnName: KEY_MAP.status,
      filterData: {
        type: 'singleSelect',
        columnName: 'status',
        listData: statusFilterList,
        selectedFilterList: appliedFilter
      },
      supportFilter: true,
      style: { minWidth: '150px', maxWidth: '150px' }
    },
    {
      name: CONTENT.CREATED,
      key: 'createdAt',
      columnName: KEY_MAP.created,
      align: 'left',
      formatBody: useCallback(
        ({ createdAt }) => (
          <div className="fw-bold">{createdAt && genericDateFormatted(createdAt)}</div>
        ),
        []
      ),

      supportSort: true,

      style: { minWidth: '180px', maxWidth: '180px' }
    },
    {
      name: CONTENT.NO_OF_ITEMS,
      key: 'count',
      align: 'right',
      supportSort: true,
      columnName: KEY_MAP.items
    },
    {
      name: CONTENT.SHIPPING_METHOD,
      key: 'shippingMethod',
      align: 'center',
      supportFilter: true,
      filterData: {
        type: 'singleSelect',
        columnName: 'shippingMethod',
        listData: shippingFilterList,
        selectedFilterList: appliedFilter
      },
      style: { minWidth: '200px', maxWidth: '200px' }
    },
    {
      name: CONTENT.GENERATED_BY,
      key: 'createdBy',
      align: 'center',
      supportFilter: false,
      // filterData: {
      //   type: 'input',
      //   columnName: 'createdBy'
      // },
      style: { minWidth: '180px', maxWidth: '180px' }
    },
    {
      name: '',
      key: 'discard',
      align: 'center',
      marginLeft: '0px',
      style: { minWidth: '110px', maxWidth: '110px' },
      formatBody: useCallback(
        ({ status, count, manifestId }) =>
          status === 'CREATED' && count === 0 ? (
            <Box
              onClick={() => removeManifest(manifestId)}
              justifyContent="center"
              color="red"
              display="flex"
              alignItems="center"
            >
              <DeleteOutlineIcon />
              Remove
            </Box>
          ) : (
            <div>-</div>
          ),

        []
      )
    }
  ];

  const fetchMoreListItems = (sortType, sortKey, nextPagePage) => {
    const st = sortType === 'ASC' ? 'asc' : 'desc';
    const obj = {};
    obj[sortKey] = st;
    objPayload.payload.sort = `${sortKey},${st}`;
    objPayload.payload.page = nextPagePage;
    dispatch(manifestListLoad(objPayload));
  };

  useEffect(() => {
    if (manifestRemoved) {
      dispatch(manifestListLoad(objPayload));
      dispatch(resetRemoveManifest());
    }
  }, [dispatch, manifestRemoved]);

  const deleteFilter = (key) => {
    delete objPayload.payload[key];
    setappliedFilter((prevReqBody) => prevReqBody.filter((value) => value.key !== key));
    dispatch(manifestListLoad(objPayload));
  };

  const filterManifest = (data) => {
    if (data) {
      objPayload.payload[data[0].key] = data[0].value;
      objPayload.payload.page = 0;
      setappliedFilter((prevBody) => {
        const filter = {
          key: data[0].key,
          label: HEADER_MAPPING[data[0].key],
          value: data[0].value,
          disableDelete: false
        };
        const updatedFilters = prevBody.filter((val) => val.key !== filter.key);
        return [...updatedFilters, filter];
      });
    } else {
      delete objPayload.payload[data[0].key];
    }
    dispatch(manifestListLoad(objPayload));
  };

  const handleModal = (type) => {
    openCreateModal(type);
  };

  return (
    <div className="manifest-list">
      <div className={classes.root}>
        <div className={classes.mainBar}>
          <div className="chips">
            {appliedFilter.length > 0 ? (
              <FilterChips filters={appliedFilter} deleteData={deleteFilter} />
            ) : null}
          </div>
          <Box width={250} mr={1}>
            <BarCodeWrapper title={CONTENT.BAG_ID} padding={1}>
              <LkInput
                fullWidth
                variant="outlined"
                className={classes.input}
                onKeyPress={(e) =>
                  e.target.value &&
                  e.key === 'Enter' &&
                  navigate(`/manifest-detail?bagId=${e.target.value}&bagSearch=true`)
                }
                placeholder={CONTENT.SCAN_BARCODE}
              />
            </BarCodeWrapper>
          </Box>
          <LkToolTip placement="bottom" title={<ShortCut name="Alt+N" />}>
            <Button
              color="primary"
              style={{ borderRadius: 4 }}
              type="submit"
              variant="contained"
              onClick={() => openCreateModal(true)}
            >
              {CONTENT.CREATE_MANIFEST}
            </Button>
          </LkToolTip>
        </div>
        <LkTable
          tableHeight={tableHeight}
          headerConfig={headerConfig}
          isDataFetching={manifestLoading}
          tableData={manifestList.content}
          totalRowsCount={manifestList?.totalElements}
          dataRequestFunction={fetchMoreListItems}
          initialSortBy={initialSortBy}
          pageLimit={PAGE_SIZE}
          setFilters={filterManifest}
          rowKey="manifestId"
          noDataText={CONTENT.NO_DATA}
        />
      </div>
      {/* Create Maifest Modal */}
      {createManifestModal && <CreateManifest handleModal={handleModal} />}
    </div>
  );
};

export default Manifest;
