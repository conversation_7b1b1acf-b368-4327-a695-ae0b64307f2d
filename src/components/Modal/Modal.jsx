import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import Modal from '@mui/material/Modal';
import CloseIcon from '@mui/icons-material/Close';
import { Button } from '@mui/material';
import Spinner from '../Spinner/Spinner';

function getModalStyle() {
  const top = 50;
  const left = 50;

  return {
    top: `${top}%`,
    left: `${left}%`,
    transform: `translate(-${top}%, -${left}%)`
  };
}

const useStyles = makeStyles()((theme, props) => ({
  paper: {
    position: 'absolute',
    width: 'max-content',
    backgroundColor: '#FFFFFF',
    border: 'none !important',
    outline: 'none',
    borderRadius: '8px',
    boxShadow: '0px 16px 32px rgba(0, 0, 0, 0.1)'
  },
  root: {
    marginLeft: `${props.marginBetweenButtons}px`
  }
}));

const LkModal = (props) => {
  const {
    open,
    handleClose,
    className,
    style,
    disableBackdrop = true,
    modalWidth = '',
    modalHeight = '',
    showClose = true,
    hideBackdrop = false,
    marginBetweenButtons = 10
  } = props;
  const { classes, cx } = useStyles({ marginBetweenButtons });
  // getModalStyle is not a pure function, we roll the style only on the first render
  const [modalStyle] = React.useState(getModalStyle);
  const modalStyling = { ...modalStyle, ...style };
  if (modalWidth) {
    modalStyling.width = modalWidth;
  }
  if (modalHeight) {
    modalStyling.height = modalHeight;
  }
  const body = () => {
    const {
      children = null,
      allowPaddingForChildren = true,
      upperHeading = '',
      title = '',
      subTitle = '',
      primaryBtn = null,
      primaryBtnText = '',
      secondaryBtn = null,
      secondaryBtnText = '',
      primaryBtnLoading = false,
      showActionButton = true,
      primaryBtnVariant = 'contained',
      secondaryBtnVariant = 'outlined',
      disablePrimaryButton = false,
      primaryBtnWidth = '',
      primaryBtnClass = '',
      secondaryBtnClass = '',
      secondaryBtnWidth = '',
      primaryBtnSize = 'medium',
      secondaryBtnSize = 'medium',
      disableSecondaryBtn = false
    } = props;
    return (
      <div
        data-cy="modal-body"
        style={modalStyling}
        className={`${classes.paper}  ${className || ''}`}
      >
        <div
          className="mr-16 display-flex align-items-center 
        justify-content-space-between close-btn"
        >
          <div data-cy="modal-upperHeading" className="fs16 fw-bold text-center flex1">
            {upperHeading}
          </div>
          <div className={showClose ? 'text-right' : 'display-none'}>
            <CloseIcon style={{ cursor: 'pointer' }} data-cy="close-modal" onClick={handleClose} />
          </div>
        </div>
        <div className={allowPaddingForChildren ? 'pd-l40 pd-r40' : ''}>
          {title && (
            <div data-cy="modal-title" className="fs28 fw-bold text-center text-33 modal-title">
              {title}
            </div>
          )}
          {subTitle && (
            <div
              data-cy="modal-subTitle"
              className={cx(
                'fs14',
                'text-center',
                'mr-t15 text-66 mr-b15',
                'sub-title',
                'display-flex align-items-center flex-direction-column'
              )}
            >
              {subTitle}
            </div>
          )}
          {children}
          {showActionButton && (
            <div className="text-center mr-b40 mr-t40">
              {secondaryBtn && (
                <Button
                  style={{ borderRadius: '8px', width: secondaryBtnWidth }}
                  variant={secondaryBtnVariant}
                  color="primary"
                  disabled={disableSecondaryBtn}
                  className={secondaryBtnClass}
                  onClick={secondaryBtn}
                  size={secondaryBtnSize}
                  data-cy="secondary-btn"
                >
                  {secondaryBtnText}
                </Button>
              )}
              {primaryBtn && !primaryBtnLoading && (
                <Button
                  disabled={disablePrimaryButton}
                  style={{ width: primaryBtnWidth, borderRadius: '8px' }}
                  className={`${classes.root} ${primaryBtnClass}`}
                  variant={primaryBtnVariant}
                  color="primary"
                  onClick={primaryBtn}
                  size={primaryBtnSize}
                  data-cy="primary-btn"
                >
                  {primaryBtnText}
                </Button>
              )}
              {primaryBtn && primaryBtnLoading && (
                <Button
                  style={{ borderRadius: '8px' }}
                  disabled
                  className={`${classes.root} ${primaryBtnClass}`}
                  variant="contained"
                  color="primary"
                  size={primaryBtnSize}
                  data-cy="spinner"
                >
                  <Spinner />
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <Modal
        open={open}
        data-cy="modal"
        // onClose={handleClose}
        onClose={(event, reason) => {
          if (reason === 'backdropClick' && !disableBackdrop) {
            handleClose(event);
          }
        }}
        aria-labelledby="simple-modal-title"
        aria-describedby="simple-modal-description"
        hideBackdrop={!!hideBackdrop}
      >
        {body()}
      </Modal>
    </div>
  );
};

export default LkModal;

LkModal.propTypes = {
  disableBackdrop: PropTypes.bool,
  modalWidth: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  modalHeight: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  showClose: PropTypes.bool,
  hideBackdrop: PropTypes.bool,
  marginBetweenButtons: PropTypes.number,
  children: PropTypes.node,
  allowPaddingForChildren: PropTypes.bool,
  upperHeading: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  title: PropTypes.string,
  subTitle: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  primaryBtn: PropTypes.func,
  primaryBtnText: PropTypes.string,
  secondaryBtn: PropTypes.func,
  secondaryBtnText: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  primaryBtnLoading: PropTypes.bool,
  showActionButton: PropTypes.bool,
  primaryBtnVariant: PropTypes.string,
  secondaryBtnVariant: PropTypes.string,
  disablePrimaryButton: PropTypes.bool,
  primaryBtnWidth: PropTypes.string,
  primaryBtnClass: PropTypes.string,
  secondaryBtnClass: PropTypes.string,
  secondaryBtnWidth: PropTypes.string,
  primaryBtnSize: PropTypes.string,
  secondaryBtnSize: PropTypes.string,
  disableSecondaryBtn: PropTypes.bool
};
