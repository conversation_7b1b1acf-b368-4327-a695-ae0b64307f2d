import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import PrintIcon from '@mui/icons-material/Print';
import SystemUpdateAltIcon from '@mui/icons-material/SystemUpdateAlt';
import { downloadTransferSummaryLoad } from 'redux/actionCreators/bulkInventory/bulkInventory';
import LkInput from 'components/MaterialUi/LkInput';
import { fileDownload, convertDateFormat, generatePayloadForSearchAPI } from 'utils/helpers';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import { LOCALISATION } from 'redux/reducers/localisation';
import TextField from '@mui/material/TextField';
import DateSelectFilter from 'components/common/DateSelectFilter';
import SelectDestination from '../CreateTransfer/SelectDestination';

const useStyles = makeStyles()(() => ({
  container: {
    padding: 16,
    background: '#ffffff',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 70
  },
  searchIcon: {
    fontSize: '1.1rem'
  },
  primaryContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  searchContainer: {
    width: 300,
    marginLeft: 26
  },
  secondaryContainer: {
    display: 'flex',
    alignItems: 'center'
  },
  actionsContainer: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 15,
    borderLeft: '1px solid rgba(0, 0, 0, 0.12)'
  },
  actionIcon: {
    width: 38,
    height: 38,
    margin: '0px 5px'
  },
  transferCreated: {
    fontSize: 18,
    fontWeight: 500,
    marginRight: 10,
    minWidth: 150
  },
  filterChipContainer: {
    display: 'flex',
    width: 'calc(100vw - 50vw)',
    margin: '0px 10px',
    overflow: 'auto'
  },
  btn: {
    borderRadius: 8,
    padding: '5px 12px',
    marginRight: 10
  }
}));

const CreatedTransfersListHeader = ({
  selectedDestination,
  changeDestination,
  setRequestBody,
  requestBody,
  appliedFilters,
  deleteFilters,
  DATE_RANGE_OPTIONS,
  dateRange,
  setRateRange
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [inputVal, setInputVal] = useState('');
  const [shouldDownload, setShouldDownload] = useState(false);
  const { data } = useSelector((state) => state.bulkInventory.downloadTranserSummary);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.TRANSFER);
  const { totalRecordsCount } = useSelector((state) => state.bulkInventory);
  useEffect(() => {
    if (data && shouldDownload) {
      fileDownload(data, `download-Transfer-Summary-${convertDateFormat(new Date(), 'export')}`);
      setShouldDownload(false);
    }
  }, [dispatch, data]);

  const downloadTransfer = () => {
    const dateFilterPayload = generatePayloadForSearchAPI(dateRange, {}, 'CREATED');
    dispatch(
      downloadTransferSummaryLoad({
        ...requestBody,
        searchFields: {
          ...requestBody.searchFields,
          created_at: [dateFilterPayload.CREATEDfrom, dateFilterPayload.CREATEDto]
        },
        pageSize: 1000,
        page: 0
      })
    );
    setShouldDownload(true);
  };

  const searchHandler = (e) => {
    if (e.key === 'Enter' && inputVal) {
      setRequestBody((prevReq) => ({
        ...prevReq,
        page: 0,
        isReset: true,
        searchFields: { transfer_code: inputVal.split(',') }
      }));
      setInputVal('');
    }
  };

  const onChangeFilter = (selectedData) => {
    setRateRange(selectedData);
  };

  return (
    <Box className={classes.container}>
      <Box className={classes.primaryContainer}>
        <span className={classes.transferCreated}>{CONTENT.TRANSFERS_DETAILS}</span>
        <Box width="240px">
          <LkInput
            label={CONTENT.SEARCH_TRANSFERS_HERE}
            fullWidth
            variant="outlined"
            value={inputVal}
            onChange={(e) => setInputVal(e.target.value)}
            onKeyPress={searchHandler}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <SearchIcon color="disabled" />
                </InputAdornment>
              )
            }}
          />
        </Box>
      </Box>
      <Box className={classes.filterChipContainer}>
        <FilterChipsV2 filters={appliedFilters} deleteData={deleteFilters} />
      </Box>
      {selectedDestination && (
        <Button
          className={classes.btn}
          size="small"
          variant="outlined"
          color="primary"
          component="span"
          onClick={() => changeDestination('')}
        >
          {CONTENT.RESET}
        </Button>
      )}
      <Box className={classes.secondaryContainer}>
        <SelectDestination
          selectedDestination={selectedDestination}
          setSelectedDestination={changeDestination}
          label={CONTENT.ALL_DESTINATIONS}
        />
        <Box className={classes.actionsContainer}>
          <TextField
            type="text"
            size="medium"
            sx={{ width: '200px', ml: 2 }}
            value={dateRange[0].value}
            variant="outlined"
            InputProps={{
              readOnly: true,
              endAdornment: (
                <DateSelectFilter
                  onSubmit={onChangeFilter}
                  columnName="CREATED"
                  monitorPhasefilter
                  dateOptions={DATE_RANGE_OPTIONS}
                  selectedFilterList={dateRange}
                  showTimePicker
                />
              )
            }}
          />
          <IconButton
            className={classes.actionIcon}
            onClick={() => downloadTransfer()}
            disabled={!totalRecordsCount}
            size="large"
            color="primary"
          >
            <SystemUpdateAltIcon />
          </IconButton>
          <IconButton disabled className={classes.actionIcon} size="large">
            <PrintIcon />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};

export default React.memo(CreatedTransfersListHeader);
