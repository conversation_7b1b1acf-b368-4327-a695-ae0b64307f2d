import { publicInstance } from '../utils/axiosInstance';
import config from '../config';

const CONSUL_ALL_ENV_FOLDERS = {
  [import.meta.env.VITE_PROD_URL]: import.meta.env.VITE_CONSUL_PROD_FOLDER,
  [import.meta.env.VITE_PREPROD_URL]: import.meta.env.VITE_CONSUL_PRE_PROD_FOLDER,
  [import.meta.env.VITE_QE1_URL]: import.meta.env.VITE_CONSUL_QE1_FOLDER
};

export const CURRENT_CONSUL_FOLDER =
  CONSUL_ALL_ENV_FOLDERS[window.location.host] || import.meta.env.VITE_CONSUL_PRE_PROD_FOLDER;

export const getConsulConfigApi = (keys = []) => {
  const consulKey = keys.map((eachKey) => `${CURRENT_CONSUL_FOLDER}${eachKey}`).join('$');
  return publicInstance().get(config.apiPath.consul, {
    params: { valueType: 'String', key: consulKey }
  });
};
