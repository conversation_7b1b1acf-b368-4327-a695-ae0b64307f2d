import React from 'react';
import Box from '@mui/material/Box';
import LkModal from 'components/Modal/Modal';
import Spinner from 'components/Spinner/Spinner';
import InputAdornment from '@mui/material/InputAdornment';
import LkInput from 'components/MaterialUi/LkInput';

const OwnDaysCardModal = ({
  isOwnDaysCardModalOpen,
  setIsOwnDaysCardModalOpen,
  classes,
  validBarcodeInput,
  setValidBarcodeInput,
  isValidBarcodeLoading,
  checkBarcodeIsValid,
  OWN_DAYS_CARD_BARCODE_SCANNER
}) => (
  <LkModal
    className={classes.owndaysCardModal}
    modalHeight="150px"
    modalWidth="410px"
    open={isOwnDaysCardModalOpen}
    upperHeading="Own Days Card Lens Detected!"
    handleClose={() => setIsOwnDaysCardModalOpen(false)}
  >
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: '15px'
      }}
    >
      <LkInput
        className={classes.scanAgainInput}
        fullWidth
        size="small"
        variant="outlined"
        placeholder="Scan Unique QR Code"
        onChange={(e) => setValidBarcodeInput(e.target.value)}
        value={validBarcodeInput}
        autoFocus
        disabled={isValidBarcodeLoading}
        id={OWN_DAYS_CARD_BARCODE_SCANNER}
        onKeyUp={(e) => e.key === 'Enter' && checkBarcodeIsValid(e.target.value)}
        InputProps={{
          endAdornment: isValidBarcodeLoading ? (
            <Spinner />
          ) : (
            <InputAdornment position="end">
              <img
                style={{ width: '25px' }}
                alt="barcode-img"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/barcodeScan.svg`}
              />
            </InputAdornment>
          )
        }}
      />
    </Box>
  </LkModal>
);

export default OwnDaysCardModal;
