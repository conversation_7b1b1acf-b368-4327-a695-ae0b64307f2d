import * as action from '../actionTypes/grnDetails'

//  get GRN PIDS
export function getGrnPidsLoad(data) {
    return {
        type: action.GET_GRN_PIDS_LOAD,
        payload: data
    };
}

export function getGrnPidsSuccess(data) {
    return {
        type: action.GET_GRN_PIDS_SUCCESS,
        payload: data
    };
}

export function getGrnPidsFail(data) {
    return {
        type: action.GET_GRN_PIDS_FAIL,
        payload: data
    };
}


//  get GRN BLOCKED PIDS
export function getBlockedsPidsLoad(data) {
    return {
        type: action.GET_BLOCKED_PIDS_LOAD,
        payload: data
    };
}

export function getBlockedsPidsSuccess(data) {
    return {
        type: action.GET_BLOCKED_PIDS_SUCCESS,
        payload: data
    };
}

export function getBlockedsPidsFail(data) {
    return {
        type: action.GET_BLOCKED_PIDS_FAIL,
        payload: data
    };
}




//  EXPORT GRN BARCODE DETAILS
export function exportGrnBarCodeDetailsLoad(data) {
    return {
        type: action.EXPORT_GRN_BARCODE_DETAILS_LOAD,
        payload: data
    };
}

export function exportGrnBarCodeDetailsReset(data) {
    return {
        type: action.EXPORT_GRN_BARCODE_DETAILS_RESET,
        payload: data
    };
}

export function exportGrnBarCodeDetailsSuccess(data) {
    return {
        type: action.EXPORT_GRN_BARCODE_DETAILS_SUCCESS,
        payload: data
    };
}

export function exportGrnBarCodeDetailsFail(data) {
    return {
        type: action.EXPORT_GRN_BARCODE_DETAILS_FAIL,
        payload: data
    };
}


//RESET

export function resetGrnDetailsReducer(data) {
    return {
        type: action.GRN_DETAILS_RESET,
        payload: data
    };
}



//  GRN PDF DOWNLOAD
export function getgrnPdfDownloadLoad(data) {
    return {
        type: action.GRN_PDF_DOWNLOAD_LOAD,
        payload: data
    };
}

export function getgrnPdfDownloadSuccess(data) {
    return {
        type: action.GRN_PDF_DOWNLOAD_SUCCESS,
        payload: data
    };
}

export function getgrnPdfDownloadFail(data) {
    return {
        type: action.GRN_PDF_DOWNLOAD_FAIL,
        payload: data
    };
}

export function getgrnPdfDownloadReset(data) {
    return {
        type: action.GRN_PDF_DOWNLOAD_RESET,
        payload: data
    };
}


