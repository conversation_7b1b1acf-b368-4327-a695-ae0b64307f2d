import { genericDateFormatted } from '../../src/utils/helpers';
import config from '../../src/config';

const storeConsolidationlistingUrl = config.storeConsolidation.list;
const storeConsolidationHistoryUrl = config.storeConsolidation.historyList;
const releaseTrayUrl = config.storeConsolidation.releaseTray;

const valueOrDefault = (val) => val ?? '-';

const checkTableCell = (index, value) => {
  cy.getByCy(`table-cell-0-${index}`).should('contain.text', value);
};

describe('Store Consolidation', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
    cy.getByCy('main-navigation').click();
    cy.getByCy('Automation').click();
    cy.intercept('POST', storeConsolidationlistingUrl).as('storeConsolidationListing');
    cy.intercept('POST', storeConsolidationHistoryUrl).as('storeConsolidationHistory');
    cy.getByCy('Tray Release').click();
  });
  after(() => {
    // cy.logout();
  });

  it('Visits the Store Consolidation Page from app bar and LISTING table is populated', () => {
    cy.fixture('trayRelease/trayReleaseListingHeader').as('listHeaders');

    // check if the url is correct
    cy.url().should('include', '/trayRelease');

    // check if we are on correct page
    cy.findByText(/Store Consolidation/i).should('exist');

    // LISTING Tab is active
    cy.getByCy('LISTING-tab')
      .should('have.text', 'LISTING')
      .should('have.css', 'color')
      .and('equal', 'rgb(0, 172, 193)');

    cy.wait('@storeConsolidationListing').then(({ response }) => {
      const { createdAt, createdBy, noOfShipments, storeCode, trayId, updatedAt, updatedBy } =
        response.body.data.data[0];

      const firstRowData = [
        '',
        trayId,
        storeCode,
        noOfShipments,
        createdAt,
        createdBy,
        updatedAt,
        updatedBy
      ];

      cy.get('@listHeaders').then((headers) => {
        headers.forEach((header, index) => {
          cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
          if (index === 0) {
            /* empty */
          } else if (index === 4 || index === 6) {
            checkTableCell(index, valueOrDefault(genericDateFormatted(firstRowData[index])));
          } else {
            checkTableCell(index, valueOrDefault(firstRowData[index]));
          }
        });
      });
    });
  });

  it('Visits the Store Consolidation Page from app bar and HISTORY table is populated', () => {
    cy.fixture('trayRelease/trayReleaseHistoryHeader').as('historyHeaders');

    // check if we are on correct page
    cy.findByText(/Store Consolidation/i).should('exist');

    // go to history tab
    cy.getByCy('HISTORY-tab').click();

    // release tray button should not be visible
    cy.getByCy('release-trays').should('not.exist');

    // check if the url is correct
    cy.url().should('include', '/trayRelease/history');

    // HISTORY Tab is active
    cy.getByCy('HISTORY-tab')
      .should('have.text', 'HISTORY')
      .should('have.css', 'color')
      .and('equal', 'rgb(0, 172, 193)');

    cy.wait('@storeConsolidationHistory').then(({ response }) => {
      const {
        createdAt,
        createdBy,

        storeCode,
        trayId,
        updatedAt,
        updatedBy,
        status
      } = response.body.data.data[0];

      const firstRowData = [trayId, storeCode, createdAt, createdBy, updatedAt, updatedBy, status];

      cy.get('@historyHeaders').then((headers) => {
        headers.forEach((header, index) => {
          cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
          if (index === 2 || index === 4) {
            checkTableCell(index, valueOrDefault(genericDateFormatted(firstRowData[index])));
          } else {
            checkTableCell(index, valueOrDefault(firstRowData[index]));
          }
        });
      });
    });
  });

  it('checks if release tray is disabled and enable when we click checkbox', () => {
    cy.wait('@storeConsolidationListing');
    // Release tray button should be disabled
    cy.getByCy('release-trays').should('be.disabled');

    // click on first checkbox
    cy.getByCy('checkbox-0').click();
    // checkbox should be checked
    cy.get('[data-cy="checkbox-0"] > input').should('be.checked');
    // release button should be enabled
    cy.getByCy('release-trays').should('not.be.disabled');

    // clicking on header checkbox should not check or unchecked the checkbox
    cy.getByCy('checkbox-header').click();
    // checkbox should be checked
    cy.get('[data-cy="checkbox-0"] > input').should('be.checked');
    // if one checkbox is selected it should show RELEASE TRAY
    cy.getByCy('release-trays').should('have.text', 'Release Tray');
    cy.getByCy('checkbox-1').click();
    // if more than one checkbox is selected it should show RELEASE TRAYS
    cy.getByCy('release-trays').should('have.text', 'Release Trays');
  });

  it('should only allow 10 tray to release at once', () => {
    cy.wait('@storeConsolidationListing');
    // Release tray button should be disabled
    cy.getByCy('release-trays').should('be.disabled');

    for (let i = 0; i < 10; i++) {
      cy.getByCy(`checkbox-${i}`).click();
    }
    cy.getByCy('checkbox-10').click();
    cy.getByCy('toaster-message').should(
      'contain.text',
      'You cannot release more than 10 trays at a time.'
    );
    cy.get('[data-cy="checkbox-10"] > input').should('not.be.checked');
  });

  it('should release the tray when we click on the release tray', () => {
    cy.wait('@storeConsolidationListing');
    cy.getByCy('checkbox-0').click();
    cy.getByCy('release-trays').click();
    // check if modal opens
    cy.getByCy('modal-body').should('exist');
    // check if modal title is correct
    cy.getByCy('modal-title').should('have.text', 'Selected Tray ID');
    // check if modal subtitle is correct
    cy.getByCy('modal-subTitle').should('have.text', 'Remove If you don\'t want to release tray');

    cy.getByCy('primary-btn').should('have.text', 'CONFIRM');
    cy.getByCy('secondary-btn').should('have.text', 'CANCEL');

    cy.intercept('PUT', releaseTrayUrl).as('releaseTray');

    cy.getByCy('primary-btn').click();
    cy.wait('@releaseTray');
  });
});
