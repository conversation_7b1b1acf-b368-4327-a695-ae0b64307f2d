import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getRTVListAPI = (payload) => tokenInstance().post(config.RTVlist.list, payload);

export const generateGatepassSequence = () =>
  tokenInstance().get(config.RTVlist.generateGatepassSequence);

export const createGatepassSequence = (payload) =>
  tokenInstance().post(config.RTVlist.createGatepass, payload);

export const submitForApproveAPI = (payload) =>
  tokenInstance().post(config.RTVlist.submitForApprove, null, { params: payload });

export const approveRejectAPI = (payload) =>
  tokenInstance().post(config.RTVlist.approveReject, payload);

export const exportGatepassAPI = (payload) => tokenInstance().post(config.RTVlist.export, payload);
