import { withStyles } from 'tss-react/mui';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';

export const StyledTableRow = withStyles(TableRow, (theme, { plaintheme, isheader, border }) => ({
  root: {
    backgroundColor: '#ffffff',
    '&:nth-of-type(even)': {
      backgroundColor: plaintheme === 'false' ? '#f5f5f5' : ''
    },
    '&:nth-of-type(odd)': {
      backgroundColor: isheader === 'true' ? 'inherit' : '#FFFFFF'
    },
    '&:hover': {
      cursor: 'pointer'
    },
    color: '#333333',
    borderBottom: border === 'false' ? 'none' : '1px solid #dddddd'
  }
}));

export const StyledTableCell = withStyles(TableCell, () => ({
  root: {
    border: 'none'
  },
  head: {
    backgroundColor: '#f5f5f5',
    color: '#333333',
    fontSize: 12,
    padding: '10px'
  },
  body: {
    fontSize: 12,
    color: '#333333',
    padding: '5px 10px 5px 10px'
  }
}));

export const StickyTableCell = withStyles(TableCell, (theme) => ({
  root: {
    padding: 0,
    background: 'inherit'
  },
  head: {
    position: 'sticky',
    zIndex: theme.zIndex.drawer - 1,
    padding: '10px',
    backgroundColor: '#f5f5f5'
  },
  body: {
    position: 'sticky',
    zIndex: theme.zIndex.drawer - 2,
    fontSize: 12,
    padding: '5px 10px 5px 10px',
    boxShadow: '0px 1px 8px rgba(0, 0, 0, 0.12)'
  }
}));
