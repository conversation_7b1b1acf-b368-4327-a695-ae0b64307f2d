export const mapSearchFilterKey = {
  APPROVED: 'approved_at_', // approved_at_from
  'Approved By': 'approved_by',
  CREATED: 'created_at_', // created_at_from
  'Created By': 'created_by',
  Facility: 'facility_info',
  poNum: 'po_num',
  PO: 'po_num',
  POType: 'po_type',
  'PO Status': 'status',
  Status: 'status',
  Vendor: 'vendor_name',
  Approved_By: 'approved_by',
  Created_By: 'created_by',
  GRN: 'grn_numbers',
  'Invoice Date': 'invoice_date_', // invoice_date_from
  Vendor_Invoice_Number: 'vendor_invoice_number',
  Invoice_Ref_Number: 'invoice_ref_number',
  DebitNote: 'debit_note_no',
  CLOSED: 'closed_at_', // closed_at_from
  NexS_Invoice_Number: 'invoice_number',
  INVOICE_DATE: 'invoice_date_', // invoice_date_from,
  COMPLETED: 'completed_at_', // completed_at_from,
  ASN: 'prefilled_invoice',
  Package: 'package_name',
  BRAND: 'brand',
  Product_Id: 'product_id',
  Vendor_Code: 'vendor_code',
  Last_Updated: 'updated_at_', // updated_at_from
  Enabled: 'enabled',
  Currency: 'currency',
  SPH: 'sph',
  CYL: 'cyl',
  AXIS: 'axis',
  AP: 'ap',
  BC: 'bc',
  GRN_GRN_Number: 'grn_code',
  invoiceRef: 'invoice_reference_number',
  GRN_invoiceRef: 'invoice_reference_number',
  GRN_PO: 'po_num',
  GRN_Vendor: 'vendor_code',
  GRN_Assigned_To: 'assigned_to',
  GRN_Created_By: 'created_by',
  GRN_Status: 'grn_status',
  GRN_CREATED: 'created_at_',
  BLOCKED_GRN_Number: 'grn_code',
  BLOCKED_Created_By: 'product_created_by',
  GRN_Vendor_Invoice_Number: 'vendor_invoice_number',
  // invoice
  Invoice_Invoice_Ref_Number: 'invoice_ref_number',
  Invoice_PO: 'po_num',
  Invoice_Created_By: 'created_by',
  Invoice_Vendor_Invoice_Number: 'vendor_invoice_number',
  Invoice_GRN: 'grn_numbers',
  Invoice_Status: 'status',
  Invoice_ASN: 'prefilled_invoice',
  Invoice_COMPLETED: 'completed_at_', // date
  'Invoice_Invoice Date': 'invoice_date_', // date
  Invoice_CREATED: 'created_at_', // date
  // debitNote
  DebitNote_DebitNote: 'debit_note_no',
  DebitNote_Vendor_Invoice_Number: 'vendor_invoice_number',
  DebitNote_NexS_Invoice_Number: 'invoice_number',
  DebitNote_PO: 'po_num',
  DebitNote_Vendor: 'vendor_name',
  DebitNote_Created_By: 'created_by',
  DebitNote_INVOICE_DATE: 'invoice_date_', // invoice_date_from,
  DebitNote_CREATED: 'created_at_',
  // facility
  Active_FacilityCode: 'facility_code',
  Active_FacilityName: 'display_name',
  Active_FacilityType: 'facility_type',
  Active_FacilityStatus: 'facility_status',
  Active_PartyName: 'legal_name',
  Active_BillingAddress: 'city',
  Active_DraftName: 'draft_name',
  // facilityList Draft
  Draft_FacilityCode: 'facility_code',
  Draft_FacilityName: 'display_name',
  Draft_FacilityType: 'facility_type',
  Draft_FacilityStatus: 'facility_status',
  Draft_PartyName: 'legal_name',
  Draft_BillingAddress: 'city',
  Draft_DraftName: 'draft_name',

  RULE_STATUS: 'ruleStatus',
  CREATED_CMS: 'created_at_', // created_at_from",
  UPDATED_CMS: 'updated_at_', // created_at_from",
  FACILITY: 'facility',
  // putaway
  PutawayNo: 'putaway',
  Type: 'type',
  Source: 'source',
  CreatedBy: 'createdby',
  ItemBarcode: 'barcode',
  // barcode Cycle
  Operation: 'operation',
  Condition: 'condition',
  Availability: 'availability',
  ActionId: 'actionId',
  Location: 'location',
  UpdatedBy: 'updatedBy',
  Brand: 'brand',
  Classification: 'classification',
  'Shipping Package': 'shippingPackageId',
  'Order Date': 'orderDate',
  name: 'name',
  'Cycle Id': 'stockTakeId',
  'Facility Code': 'facilityCode'
};
