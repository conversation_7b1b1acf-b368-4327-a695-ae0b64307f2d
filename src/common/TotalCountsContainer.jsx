import React, { Fragment } from 'react';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()((theme, noMarginLeft) => ({
  statusContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    minWidth: noMarginLeft ? 0 : 600,
    padding: 10,
    marginLeft: noMarginLeft ? 0 : 60,
    background: '#E0F7F7',
    border: '1px solid #00B9C6',
    borderRadius: 4
  },
  statusIndication: {
    height: 25,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: 16,
    lineHeight: '157%',
    color: '#666666',
    flex: 'none',
    order: 0,
    flexGrow: 0,
    margin: '0 10px'
  }
}));

const TotalCountsContainer = ({ data, TOTAL_COUNT_KEYS, noMarginLeft = false }) => {
  const { classes } = useStyles(true, {
    props: noMarginLeft
  });
  return (
    <Box className={classes.statusContainer}>
      {TOTAL_COUNT_KEYS.map(({ key, label }, index) => (
        <Fragment key={key}>
          <Box className={classes.statusIndication}>
            {label}:
            <Typography variant="h5" display="inline" className="pd-l5">
              {data?.[key] || 0}
            </Typography>
          </Box>
          {index + 1 !== TOTAL_COUNT_KEYS.length && <div className={classes.verticalLineDivider} />}
        </Fragment>
      ))}
    </Box>
  );
};

export default TotalCountsContainer;
