import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { LOCALISATION } from 'redux/reducers/localisation';
import { getOrderDetailsReset } from 'redux/actionCreators/order';
import useOrderHook from 'common/useOrderHook';
import Topbar from 'layouts/Main/components/Topbar/Topbar';
import TopbarRightElement from 'layouts/Main/components/Topbar/TopbarRightElement';

import PackingHoldModal from './PackingHoldModal';
import PackingUnHoldModal from './PackingUnHoldModal';
import PackingPanelBarcode from './PackingPanelBarcode';
import PackingPanelProcess from './PackingPanelProcess';
import { Style_PackingPanel } from './Style';

const useStyles = makeStyles()(() => ({ ...Style_PackingPanel }));

const PackingPanel = () => {
  const location = useLocation();
  useOrderHook();
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const qcProcess = location?.state?.qcProcess;
  const inputBarcode = location?.state?.inputBarcode;

  const packingLocal = useSelector((state) => state[LOCALISATION].localeData.PACKING);
  const { isHold } = useSelector((state) => state.packing);
  const { packerData } = useSelector((state) => state.packing);
  const { orderDetails, orderBarcodeInfo } = useSelector((state) => state.order);

  const isMLSP = !!(orderBarcodeInfo && `${orderBarcodeInfo.printShipment}` === '1');
  let isOrderOpening = false;

  /** Checking condition that "Is Order is opening or not?" */
  if (orderDetails) {
    if (!orderDetails.length || orderDetails.error) {
      // Error
    } else if (packerData && !packerData.packingMode) {
      // Error
    } else if (
      orderBarcodeInfo &&
      `${orderBarcodeInfo.printShipment}` === '1' &&
      packerData &&
      packerData.packingMode &&
      packerData.packingMode.toLowerCase().split('_')[1] === 'auto'
    ) {
      // Error
    } else if (orderBarcodeInfo && Object.keys(orderBarcodeInfo).length && packerData) {
      isOrderOpening = true;
    }
  }

  useEffect(
    () => () => {
      dispatch(getOrderDetailsReset());
    },
    [dispatch]
  );

  return (
    <div>
      <Topbar>
        <div className={`${classes.header} ${isOrderOpening && isMLSP ? classes.colorRed : ''}`}>
          {isOrderOpening && isMLSP ? packingLocal.P_PANEL_MSLP : packingLocal.P_PANEL}
        </div>
        <TopbarRightElement />
      </Topbar>
      <div className={classes.root} style={{ height: qcProcess ? 'auto' : 'calc(100vh - 64px)' }}>
        <Box
          width={qcProcess ? '100%' : 'auto'}
          boxShadow="0 8px 16px rgba(0,0,0,0.05)"
          borderRadius="12px"
        >
          <Card
            sx={{
              pt: 6,
              pb: 6
            }}
            style={{
              paddingTop: qcProcess ? '0px' : '',
              boxShadow: 'rgb(0 0 0 / 30%) 0px 8px 16px'
            }}
          >
            {qcProcess ? (
              <PackingPanelProcess inputBarcode={inputBarcode} />
            ) : (
              <PackingPanelBarcode />
            )}
          </Card>
        </Box>
      </div>
      <PackingHoldModal />
      {isHold && <PackingUnHoldModal />}
    </div>
  );
};

export default PackingPanel;
