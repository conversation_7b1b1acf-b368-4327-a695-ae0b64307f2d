import Button from '@mui/material/Button';
import React from 'react';
import PropTypes from 'prop-types';
import Spinner from 'components/Spinner/Spinner';

/**
 *
 * @typedef {import('@mui/material').ButtonProps} ButtonProps
 */

/**
 *
 * @typedef {Object} LKButtonProps
 * @property {boolean} [isLoading]
 * @property {React.ReactNode} children The button content
 */

/**
 * @param {LKButtonProps & ButtonProps} rest
 */
const LKButton = ({ children, isLoading = false, ...rest }) => {
  if (isLoading) {
    return (
      <Button {...rest} disabled>
        <Spinner />
      </Button>
    );
  }
  return <Button {...rest}>{children}</Button>;
};

export default LKButton;

LKButton.propTypes = {
  isLoading: PropTypes.bool,
  children: PropTypes.node.isRequired
};
