import React from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';

const styles = makeStyles()(() => ({
  showingButton: {
    width: 'fit-content',
    left: 'calc(50% - 83px)',
    position: 'absolute',
    bottom: 40,
    border: '1px solid #DDDDDD',
    background: 'white',
    boxShadow: '0px 16px 32px rgba(0, 0, 0, 0.1)',
    padding: '12px',
    cursor: 'default',
    fontSize: 12
  },
  label: {
    marginRight: 24,
    color: '#666666'
  }
}));
const TableCount = ({ loading, totalCount, count }) => {
  const { classes } = styles();

  if (!loading) {
    return (
      <Box className={classes.showingButton} margin="0  auto" borderRadius="40px">
        <span className={classes.label}>Showing</span>
        <span className="text-33">
          {count}/{totalCount}
        </span>
      </Box>
    );
  }
  return null;
};

export default React.memo(TableCount);
