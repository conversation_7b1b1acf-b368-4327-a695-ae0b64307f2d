import React from 'react';
import { useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import InputAdornment from '@mui/material/InputAdornment';
import { makeStyles } from 'tss-react/mui';

import { Style_PackingPanelBarcode } from 'views/Packing/Style';
import { LOCALISATION } from 'redux/reducers/localisation';
import LkInput from 'components/MaterialUi/LkInput';

const styles = makeStyles()((theme) => ({
  root: {
    borderRadius: theme.spacing(0.5)
  },
  cardShadow: {
    boxShadow: 'rgb(0 0 0 / 30%) 0px 8px 16px',
    padding: theme.spacing(6)
  },
  boxWrapper: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)'
  },
  ...Style_PackingPanelBarcode
}));

const BarcodeScanner = ({
  onChangeFun,
  barcode,
  onKeyUpFun,
  title,
  actions = null,
  placeholder = ''
}) => {
  const { classes } = styles();
  const packingLocal = useSelector((state) => state[LOCALISATION].localeData.PACKING);
  return (
    <Box data-cy="barcodeScannerContainer" className={classes.boxWrapper}>
      <Box width="auto" pt={6} pb={6} pr={2} pl={2} mt={2} borderRadius={12}>
        <Card className={classes.cardShadow}>
          <Box className={classes.card}>
            <img
              data-cy="scannerImage"
              className={classes.scanImg}
              alt="scan-img"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/packingScanner.svg`}
            />
            <Box data-cy="scannerTitle" className={classes.label}>
              {title}
            </Box>
            <Box
              display="flex"
              justifyContent="center"
              padding="32px 48px 0px 48px"
              paddingBottom={0}
            >
              <Box className={classes.barcode}>
                <LkInput
                  value={barcode}
                  autoFocus
                  onChange={onChangeFun}
                  id="barcodeId"
                  className={classes.root}
                  variant="outlined"
                  placeholder={placeholder || packingLocal.SCAN_BARCODE}
                  fullWidth
                  size="small"
                  onKeyUp={(e) => e.keyCode === 13 && onKeyUpFun(e)}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <img
                          style={{ width: '25px' }}
                          alt="barcode-img"
                          src={`${import.meta.env.VITE_STATIC_SERVER}/images/barcodeScan.svg`}
                        />
                      </InputAdornment>
                    )
                  }}
                />
              </Box>
            </Box>
            {actions && (
              <Box className={classes.actions} mt={2}>
                {actions}
              </Box>
            )}
          </Box>
        </Card>
      </Box>
    </Box>
  );
};

export default BarcodeScanner;
