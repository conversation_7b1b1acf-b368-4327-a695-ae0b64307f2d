import React, { useState } from 'react';

import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';

import LkInput from 'components/MaterialUi/LkInput';
import FlexBox from '../core/FlexBox';

const InfoTable = (props) => {
  const { titleKey, titleValue, header, data = [], valueColor, keyColor, noBorder = false } = props;
  const [selectedEditIndex, setselectedEditIndex] = useState();

  const setselectedEditIndexClick = (index, obj) => {
    setselectedEditIndex(index);
    setTimeout(() => {
      const el = document.getElementById(`edit${index}`);
      if (el) {
        el.value = obj.value;
        el.focus();
      }
    }, 0);
  };

  const blurEdit = (index, d) => {
    setselectedEditIndex(null);
    d.editFn(d, document.getElementById(`edit${index}`)?.value);
  };
  const tempBorder = {
    border: 1,
    borderRadius: 8,
    borderColor: 'grey.200'
  };
  const borderProperty = noBorder ? {} : tempBorder;
  return (
    <Box width="300px" maxHeight="550px" overflow="scroll" {...borderProperty}>
      {titleKey && (
        <>
          <FlexBox component="div" justifyContent="space-between" p={1}>
            <FlexBox alignItems="center">
              <Typography color={keyColor} variant="subtitle1">
                {titleKey}
              </Typography>
            </FlexBox>
            <Typography color={valueColor} variant="h5">
              {titleValue}
            </Typography>
          </FlexBox>
          <Box mr={1} ml={1}>
            <Divider />
          </Box>
        </>
      )}

      <Box p={2} pb={1}>
        {header && (
          <Typography variant="h5" paragraph>
            {header}
          </Typography>
        )}
        {data &&
          data.map(
            (d, i) =>
              ((d.link && d.value) || !d.link) && (
                <React.Fragment key={d.key}>
                  {i !== 0 && <Divider />}
                  {d.header && (
                    <div className="mr-t16 mr-b16">
                      <Typography variant="h5" paragraph>
                        {d.header}
                      </Typography>
                    </div>
                  )}
                  <FlexBox justifyContent="space-between" pt={1.5} pb={1.5}>
                    <Typography color={d.colorKey} variant="subtitle1">
                      {d.key}
                    </Typography>
                    <div className="display-flex">
                      {selectedEditIndex !== i && !d.link && (
                        <div className="fs12 fw-bold text-33">{d.value ?? '-'}</div>
                      )}
                      {selectedEditIndex !== i && d.link && d.value && (
                        <a
                          target="_blank"
                          href={d.link}
                          rel="noopener noreferrer"
                          className="fs12 fw-bold text-turquioise_surf"
                        >
                          {d.value}
                        </a>
                      )}
                      {d.edit && selectedEditIndex === i && d.inputType === 'text' && (
                        <LkInput
                          size="small"
                          id={`edit${i}`}
                          onBlur={() => {
                            blurEdit(i, d);
                          }}
                        />
                      )}
                      {d.edit && selectedEditIndex === i && d.inputType === 'select' && (
                        <LkInput
                          size="small"
                          style={{ width: '190px' }}
                          id={`edit${i}`}
                          fullWidth
                          select
                          label="Procurement Type"
                          onChange={() => {
                            blurEdit(i, d);
                          }}
                          options={[]}
                        >
                          <MenuItem value="">Select</MenuItem>
                          <MenuItem value="DISPLAY">Display Order</MenuItem>
                          <MenuItem value="BULK">Bulk Order</MenuItem>
                        </LkInput>
                      )}
                      {d.edit && selectedEditIndex !== i && (
                        <div
                          style={{ display: 'inline' }}
                          onClick={() => setselectedEditIndexClick(i, d)}
                        >
                          <img
                            className="cursor-pointer mr-l10"
                            src={`${import.meta.env.VITE_STATIC_SERVER}/images/edit.svg`}
                            alt="img"
                          />
                        </div>
                      )}
                    </div>
                  </FlexBox>
                  {data.length === (i + 1) && (
                    <Box mb={6}>
                      <Divider />
                    </Box>
                  )}
                </React.Fragment>
              )
          )}
      </Box>
    </Box>
  );
};

export default InfoTable;
