import React from 'react';
import { useSelector } from 'react-redux';
import { Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import ShipmentInfo from './ShipmentInfo';
import ProductNav from './ProductNav';
// import PackingHoldUnholdCTA from '../Packing/PackingHoldUnholdCTA';

const styles = makeStyles()(() => ({
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '10px 24px',
    backgroundColor: '#fff',
    width: '100%',
    zIndex: 1
  }
}));

const ShipmentHeader = () => {
  const { classes } = styles();
  const { products } = useSelector((state) => state.order);
  const url = window.location.pathname;
  return (
    <Box id="shipment_header" className={`border-bottom-dd ${classes.header}`} position="fixed">
      <ShipmentInfo />
      {url.includes('/qc') && !url.includes('/qc-summary') && products && products.length > 1 ? (
        <ProductNav />
      ) : null}
      {/*
                url.includes('/packing') ? <PackingHoldUnholdCTA /> : null
            */}
    </Box>
  );
}

export default ShipmentHeader;
