import React, { useEffect } from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box, Button } from '@mui/material';
import { fileDownload, convertDateFormat } from 'utils/helpers';
import { useDispatch, useSelector } from 'react-redux';
import ShortCut from 'components/shortCut/shortCut';
import LkToolTip from 'components/ToolTip/ToolTip';
import LkChip from 'components/MaterialUi/LkChip';
import CloseIcon from '@mui/icons-material/Close';
import { LOCALISATION } from 'redux/reducers/localisation';
import { exportGrnDetailsReset } from '../../redux/actionCreators/grnListing';

const styles = makeStyles()((theme) => ({
  button: {
    borderRadius: theme.spacing(1),
    marginLeft: theme.spacing(2),
    height: 35
  }
}));

const GrnTabActions = ({
  resetFilters,
  exportFun,
  selectedFilterList = [],
  deleteSelectedFilter
}) => {
  const { exportGrnDetail, exportGrnLoading } = useSelector((state) => state.grnListing);
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_LIST);

  const dispatch = useDispatch();

  useEffect(() => {
    if (exportGrnDetail) {
      const { data } = exportGrnDetail.response;
      fileDownload(data, `GRN-Export-${convertDateFormat(new Date(), 'export')}`);
      dispatch(exportGrnDetailsReset());
    }
  }, [exportGrnDetail, dispatch]);
  const { classes } = styles();

  const filterList = React.useMemo(
    () =>
      // let fromPo = getQueryParam(window.location.search.replace('?', ''), 'fromPo');
      selectedFilterList.filter(({ key }) => key.startsWith('GRN') || key === 'poNum'),
    [selectedFilterList]
  );

  const isDisabledFun = React.useMemo(() => {
    const temp = filterList.filter(({ key }) => key !== 'poNum');
    return !temp.length;
  }, [filterList]);

  return (
    <div className={classes.root}>
      <Box p={2} display="flex" justifyContent="space-between">
        <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
          {filterList.map(({ key, value }) => {
            const temp = key.startsWith('GRN_') ? key.slice(4) : key;
            if (key === 'poNum') {
              return null;
            }
            // eslint-disable-next-line react/no-array-index-key
            return (
              <Box key={`${key} ${value}`} mr={2} mb={1}>
                <LkChip
                  label={`${temp}: ${value}`}
                  type="filter"
                  deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                  size="small"
                  onDelete={() => deleteSelectedFilter(key, value)}
                />
              </Box>
            );
          })}
        </Box>

        <Box display="flex" justifyContent="space-between">
          <Button
            variant="outlined"
            disabled={isDisabledFun}
            onClick={resetFilters}
            color="primary"
            size="medium"
            className={classes.button}
          >
            {CONTENT.RESET}
          </Button>
          <LkToolTip placement="bottom" title={<ShortCut name="Alt+E" />} open={isShowShortCuts}>
            <Button
              disabled={exportGrnLoading}
              variant="outlined"
              color="primary"
              size="medium"
              className={classes.button}
              onClick={exportFun}
            >
              {CONTENT.EXPORT}
            </Button>
          </LkToolTip>
        </Box>
      </Box>
    </div>
  );
};

export default GrnTabActions;
