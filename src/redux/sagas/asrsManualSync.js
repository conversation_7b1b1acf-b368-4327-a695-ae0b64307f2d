import { call, put, all, takeLatest, takeEvery } from 'redux-saga/effects';

import {
  ASRS_MANUAL_SYNC,
  getOrdersCountSuccess,
  getOrdersCountFail,
  createWaveComplete
} from 'redux/reducers/asrsManualSync.slice';
import { getOrdersCountApi, createWaveApi } from 'apis/asrsManualSync';
import { showToaster } from './toast';

function* getOrdersCountSaga(action = { payload: {} }) {
  try {
    const { data } = yield call(getOrdersCountApi, action.payload);
    yield put(getOrdersCountSuccess(data.data));
  } catch (error) {
    yield showToaster(error);
    yield put(getOrdersCountFail());
  }
}

function* createWaveSaga(action = { payload: {} }) {
  try {
    const { data } = yield call(createWaveApi, action.payload);
    if (data.data) {
      yield showToaster('', 'success', 'Wave sync initiated successfully');
    }
  } catch (error) {
    yield showToaster(error);
  } finally {
    yield put(createWaveComplete(action.payload));
  }
}

export default function* runAsrsManualSyncCalls() {
  yield all([
    takeEvery(`${ASRS_MANUAL_SYNC}/getOrdersCountLoad`, getOrdersCountSaga),
    takeLatest(`${ASRS_MANUAL_SYNC}/createWaveLoad`, createWaveSaga)
  ]);
}
