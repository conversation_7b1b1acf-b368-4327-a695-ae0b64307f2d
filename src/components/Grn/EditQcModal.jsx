import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import { makeStyles } from 'tss-react/mui';

import { getTwoYearDateFromNow, formatDateDashedSeparated } from 'utils/helpers';
import { toastOpen } from 'redux/actionCreators/toast';
import { updateGrnItemLoad, getQcDataLoad, updateGrnItemReset } from 'redux/actionCreators/grn';
import QcFailCodeData from '__mocks__/QcFailCodeData';
import { CATEGORY_MAPPING } from 'config/CategoryMapping';
import LkModal from '../Modal/Modal';
import InfoBar from '../InfoBar/InfoBar';
import LkInput from '../MaterialUi/LkInput';
import './EditQcModal.scss';

const styles = makeStyles()(() => ({
  orDivider: {
    fontSize: '10px',
    background: '#FFFFFF',
    position: 'absolute',
    bottom: '-18px',
    right: '144px',
    border: '1px solid #DDDDDD',
    left: '50%',
    marginLeft: '-18px',
    width: '33px'
  },
  divider: {
    borderBottom: ' 1px solid  #DDDDDD;'
  },
  failerError: {
    color: '#DF3747'
  },
  statusSuccess: {
    color: '#00BAC6'
  },
  buttonWidth: {
    width: '178px'
  },
  markAdFailButton: {
    width: '150px'
  },
  bodyPadding: {
    padding: '0 75px'
  }
}));

const EditQcModal = (props) => {
  const dispatch = useDispatch();

  const { open, onClose, toggleAddMoreItemModal } = props;
  const {
    grnItemSearchSuccess,
    meta,
    pidDetail,
    QCData,
    itemUpdateSuccess,
    itemupdateError,
    isProceedToGreenChannel,
    pidList
  } = useSelector((state) => state.grn);
  const { barcode, pid, qc_status, grn_code, expiry_date } = grnItemSearchSuccess;

  const [values, setValues] = React.useState({
    expiry_date: expiry_date || getTwoYearDateFromNow(),
    qc_reason: ''
  });

  useEffect(() => {
    if (expiry_date) {
      setValues((v) => ({ ...v, expiry_date: expiry_date.slice(0, 10) }));
    }
  }, [grnItemSearchSuccess, expiry_date]);

  // use this expiry format for expiry date formating
  let expiryDetails = false;
  if (pidDetail && meta) {
    const categoryId = pidDetail.result?.data?.categoryId;
    expiryDetails = categoryId === 19153 || categoryId === 11354;
  }
  const infoBarData = [
    { key: 'PID', value: pid },

    { key: 'Item Barcode', value: barcode }
  ];
  const { classes } = styles();

  const isPass = qc_status === 'pass' || false;
  const primaryButtonText = qc_status === 'pass' ? 'Mark QC FAIL' : 'Mark QC Pass';
  useEffect(() => {
    if (itemUpdateSuccess) {
      const { data } = pidDetail.result;
      dispatch(getQcDataLoad(data));
      toggleAddMoreItemModal(false);
      onClose();
    }
    if (itemupdateError) {
      const { data } = pidDetail.result;
      dispatch(getQcDataLoad(data));
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: itemupdateError.display_message,
          severity: 'error'
        })
      );
      dispatch(updateGrnItemReset());
    }
  }, [itemupdateError, itemUpdateSuccess, dispatch, pidDetail]);

  const submit = (status) => {
    const tempStatus = status || qc_status;
    const categoryId = pidDetail.result?.data?.categoryId;
    if (expiryDetails && values.expiry_date === '') {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: 'Please Select Valid Date',
          severity: 'error'
        })
      );
    }
    const QcFailCodeDataTemp =
      QcFailCodeData[CATEGORY_MAPPING[categoryId]] || QcFailCodeData.Eyeframe;
    const findQcReason = QcFailCodeDataTemp.content.filter((t) => t.textCode === values.qc_reason);
    const payload = {
      barcode,
      // "expiry_date": expiry_date ,
      grn_code,
      meta,
      pid,
      qc_fail_code: values.qc_reason,
      qc_reason: findQcReason[0]?.['QC Fail Reason'] || '',
      qc_status: tempStatus,
      green_channel: isProceedToGreenChannel[pid],
      type: pidList.result.data.type
    };
    if (expiryDetails) {
      payload.expiry_date = values.expiry_date;
    }
    const is_box_required = pidDetail?.result?.data?.is_box_required;
    if (tempStatus === 'fail' && findQcReason.length === 0) {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: 'Enter Enter Valid Fail Code',
          severity: 'error'
        })
      );
    }

    if (tempStatus === 'pass' && is_box_required) {
      const [firstKeyBarcode] = Object.keys(QCData.qc_passed);
      payload.box_barcode = firstKeyBarcode;
    }
    dispatch(updateGrnItemLoad(payload));
    return null;
  };

  const onchange = (key, e) => {
    const { value } = e.target;
    setValues({ ...values, [key]: value });
  };

  return (
    <LkModal
      allowPaddingForChildren={false}
      modalWidth="520px"
      open={open}
      showActionButton={!isPass}
      handleClose={onClose}
      title="Edit QC Status"
      primaryBtn={() => submit('pass')}
      primaryBtnWidth="178px"
      primaryBtnVariant="outlined"
      primaryBtnText={primaryButtonText}
    >
      <div className={`edit-qc text-center ${classes.bodyPadding}`}>
        <InfoBar data={infoBarData} className="mr-t15 mr-b10" />
        <Box display="flex" borderRadius="5px" mb={2} border={0.3} borderColor="grey.500">
          {isPass ? (
            <Box className="pd-5 flex1" textAlign="center">
              <span className="fs12 text-66 mr-b5 ">QC Status: </span>

              <span className={`fs14 mr-l2 fw-bold ${classes.statusSuccess}`}>PASSED</span>
            </Box>
          ) : (
            <>
              <div className="pd-15">
                <div className="fs12 text-66 mr-b5 ">QC Status</div>

                <div className={`fs15 fw-bold ${classes.failerError}`}>FAILED</div>
              </div>

              <Box className="pd-15" borderLeft={0.3} textAlign="left" borderColor="grey.500">
                <div className="fs12 fw-bold mr-b5">The Outerbox is damaged</div>

                <div className="fs12 text-66">the flags of the box are torn from the side</div>
              </Box>
            </>
          )}
        </Box>

        <div className="mr-t25 pd-b15 display-flex justify-content-space-between ">
          {!isPass && (
            <div className="text-left" style={{ flexBasis: expiryDetails ? '48%' : '100%' }}>
              <div className=" fs14 fw-bold mr-b10">Change/ Update Reason</div>
              <LkInput
                onChange={(e) => onchange('qc_reason', e)}
                value={values.qc_reason}
                fullWidth
                margin="none"
                placeholder="Scan here"
              />
            </div>
          )}
          {expiryDetails && (
            <div className="text-left mr-b10 " style={{ flexBasis: isPass ? '100%' : '48%' }}>
              <div className="fs14 fw-bold mr-b10">Update Expiry Date</div>
              <TextField
                value={values.expiry_date}
                onChange={(e) => onchange('expiry_date', e)}
                id="expiryDate"
                type="date"
                fullWidth
                variant="outlined"
                size="small"
                inputProps={{
                  min: formatDateDashedSeparated(Date.now())
                }}
                InputLabelProps={{
                  shrink: true
                }}
              />
            </div>
          )}
        </div>
        <Box pb={4.2} className={`mark-qc ${classes.divider}`} style={{ position: 'relative' }}>
          {/* <div className="fs18 fw-bold ">Expiry Date</div> */}

          <Button
            className={classes.buttonWidth}
            onClick={() => submit()}
            size="small"
            style={{ padding: '4px 35px' }}
            variant="contained"
            color="primary"
          >
            UPDATE
          </Button>
          <Box p={1.2} borderRadius="50%px" component="span" className={classes.orDivider}>
            {' '}
            OR
          </Box>
        </Box>
        {isPass && (
          <Box display="flex" justifyContent="space-between" mt={2} mb={6} width="100%">
            <Box flexBasis="55%">
              <div className="fs14 fw-bold mr-b10 text-left">Mark QC Fail</div>
              <LkInput
                value={values.qc_reason}
                onChange={(e) => onchange('qc_reason', e)}
                margin="none"
                placeholder="Scan to Mark QC Fail"
                fullWidth
              />
            </Box>
            <Box mt={3} flexBasis="40%">
              <Button
                className={classes.markAdFailButton}
                onClick={() => submit('fail')}
                variant="outlined"
                color="primary"
                size="medium"
              >
                {primaryButtonText}
              </Button>
            </Box>
          </Box>
        )}
      </div>
    </LkModal>
  );
};

export default EditQcModal;
