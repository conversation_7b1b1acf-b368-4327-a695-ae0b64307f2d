import React from 'react';
import { capitalise, genericDateFormatted } from 'utils/helpers';
import FinanceDashboard from '../index';

import DOListData from './data/tableData.json';
import DOListHeader from './data/tableHeader.json';
import DODetailsData from './data/extendedTableData.json';
import config from '../../../config';
import { FINANCE_DO as CONTENT } from '../../../../public/localisation/lang_en.json';

const PLACEHOLDER_TEXT = CONTENT.SEARCH_BY_DO_NUMBER;
const EMP_CODE = 'EMP-123';

describe('FinanceTable Dashboard', () => {
  beforeEach(() => {
    cy.viewport('macbook-15');
    cy.intercept('GET', `${config.finance.getDOList}**`, {
      statusCode: 200,
      body: DOListData
    }).as('getChildRoles');

    cy.mount(<FinanceDashboard />, {
      state: {
        login: {
          user: {
            userDetail: {
              empCode: EMP_CODE
            }
          }
        }
      }
    });
  });

  it('updating filters works correctly', () => {
    cy.getByCy('Status-CREATED').should('have.text', 'Status (CREATED)');
    cy.getByCy('Status-filter-delete-icon').click();
    cy.findByRole('textbox')
      .should('have.attr', 'placeholder', PLACEHOLDER_TEXT)
      .type('test{enter}');
    cy.getByCy('DO Number-test').should('have.text', 'DO Number (test)');
    cy.getByCy('DO Number-filter-delete-icon').click();
    cy.get('[data-cy="table-header-cell-7"]  [data-testid="FilterListIcon"]').click();
    cy.get('[aria-labelledby="checkbox-list-label-CREATED"]').click();
    cy.findByRole('button', { name: /apply/i }).click();
    cy.getByCy('Status-CREATED').should('have.text', 'Status (CREATED)');
  });

  it('renders correctly with gloabl search', () => {
    const mockData = DOListData.data.content;
    cy.findByRole('textbox').should('have.attr', 'placeholder', PLACEHOLDER_TEXT);
    cy.getByCy('Status-CREATED').should('have.text', 'Status (CREATED)');
    cy.getByCy('Status-filter-delete-icon').should('exist');
    cy.findByRole('textbox').type('test{enter}');
    cy.getByCy('DO Number-test').should('have.text', 'DO Number (test)');
    cy.findByRole('button', { name: /download csv/i })
      .should('not.be.disabled')
      .click();
    DOListHeader.map((header, index) => cy.headerTableCell(index, header));

    const {
      incrementId,
      poNumber,
      doType,
      customer: { name },
      orderItems,
      createdBy,
      createdAt,
      status
    } = mockData[0];

    const soValue = orderItems.reduce((acc, { price, quantity }) => acc + price * quantity, 0);
    const firstRowData = [
      incrementId,
      poNumber,
      doType,
      name,
      soValue,
      createdBy,
      createdAt,
      status
    ];
    firstRowData.map((data, index) => {
      if (index === 6) {
        return cy.firstRowTableCell(index, genericDateFormatted(createdAt));
      }
      if (index === 7) {
        return cy.firstRowTableCell(index, capitalise(status));
      }
      return cy.firstRowTableCell(index, firstRowData[index]);
    });
    cy.getByCy('table-cell-0-8').contains('Approve');
    cy.getByCy('table-cell-0-8').contains('Reject');
    cy.intercept('POST', `${config.finance.getDODetails}/**`, {
      statusCode: 200,
      body: { data: DODetailsData }
    }).as('DOdetailsData');

    cy.getByCy('expand-icon-0').click({ multiple: true });
    cy.wait('@DOdetailsData').its('response.statusCode').should('eq', 200);
  });

  it('approve DO Successfully', () => {
    cy.getByCy('table-cell-0-8').contains('Approve').click();

    cy.intercept('POST', `${config.finance.approveAndRejectDO}/**`, {
      statusCode: 200,
      body: { message: 'success' }
    }).as('approveDO');
    cy.getByCy('close-modal').click();
    cy.getByCy('modal').should('not.exist');
    cy.getByCy('table-cell-0-8').contains('Approve').click();
    cy.findByRole('button', { name: CONTENT.OKAY_APPROVE }).click();
    cy.wait('@approveDO').its('response.statusCode').should('eq', 200);

    cy.wait('@getChildRoles').its('response.statusCode').should('eq', 200);
    cy.getByCy('close-modal').click();
  });

  it('reject DO Successfully', () => {
    cy.getByCy('table-cell-0-8').contains('Reject').click();
    cy.intercept('POST', `${config.finance.approveAndRejectDO}/**`, {
      statusCode: 200,
      body: { message: 'success' }
    }).as('rejectDo');

    cy.getByCy('text-box').type('test reason');
    const resizeObserverLoopErrRe = /^[^(ResizeObserver loop limit exceeded)]/;
    Cypress.on('uncaught:exception', (err) => !resizeObserverLoopErrRe.test(err.message));
    cy.findByRole('button', { name: CONTENT.REJECT }).click();

    cy.wait('@rejectDo').its('response.statusCode').should('eq', 200);

    cy.wait('@getChildRoles').its('response.statusCode').should('eq', 200);
    cy.getByCy('toaster-message').should('have.text', 'DO Rejected Successfully');
  });

  it('should show error message when Approve or Reject API fails', () => {
    cy.getByCy('table-cell-0-8').contains('Approve').click();
    cy.intercept('POST', `${config.finance.approveAndRejectDO}/**`, {
      statusCode: 500,
      body: { message: 'Error' }
    }).as('approveRejectDO');
    cy.findByRole('button', { name: CONTENT.OKAY_APPROVE }).click();
    cy.wait('@approveRejectDO').its('response.statusCode').should('eq', 500);
    cy.getByCy('toaster-message').should('have.text', 'Error');
    cy.getByCy('close-modal').click();
    cy.getByCy('table-cell-0-8').contains('Reject').click();
    cy.findByRole('button', { name: CONTENT.REJECT }).click();
    cy.getByCy('toaster-message').should('have.text', CONTENT.PLEASE_ENTER_REASON_FOR_REJECTION);
    cy.getByCy('text-box').type('test');
    cy.findByRole('button', { name: CONTENT.REJECT }).click();
    cy.wait('@approveRejectDO').its('response.statusCode').should('eq', 500);
  });
});

describe('Finance API Fail', () => {
  beforeEach(() => {
    cy.viewport('macbook-15');
  });

  it('should show error message when API fails', () => {
    cy.intercept('GET', `${config.finance.getDOList}**`, {
      statusCode: 404,
      body: { message: 'error' }
    }).as('getChildRoles');
    cy.mount(<FinanceDashboard />);
    cy.wait('@getChildRoles').its('response.statusCode').should('eq', 404);
    cy.findByRole('button', { name: /download csv/i }).should('be.disabled');
    cy.getByCy('toaster-message').should('have.text', 'error');
  });

  it('should show error message when we download CSV and data is more than 2000', () => {
    cy.intercept('GET', `${config.finance.getDOList}**`, {
      statusCode: 200,
      body: { data: { totalCount: 2001, content: [] } }
    }).as('getChildRoles');
    cy.mount(<FinanceDashboard />);
    cy.wait('@getChildRoles').its('response.statusCode').should('eq', 200);
    cy.findByRole('button', { name: /download csv/i }).click();
    cy.getByCy('toaster-message').should('have.text', CONTENT.PLEASE_APPLY_FILTER);
  });
});
