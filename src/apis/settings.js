import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

// export const getFacilitiesApi = () => {
//   return publicInstance().get(config.settings.getFacilities)
//     .then(response => response.data)
//     .catch(error => { throw error });
// };

export const getFacilitiesApi = (payload) => {
  const url = `${config.facility.facilityList}`
  return tokenInstance().post(url, payload)
    .then(response => (response.data.data))
    .catch(error => { throw error });
}
