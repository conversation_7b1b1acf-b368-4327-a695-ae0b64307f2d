import React, { useState, useMemo, memo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { debounce } from 'utils/lodash';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import LkModal from 'components/Modal/Modal';
import {
  createBulkUserLoad,
  createUserLoad,
  getActiveApproverRoleLoad,
  getActiveUserRoleLoad,
  getChildRoleToAddUserLoading,
  getChildRoleToAddUserReset,
  getEmployeeDataLoad,
  getEmployeeDataReset
} from 'redux/reducers/userManagement';
import Spinner from 'components/Spinner/Spinner';

const useStyles = makeStyles()((theme) => ({
  root: {
    '& .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"]': {
      minHeight: 100
    },
    '& .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-endAdornment': {
      top: 10
    }
  },
  modal: {
    padding: '0 100px'
  },
  lineBreak: {
    border: 'none',
    borderTop: '1px solid #ddd',
    paddingBottom: 12
  },
  autocomplete: {
    marginBottom: 16
  },
  autocompleteText: {
    marginTop: 8
  },
  lineDividerContainer: {
    display: 'flex',
    alignItems: 'center',
    marginBlock: 16
  },
  lineDivider: {
    border: 'none',
    borderTop: '1px solid #ddd',
    width: '50%'
  },
  or: {
    paddingInline: 8,
    color: '#999'
  },
  csvBox: {
    backgroundColor: '#f5f5f5',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBlock: 32,
    borderRadius: 12
  },
  fileDrag: {
    backgroundColor: '#DDDDDD',
    border: '2px dashed #3c3c3c'
  },
  csvText: {
    width: 230,
    marginInline: 'auto',
    marginTop: 12,
    color: '#666',
    lineHeight: '120%'
  },
  openFile: {
    color: theme.palette.primary.main,
    textDecoration: 'underline',
    cursor: 'pointer'
  },
  addButton: {
    display: 'flex',
    marginInline: 'auto',
    marginTop: 16,
    width: 206
  }
}));

const AddUserModal = ({ open, handleClose, roleName }) => {
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [files, setFiles] = useState([]);
  const [role, setRole] = useState([]);
  const [facility, setFacility] = useState([]);
  const [employeeId, setemployeeId] = useState([]);
  const [dragging, setDragging] = useState(false);

  const facilities = useSelector((state) => state.settings.facilitiesObj.facilities);
  const getEmployeeCode = useSelector((state) => state.userManagement.getEmployeeCode);
  const childRoleToAddUser = useSelector((state) => state.userManagement.childRoleToAddUser);

  useEffect(() => {
    dispatch(getChildRoleToAddUserLoading());
    if (roleName) {
      setRole([{ name: roleName }]);
    }
    return () => {
      dispatch(getEmployeeDataReset());
      dispatch(getChildRoleToAddUserReset());
    };
  }, []);

  const getDebounceEmployeeId = debounce((empData) => {
    dispatch(getEmployeeDataLoad({ empData }));
  }, 500);

  const handleRoleChange = (event, newValue) => {
    if (roleName) {
      setRole([newValue]);
    } else {
      setRole(newValue);
    }
  };

  const handleFacilityChange = (event, newValue) => {
    setFacility(newValue);
  };

  const handleEmployeeIdChange = (event, newValue) => {
    setemployeeId(newValue);
  };

  const handleTextChange = (e) => {
    getDebounceEmployeeId(e.target.value);
  };

  const handleFileChange = (event) => {
    event.preventDefault();
    setFiles([event.target.files[0]]);
  };

  const onDrop = (event) => {
    event.preventDefault();
    const droppedFiles = event.dataTransfer.files;
    setFiles([droppedFiles[0]]);
    setDragging(false);
  };

  const onDragOver = (event) => {
    event.preventDefault();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(false);
  };

  const createBulkUser = () => {
    const formData = new FormData();
    formData.set('csvFile', files[0]);
    dispatch(createBulkUserLoad(formData));
  };

  const createUser = (e) => {
    e.preventDefault();
    const payload = {
      employeeCodes: employeeId.map((employee) => employee.employeeCode),
      facilities: facility.map((f) => f.code),
      roleGroups: role.map((r) => r.name)
    };
    if (files?.length) {
      createBulkUser();
    } else {
      dispatch(createUserLoad(payload));
    }

    dispatch(getActiveUserRoleLoad());
    dispatch(getActiveApproverRoleLoad());
    navigate('/usermanagement/roles');
    handleClose();
  };

  const disableBtn = useMemo(
    () => (role?.length && facility?.length && employeeId?.length) || files?.length,
    [role, facility, employeeId, files]
  );

  return (
    <div>
      <LkModal
        modalWidth="600px"
        open={open}
        upperHeading={<Typography variant="h4">Add Users</Typography>}
        allowPaddingForChildren={false}
        handleClose={handleClose}
      >
        <hr className={classes.lineBreak} />
        <Box className={classes.modal}>
          <form>
            <Box className={classes.autocomplete}>
              {roleName ? (
                <Autocomplete
                  className={classes.autocomplete}
                  options={[{ name: roleName }]}
                  getOptionLabel={(option) => option?.name}
                  defaultValue={{ name: roleName }}
                  onChange={handleRoleChange}
                  isOptionEqualToValue={(option, value) => option.name === value.name}
                  renderInput={(params) => (
                    <TextField {...params} label="Role" variant="outlined" fullWidth />
                  )}
                />
              ) : (
                <Autocomplete
                  loading={childRoleToAddUser.isLoading}
                  multiple
                  disabled={files?.length > 0}
                  value={role}
                  onChange={handleRoleChange}
                  options={
                    childRoleToAddUser.isLoading || childRoleToAddUser?.data.length === 0
                      ? []
                      : childRoleToAddUser?.data
                  }
                  getOptionLabel={(option) => option?.name}
                  isOptionEqualToValue={(option, value) => option?.name === value?.name}
                  renderInput={(params) => (
                    <TextField label="Select new role" {...params} variant="outlined" />
                  )}
                />
              )}
              <Typography className={classes.autocompleteText}>
                You can select multiple role
              </Typography>
            </Box>
            <Box className={classes.autocomplete}>
              <Autocomplete
                multiple
                disabled={files?.length > 0}
                value={facility}
                onChange={handleFacilityChange}
                options={[{ code: 'ALL', name: 'ALL' }, ...facilities]}
                getOptionLabel={(option) => option.code}
                isOptionEqualToValue={(option, value) => option.code === value.code}
                renderInput={(params) => (
                  <TextField label="Select facility" {...params} variant="outlined" />
                )}
              />
              <Typography className={classes.autocompleteText}>
                You can select multiple facility
              </Typography>
            </Box>
            <Box>
              <Autocomplete
                loading={getEmployeeCode.isLoading}
                multiple
                disabled={files?.length > 0}
                value={employeeId}
                onChange={handleEmployeeIdChange}
                options={getEmployeeCode?.data}
                getOptionLabel={(option) => option?.employeeCode}
                isOptionEqualToValue={(option, value) =>
                  option?.employeeCode === value?.employeeCode
                }
                renderInput={(params) => (
                  <TextField
                    label="Select employee Id"
                    {...params}
                    variant="outlined"
                    onChange={handleTextChange}
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {getEmployeeCode.isLoading ? <Spinner /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      )
                    }}
                  />
                )}
              />
              <Typography className={classes.autocompleteText}>
                You can select multiple employee id
              </Typography>
            </Box>
            <Box className={classes.lineDividerContainer}>
              <Box className={classes.lineDivider} />
              <Typography className={classes.or}>OR</Typography>
              <Box className={classes.lineDivider} />
            </Box>
            <Box
              className={cx(classes.csvBox, { [classes.fileDrag]: dragging })}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
            >
              <input
                id="upload-items-file"
                onChange={handleFileChange}
                type="file"
                className="display-none"
                accept=".csv"
              />
              <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/csv.svg`} alt="csv" />
              <Typography align="center" className={classes.csvText}>
                Drag & drop or upload your CSV file of employee IDs by{' '}
                <label htmlFor="upload-items-file" className={classes.openFile}>
                  clicking here
                </label>
              </Typography>
            </Box>

            <Button
              className={classes.addButton}
              color="primary"
              variant="contained"
              onClick={createUser}
              disabled={!disableBtn}
            >
              Add
            </Button>
          </form>
        </Box>
      </LkModal>
    </div>
  );
};

export default memo(AddUserModal);
