import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';

import { mergeContentValues, regextest } from 'utils/helpers';
import { toastOpen } from 'redux/actionCreators/toast';
import { getBarcodeSeriesSelector } from 'selectors/barcodeSeries';
import { getBarCodeSeriesLoad } from 'redux/actionCreators/barcodeSeries';
import { IOSSwitch } from 'components/common/IOSSwitch';
import { LOCALISATION } from 'redux/reducers/localisation';
import { getBarcodeFromURL } from '../../utils/helpers';

const useStyles = makeStyles()(() => ({
  container: {
    width: 384,
    maxHeight: 129,
    borderRadius: 8,
    background: '#00BAC6',
    padding: 16
  },
  label: {
    color: '#FFFFFF',
    fontWeight: 400,
    fontSize: 12,
    lineHeight: '14px'
  },
  textField: {
    width: 352,
    height: 40,
    borderRadius: 6,
    border: 'none',
    outline: 'none',
    padding: 10,
    marginTop: 7,
    marginBottom: 5,
    textTransform: 'uppercase'
  },
  enableBoxToggle: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  boxLabel: {
    display: 'flex',
    alignItems: 'center',
    color: '#FFFFFF',
    fontWeight: 400,
    fontSize: 14,
    lineHeight: '143%',
    letterSpacing: '0.15px'
  }
}));

const ProductsScannerTextField = ({
  activeBoxBarcode,
  setActiveBoxBarcode,
  savedScannedBarcodes,
  ScannedBarcodeDetails,
  scanBarcode,
  disableInputField,
  setBoxWiseTransfer = null,
  scannedBoxes = [],
  isBoxWiseTransfer = false,
  enableBoxTransfers = false,
  hideEnableBoxOption = false,
  isAllowDuplicateScan = false
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { boxCodeRegex } = useSelector(getBarcodeSeriesSelector);
  const { data: barcodeSeries, isLoading: barcodeSeriesIsLoading } = useSelector(
    (state) => state.barcodeSeries
  );
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.PRODUCTS_SCANNER_TEXTFEILD);
  const inputRef = useRef(null);

  useEffect(() => {
    if (!barcodeSeries.length) {
      dispatch(getBarCodeSeriesLoad());
    }
  }, [barcodeSeries]);

  const { areBoxesExist, areItemsExist } = ScannedBarcodeDetails;
  const scanBarcodeFunc = (inputBarcode) => {
    let barcode = getBarcodeFromURL(inputBarcode.trim());
    if (barcode.length > 1) {
      barcode = barcode.toUpperCase();
      const isBarcodeExist = savedScannedBarcodes.some(
        (eachItem) => eachItem.barcode === barcode && eachItem.status === 'success'
      );
      const isBoxBarCode = boxCodeRegex.some((boxRegx) => regextest(barcode, boxRegx));

      inputRef.current.value = '';
      if (isBoxWiseTransfer && isBoxBarCode && scannedBoxes.includes(barcode)) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.ERROR,
            subHeading: mergeContentValues(CONTENT.BOX_ALREADY_SCANNED, barcode),
            severity: 'error'
          })
        );
      } else if (isBoxWiseTransfer && !isBoxBarCode) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.ERROR,
            subHeading: CONTENT.ONLY_BOX_SCAN_ALLOWED,
            severity: 'error'
          })
        );
      } else if (areBoxesExist && !activeBoxBarcode && !isBoxBarCode) {
        // @TODO discussion on these messages
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.ERROR,
            subHeading: `${CONTENT.SCAN_BOX_BFR_SCNG_ITEM} ${barcode}`,
            severity: 'error'
          })
        );
      } else if (areItemsExist && isBoxBarCode) {
        // @TODO discussion on these messages
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.ERROR,
            subHeading: `${CONTENT.CANT_ADD_BOXES_FR_TRANSFER} ${barcode}`,
            severity: 'error'
          })
        );
      } else if (isBarcodeExist && !isAllowDuplicateScan) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: CONTENT.ERROR,
            subHeading: `${CONTENT.DUPLICATE_BARCODE_SCANNED} ${barcode}`,
            severity: 'error'
          })
        );
      } else if (isBoxBarCode) {
        setActiveBoxBarcode(barcode);
        // enable this once BE changes are done for box validation
        // else if (isBoxBarCode && !isBoxWiseTransfer) {
        //   dispatch(validateBoxBarcodeRequest(barcode));
        // }
      } else {
        scanBarcode(barcode);
      }
    }
  };

  useEffect(() => {
    // focusing input field once its enabled
    if (!disableInputField && inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputRef, disableInputField]);

  return (
    <Box className={classes.container}>
      <p className={classes.label}>{CONTENT.ENTER_DETAILS}</p>
      <input
        type="text"
        placeholder={CONTENT.SCAN_BARCODE}
        ref={inputRef}
        id="product-scanner-input-field"
        disabled={barcodeSeriesIsLoading || disableInputField}
        className={classes.textField}
        onKeyDown={(e) =>
          e.key === 'Enter' && !barcodeSeriesIsLoading && scanBarcodeFunc(e.target.value)
        }
      />
      {!hideEnableBoxOption && (
        <Box className={classes.enableBoxToggle}>
          <p className={classes.boxLabel}>{CONTENT.SCAN_BOXES_ONLY}</p>
          <IOSSwitch
            color="primary"
            checked={isBoxWiseTransfer}
            onChange={() => setBoxWiseTransfer(!isBoxWiseTransfer)}
            disabled={!enableBoxTransfers}
          />
        </Box>
      )}
    </Box>
  );
};

export default ProductsScannerTextField;
