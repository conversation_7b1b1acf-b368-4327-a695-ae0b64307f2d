import { Box, Tab } from '@mui/material';
import { StyledTabs } from 'components/common/StyledTabs';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import InvoiceViewTable from './InvoiceViewTable';
import SummaryTable from './SummaryTable';

const useStyles = makeStyles()(() => ({
  tabselected: {
    border: '1px solid #DDD',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderBottom: 'none',
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      width: '100% ',
      height: 10,
      backgroundColor: 'white',
      bottom: -10
    }
  },

  tabs: {
    minWidth: 160,
    maxWidth: 160,
    fontSize: 11
  },

  positionRelative: {
    position: 'relative'
  },
  tab1Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 0
  },
  tab2Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 160
  }
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`simple-tabpanel-${index}`}
    aria-labelledby={`simple-tab-${index}`}
    {...other}
  >
    {value === index && <Box>{children}</Box>}
  </div>
);

const InvoiceTabContainer = ({
  tableClasses,
  invoiceViewData,
  invoiceLoading,
  setShowSummary,
  invoiceRefNumber,
  showSummary
}) => {
  const { classes } = useStyles();
  const [value, setValue] = useState(0);
  const navigate = useNavigate();

  const handleChange = (event, newValue) => {
    setValue(newValue);
    if (newValue === 1) {
      navigate(`/invoice/view/summary?invoiceRefNumber=${invoiceRefNumber}`);
      setShowSummary(true);
    } else {
      navigate(`/invoice/view?invoiceRefNumber=${invoiceRefNumber}`);
      setShowSummary(false);
    }
  };

  return (
    <div className={classes.root}>
      <Box>
        <StyledTabs value={value} onChange={handleChange}>
          <Tab
            label="Invoice Details"
            className={`${value === 0 ? classes.tabselected : ''} tabs`}
          />
          {showSummary && (
            <Tab label="Summary" className={`${value === 1 ? classes.tabselected : ''} tabs`} />
          )}
        </StyledTabs>
        <Box className={`border-grey5-radiusbase ${value === 0 && 'border-left-no-radius'}`}>
          <TabPanel value={value} index={0} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab1Absolute} />
            <InvoiceViewTable
              classes={tableClasses}
              invoiceViewData={invoiceViewData}
              isLoading={invoiceLoading}
            />
          </TabPanel>
          {showSummary && (
            <TabPanel index={1} value={value} className={classes.positionRelative}>
              <Box width={158} component="div" height={2} className={classes.tab2Absolute} />
              <SummaryTable invoiceRefNo={invoiceViewData?.data.invoice_ref_number} />
            </TabPanel>
          )}
        </Box>
      </Box>
    </div>
  );
};

export default InvoiceTabContainer;
