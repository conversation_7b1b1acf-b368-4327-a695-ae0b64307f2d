import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getQCHoldReasons = (id) => {
  const URL = `${config.apiPath.qcHoldReasons}?parentId=${id}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const markQCDone = (payload) => {
  const { salesOrderId, qcData } = payload;
  // eslint-disable-next-line max-len
  const URL = `${config.apiPath.qcDone}?wmsOrderCode=${salesOrderId}&printAtPacking=${0}`;
  return tokenInstance()
    .post(URL, qcData)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const markDamagedApi = (payload) => {
  const { orderItemId } = payload;
  const URL = `${config.apiPath.qcFailDamage}?orderItemId=${orderItemId}&operation=DAMAGED`;

  return tokenInstance().put(URL);
};

export const fittingByStationCodeApi = (payload) => {
  const { url, ...rest } = payload;

  const URL = `${url}${config.qcLensoMeter.fittingByStationCode}`;
  return tokenInstance().post(URL, rest);
};

export const markFullFillableApi = (payload) =>
  tokenInstance().put(config.apiPath.markFullFillable, payload);
