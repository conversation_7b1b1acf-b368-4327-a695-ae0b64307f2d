import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { resetInitialAPIresponse } from 'redux/actionCreators/order';

const useOrderHook = () => {
  const dispatch = useDispatch();
  const { orderDetailsAPIresponse, orderInfoAPIresponse } = useSelector((state) => state.order);

  useEffect(() => {
    if (orderDetailsAPIresponse && orderInfoAPIresponse) {
      dispatch(resetInitialAPIresponse());
    }
  }, [orderDetailsAPIresponse, orderInfoAPIresponse]);
};

export default useOrderHook;
