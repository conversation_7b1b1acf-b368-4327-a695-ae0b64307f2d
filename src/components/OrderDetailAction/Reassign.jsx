import React from 'react';
import { useDispatch } from 'react-redux';
import { autoReassignLoad } from 'redux/actionCreators/orderDetails';

const Reassign = ({ payload }) => {
  const dispatch = useDispatch();

  return (
    <div>
      If you want to reassign, please{' '}
      <span
        style={{ cursor: 'pointer' }}
        className="text-white fs16 text-underline"
        onClick={() => dispatch(autoReassignLoad(payload))}
      >
        click here...
      </span>
    </div>
  );
};

export default Reassign;
