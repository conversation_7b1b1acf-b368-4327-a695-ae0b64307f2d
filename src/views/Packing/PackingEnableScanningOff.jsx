/* eslint-disable no-console */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import InputAdornment from '@mui/material/InputAdornment';

import { LOCALISATION } from 'redux/reducers/localisation';
import { toastOpen } from 'redux/actionCreators/toast';
import {
  isBarcodeValidLoad,
  isBarcodeValidReset,
  markPackingDone
} from 'redux/actionCreators/packing';
import { getOrderDetailsReset } from 'redux/actionCreators/order';
import { CONSUL, getConsulKeyValue } from 'redux/reducers/consul';
import { getProductTitleBy_Brand_Model, getProductTitleBy_Value } from 'utils/helpers';
import Carousel from 'components/Carousel/Carousel';
import CONSTANTS from 'constant';

import { LOGIN } from 'redux/reducers/login';
import LkInput from 'components/MaterialUi/LkInput';
import { Style_PackingEnableScanningOff } from './Style';
import ProductTypeLabel from './ProductTypeLabel';
import OwnDaysCardModal from './OwnDaysCardModal';

const styles = makeStyles()(() => ({ ...Style_PackingEnableScanningOff }));

/** Defined Constants */
const {
  SLIDES_TO_SHOW,
  CAROUSEL_SETTINGS,
  KEY_SCAN_AGAIN_TO_COMPLETE,
  KEY_MONO_CARTON,
  KEY_SCREWDRIVER,
  KEY_RODEN_STOCK,
  KEY_TOKAI_CARD,
  KEY_DELAY_CARD,
  TITLE_BRAND_MODEL,
  KEY_OWN_DAYS,
  KEY_PRODUCT_CASE,
  SLIDES_TO_SHOW_FOR_BULK,
  SHARK_TANK_IMAGE,
  OWN_DAYS_CARD_BARCODE_SCANNER
} = CONSTANTS;

const accessoriesKey = {
  screwdriver: KEY_SCREWDRIVER,
  monocarton: KEY_MONO_CARTON
};

const CARD_TITLE = {
  KEY_RODEN_STOCK: 'RodenStock Lenses',
  KEY_TOKAI_CARD: 'Tokai Card',
  KEY_DELAY_CARD: 'Delay Card',
  KEY_OWN_DAYS: 'Owndays Card',
  KEY_MONO_CARTON: '',
  KEY_SCREWDRIVER: ''
};

const applyGridCss = ['CONTACT_LENS_SOLUTION', 'ACCESSORIES'];

const PackingEnableScanningOff = (props) => {
  const navigate = useNavigate();
  const { inputBarcode } = props;
  const { classes } = styles();
  const dispatch = useDispatch();

  const { enableOwnDaysCard = [] } = useSelector((state) => state.consul.featureToggle.data);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);
  const isLoading = useSelector((state) => state.loader.isLoading);
  const packingLocal = useSelector((state) => state[LOCALISATION].localeData.PACKING);

  const {
    products,
    accessories,
    delayOrderCard,
    roden_stock_values,
    tokai_card_values,
    lenses_by_parentuw,
    orderDetails,
    caseImage,
    orderBarcodeInfo,
    shipmentHeaders,
    packingAccessories,
    owndaysCard,
    magentoitemId,
    role,
    additionalImageRequired,
    itemType
  } = useSelector((state) => state.order);
  const { time: autoFocusTimer } = useSelector((state) => state[CONSUL].autoFocusTimer.data) || {};
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const { packingCompleteData, packingFailData, lsmCourierAwbData, isValidBarcode } = useSelector(
    (state) => state.packing
  );
  const { isLoading: isValidBarcodeLoading, failCount: validBarcodeFailCount } = isValidBarcode;

  const isBulkOrder = shipmentHeaders?.navChannel.toLowerCase().includes('bulk');
  const slidesToShow = isBulkOrder ? SLIDES_TO_SHOW_FOR_BULK : SLIDES_TO_SHOW;

  /** Variables to check is Rodenstock and Delaycard available */
  const rodenStock = !!(roden_stock_values && Object.keys(roden_stock_values).length);
  const tokaiCard = !!(tokai_card_values && Object.keys(tokai_card_values).length);
  const delayCard = delayOrderCard;

  // To show DropballCertificate
  // eslint-disable-next-line no-unused-vars

  /** State to maintain Barcode Input */
  const [barcodeInput, setBarcodeInput] = useState('');
  const [validBarcodeInput, setValidBarcodeInput] = useState('');

  // owndaysCard Modal
  const [isOwnDaysCardModalOpen, setIsOwnDaysCardModalOpen] = useState(false);

  /** Carousal Slides to show images in product */
  const getCarousalSlides = (images) =>
    images.map((image, index) => (
      // eslint-disable-next-line react/no-array-index-key
      <div key={`img_${image}`}>
        <img className={classes.img} src={`${image || ''}`} alt={`img-${index}`} />
      </div>
    ));

  /** To render Product Card */
  const slideHtml = (data, index) => {
    const barcode = data?.order?.barcode;
    const product = data?.order?.product;

    let productTitle = '';
    if (
      product?.brand?.length &&
      product?.size?.length &&
      product.classification &&
      TITLE_BRAND_MODEL.includes(`${product.classification}`)
    ) {
      productTitle = getProductTitleBy_Brand_Model({ product, isCapital: true });
    } else if (product?.value) {
      productTitle = getProductTitleBy_Value({ title: product?.value, isCapital: true });
    }

    return (
      <div
        key={`product_${index}`}
        className={`${classes.product} ${
          index % slidesToShow === 1 ? classes.marginLeftRightProduct : ''
        }`}
      >
        <div className={classes.productDesc}>
          {/* Product Images Carousel */}
          <div className={classes.productCarousel}>
            <Carousel
              customSettings={CAROUSEL_SETTINGS}
              currentSlide={0}
              slides={getCarousalSlides(product?.productImages || [])}
            />
          </div>
          {/* Brand Name */}
          <div className={classes.productTypeWrapper}>
            <div className={classes.productTitle}>{productTitle}</div>
          </div>
          {/* Product Details */}
          <div className={classes.productMetaInfo}>
            {product?.productId ? (
              <div>
                <div className={classes.productMetaLabel}>{packingLocal.PID}</div>
                <div className={classes.productMetaValue}>{product?.productId}</div>
              </div>
            ) : (
              ''
            )}
            {product?.frameType ? (
              <div>
                <div className={classes.productMetaLabel}>{packingLocal.FRAME_TYPE}</div>
                <div className={classes.productMetaValue}>{product?.frameType}</div>
              </div>
            ) : (
              ''
            )}
          </div>
          {/* Product Barcode */}
          {barcode ? (
            <div className={classes.barcodeProductTypeWrap}>
              <div className={classes.productBarcodeWrapper}>
                <div className={classes.productBarcodeLabel}>{packingLocal.BARCODE}</div>
                <div className={classes.productBarcodeValue}>
                  {barcode.substring(0, barcode.length - 3)}
                  <b>{barcode.substring(barcode.length - 3)}</b>
                </div>
              </div>
              {/* Product Type like Sunglasses, Eyeglasses */}
              {data?.product_type_detail ? (
                <ProductTypeLabel product_type_detail={data.product_type_detail} />
              ) : (
                ''
              )}
            </div>
          ) : (
            ''
          )}
        </div>
        {/* Quantity Wrapper */}
        <div className={classes.productQuantityWrapper}>
          {/* Quantity */}
          <div
            className={`${classes.prductQuanitySubWrap} ${
              lenses_by_parentuw[data.order.uwItemId] ? classes.backgroundF5 : ''
            }`}
          >
            <div className={classes.metaLabel}>{packingLocal.QTY}</div>
            <div className={classes.metaLabelValue}>
              {lenses_by_parentuw[data.order.uwItemId]
                ? lenses_by_parentuw[data.order.uwItemId] + 1
                : 1}
            </div>
          </div>
          {/* Frame and Lens */}
          {lenses_by_parentuw[data.order.uwItemId] ? (
            <>
              {/* Frame */}
              <div className={classes.prductQuanitySubWrap}>
                <div className={classes.metaLabel}>{packingLocal.FRAME}</div>
                <div className={classes.metaLabelValue}>1</div>
              </div>
              {/* Lens */}
              <div className={classes.prductQuanitySubWrap}>
                <div className={classes.metaLabel}>{packingLocal.LENS}</div>
                <div className={classes.metaLabelValue}>
                  {lenses_by_parentuw[data.order.uwItemId]}
                </div>
              </div>
            </>
          ) : (
            ''
          )}
        </div>
      </div>
    );
  };

  const cardImage = (key) => {
    switch (key) {
      case KEY_RODEN_STOCK:
        return (
          <img
            className={classes.rodenImg}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/Roden_stock.svg`}
            alt="img"
          />
        );
      case KEY_TOKAI_CARD:
        return (
          <img
            className={classes.rodenImg}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/tokai_card.png`}
            alt="img"
          />
        );
      case KEY_OWN_DAYS:
        return (
          <img
            className={classes.rodenImg}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/owndays.jpeg`}
            alt="OwnDays Card"
          />
        );
      case KEY_MONO_CARTON:
        return (
          <img
            className={classes.rodenImg}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/monocarton.jpeg`}
            alt="OwnDays Card"
          />
        );
      case KEY_SCREWDRIVER:
        return (
          <img
            className={classes.rodenImg}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/screwdriver.jpeg`}
            alt="OwnDays Card"
          />
        );
      default:
        return (
          <img
            className={classes.rodenImg}
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/Delay_card.svg`}
            alt="img"
          />
        );
    }
  };

  /** To render Roden stock or Delay Card */
  const renderCard = (key, index, isBulkCaseImage = false) => {
    let length = '1';
    if (key === KEY_RODEN_STOCK) {
      length = Object.keys(roden_stock_values).length;
    } else if (key === KEY_TOKAI_CARD) {
      length = Object.keys(tokai_card_values).length;
    }

    if (key === SHARK_TANK_IMAGE) {
      return (
        <Box
          className={`${classes.sharkTankImageContainer} ${
            index % slidesToShow === 1 ? classes.marginLeftRightProduct : ''
          }`}
        >
          <Box className={classes.sharkTankTitle}>
            {additionalImageRequired?.[0]?.sharkTankImageAlt ?? 'SHARK TANK'} ORDER
          </Box>
          <img
            className={classes.sharkTankImage}
            src={additionalImageRequired?.[0]?.sharkTankImage}
            alt={additionalImageRequired?.[0]?.sharkTankImageAlt}
          />
        </Box>
      );
    }

    return (
      <Box
        key={`product_${key}`}
        className={`${classes.rodenDelayCard} ${
          index % slidesToShow === 1 ? classes.marginLeftRightProduct : ''
        }`}
        ml={isBulkCaseImage ? 2 : 0}
      >
        {key === KEY_PRODUCT_CASE ? (
          <Box key={`product_${key}`} className={classes.productCase}>
            <img src={caseImage} alt="product case" />
          </Box>
        ) : (
          <>
            <div className={classes.emptyProductDesc}>
              <div className={classes.emptyProductInfo}>
                <div className={classes.rodenImgDiv}>{cardImage(key)}</div>
                <div className={classes.rodenTitle}>{CARD_TITLE[key ?? KEY_DELAY_CARD]}</div>
              </div>
              <div className={classes.productQuantityWrapper}>
                <div className={`${classes.prductQuanitySubWrap}`}>
                  <div className={classes.metaLabel}>{packingLocal.QTY}</div>
                  <div className={classes.metaLabelValue}>{length}</div>
                </div>
              </div>
            </div>
            <div className={classes.toPutInBagDiv}>To Put in Bag</div>
          </>
        )}
      </Box>
    );
  };

  /** OnClick functionality of Scan again to Complete */
  const keyPressHandler = (value) => {
    if (
      value?.trim()?.length &&
      inputBarcode &&
      value.trim().toLowerCase() === inputBarcode.toLowerCase()
    ) {
      if (owndaysCard && enableOwnDaysCard?.includes(facilityCode)) {
        setIsOwnDaysCardModalOpen(true);
        return;
      }
      const packingData = {
        packingDetails: {
          incrementId: products[0].order.incrementId,
          shipmentId: products[0].order.shippingPackageId,
          packerName: userDetail.empCode,
          status: 'DONE'
        },
        unicomOrderCode: orderBarcodeInfo.unicomOrderCode,
        magentoItemId: magentoitemId
      };

      dispatch(markPackingDone({ packingData }));
      setBarcodeInput('');
    } else {
      /** If input value is empty or wrong shipment id is scanned, Then show error message */
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: packingLocal.INVALID_CODE_SCANNED,
          severity: 'error'
        })
      );
      setBarcodeInput('');
    }
  };

  /** Used to Render Blank Card so that design will not break */
  const blankHtml = (index) => <div key={`blank_${index}`} className={classes.blankProduct} />;

  /** Method to show all the slides */
  const getSlides = () => {
    const slides = []; // Used to render all Slides
    let slidesPerRow = []; // Used to render Slides per row
    let count = 0;
    /** count variable is mainly used to give left/right margin to middle card */
    for (let i = 0; i < products.length; i += 1) {
      count += 1;
      slidesPerRow.push(slideHtml(products[i], i));
      if (count % slidesToShow === 0) {
        slides.push(
          <div key={i} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );

        if (products[i]?.caseImage && isBulkOrder) {
          slidesPerRow.push(renderCard(KEY_PRODUCT_CASE, slidesPerRow.length, true));
        }

        slidesPerRow = [];
      }
    }
    /** To show Roden Stock Card */
    if (rodenStock) {
      slidesPerRow.push(renderCard(KEY_RODEN_STOCK, slidesPerRow.length));
      if (slidesPerRow.length === slidesToShow) {
        slides.push(
          <div key={KEY_RODEN_STOCK} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }
    /** To show Tokai Card */
    if (tokaiCard) {
      slidesPerRow.push(renderCard(KEY_TOKAI_CARD, slidesPerRow.length));
      if (slidesPerRow.length === slidesToShow) {
        slides.push(
          <div key={KEY_TOKAI_CARD} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }
    /** To show Delay Card */
    if (delayCard) {
      slidesPerRow.push(renderCard(KEY_DELAY_CARD, slidesPerRow.length));
      if (slidesPerRow.length === slidesToShow) {
        slides.push(
          <div key={KEY_DELAY_CARD} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }
    /** To show OwnDays Card */
    if (owndaysCard) {
      slidesPerRow.push(renderCard(KEY_OWN_DAYS, slidesPerRow.length));
      if (slidesPerRow.length === slidesToShow) {
        slides.push(
          <div key={KEY_OWN_DAYS} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }
    /** To show Product Case Image (Confirmed with sreeram to add only
      condition for caseImage if it is Shark Tank Order) */
    if (caseImage && !isBulkOrder) {
      slidesPerRow.push(renderCard(KEY_PRODUCT_CASE, slidesPerRow.length));
      if (slidesPerRow.length === SLIDES_TO_SHOW) {
        slides.push(
          <div key={KEY_PRODUCT_CASE} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }
    /** To show Shark Tank Image */
    if (additionalImageRequired?.length > 0 && additionalImageRequired?.[0]?.sharkTankImage) {
      slidesPerRow.push(renderCard(SHARK_TANK_IMAGE, slidesPerRow.length));
      if (slidesPerRow.length === slidesToShow) {
        slides.push(
          <div key={SHARK_TANK_IMAGE} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }
    // slidesPerRow.push(scanAgainToCompleteHtml(KEY_SCAN_AGAIN_TO_COMPLETE, slidesPerRow.length));

    for (let index = 0; index < packingAccessories?.length; index++) {
      const item = packingAccessories[index];
      const filteredAccessory = accessoriesKey[item.toLowerCase()] ?? null;
      if (filteredAccessory) {
        slidesPerRow.push(renderCard(filteredAccessory, slidesPerRow.length));
      }
      if (slidesPerRow.length === SLIDES_TO_SHOW) {
        slides.push(
          <div key={KEY_OWN_DAYS} className={classes.productsRoot}>
            {slidesPerRow}
          </div>
        );
        slidesPerRow = [];
      }
    }

    /** Adding Blank card in end of the row to maintain CSS (For broken row design) */
    while (slidesPerRow.length !== slidesToShow) {
      slidesPerRow.push(blankHtml(slidesPerRow.length));
    }

    /** Finally adding Scan again to complete Card (last card) */
    slides.push(
      <div key={KEY_SCAN_AGAIN_TO_COMPLETE} className={classes.productsRoot}>
        {slidesPerRow}
      </div>
    );
    slidesPerRow = [];
    return slides;
  };

  useEffect(() => {
    dispatch(getConsulKeyValue(['featureToggle']));
  }, []);

  useEffect(() => {
    dispatch(getConsulKeyValue(['autoFocusTimer']));
    return () => {
      console.log('PackingEnableScanningOff Unmounted');
    };
  }, []);

  /** To give focus to Scan again to complete input */
  useEffect(() => {
    let timeoutId;
    const targetId = isOwnDaysCardModalOpen
      ? OWN_DAYS_CARD_BARCODE_SCANNER
      : KEY_SCAN_AGAIN_TO_COMPLETE;
    const targetEl = document.getElementById(targetId);

    if (targetEl) {
      timeoutId = setTimeout(() => {
        targetEl.focus();
      }, autoFocusTimer || 0);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [barcodeInput, inputBarcode, isOwnDaysCardModalOpen, autoFocusTimer]);

  /** To call Courier AWB api call to fetch information of courier number. */
  useEffect(() => {
    if (
      orderDetails?.length &&
      shipmentHeaders?.shippingPackageId &&
      orderBarcodeInfo &&
      Object.keys(orderBarcodeInfo).length &&
      orderBarcodeInfo.isFranchiseOrderShipToCustomer &&
      !orderBarcodeInfo.isFranchiseOrderShipToCustomer.toLowerCase().includes('ship to store')
    ) {
      // dispatch(lsmCourierAwb({ shippingPackageId: shipmentHeaders.shippingPackageId,
      // lsmCourierHost: orderBarcodeInfo.awb }));
    }
  }, [orderDetails, orderBarcodeInfo, shipmentHeaders]);

  /** On Mrak Packing Done, used to change screen to Packing Summary */
  useEffect(() => {
    if (packingCompleteData) {
      dispatch(getOrderDetailsReset());
      navigate('/packing-summary');
    }
  }, [packingCompleteData]);

  /** To Show error when got Error in Mark packing done */
  useEffect(() => {
    if (packingFailData) {
      if (packingFailData?.meta?.displayMessage) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: packingFailData.meta.displayMessage,
            severity: 'error'
          })
        );
      }
    }
  }, [packingFailData]);

  useEffect(() => {
    if (validBarcodeFailCount === 2 && role !== 'USER_SUPERVISOR') {
      navigate('/packing');
      dispatch(getOrderDetailsReset());
      dispatch(isBarcodeValidReset());
    }
  }, [validBarcodeFailCount, role]);

  /** Calculating Total Quantity including RodenStock and DelayCard */
  let Total_qty = orderDetails.length - accessories.length;
  let total_items = products.length;
  if (rodenStock) {
    Total_qty += Object.keys(roden_stock_values).length;
  }
  if (tokaiCard) {
    Total_qty += Object.keys(tokai_card_values).length;
  }
  if (delayCard) {
    Total_qty += 1;
    total_items += 1;
  }

  const checkBarcodeIsValid = (barcode) => {
    if (barcode.trim()) {
      const barcodeValidPayload = {
        incrementId: products[0].order.incrementId,
        shipmentId: products[0].order.shippingPackageId,
        barcode,
        magentoItemId: magentoitemId,
        isOwnDays: owndaysCard
      };
      const packingData = {
        packingDetails: {
          incrementId: products[0].order.incrementId,
          shipmentId: products[0].order.shippingPackageId,
          packerName: userDetail.empCode,
          status: 'DONE'
        },
        unicomOrderCode: orderBarcodeInfo.unicomOrderCode,
        ownDaysBarcode: validBarcodeInput,
        magentoItemId: magentoitemId
      };
      dispatch(isBarcodeValidLoad({ barcodeValidPayload, packingData }));
    }
  };

  return (
    <>
      <div className={classes.productContainerRoot}>
        <div className={classes.productDataDiv}>
          <div className="display-flex justify-content-space-between wd-100">
            <div style={{ display: 'flex' }}>
              {/* Total Quantity */}
              <div>
                <div className={classes.metaLabel}>{packingLocal.TOTAL_QTY}:</div>
                <div className={classes.metaLabelValue}>{Total_qty}</div>
              </div>
              <div className="mr-l8">
                <div className={classes.metaLabel}>{packingLocal.TOTAL_ITEMS}:</div>
                <div className={classes.metaLabelValue}>{total_items}</div>
              </div>
            </div>
            <div style={{ width: '300px' }}>
              <LkInput
                autoFocus
                className={classes.scanAgainInput}
                fullWidth
                id={KEY_SCAN_AGAIN_TO_COMPLETE}
                size="small"
                variant="outlined"
                placeholder={packingLocal.SCAN_BARCODE}
                onChange={(e) => setBarcodeInput(e.target.value)}
                value={barcodeInput}
                disabled={isLoading}
                onKeyUp={(e) => e.key === 'Enter' && keyPressHandler(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <img
                        style={{ width: '25px' }}
                        alt="barcode-img"
                        src={`${import.meta.env.VITE_STATIC_SERVER}/images/barcodeScan.svg`}
                      />
                    </InputAdornment>
                  )
                }}
              />
            </div>
            <div />
          </div>

          {/* Courier code and AWB number */}
          {lsmCourierAwbData ? (
            <div>
              <div className={classes.metaLabel}>
                {`${lsmCourierAwbData.courierCode ? lsmCourierAwbData.courierCode : ''} ${
                  packingLocal.AWB
                }:`}
              </div>
              <div className={classes.metaLabelValue}>
                {lsmCourierAwbData.awb ? lsmCourierAwbData.awb : ''}
              </div>
            </div>
          ) : (
            ''
          )}
        </div>
        {/* To show Product Cards */}
        <div
          style={{
            display: 'flex',
            flexDirection: accessories?.length ? 'row' : 'column'
          }}
        >
          <div
            className={
              applyGridCss.includes(itemType) ? classes.accessoriesOrder : classes.flexAuto
            }
          >
            {getSlides()}
          </div>
          {/* Show accessories table */}
          {/* {accessories && accessories.length ?
                    <div style={{ flex: '0.5' }}>
                        {accessoriesListing()}
                    </div>
                    : ''
                } */}
        </div>
      </div>

      {isOwnDaysCardModalOpen && (
        <OwnDaysCardModal
          isOwnDaysCardModalOpen={isOwnDaysCardModalOpen}
          setIsOwnDaysCardModalOpen={setIsOwnDaysCardModalOpen}
          classes={classes}
          validBarcodeInput={validBarcodeInput}
          setValidBarcodeInput={setValidBarcodeInput}
          isValidBarcodeLoading={isValidBarcodeLoading}
          checkBarcodeIsValid={checkBarcodeIsValid}
          OWN_DAYS_CARD_BARCODE_SCANNER={OWN_DAYS_CARD_BARCODE_SCANNER}
        />
      )}
    </>
  );
};

export default PackingEnableScanningOff;
