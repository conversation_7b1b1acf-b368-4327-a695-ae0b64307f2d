/* eslint-disable no-param-reassign */
import React, { useState } from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import { useSelector, useDispatch } from 'react-redux';
import Spinner from 'components/Spinner/Spinner';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import TableCount from 'components/common/TableCount';
import LkToolTip from 'components/ToolTip/ToolTip';
import ProductToolTip from 'components/common/ProductToolTip';
import TableSortLabel from '@mui/material/TableSortLabel';
import AutoSelectFilter from 'components/common/AutoSelectFilter';
import { searchBlockedPidsLoad } from '../../redux/actionCreators/grnListing';
import useWindowResize from '../../common/useWindowResize';
import { StyledTableCell, StyledTableRow } from '../../theme/table.theme';

const styles = makeStyles()(() => ({
  container: {
    borderTop: '1px solid #DDDDDD',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
    maxHeight: 600,
    position: 'relative'
  },
  vendorName: {
    width: 160
  },
  tableCellMaxWidth: {
    width: 180
  }
}));

const PAGE_SIZE = 25;

const BloackedProductsTable = ({
  setSortkey,
  onChangeFilterList,
  selectedFilterList,
  sortKey,
  sortingData,
  objPayload,
  overrideFun,
  actionValues
}) => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 300);
  const [sortType, setSortType] = useState({
    grn_code: 'desc',
    product_id: 'desc',
    product_created_by: 'desc',
    product_invoice_quantity: 'desc',
    product_estimated_quantity: 'desc',
    product_sampling_percent: 'desc',
    product_qc_pass: 'desc',
    product_qc_fail: 'desc',
    failed_since: 'desc'
  });

  const { blockedPids, blockedLoading, totalBlockedPidRecordsCount } = useSelector(
    (state) => state.grnListing
  );
  const { autoSuggestionListBLOCKED_GRN_Number, autoSuggestionListBLOCKED_Created_By } =
    useSelector((state) => state.filters);

  const sortTable = (key) => {
    if (key) {
      const st = sortType[key] === 'ASC' ? 'DESC' : 'ASC';
      const obj = {};
      obj[key] = st;
      setSortkey(key);
      setSortType({ ...sortType, ...obj });
      sortingData.key = key;
      sortingData.order = st;
      objPayload.payload.pageRequest.sortKey = key;
      objPayload.payload.pageRequest.sortOrder = st;
      objPayload.payload.pageRequest.pageNumber = 0;
      objPayload.isReset = true;
      dispatch(searchBlockedPidsLoad(objPayload));
    }
  };

  const callFilterComponent = (name, marginLeft) => (
    <Box pl={0.5}>
      {name === 'GRN' && (
        <AutoSelectFilter
          listData={autoSuggestionListBLOCKED_GRN_Number}
          onSubmit={onChangeFilterList}
          selectedFilterList={selectedFilterList}
          marginLeft={marginLeft}
          columnName="BLOCKED_GRN_Number"
          apiName="grnProduct"
        />
      )}
      {name === 'Created By' && (
        <AutoSelectFilter
          listData={autoSuggestionListBLOCKED_Created_By}
          onSubmit={onChangeFilterList}
          selectedFilterList={selectedFilterList}
          marginLeft={marginLeft}
          columnName="BLOCKED_Created_By"
          apiName="grnProduct"
        />
      )}
    </Box>
  );

  const scrollTable = (event) => {
    const tBody = event.target;
    const totalPages = Math.floor(totalBlockedPidRecordsCount / PAGE_SIZE);
    if (tBody.scrollTop >= tBody.scrollHeight - 800) {
      if (!blockedLoading && objPayload.payload.pageRequest.pageNumber < totalPages) {
        dispatch(searchBlockedPidsLoad(objPayload));
      }
    }
  };

  const headerConfig = [
    {
      name: 'GRN',
      key: 'grn_code',
      align: 'left',
      supportFilter: true,
      marginLeft: '20px',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: 'Product ID',
      key: 'product_id',
      align: 'left',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: 'Unicomm GRN',
      key: 'unicom_grn_code',
      align: 'left',
      className: classes.vendorName,
      style: { minWidth: '150px', maxWidth: '150px' }
    },
    {
      name: 'Created By',
      key: 'product_created_by',
      align: 'left',
      supportFilter: true,
      marginLeft: '',
      style: { minWidth: '130px', maxWidth: '130px' }
    },
    {
      name: 'Invoice Qty',
      key: 'product_invoice_quantity',
      align: 'left',
      style: { minWidth: '130px', maxWidth: '130px' }
    },
    {
      name: 'Estim. Qty',
      key: 'product_estimated_quantity',
      align: 'left',
      className: classes.vendorName,
      style: { minWidth: '130px', maxWidth: '130px' }
    },
    {
      name: 'Sampling',
      key: 'product_sampling_percent',
      align: 'left',
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: 'QC Pass',
      key: 'product_qc_pass',
      align: 'left',
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: 'QC Fail',
      key: 'product_qc_fail',
      align: 'left',
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: 'Failed Since',
      key: '',
      align: 'left',
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: 'Take Action',
      key: '',
      align: 'right',
      className: classes.tableCellMaxWidth,
      pr: 1,
      style: { minWidth: '180px', maxWidth: '180px' }
    }
  ];

  return (
    <div>
      <Box>
        <TableContainer
          id="tableBody"
          style={{ height: tableHeight, overflowX: 'scroll' }}
          onScroll={(event) => scrollTable(event)}
          className={classes.container}
        >
          <Table stickyHeader aria-label="sticky table" className={classes.table}>
            <TableHead>
              <StyledTableRow>
                {headerConfig.map((header) => {
                  const {
                    name,
                    key,
                    marginLeft,
                    style,
                    align,
                    supportFilter,
                    className = '',
                    pr = 0
                  } = header;
                  return (
                    <StyledTableCell
                      key={name}
                      style={style}
                      onKeyPress={(e) => e.which === 13 && sortTable(key)}
                      align={align}
                      sortDirection={false}
                      className={className}
                    >
                      <Box display={align === 'right' ? '' : 'flex'}>
                        <Box onClick={() => sortTable(key)} pr={pr}>
                          <span className="cursor-pointer">{name}</span>
                          {sortKey === key && (
                            <TableSortLabel
                              active
                              direction={sortType[key] === 'ASC' ? 'asc' : 'desc'}
                            />
                          )}
                        </Box>
                        {supportFilter && callFilterComponent(name, marginLeft)}
                      </Box>
                    </StyledTableCell>
                  );
                })}
              </StyledTableRow>
            </TableHead>
            <TableBody>
              {
                blockedPids?.map((data) => {
                  const {
                    grn_code,
                    product_id,
                    product_created_by,
                    product_invoice_quantity,
                    product_estimated_quantity,
                    product_sampling_percent,
                    product_qc_pass,
                    product_qc_fail,
                    failed_since,
                    category_id,
                    po_num
                  } = data;
                  const { manual_override_allowed, manual_override_rejected } = actionValues;
                  const testOverRideAllowed = manual_override_allowed.some(
                    (t) => t.grn_code === grn_code && t.pid === product_id
                  );
                  const testOverRideRejected = manual_override_rejected.some(
                    (t) => t.grn_code === grn_code && t.pid === product_id
                  );
                  return (
                    <React.Fragment key={grn_code}>
                      <StyledTableRow>
                        <StyledTableCell align="left">
                          <div className="">{grn_code}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <LkToolTip
                            title={
                              <ProductToolTip
                                poId={po_num}
                                pid={product_id}
                                classification={category_id}
                              />
                            }
                            width={category_id === 11356 ? '300px' : '756px'}
                          >
                            <div className="">{product_id}</div>
                          </LkToolTip>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{data.unicom_grn_code}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{product_created_by}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{product_invoice_quantity}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{product_estimated_quantity}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{product_sampling_percent}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{product_qc_pass}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{product_qc_fail}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{failed_since}</div>
                        </StyledTableCell>
                        <StyledTableCell align="right">
                          <Box pr={1}>
                            <CheckCircleIcon
                              className="mr-r12"
                              color={testOverRideAllowed ? 'primary' : 'disabled'}
                              onClick={() => overrideFun(true, grn_code, product_id)}
                            />
                            <CancelIcon
                              color={testOverRideRejected ? 'error' : 'disabled'}
                              onClick={() => overrideFun(false, grn_code, product_id)}
                            />
                          </Box>
                        </StyledTableCell>
                      </StyledTableRow>
                    </React.Fragment>
                  );
                })
                //     })
              }
              {!blockedLoading && !blockedPids?.length && (
                <StyledTableRow>
                  <StyledTableCell colSpan={11} align="center">
                    No Data...
                  </StyledTableCell>
                </StyledTableRow>
              )}

              {blockedLoading && (
                <StyledTableRow>
                  <StyledTableCell colSpan={11} align="center">
                    <Spinner className="display-grid-center mr-t10 mr-b10" />
                  </StyledTableCell>
                </StyledTableRow>
              )}
            </TableBody>
          </Table>
          {blockedPids && (
            <TableCount
              loading={blockedLoading}
              count={blockedPids.length}
              totalCount={totalBlockedPidRecordsCount}
            />
          )}
        </TableContainer>
        {/* {(blockedLoading) && <Spinner className="display-grid-center mr-t10 mr-b10" />} */}
      </Box>
    </div>
  );
};

export default React.memo(BloackedProductsTable);
