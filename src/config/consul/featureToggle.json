{"enableBarcodeHistoryOperationId": true, "qcCaseImageFacilityList": ["NXS2", "SGNXS1"], "enableOwnDaysCard": [], "markAsBadBarcode": false, "forcePrintEnabled": ["NXS1"], "showCompletePutAwayFacilities": {"SGNXS1": {"executionUserId": "SGNXS1_PUTAWAY", "sources": ["NEXS", "ATHENA"]}, "NXS2": {"executionUserId": "NXS2_RETURNS", "sources": ["ATHENA"], "types": ["PUTAWAY_GATEPASS_ITEM", "PUTAWAY_INVENTORY_ADJUSTMENT"]}}, "markDamageStatus": ["IN_TRAY", "PICKED", "EDGING", "PENDING_CUSTOMIZATION"], "stockOutFallbackFacilities": ["NXS2", "SGNXS1"], "stockOutFallbackErrorMessages": ["PicklistOrderItem not found", "Unable to fetch response from unicom", "Facility selected in header is wrong", "Addverb order not allow to pick from here", "Unable to scan barcode", "Item is not found at picklistOrderItem table"], "showStockOut": {"NXS2": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"], "SGNXS1": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"], "defaultStatus": ["IN_PICKING"]}, "showMarkFullFillableForUff": {"NXS2": {"status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"], "empCodes": ["LSP06123", "LSP03136", "LSP06074", "LSP06291", "T0005078", "T0004359", "LSP01861", "138663", "141874"]}, "NXS1": {"status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"], "empCodes": ["LSP06291", "LSP08139", "155358", "128311"]}, "SGNXS1": {"status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"], "empCodes": ["LSP08025", "LSS00341", "112148"]}}, "wareHouseRecieve": {"NXS2": {"vendors": ["Lenskart Eyetech Solutions Pvt. Ltd."]}}, "autoToManualConditions": {"NXS1": {"status": ["CREATED", "JIT_PROCESSING", "IN_PICKING", "PICKED", "BLANK_IN_TRAY"], "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "arisetty.r<PERSON><PERSON>@lenskart.in", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}}, "inventoryExportBtn": ["NXS2"], "notFound": {"SGNXS1": {"status": ["IN_PICKING"], "NFStatus": {"NOT_FOUND": "NF", "TEMP_NOT_FOUND": "TNF"}}}, "qcLensPowerDetailsFacilityList": {"NXS2": "https://qc-lensometer.lenskart.com"}, "doPicking": {"printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"], "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"], "channelsToExclude": ["INT_BULKTOVENDOR"]}, "superVisorPickingSkipped": {"NXS1": {"showDiscarded": true, "showMoveToManual": true, "showMoveToDiscard": false}}, "allowedMoveToJITStatuses": ["IN_PICKING", "PICKED", "IN_TRAY", "EDGING", "PENDING_CUSTOMIZATION", "QC_FAILED", "PROCESSING", "CREATED"], "allowedMoveToJITFacilities": ["NXS2", "UAE1", "SGNXS1"], "allowedMoveToJITEmpCodes": ["LSP07099"], "pickingScanAllowDuplicates": ["/PickingScan/distributor"], "shouldShowHsnColumnForDoPicking": false, "clExpiryCheckFacilityList": {"BR01": 30, "NXS2": 30, "DXB1": 30, "SGNXS1": 30}}