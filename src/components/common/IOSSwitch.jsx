import React from 'react';
import { Switch } from '@mui/material';

import { withStyles } from 'tss-react/mui';

export const IOSSwitch = withStyles(({ classes, ...props }) => (
  <Switch
    focusVisibleClassName={classes.focusVisible}
    disableRipple
    classes={{
      root: classes.root,
      switchBase: classes.switchBase,
      thumb: classes.thumb,
      track: classes.track,
      checked: classes.checked
    }}
    {...props}
  />
), (theme, _params, classes) => ({
  root: {
    width: 42,
    height: 22,
    padding: 0,
    margin: theme.spacing(1)
  },
  switchBase: {
    padding: 1.5,
    [`&.${classes.checked}`]: {
      transform: 'translateX(20px)',
      color: theme.palette.common.white,
      [`& + .${classes.track}`]: {
        backgroundColor: theme.palette.primary,
        opacity: 1,
        border: 'none'
      }
    },
    [`&.${classes.focusVisible} .${classes.thumb}`]: {
      color: '#52d869',
      border: '6px solid #fff'
    }
  },
  thumb: {
    width: 18,
    height: 18
  },
  track: {
    borderRadius: 22 / 2,
    border: `1px solid ${theme.palette.grey[400]}`,
    backgroundColor: theme.palette.grey[50],
    opacity: 1,
    transition: theme.transitions.create(['background-color', 'border'])
  },
  checked: {},
  focusVisible: {}
}));
