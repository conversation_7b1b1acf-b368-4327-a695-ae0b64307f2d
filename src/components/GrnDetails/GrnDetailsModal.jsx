import React from 'react';
import { useSelector } from 'react-redux';
import Backdrop from '@mui/material/Backdrop';
import { Button, CircularProgress, Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';

import LkInput from 'components/MaterialUi/LkInput';
import { LOCALISATION } from 'redux/reducers/localisation';

import LkModal from '../Modal/Modal';
import FlexBox from '../core/FlexBox';
import { InfoTable } from '../common';
import SubtitleInfoStrip from '../common/SubtitleInfoStrip';
import Spinner from '../Spinner/Spinner';
import ChipBox from '../common/ChipBox';
import { formatDateDashedSeparated } from '../../utils/helpers';

const useStyles = makeStyles()((theme) => ({
  content: {
    marginTop: theme.spacing(2)
  },
  table: {
    maxHeight: 'calc(100vh - 286px)'
  },
  textAlignCenter: {
    textAlign: 'center'
  },
  input: {
    width: theme.spacing(10)
  },
  chip: {
    borderRadius: 4,
    marginLeft: 22,
    height: 24,
    color: theme.palette.success.main,
    borderColor: '#45C476'
  },
  modalField: {
    width: 200,
    borderColor: '#DDDDDD'
  }
}));

const GrnDetailsModal = ({
  showModal,
  onSubmit,
  setShowModal,
  invoiceText,
  infoTableData,
  vendorText,
  subtitleInfoStripData,
  chipBoxData,
  inputsValue,
  createVendorInvoiceLoading,
  status,
  titleKey = 'Invoice'
}) => {
  const { classes } = useStyles();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_DETAIL);
  return (
    <LkModal
      open={showModal}
      upperHeading={CONTENT.GRN_DETAILS}
      handleClose={() => setShowModal(false)}
    >
      <div className="pos-rel" style={{ overflow: 'scroll' }}>
        <Backdrop className={classes.backdrop} open={false}>
          <Spinner />
        </Backdrop>
        <FlexBox pt={2.5} justifyContent="space-between" width="750px">
          <Box border={1} borderRadius="8px" borderColor="grey.200">
            <InfoTable
              noBorder
              titleKey={titleKey}
              titleValue={invoiceText}
              data={infoTableData}
              header={vendorText.name}
            />
          </Box>

          <Box width={430}>
            <SubtitleInfoStrip data={subtitleInfoStripData} />
            <FlexBox mt={3.75} justifyContent="space-between">
              <LkInput
                className={classes.modalField}
                label={CONTENT.VENDOR_INV_NO}
                variant="outlined"
                value={inputsValue.VendorInvoiceNo}
              />

              <LkInput
                className={classes.modalField}
                value={formatDateDashedSeparated(inputsValue.VendorInvoiceDate)}
                label={CONTENT.VENDOR_INV_DATE}
                type="date"
                variant="outlined"
                inputProps={{
                  max: formatDateDashedSeparated(Date.now())
                }}
              />
            </FlexBox>
            <FlexBox mt={3.75} justifyContent="space-between">
              <LkInput
                className={classes.modalField}
                value={inputsValue.B2BInvoiceDate}
                label={CONTENT.B2B_INV_DATE}
                variant="outlined"
              />
              <LkInput
                className={classes.modalField}
                value={inputsValue.HandoverBy}
                label={CONTENT.HANDOVER_BY}
                variant="outlined"
              />
            </FlexBox>
            <FlexBox mt={3.75} justifyContent="space-between">
              <LkInput
                className={classes.modalField}
                value={inputsValue.SendtoParty}
                label={CONTENT.SEND_TO_PARTY}
                variant="outlined"
              />
            </FlexBox>
            <ChipBox maxHeight={94} mt={3.75} value={chipBoxData} label={CONTENT.BLOCKED_PIDS} />
          </Box>
        </FlexBox>

        <Box m="auto" mt={3.25} className={classes.textAlignCenter}>
          <Button
            style={{ marginRight: 10, width: 192 }}
            color="primary"
            onClick={() => setShowModal(false)}
            variant="outlined"
          >
            {!createVendorInvoiceLoading ? CONTENT.CANCEL : <CircularProgress color="inherit" />}
          </Button>
          <Button
            color="primary"
            style={{ width: 192 }}
            type="submit"
            disabled={status === 'closed'}
            onClick={() => onSubmit()}
            variant="contained"
          >
            {!createVendorInvoiceLoading ? (
              CONTENT.SAVE_AND_CLOSE
            ) : (
              <CircularProgress color="inherit" />
            )}
          </Button>
        </Box>
      </div>
    </LkModal>
  );
};

export default React.memo(GrnDetailsModal);
