/* eslint-disable no-shadow */
import React, { useState } from 'react';
import { Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import LkInput from 'components/MaterialUi/LkInput';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import IconButton from '@mui/material/IconButton';
import FilterWrapper from './FilterWrapper';
import { getBarcodeFromURL } from '../../utils/helpers';

const useStyles = makeStyles()(() => ({
  resize: {
    padding: '0px',
    paddingLeft: '5px',
    height: '34px',
    fontSize: '12px'
  }
}));

const InputFilter = ({
  onSubmit,
  marginLeft,
  name,
  columnName,
  selectedFilterList = [],
  clearOnSubmit = false
}) => {
  const { classes } = useStyles();
  const [value, setValue] = useState('');
  const [open, setOpen] = useState(false);

  const clearSearchField = () => {
    setOpen(!open);
    if (clearOnSubmit) {
      setValue('');
    }
  };

  const submitData = () => {
    if (value) {
      const extractedBarcode = getBarcodeFromURL(value);
      const testExisting = selectedFilterList.findIndex(
        (t) => t.key === columnName && t.value === extractedBarcode
      );
      if (testExisting === -1) {
        onSubmit([{ key: columnName, value: extractedBarcode, name }]);
      }
    }
    clearSearchField();
  };

  const onSubmitWithEnter = (event) => {
    event.stopPropagation();
    if (event.which === 13) {
      submitData();
    }
  };

  return (
    <FilterWrapper
      disabled={value}
      open={open}
      setOpen={setOpen}
      width="210px"
      onSubmit={submitData}
      marginLeft={marginLeft}
      onCloseUpdateFilter={() => null}
      dataCy={columnName}
    >
      <Box pl={2} pr={2} pt={2} pb={1}>
        <LkInput
          label="Search here..."
          fullWidth
          value={value}
          onChange={({ target: { value } }) => setValue(value)}
          onKeyPress={onSubmitWithEnter}
          data-cy={`${columnName}-input-field`}
          size="small"
          InputProps={{
            classes: { input: classes.resize },
            endAdornment: (
              <IconButton
                size="small"
                aria-label="toggle password visibility"
                onClick={() => setValue('')}
                edge="end"
              >
                {value !== '' && <HighlightOffIcon fontSize="small" />}
              </IconButton>
            )
          }}
        />
      </Box>
    </FilterWrapper>
  );
};

export default InputFilter;
