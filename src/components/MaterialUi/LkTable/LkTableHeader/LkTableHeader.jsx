import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import TableHead from '@mui/material/TableHead';
import TableSortLabel from '@mui/material/TableSortLabel';
import Box from '@mui/material/Box';
import { StickyTableCell, StyledTableCell, StyledTableRow } from '../LkTableCommon';
import LkTableHeaderFilter from './LkTableHeaderFilter';

const useStyles = makeStyles()(() => ({
  rightStickyCell: {
    right: 0
  },
  leftStickyCell: {
    left: 0
  },
  headerRowCell: {
    fontSize: 14,
    lineHeight: '24px',
    letterSpacing: '0.15px',
    color: '#3C3C3C',
    fontWeight: 500
  }
}));

const LkTableHeader = ({
  headerConfig,
  tableData,
  isDataFetching,
  columnWidth,
  setFilters = null,
  sortKey = null,
  sortType = null,
  changeSortOrder = null
}) => {
  const { classes, cx } = useStyles();

  const formatCellContent = useCallback(
    (
      {
        name,
        formatHeader = null,
        supportSort = false,
        filterData = null,
        columnName = null,
        customSort,
        customSortFun
      },
      align,
      key
    ) => {
      if (formatHeader) {
        if (isDataFetching || tableData.length === 0) {
          return null;
        }
        return formatHeader(tableData);
      }

      // default header content (sorting, filtering, etc.)
      const tempColumnName = columnName || key;
      const onClickSortFun = () => {
        if (customSort) {
          customSortFun();
        }
        changeSortOrder(tempColumnName, customSort);
      };

      return (
        <Box display="flex" alignItems="center" justifyContent={align}>
          <Box onClick={supportSort ? onClickSortFun : null}>
            <span className="cursor-pointer">{name}</span>
            {supportSort && sortKey === tempColumnName && (
              <TableSortLabel active direction={sortType === 'ASC' ? 'asc' : 'desc'} />
            )}
          </Box>
          <LkTableHeaderFilter submitFilter={setFilters} {...filterData} name={name} />
        </Box>
      );
    },
    [tableData, sortKey, sortType]
  );

  return (
    <TableHead component="div">
      <StyledTableRow component="div" isheader="true">
        {headerConfig.map(
          (
            {
              key,
              align = 'left',
              style = {},
              leftSticky = false,
              rightSticky = false,
              hideColumn = false,
              ...columnConfig
            },
            index
          ) => {
            if (hideColumn) {
              return null;
            }
            if (leftSticky || rightSticky) {
              return (
                <StickyTableCell
                  component="div"
                  data-cy={`table-header-cell-${index}`}
                  style={{ minWidth: columnWidth, maxWidth: columnWidth, ...style }}
                  className={cx(classes.headerRowCell, {
                    [classes.leftStickyCell]: leftSticky,
                    [classes.rightStickyCell]: rightSticky
                  })}
                  key={key}
                  align={align}
                >
                  {formatCellContent(columnConfig, align, key)}
                </StickyTableCell>
              );
            }
            return (
              <StyledTableCell
                component="div"
                data-cy={`table-header-cell-${index}`}
                key={key}
                align={align}
                style={{ minWidth: columnWidth, maxWidth: columnWidth, ...style }}
                className={classes.headerRowCell}
              >
                {formatCellContent(columnConfig, align, key)}
              </StyledTableCell>
            );
          }
        )}
      </StyledTableRow>
    </TableHead>
  );
};

export default React.memo(LkTableHeader);

LkTableHeader.propTypes = {
  headerConfig: PropTypes.arrayOf(PropTypes.instanceOf(Object).isRequired).isRequired,
  setFilters: PropTypes.func,
  sortKey: PropTypes.string,
  sortType: PropTypes.string,
  changeSortOrder: PropTypes.func,
  columnWidth: PropTypes.number.isRequired
};
