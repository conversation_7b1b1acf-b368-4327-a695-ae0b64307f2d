const NexsConfig = {
    "po": {},
    "invoice": {},
    "grn": {
        "fail_barcodes": {
            "Eyeframe": {
                "header": [
                    "Text Code",
                    "BarCode",
                    "QC Fail Reason"
                ],
                "content": [
                    {
                        "textCode": "101",
                        "Barcode": "101q",
                        "QC Fail Reason": "Frame Scratch"
                    },
                    {
                        "textCode": "102",
                        "Barcode": "101q",
                        "QC Fail Reason": "Frame Broken"
                    },
                    {
                        "textCode": "103",
                        "Barcode": "101q",
                        "QC Fail Reason": "Printing issue"
                    },
                    {
                        "textCode": "105",
                        "Barcode": "101q",
                        "QC Fail Reason": "Out of po"
                    },
                    {
                        "textCode": "106",
                        "Barcode": "101q",
                        "QC Fail Reason": "Wrong Product received"
                    },
                    {
                        "textCode": "107",
                        "Barcode": "101q",
                        "QC Fail Reason": "Short received"
                    },
                ]
            },
            "Sunglasses": {
                "header": [
                    "Text Code",
                    "BarCode",
                    "QC Fail Reason"
                ],
                "content": [
                    {
                        "textCode": "101",
                        "Barcode": "101q",
                        "QC Fail Reason": "Frame Scratch"
                    },
                    {
                        "textCode": "102",
                        "Barcode": "101q",
                        "QC Fail Reason": "Frame Broken"
                    },
                    {
                        "textCode": "103",
                        "Barcode": "101q",
                        "QC Fail Reason": "Printing issue"
                    },
                    {
                        "textCode": "105",
                        "Barcode": "101q",
                        "QC Fail Reason": "Out of po"
                    },
                    {
                        "textCode": "106",
                        "Barcode": "101q",
                        "QC Fail Reason": "Wrong Product received"
                    },
                    {
                        "textCode": "107",
                        "Barcode": "101q",
                        "QC Fail Reason": "Short received"
                    },
                    {
                        "textCode": "108",
                        "Barcode": "101q",
                        "QC Fail Reason": "Lens Scratch"
                    }
                ]
            },
            "Contact Lens": {
                "header": [
                    "Text Code",
                    "BarCode",
                    "QC Fail Reason"
                ],
                "content": [
                    {
                        "textCode": "101",
                        "Barcode": "101q",
                        "QC Fail Reason": "Dirty Box"
                    },
                    {
                        "textCode": "102",
                        "Barcode": "101q",
                        "QC Fail Reason": "Box Damaged"
                    },
                    {
                        "textCode": "103",
                        "Barcode": "101q",
                        "QC Fail Reason": "Power Not Visible on Box"
                    },
                    {
                        "textCode": "104",
                        "Barcode": "101q",
                        "QC Fail Reason": "Expired Product"
                    },
                ]
            },
            "Prescription Lens": {
                "header": [
                    "Text Code",
                    "BarCode",
                    "QC Fail Reason"
                ],
                "content": [
                    {
                        "textCode": "101",
                        "Barcode": "101q",
                        "QC Fail Reason": "Scratch/Damaged"
                    },
                    {
                        "textCode": "102",
                        "Barcode": "101q",
                        "QC Fail Reason": "Chipped/Damaged"
                    },
                    {
                        "textCode": "105",
                        "Barcode": "101q",
                        "QC Fail Reason": "Out of po"
                    },
                    {
                        "textCode": "107",
                        "Barcode": "101q",
                        "QC Fail Reason": "Short received"
                    }
                ]
            }
        }
    }
}

export default NexsConfig;
