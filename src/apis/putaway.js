import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const userPutawayListApi = ({ type, userid }) => {
  const url = `${config.putaway.userPutawayList}${type}/${userid}`;
  return tokenInstance({ 'Content-Type': 'application/json' }, true)
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const markPutawayPendingApi = ({ ids, isV2 = false }) => {
  const url = isV2 ? `${config.putaway.putawayPendingV2}` : `${config.putaway.putawayPending}`;

  return tokenInstance()
    .put(url, ids)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const printPendingApi = (id) => {
  const url = `${config.putaway.printPutaway}/${id}`;
  return tokenInstance()
    .get(url, { responseType: 'arraybuffer' })
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const putawayCreationApi = (barcode) =>
  tokenInstance().post(`${config.putaway.createPutaway}?barcode=${barcode}`);

export const completePutAwaySGApi = (payload) =>
  tokenInstance().post(config.putaway.completePutAwaySg, payload);
