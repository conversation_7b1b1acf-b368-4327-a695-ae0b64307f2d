/* eslint-disable react/no-array-index-key */
/* eslint-disable no-sparse-arrays */
import React, { useState } from 'react';
import './regexModal.scss';
import {
  Paper,
  Snackbar,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip
} from '@mui/material';
import { StyledTableCell, StyledTableRow } from 'theme/table.theme';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ModalCustom from 'components/ModalCustom/ModalCustom';

const RegexModal = ({ showModal, setShowModal, CONTENT }) => {
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [showInput, setShowInput] = useState(false);

  const data = [
    { head: CONTENT.TARGET_FORMAT },
    { head: CONTENT.EXPRESSION },
    { head: CONTENT.MATCHES },
    { head: CONTENT.DOSENOT_MATCH }
  ];

  const rowData = [
    ['3-digit number', '^(\\d{3})$', '123,456,789', 'a23,a45,12345'],
    ['List (BLUE, RED, GREEN)', '^(RED|BLUE|GREEN)$', 'RED, BLUE, GREEN', 'YELLOW, Red, red'],
    [
      'Weekdays',
      '^(mon|tue|wed|thu|fri|sat|sun)$',
      'mon, tue, wed, thu, fri, sat, sun',
      'Monday, Mon, xyz'
    ],
    [
      'Single word (no white spaces)',
      '^(\\w*)$',
      'Single_word, Eyeframes, Sun_glass',
      'Singlee Word, Eye frame'
    ],
    [
      'Alphanumeric (length:5)',
      '^(\\w{5})$',
      '12345, A1234, AA124, a13ty',
      '123456, abcdef, no space'
    ],
    ['Alphabets (length: 3)', '^[A-Za-z]{3}$', 'ABC, abc, axy, Axy', 'A12, abcd, ab']
  ];

  const copyData = (event, ds) => {
    setShowSnackbar(true);
    setShowInput(true);
    setTimeout(() => {
      const copyText = document.getElementById('inputCopy');
      if (copyText) {
        copyText.value = ds;
        copyText.select();
        // document.execCommand('copy');
        navigator.clipboard.writeText(ds)
          .then(() => {
            setShowInput(false);
            setTimeout(() => {
              setShowSnackbar(false);
            }, 2000);
          })
          .catch((error) => {
            console.error('Error copying text: ', error);
          });
      }
    }, 0);
  };

  return (
    <ModalCustom open={showModal} close={() => setShowModal()} heading={CONTENT.REGULAR_EXPRESSION}>
      <div className="regex-container mr-t10">
        <div className="fs14 link-container pd-10 display-flex align-items-center">
          <div>
            <img
              className="img-action mr-r10"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/Info.svg`}
              alt="info"
            />
          </div>
          <div>
            {CONTENT.FOR_MORE_INFORMATION}, 
            {CONTENT.VISIT}- https://cs.lmu.edu/~ray/notes/regex/{' '}
          </div>
          <div className="mr-l10">
            <OpenInNewIcon />
          </div>
        </div>
      </div>
      <div className="mr-t30">
        <TableContainer component={Paper}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                {data.map((d) => (
                  <StyledTableCell key={d.head} align="left">{d.head}</StyledTableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {rowData.map((d, index) => (
                <StyledTableRow key={d[index]}>
                  {rowData[index].map((ds, i) => (
                    <StyledTableCell key={ds} align="left">
                      <div className="display-flex justify-content-space-between">
                        <div>{ds}</div>
                        {/* <input id={`copy${index}${i}`} value={ds} type="text" /> */}
                        <div onKeyDown={(e) => copyData(e, ds)} onClick={(e) => copyData(e, ds)}>
                          {i === 1 && (
                            <Tooltip title={CONTENT.COPY_CODE} aria-label="copy code">
                              <FileCopyIcon />
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </StyledTableCell>
                  ))}
                </StyledTableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        open={showSnackbar}
        autoHideDuration={2000}
        message={CONTENT.CODE_COPIED}
      />
      <div style={{ minHeight: '50px' }}>
        {showInput && <input style={{ width: '10px' }} id="inputCopy" type="text" />}
      </div>
    </ModalCustom>
  );
};

export default RegexModal;
