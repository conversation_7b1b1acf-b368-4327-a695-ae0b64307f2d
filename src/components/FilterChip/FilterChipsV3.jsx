import { Box } from '@mui/material';
import LkChip from 'components/MaterialUi/LkChip';
import React from 'react';
import CloseIcon from '@mui/icons-material/Close';

const FilterChipsV3 = ({ selectedFilterList, deleteSelectedFilter }) => (
  <Box display="flex">
    {selectedFilterList.map(({ key, value, hideDeleteOption = false }) => (
      <Box key={key + value} mr={2} mb={1}>
        <LkChip
          label={`${key}: ${value}`}
          type="filter"
          id="deleteFilterBtn"
          deleteIcon={
            <CloseIcon style={{ color: '#666666' }} data-cy={`${key}-filter-delete-icon`} />
          }
          size="small"
          data-cy={`${key}:${value}`}
          onDelete={hideDeleteOption ? null : () => deleteSelectedFilter(key, value)}
        />
      </Box>
    ))}
  </Box>
);

export default FilterChipsV3;
