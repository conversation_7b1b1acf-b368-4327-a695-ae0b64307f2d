import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import { keys, omit } from 'utils/lodash';
import Box from '@mui/material/Box';

import { createdTransferListLoad } from 'redux/actionCreators/bulkInventory/bulkInventory';
import { generatePayloadForSearchAPI } from 'utils/helpers';
import CreatedTransferListHeader from './CreatedTransferListHeader';
import CreateTransfersListTable from './CreateTransfersListTable';

const useStyles = makeStyles()(() => ({
  container: {
    borderTop: '1px solid rgba(0, 0, 0, 0.12)'
  }
}));

const DATE_RANGE_OPTIONS = [
  { key: 'Last 7 Days', value: 'Last 7 Days' },
  { key: 'Last 15 Days', value: 'Last 15 Days' }
];

const CreatedTransfersList = ({ requestBody, setRequestBody }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const previousFacility = useRef();
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [selectedDestination, setSelectedDestination] = useState('');
  const [dateRange, setRateRange] = useState([
    { key: 'CREATED', value: DATE_RANGE_OPTIONS[0].value }
  ]);

  const { selectedFacility } = useSelector((state) => state.settings);

  useEffect(() => {
    setRequestBody((prevReq) => ({
      ...prevReq,
      page: 0,
      isReset: true,
      source_facility: selectedFacility,
      searchFields: {},
      destination_facility:
        selectedDestination && previousFacility.current === selectedFacility
          ? selectedDestination
          : (() => {
            previousFacility.current = selectedFacility;
            return '';
          })()
    }));
  }, [selectedDestination, selectedFacility]);

  const getFilterKeyValue = (key) => {
    switch (key) {
      case 'created_by':
        return 'Created By';
      case 'updated_by':
        return 'Updated By';
      case 'transfer_code':
        return 'Transfer ID';
      case 'status':
        return 'Status';
      default:
        return '';
    }
  };

  useEffect(() => {
    const filterKeys = keys(requestBody.searchFields);
    const dateFilterPayload = generatePayloadForSearchAPI(dateRange, {}, 'CREATED');
    filterKeys.forEach((key) => {
      const filter = {
        key,
        keyValue: getFilterKeyValue(key === 'createdBy' ? 'created_by' : key),
        value: key === 'status' ? requestBody.searchFields[key] : null,
        values: requestBody.searchFields[key],
        disableDelete: false
      };
      setAppliedFilters((prevFilter) => [...prevFilter.filter((ft) => ft.key !== key), filter]);
      return null;
    });
    if (!filterKeys.length) {
      setAppliedFilters([]);
    }
    if (requestBody.source_facility) {
      dispatch(
        createdTransferListLoad({ ...requestBody, startDate: dateFilterPayload.CREATEDfrom })
      );
    }
  }, [requestBody, dateRange]);

  const deleteFilters = (key) => {
    setRequestBody((prevReq) => ({
      ...prevReq,
      isReset: true,
      page: 0,
      searchFields: omit(prevReq.searchFields, [key])
    }));
    setAppliedFilters((prevFilter) => prevFilter.filter((ft) => ft.key !== key));
  };

  return (
    <Box className={classes.container}>
      <CreatedTransferListHeader
        selectedDestination={selectedDestination}
        changeDestination={setSelectedDestination}
        setRequestBody={setRequestBody}
        requestBody={requestBody}
        appliedFilters={appliedFilters}
        deleteFilters={deleteFilters}
        DATE_RANGE_OPTIONS={DATE_RANGE_OPTIONS}
        dateRange={dateRange}
        setRateRange={setRateRange}
      />
      <CreateTransfersListTable
        setAppliedFilters={setAppliedFilters}
        setRequestBody={setRequestBody}
        requestBody={requestBody}
        appliedFilters={appliedFilters}
      />
    </Box>
  );
};

export default CreatedTransfersList;
