import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { toastOpen } from '../redux/actionCreators/toast';


const useProccessStatusHook = props => {
    const [refData, setRefData] = useState();
    const [statusSuccessfull, setStatusSuccessfull] = useState(false);
    const { processingStatus, processingStatusFail, processingStatusLoad, processingStatusReset } = props;
    const dispatch = useDispatch();
    const timeOut = 3000;

    useEffect(() => {
        if (refData) {
            setStatusSuccessfull(false);
            setTimeout(() => {
                const { refNo } = refData;
                dispatch(toastOpen({
                    isToastOpen: true,
                    heading: 'Fetching Updated Data....',
                    severity: 'info',
                    autoHideDuration: null
                }));
                dispatch(processingStatusLoad(refNo));
            }, timeOut);
        }
    }, [refData,dispatch]);

    useEffect(() => {
        if (processingStatus?.status === 'done') {
            if (processingStatus?.status === 'done' && !processingStatus?.error_msg) {
                dispatch(toastOpen({
                    isToastOpen: true,
                    heading: 'Data Successfully Updated',
                    severity: 'success',
                    autoHideDuration: 3000
                }));
                setStatusSuccessfull(true);
                setRefData(null);
            }
            if (processingStatus?.status === 'done' && Boolean(processingStatus?.error_msg?.length)) {
                dispatch(toastOpen({
                    isToastOpen: true,
                    heading: processingStatus?.error_msg[0],
                    severity: 'error',
                    autoHideDuration: 3000
                }));
                setRefData(null);
            }
        }
        if (processingStatus?.status === 'processing') {
            const { refNo } = refData;
            dispatch(processingStatusLoad(refNo));
        }
        if (processingStatusFail) {
            dispatch(toastOpen({
                isToastOpen: true,
                heading: processingStatusFail,
                severity: 'error',
                autoHideDuration: 3000
            }));
            setRefData(null);
        }
    }, [processingStatus, processingStatusFail,dispatch]);

    useEffect(() => {
        return () => {           
            dispatch(processingStatusReset());            
            dispatch(toastOpen({
                isToastOpen: false,
                heading: '',
                severity: '',
                autoHideDuration: 3000
            }));
        };
    }, [dispatch]);

    return [refData, statusSuccessfull, setRefData];
};

export default useProccessStatusHook;
