import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { isEmpty } from 'utils/lodash';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';

import { InfoStrip } from 'components/common';
import Spinner from 'components/Spinner/Spinner';
import InvoiceDetailmodal from 'components/common/InvoiceDetailmodal';
import PrintConfirmModal from 'components/PrintConfirmModal/PrintConfirmModal';

import useProccessStatusHook from 'common/useProccessStatusHook';
import useKeyboardShortcut from 'common/useKeyboardShortcut';

import { InvoiceOrderStatus } from 'config/InvoiceOrderStatus';

import {
  convertDateFormat,
  getApplicableTaxes,
  roundUptoFixedDigits,
  parseQuery,
  fileDownload,
  getCurrencySumbol
} from 'utils/helpers';

import { LOCALISATION } from 'redux/reducers/localisation';
import { pdfDownloadLoad, pdfDownloadReset } from 'redux/actionCreators/pdfDownload';
import {
  getVendorInvoiceCreationStatusLoad,
  getVendorInvoiceCreationStatusReset,
  createVendorInvoiceLoad,
  resetVendorInvoiceCreationData
} from 'redux/actionCreators/invoiceReferenceCreation';
import { toastOpen } from 'redux/actionCreators/toast';
import { invoiceViewLoad, invoiceViewReset } from 'redux/actionCreators/invoiceView';
import {
  approveOrRejectStockLoad,
  approveOrRejectStockReset,
  invoiceIqcSummaryLoad
} from 'redux/reducers/inwardQC';

import InvoiceTabContainer from './InvoiceTabContainer';
import InfoStripAction from './InfoStrapAction';
import '../invoiceDashboard.scss';

let printOptions = [
  // { value: 'Invoice Print', key: 'invoiceprint' },
  { value: 'Invoice Details', key: 'invoicedetails' }
];

const useStyles = makeStyles()((theme) => ({
  table: {
    minWidth: 700
  },
  container: {
    borderRadius: theme.spacing(1)
  },
  infoStripButton: {
    borderRadius: theme.spacing(1)
  },
  tableCell: {
    width: 240
  },
  tableCellMaxWidth: {
    width: 300
  },
  tabContainer: {
    backgroundColor: 'white',
    padding: theme.spacing(3),
    minHeight: 550
  }
}));

const pdfNameMapping = {
  invoicesummary: 'INVOICE_SUMMARY',
  invoiceprint: 'INVOICE_PRINT',
  invoicedetails: 'INVOICE_DETAILS'
};

const InvoiceView = () => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const { summary } = useParams();
  const dispatch = useDispatch();

  const [showSummary, SetshowSummary] = useState(typeof summary !== 'undefined');
  const [invoiceRefNum, setInvoiceRefNum] = useState('');
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [selectedPrintOption, setSelectedPrintOption] = useState(0);
  const [infoStripData, setInfoStripData] = useState([]);
  const [showModal, setShowModal] = useState(false);

  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const { invoiceLoading, invoiceViewData, invoiceViewError } = useSelector(
    (state) => state.invoiceView
  );
  const {
    createVendorInvoiceData,
    vendorInvoiceStatus,
    vendorInvoiceStatusFail,
    createVendorInvoiceLoading
  } = useSelector((state) => state.invoiceReferenceCreation);
  const invoiceIqcSummaryData = useSelector((state) => state.inwardQC.invoiceIqcSummary);
  const { pdfDownloadSuccess, pdfDownloadError } = useSelector((state) => state.pdfDownload);
  const approveOrRejectState = useSelector((state) => state.inwardQC.approveOrRejectStock);

  const [invoiceStatus, statusSuccessFul, setInvoiceStatus] = useProccessStatusHook({
    processingStatus: vendorInvoiceStatus,
    processingStatusFail: vendorInvoiceStatusFail,
    processingStatusLoad: getVendorInvoiceCreationStatusLoad,
    processingStatusReset: getVendorInvoiceCreationStatusReset
  });

  const queryParam = parseQuery(location.search);

  const setShowPrintModalcallBack = useCallback(
    (value) => setShowPrintModal(value),
    [setShowPrintModal]
  );

  const getInvoiceDetail = () => {
    const payloadObj = {
      items: true,
      invoice_ref_no: queryParam?.invoiceRefNumber,
      invoice_number: queryParam?.invoiceNumber
    };
    setInvoiceRefNum(queryParam?.invoiceRefNumber || queryParam?.invoiceNumber);
    dispatch(invoiceViewLoad(payloadObj));
  };
  const invoiceDetailmodalfun = () => {
    setShowModal(true);
  };

  useEffect(() => {
    getInvoiceDetail();
  }, []);

  useEffect(() => {
    if (invoiceViewData?.data?.iqc) {
      dispatch(invoiceIqcSummaryLoad({ invoiceRefNo: queryParam?.invoiceRefNumber }));
    }
  }, [invoiceViewData?.data?.iqc]);

  useEffect(
    () => () => {
      dispatch(resetVendorInvoiceCreationData());
      dispatch(invoiceViewReset());
      dispatch(dispatch(getVendorInvoiceCreationStatusReset()));
      dispatch(approveOrRejectStockReset());
      printOptions = [{ value: 'Invoice Details', key: 'invoicedetails' }];
    },
    []
  );

  useEffect(() => {
    if (pdfDownloadSuccess) {
      fileDownload(
        pdfDownloadSuccess,
        `${pdfNameMapping[selectedPrintOption]}-NEXS-${
          queryParam?.invoiceRefNumber || queryParam?.invoiceNumber
        }`,
        'pdf'
      );
      dispatch(pdfDownloadReset());
    }
    if (pdfDownloadError) {
      dispatch(pdfDownloadReset());
    }
  }, [pdfDownloadError, pdfDownloadSuccess]);

  useEffect(() => {
    if (statusSuccessFul && vendorInvoiceStatus) {
      getInvoiceDetail();
    }
  }, [statusSuccessFul, vendorInvoiceStatus]);

  useKeyboardShortcut([18, 68], () => {
    setShowModal(true);
  });

  const printFunCall = () => {
    setShowPrintModal(false);
    dispatch(
      pdfDownloadLoad({ type: selectedPrintOption, id: invoiceViewData?.data?.invoice_ref_number })
    );
  };

  const handleCloseDetailModal = useCallback((value) => setShowModal(value), [setShowModal]);

  useEffect(() => {
    if (createVendorInvoiceData?.error?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createVendorInvoiceData.error.response.data.meta.displayMessage,
          severity: 'error'
        })
      );
    } else if (createVendorInvoiceData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createVendorInvoiceData.response.data.meta.displayMessage,
          severity: 'success'
        })
      );
      handleCloseDetailModal(false);
      setInvoiceStatus({ refNo: createVendorInvoiceData.response.data.data });
    }
  }, [createVendorInvoiceData, dispatch]);

  const goToPoNum = (po_id) => {
    window.open(`${window.location.origin}/po/detail?poNum=${po_id}&fromPo=true`, '_blank');
  };

  useEffect(() => {
    if (invoiceViewData) {
      const {
        vendor_invoice_number,
        status,
        invoice_date,
        total_invoice_amount,
        po_num,
        vendor_name,
        currency
      } = invoiceViewData.data;
      const infoStripTemp = [
        {
          key: INVOICE.VENDOR_INVOICE,
          className: 'text-primary cursor-pointer',
          value: vendor_invoice_number,
          status: InvoiceOrderStatus[status]?.text || status,
          type: InvoiceOrderStatus[status]?.type,
          onClick: () => invoiceDetailmodalfun()
        },
        { key: INVOICE.VENDOR_INVOICE_DATE, value: convertDateFormat(invoice_date, 'shortDate') },
        { key: INVOICE.VENDOR, value: vendor_name },
        {
          key: INVOICE.PO,
          value: po_num,
          className: 'text-primary cursor-pointer',
          onClick: () => goToPoNum(po_num)
        },

        {
          key: INVOICE.TOTAL,
          value: `${getCurrencySumbol(currency)}${roundUptoFixedDigits(
            total_invoice_amount
          )}`
        }
      ];
      setInfoStripData(infoStripTemp);
      if (status === 'CLOSED') {
        printOptions.push({ value: 'Invoice Summary', key: 'invoicesummary' });
      }
    }
    if (!isEmpty(invoiceIqcSummaryData) && invoiceViewData?.data?.iqc) {
      const { totalCount, samplingCount, qcPassCount, qcFailCount } = invoiceIqcSummaryData.data;
      setInfoStripData((prevItems) => [
        ...prevItems,
        {
          key: INVOICE.TOTAL_QUANTITY,
          value: totalCount ?? '-'
        },
        {
          key: INVOICE.SAMPLE_SIZE,
          value: samplingCount ?? '-'
        },
        {
          key: INVOICE.QC_PASS,
          value: qcPassCount ?? '-'
        },
        {
          key: INVOICE.QC_FAIL,
          value: qcFailCount ?? '-'
        }
      ]);
    }
  }, [invoiceViewData, invoiceIqcSummaryData]);

  useEffect(() => {
    if (approveOrRejectState.status === 'approved' || approveOrRejectState.status === 'rejected') {
      navigate('/invoice/dashboard');
    }
  }, [approveOrRejectState]);

  const changePrintOptions = useCallback(
    (e) => setSelectedPrintOption(e.target.value),
    [setSelectedPrintOption]
  );

  const handleSubmit = ({ vendorInvoiceNumber, vendorInvoiceDate, batch_no_list = [] }) => {
    const {
      version,
      items,
      po_num,
      b2b_invoice_date,
      bill_of_entry_amount,
      bill_of_entry_date,
      bill_of_entry_number,
      send_to_party,
      handover_party
    } = invoiceViewData.data;
    const i = [];
    items.forEach((item) => {
      const {
        id,
        product_id,
        quantity,
        vendor_unit_cost_price,
        cgst_per,
        igst_per,
        sgst_per,
        ugst_per,
        vendor_tax_type
      } = item;
      const taxes = getApplicableTaxes(vendor_tax_type);
      const obj = {
        id: id || '',
        product_id,
        quantity,
        vendor_unit_cost_price,
        cgst_per: taxes.includes('cgst') ? cgst_per : '',
        igst_per: taxes.includes('igst') ? igst_per : '',
        sgst_per: taxes.includes('sgst') ? sgst_per : '',
        ugst_per: taxes.includes('ugst') ? ugst_per : ''
      };
      i.push(obj);
    });

    const obj = {
      invoice_date: vendorInvoiceDate,
      invoice_ref_no: invoiceRefNum,
      items: i,
      po_num,
      vendor_invoice_number: vendorInvoiceNumber.trim(),
      version,
      b2b_invoice_date,
      bill_of_entry_amount,
      bill_of_entry_date,
      bill_of_entry_number,
      send_to_party,
      handover_party,
      batch_no_list
    };
    dispatch(createVendorInvoiceLoad(obj));
  };

  const discardFun = useCallback(() => {
    const { invoice_ref_number, vendor_invoice_number, po_num, invoice_date, version } =
      invoiceViewData.data;
    const payload = {
      items: [],
      invoice_ref_no: invoice_ref_number,
      vendor_invoice_number: vendor_invoice_number.trim(),
      po_num,
      invoice_date,
      version,
      status: 'DISCARDED'
    };
    dispatch(createVendorInvoiceLoad(payload));
  }, [invoiceViewData, dispatch]);

  const handleApproveOrRejectStock = (status) => {
    dispatch(
      approveOrRejectStockLoad({
        status,
        invoice_ref_number: invoiceViewData.data?.invoice_ref_number
      })
    );
  };
  const stockButtonDiabled = useMemo(
    () =>
      approveOrRejectState.isLoading ||
      approveOrRejectState.status === 'approved' ||
      approveOrRejectState.status === 'rejected' ||
      invoiceIqcSummaryData?.data?.totalCount === 0,
    [approveOrRejectState, invoiceIqcSummaryData]
  );

  return (
    <div>
      {invoiceViewError && !invoiceLoading && (
        <Box
          height="400px"
          className="fs20 fs-bold"
          alignItems="center"
          display="flex"
          justifyContent="center"
        >
          {' '}
          {invoiceViewError?.meta?.displayMessage}{' '}
        </Box>
      )}
      {invoiceLoading && <Spinner className="display-grid-center mr-t20" />}
      {!invoiceLoading && invoiceViewData && (
        <>
          <InfoStrip data={infoStripData}>
            {invoiceViewData?.data?.status === 'CREATED' &&
            showSummary &&
            invoiceViewData?.data?.iqc ? (
                <>
                  <Button
                    onClick={() => handleApproveOrRejectStock('IQC_REJECT')}
                    variant="outlined"
                    color="primary"
                    disabled={stockButtonDiabled}
                    style={{ marginRight: 10 }}
                  >
                    {INVOICE.REJECT_STOCK}
                  </Button>
                  <Button
                    onClick={() => handleApproveOrRejectStock('IQC_ACCEPT')}
                    variant="contained"
                    color="primary"
                    disabled={stockButtonDiabled}
                  >
                    {INVOICE.APPROVE_STOCK}
                  </Button>
                </>
              ) : (
                <InfoStripAction
                  status={invoiceViewData?.data?.status}
                  infoStripButton={classes.infoStripButton}
                  invoiceRefNum={invoiceViewData?.data?.invoice_ref_number}
                  debit_note_number={invoiceViewData?.data?.debit_note_number}
                  vendorName={invoiceViewData?.data?.vendor_name}
                  invoiceName={invoiceViewData?.data?.vendor_invoice_number}
                  po_num={invoiceViewData?.data?.po_num}
                  openPrintModal={setShowPrintModalcallBack}
                  vendorId={invoiceViewData?.data?.vendor_id}
                  discardFun={discardFun}
                  contains_batch_no={invoiceViewData?.data?.contains_batch_no || false}
                />
              )}
          </InfoStrip>
          {showModal && (
            <InvoiceDetailmodal
              showModal={showModal}
              setShowModal={handleCloseDetailModal}
              invoiceViewData={invoiceViewData}
              handleInvoiceSubmit={handleSubmit}
              createVendorInvoiceLoading={createVendorInvoiceLoading}
              invoiceStatus={invoiceStatus}
              invoiceRefNum={invoiceRefNum}
            />
          )}
          <Box p={3} className={classes.tabContainer}>
            <InvoiceTabContainer
              tableClasses={classes}
              invoiceViewData={invoiceViewData}
              invoiceRefNumber={invoiceViewData.data?.invoice_ref_number}
              isLoading={invoiceLoading}
              setShowSummary={SetshowSummary}
              showSummary={invoiceViewData?.data?.iqc}
            />
          </Box>

          {showPrintModal && (
            <PrintConfirmModal
              changePrintfun={changePrintOptions}
              printValueTemp={selectedPrintOption}
              open={showPrintModal}
              printFun={printFunCall}
              handleClose={() => setShowPrintModal(false)}
              printOptions={printOptions}
            />
          )}
        </>
      )}
    </div>
  );
};

export default InvoiceView;
