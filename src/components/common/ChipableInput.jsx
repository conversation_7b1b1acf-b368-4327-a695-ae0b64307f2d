import React, { useRef, useState } from 'react';
import { Box, InputBase, Chip } from '@mui/material';
import { Close } from '@mui/icons-material';
import { makeStyles } from 'tss-react/mui';
import LabeledBox from './LabeledBox';

const useStyles = makeStyles()(() => ({
  success: {
    backgroundColor: '#45C476',
    margin: '6px 4px',
    fontWeight: '500'
  }
}));

const ChipableInput = (props) => {
  const { classes } = useStyles();
  const { value, label, onClick, className, disableInput, onChange, ...rest } = props;

  const [unControlledData, setUnControlledData] = useState(value || []);
  const [focused, setFocused] = useState(false);
  const inputEl = useRef(null);

  const onButtonClick = () => {
    if (disableInput) {
      return;
    }
    inputEl.current.focus();
    setFocused(true);
    if (onclick) {
      onClick();
    }
  };
  const onTabPress = (e) => {
    const text = e.target.value;
    if (text === '') {
      return;
    }
    e.preventDefault();
    inputEl.current.value = '';
    const newData = [...unControlledData, text];
    setUnControlledData(newData);
    if (onChange) {
      onChange(newData);
    }
  };
  const deleteClicked = (i) => {
    if (disableInput) {
      return;
    }
    unControlledData.splice(i, 1);
    setUnControlledData([...unControlledData]);
    if (onChange) {
      onChange(unControlledData);
    }
  };
  return (
    <LabeledBox
      focused={focused}
      className={className}
      {...rest}
      onClick={() => onButtonClick()}
      label={label}
    >
      {value
        ? value.map((d, i) => (
          <Box key={d} m={0.75} component="span">
            <Chip
              className={classes.success}
              size="small"
              label={d}
              color="primary"
              onDelete={() => deleteClicked(i)}
              deleteIcon={<Close />}
            />
          </Box>
        ))
        : unControlledData.map((d, i) => (
          <Box key={d} m={0.75} component="span">
            <Chip
              className={classes.success}
              size="small"
              label={d}
              color="primary"
              onDelete={() => deleteClicked(i)}
              deleteIcon={<Close />}
            />
          </Box>
        ))}
      <InputBase
        fullWidth
        disabled={disableInput}
        inputRef={inputEl}
        onBlur={() => setFocused(false)}
        onKeyDown={(e) => (e.keyCode === 9 || e.keyCode === 13) && onTabPress(e)}
        inputProps={{ 'aria-label': 'naked' }}
      />
    </LabeledBox>
  );
};

export default ChipableInput;
