import React, { useState } from 'react';

import { IconButton, Menu, MenuItem } from '@mui/material';
import HelpCenterIcon from '@mui/icons-material/HelpCenter';

const InfoAction = ({ classes, disabled, items }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const openMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const openMenu = Boolean(anchorEl);

  return (
    <>
      <IconButton
        disabled={disabled}
        color="secondary"
        id="actions"
        aria-label="actions"
        aria-controls={openMenu ? 'actions' : undefined}
        aria-expanded={openMenu ? 'true' : undefined}
        aria-haspopup="true"
        onClick={openMenuClick}
      >
        <HelpCenterIcon />
      </IconButton>
      <Menu
        anchorOrigin={{
          horizontal: 'right',
          vertical: 'bottom'
        }}
        anchorEl={anchorEl}
        open={openMenu}
        onClose={() => setAnchorEl(null)}
      >
        {items?.map(
          (item) =>
            item && (
              <MenuItem key={item} className={classes.menuItem}>
                {item}
              </MenuItem>
            )
        )}
      </Menu>
    </>
  );
};

export default InfoAction;
