import React from 'react';

import { makeStyles } from 'tss-react/mui';

import Box from '@mui/material/Box';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({
  lensCard: {
    width: '100%',
    border: '1px solid #EEEEEE',
    borderRadius: '8px'
  },
  tableDetails: {
    display: 'flex',
    justifyContent: 'space-around',
    borderBottom: '1px solid #EEEEEE',
    padding: '8px 0px'
  },
  tableKeys: {
    color: '#666666',
    paddingBottom: '8px'
  },
  tableValues: {
    fontWeight: 'bold',
    paddingTop: '4px'
  },
  lensCardFooter: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: '20px',
    paddingBottom: '10px',
    borderBottom: '1px solid #EEEEEE',
    paddingLeft: '25px',
    paddingRight: '25px'
  }
}));

const QCLensCard = ({
  side,
  description,
  cardData,
  secondCardDetails,
  children,
  showMEIFooter = false,
  cardFooterData = {}
}) => {
  const { classes } = useStyles();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.HAND_EDGING);

  const getTextColorClass = (key, value) => {
    if (key === 'FH' && value?.includes('-')) {
      return 'text-red';
    }
    if (key === 'PD') {
      return 'text-red';
    }
    return '';
  };

  return (
    <Box
      data-cy="power-details-container"
      sx={{
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'column',
        padding: {
          xs: '4px',
          lg: '20px'
        },
        fontSize: {
          lg: '14px',
          xs: '20px'
        },
        width: {
          lg: '27.5%',
          xs: '35%'
        }
      }}
    >
      <Box data-cy={`lens-side-${side}`} className="fw-bold">
        {side} {CONTENT.LENS}
      </Box>
      <Box
        data-cy={`power-description-${side}`}
        sx={{
          wordWrap: 'break-word',
          mb: '25px',
          mt: {
            lg: '35px',
            xs: '25px'
          }
        }}
      >
        {description}
      </Box>
      <Box data-cy="power-details" className={classes.lensCard}>
        <Box className={`${classes.tableDetails}`}>
          {Object.entries(cardData).map(([key, value]) => (
            <Box key={key}>
              <Box
                className={`${classes.tableKeys}`}
                sx={{
                  fontSize: {
                    lg: '11px',
                    xs: '23px'
                  }
                }}
              >
                {key}
              </Box>
              <Box
                className={`${classes.tableValues} text-${value?.includes('-') && 'red'}`}
                sx={{
                  fontSize: {
                    lg: '14px',
                    xs: '23px'
                  }
                }}
              >
                {value || '-'}
              </Box>
            </Box>
          ))}
        </Box>
        <Box className={classes.tableDetails}>
          {Object.entries(secondCardDetails).map(([key, value]) => (
            <Box key={key}>
              <Box
                className={`${classes.tableKeys}`}
                sx={{
                  fontSize: {
                    lg: '11px',
                    xs: '23px'
                  }
                }}
              >
                {key}
              </Box>
              <Box
                className={`${classes.tableValues} text-${value?.includes('-') && 'red'}`}
                sx={{
                  fontSize: {
                    lg: '14px',
                    xs: '23px'
                  }
                }}
              >
                {value || '-'}
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
      <Box
        sx={{
          pt: {
            lg: '25px',
            xs: '0px'
          }
        }}
      >
        {Object.entries(cardFooterData).map(([key, value]) => (
          <Box className={`${classes.lensCardFooter}`} key={key}>
            <Box
              sx={{
                fontSize: {
                  lg: '12px',
                  xs: '18px'
                }
              }}
            >
              {key}
            </Box>
            {!showMEIFooter ? (
              <Box
                sx={{
                  fontSize: {
                    lg: '14px',
                    xs: '18px'
                  },
                  fontWeight: 'bold'
                }}
              >
                {value}
              </Box>
            ) : (
              <Box
                className={`${classes.tableValues} ${getTextColorClass(key, value)}`}
                sx={{
                  fontSize: {
                    lg: '14px',
                    xs: '23px'
                  }
                }}
              >
                {value || '-'}
              </Box>
            )}
          </Box>
        ))}
      </Box>
      {children}
    </Box>
  );
};

export default QCLensCard;
