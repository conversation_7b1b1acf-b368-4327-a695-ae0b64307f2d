import React from 'react';
import { useArgs } from '@storybook/preview-api';
import LKButton from 'components/Button/LKButton';
import { Box } from '@mui/material';
import LkModal from 'components/Modal/Modal';

export default {
  title: 'Components/Modal',
  component: LkModal,
  args: {
    modalHeight: '334px',
    modalWidth: ' 520px',
    title: 'Title in a modal',
    subTitle: 'Sub Title in a Modal',
    showClose: true,
    open: false,
    showActionButton: true,
    primaryBtnText: 'Confirm',
    allowPaddingForChildren: true,
    upperHeading: '',
    secondaryBtnText: 'Cancel',
    primaryBtnVariant: 'contained',
    disablePrimaryButton: false,
    primaryBtnWidth: '',
    primaryBtnClass: '',
    secondaryBtnClass: '',
    secondaryBtnWidth: '',
    primaryBtnSize: 'medium',
    secondaryBtnSize: 'medium',
    disableSecondaryBtn: false
  },
  argTypes: {
    handleClose: { action: 'handleClose' }
  }
};

const Template = (args) => {
  const [, setArgs] = useArgs();

  const handleClose = () => {
    setArgs({ ...args, open: false });
  };

  const handleOpen = () => {
    setArgs({ ...args, open: true });
  };

  return (
    <>
      <LKButton onClick={handleOpen}>Open Modal</LKButton>
      <LkModal {...args} handleClose={handleClose} primaryBtn={handleClose}>
        <Box margin="0 auto" mt={4}>
          Lorem ipsum dolor sit amet consectetur, adipisicing elit. Beatae ratione, est eveniet
          dolorem error rem laudantium et atque temporibus sunt corporis iure pariatur! Molestiae
          consequatur dolorem atque ipsum fuga deserunt?
        </Box>
      </LkModal>
    </>
  );
};

export const Modal = Template.bind({});
