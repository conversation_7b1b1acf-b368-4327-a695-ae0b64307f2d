import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import LkModal from '../Modal/Modal';
import FileUploadModalActions from './FileUploadModalActions';
import FileUploadModalBody from './FileUploadModalBody';

const FileUpload = ({
  open,
  close,
  handleSubmit,
  downloadReport = null,
  failDataMessage = null,
  checkedItemLabel = null,
  sampleCSV = [],
  sampleFileName = 'Sample',
  localUpload = false,
  importLoading = false,
  subTitle = '',
  showSampleArray = true,
  selectFiles = null,
  uploadCsvPass = false,
  disableImport = false
}) => {
  const [checkedItems, setCheckedItems] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [showMessage, setShowMessage] = useState(false);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.FILE_UPLOAD);

  useEffect(() => {
    if (failDataMessage && downloadReport) {
      setShowMessage(true);
    }
  }, [failDataMessage]);

  useEffect(() => {
    if (localUpload && selectedFile) {
      handleSubmit(selectedFile);
    }
  }, [localUpload, selectedFile]);

  const selectFile = (event) => {
    setSelectedFile(event.target.files[0]);
    selectFiles(event.target.files[0]);
  };

  const cancelFileSelection = () => {
    if (!importLoading) {
      setSelectedFile(null);
      selectFiles(null);
      setShowMessage(false);
    }
  };

  const closeModal = () => {
    if (!importLoading) {
      cancelFileSelection();
      close();
      setShowMessage(false);
    }
  };

  useEffect(() => {
    if (open) {
      cancelFileSelection();
      setShowMessage(false);
    }
  }, [open]);

  const config = {
    title: CONTENT.CSV_FILE_IMPORT,
    subTitle
  };

  if (uploadCsvPass) {
    config.title = CONTENT.IMPORT_PROCESSED_SUCCESSFULLY;
    config.subTitle = CONTENT.FILE_IMPORTED_SUCCESSFULLY;
  } else if (showMessage) {
    config.title = CONTENT.PROBLEMS_WITH_CSV_FILE;
    config.subTitle = `${CONTENT.IMPORT_UNSUCCESSFUL}:`;
  } else if (importLoading) {
    config.title = CONTENT.UPLOAD_FILE;
    config.subTitle = CONTENT.WAIT_WHILE_UPLOAD_UR_CSV_FILE;
  }

  return (
    <LkModal open={open} title={config.title} subTitle={config.subTitle} handleClose={closeModal}>
      <Box className="upload-csv mr-40 text-center">
        <FileUploadModalBody
          CONTENT={CONTENT}
          sampleCSV={sampleCSV}
          showSampleArray={showSampleArray}
          sampleFileName={sampleFileName}
          checkedItemLabel={checkedItemLabel}
          checkedItems={checkedItems}
          setCheckedItems={setCheckedItems}
          uploadCsvPass={uploadCsvPass}
          closeModal={closeModal}
          showMessage={showMessage}
          failDataMessage={failDataMessage}
          selectedFile={selectedFile}
          downloadReport={downloadReport}
          importLoading={importLoading}
          cancelFileSelection={cancelFileSelection}
        />
        <Box className="display-flex justify-content-center mr-b20 mr-t20" />
        <FileUploadModalActions
          CONTENT={CONTENT}
          closeModal={closeModal}
          uploadCsvPass={uploadCsvPass}
          selectedFile={selectedFile}
          handleSubmit={handleSubmit}
          importLoading={importLoading}
          selectFile={selectFile}
          checkedItems={checkedItems}
          checkedItemLabel={checkedItemLabel}
          errorInImport={showMessage}
          disableImport={disableImport}
        />
      </Box>
    </LkModal>
  );
};

export default FileUpload;
