import React from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';

const styles = makeStyles()(() => ({
  showingButton: {
    position: 'absolute',
    bottom: 60,
    left: '50%',
    transform: 'translateX(-50%)'
  },
  showingButtonData: {
    border: '1px solid #DDDDDD',
    background: 'white',
    boxShadow: '0px 16px 32px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  }
}));
const TableCount = ({ loading, totalCount, count, className }) => {
  const { classes, cx } = styles();
  if (!loading) {
    return (
      (<Box
        component="span"
        className={cx(classes.showingButton, { [className]: className })}
        textAlign="center"
      >
        <Box
          className={classes.showingButtonData}
          width="156px"
          margin="0  auto"
          p={2}
          height="40px"
          borderRadius="222px"
        >
          <Box className="fs12 text-66">Showing</Box>
          <Box className="fs12 text-33">
            {count}/{totalCount}
          </Box>
        </Box>
      </Box>)
    );
  }
  return null;
};

export default React.memo(TableCount);
