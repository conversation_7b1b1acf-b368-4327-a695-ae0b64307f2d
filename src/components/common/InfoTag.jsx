import { useTheme } from '@emotion/react';
import { Box, Skeleton, Typography } from '@mui/material';
import React from 'react';

const InfoTag = ({
  header,
  body,
  isLoading,
  width: propWidth,
  headerColor: propHeaderColor,
  bodyColor: propBodyColor,
  headerFontSize: propHeaderFontSize,
  bodyFontSize: propBodyFontSize,
  headerFontWeight: propHeaderFontWeight,
  bodyFontWeight: propBodyFontWeight
}) => {
  const theme = useTheme();

  const width = propWidth || 280;
  const headerColor = propHeaderColor || theme.palette.text.light;
  const bodyColor = propBodyColor || theme.palette.text.main;
  const headerFontSize = propHeaderFontSize || 12;
  const bodyFontSize = propBodyFontSize || 16;
  const headerFontWeight = propHeaderFontWeight || 400;
  const bodyFontWeight = propBodyFontWeight || 500;

  return (
    <Box width={width} display="flex" flexDirection="column" gap={1} alignItems="center">
      <Typography
        data-cy={header}
        fontWeight={headerFontWeight}
        fontSize={headerFontSize}
        color={headerColor}
      >
        {header ?? '--'}
      </Typography>
      <Typography
        data-cy={`${header}-${body}`}
        variant="h6"
        fontWeight={bodyFontWeight}
        fontSize={bodyFontSize}
        color={bodyColor}
      >
        {isLoading ? <Skeleton variant="text" width={180} height={20} /> : body ?? '--'}
      </Typography>
    </Box>
  );
};

export default InfoTag;
