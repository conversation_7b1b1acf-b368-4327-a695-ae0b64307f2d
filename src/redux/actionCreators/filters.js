import * as action from '../actionTypes/filters';

//  Search List
export function searchListLoad(data) {
  return {
    type: action.SEARCH_LIST_LOAD,
    payload: data
  };
}

export function searchListSuccess(data) {
  return {
    type: action.SEARCH_LIST_SUCCESS,
    payload: data
  };
}

export function searchListFail(data) {
  return {
    type: action.SEARCH_LIST_FAIL,
    payload: data
  };
}

export function searchListReset(data) {
  return {
    type: action.SEARCH_LIST_RESET,
    payload: data
  };
}

//  Search Autosuggestion

export function searchSuggestionReset() {
  return {
    type: action.SEARCH_SUGGESTION_RESET
  };
}
export function searchSuggestionLoad(data) {
  return {
    type: action.SEARCH_SUGGESTION_LOAD,
    payload: data
  };
}

export function searchSuggestionSuccess(data) {
  return {
    type: action.SEARCH_SUGGESTION_SUCCESS,
    payload: data
  };
}

export function searchSuggestionFail(data) {
  return {
    type: action.SEARCH_SUGGESTION_FAIL,
    payload: data
  };
}
