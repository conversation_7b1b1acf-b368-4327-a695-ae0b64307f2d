import { put } from 'redux-saga/effects';
import { toastOpen } from 'redux/actionCreators/toast';
import errorConstant from 'constant/error';

export const extractErrorFromMeta = (error) =>
  error?.response?.data?.meta?.displayMessage ||
  error?.response?.data?.meta?.display_message ||
  error?.response?.data?.meta?.errorMessage ||
  error?.response?.data?.meta?.error_message ||
  error?.response?.data?.display_message ||
  error?.response?.data?.meta?.message ||
  error?.response?.data?.message ||
  (typeof error?.response?.data === 'string' && error?.response?.data) ||
  '';

export function* showToaster(
  error,
  severity = 'error',
  displayMessage = null,
  subHeading = '',
  time = 3000
) {
  const message =
    displayMessage || extractErrorFromMeta(error) || errorConstant.SOMETHING_WENT_WRONG;

  yield put(
    toastOpen({
      isToastOpen: true,
      heading: message,
      subHeading,
      severity,
      autoHideDuration: time
    })
  );
}

export function* showErrorToaster(displayMessage, subHeading = '') {
  yield showToaster(null, 'error', displayMessage, subHeading);
}

export function* showSuccessToaster(message, subHeading = '') {
  yield showToaster(null, 'success', message, subHeading);
}

export function* showInfoToaster(message, subHeading = '') {
  yield showToaster(null, 'info', message, subHeading);
}
