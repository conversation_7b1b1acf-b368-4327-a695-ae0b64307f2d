import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import StarIcon from '@mui/icons-material/Star';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';

import { printBlobPdf } from 'utils/helpers';
import PrintShipmentNew from 'components/PrintShipment/PrintShipmentNew';
import { printPackingInvoiceLoad, printPackingInvoiceReset } from 'redux/actionCreators/packing';
import { toastOpen } from 'redux/actionCreators/toast';
import { Link } from 'react-router-dom';
import { AGENT, USER_SUPERVISOR, PENDING, DONE } from 'views/Packing/SupervisorPackingConstants';
import usePermissions from 'common/usePermissions';
import { LOGIN } from 'redux/reducers/login';
import { getConsulKeyValue } from 'redux/reducers/consul';

const styles = makeStyles()(() => ({
  star: {
    verticalAlign: 'bottom',
    height: '18px',
    width: '18px',
    color: '#FAA61A'
  },
  btn: {
    borderRadius: 8,
    marginRight: '20px'
  },
  btnRadius: {
    borderRadius: 8,
    marginRight: 20
  },
  spn: {
    position: 'absolute',
    right: '0px'
  },
  delayedColor: {
    color: '#DD0000 !important'
  },
  orderSourceImgDiv: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  franchiseNoInvoice: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    color: 'red',
    fontWeight: 500,
    fontSize: '14px'
  }
}));

const QC_COLOUR = {
  ORDER_ID: '#0088CB',
  ORDER_TYPE: '#0088CB',
  SHIPMENT_ID: '#00BAC6'
};
const PACKING_COLOUR = {
  ORDER_ID: '#0088CB',
  ORDER_TYPE: '#0088CB',
  FACILITY_CODE: '#0088CB'
};

const AFTER_QC_PASS_STATUS = [
  'QC_PASS',
  'QCPass',
  'AWB_CREATED',
  'DISPATCHED',
  'READY_TO_SHIP',
  'INVOICED',
  'QC_DONE'
];

const WMInfoStrip = ({ data }) => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const manualRetryCount = useRef(0);
  const url = window.location.pathname;
  const isPackingSummary = url.includes('/packing-summary');
  const isQCSummary = url.includes('/qc-summary');
  const {
    isWebB2BOrder,
    shipmentHeaders,
    isFr0Order,
    isFR1order,
    role,
    packingDetailStatus,
    orderDetailStatus
  } = useSelector((state) => state.order);

  const { qcData = [] } = useSelector((state) => state.qc);
  const { printPackingInvoiceLoaded, printPackingInvoiceLoading } = useSelector(
    (state) => state.packing
  );
  const featureToggle = useSelector((state) => state.consul.featureToggle.data);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);
  const { permission } = useSelector((state) => state[LOGIN].user.userDetail);
  const { isQCSuperVisor, isPackingSuperVisor } = usePermissions(permission);
  const [autoPrintShipment, setAutoPrintShipment] = useState(isQCSummary);

  let isQCPassed = true;

  if (qcData) {
    for (let i = 0, len = qcData.length; i < len; i += 1) {
      if (!AFTER_QC_PASS_STATUS.includes(qcData[i].status)) {
        isQCPassed = false;
      }
    }
  }

  const isPackingOrQc = () => {
    if (url.includes('/packing-summary')) {
      return 'packing';
    }
    if (url.includes('/qc-summary')) {
      return 'qc';
    }
    return '';
  };

  const printPackingInvoiceFun = (manualRetry = false) => {
    if (featureToggle.forcePrintEnabled?.includes(facilityCode) && manualRetry) {
      manualRetryCount.current += 1;
    }
    dispatch(
      printPackingInvoiceLoad({
        shipmentId: shipmentHeaders.shippingPackageId,
        panel: isPackingOrQc(),
        forcePrint: manualRetryCount.current > 2
      })
    );
  };

  useEffect(() => {
    dispatch(getConsulKeyValue(['featureToggle']));
    const isBulkOrder = shipmentHeaders?.navChannel?.toLowerCase()?.includes('bulk');
    if (isQCSummary && isBulkOrder) {
      printPackingInvoiceFun();
    } else if (
      shipmentHeaders &&
      isPackingSummary &&
      (role === USER_SUPERVISOR || packingDetailStatus === PENDING) &&
      shipmentHeaders?.navChannel !== 'MPDTC'
    ) {
      printPackingInvoiceFun();
    } else if (
      isPackingSummary &&
      role === AGENT &&
      packingDetailStatus === DONE &&
      url.includes('/packing-summary')
    ) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'To print shipping label, Please contact to supervisor',
          severity: 'warning'
        })
      );
    }
  }, []);

  /** Used to give focus on input field */
  const focusOnInput = () => {
    const barcodeEl = document.getElementById('barcodeId');
    if (barcodeEl) {
      barcodeEl.focus();
    }
  };

  useEffect(() => {
    if (printPackingInvoiceLoaded) {
      printBlobPdf(printPackingInvoiceLoaded, () => {
        /** Used to give focus when Invoice Printed  */
        focusOnInput();
      });
      setAutoPrintShipment(true);
      dispatch(printPackingInvoiceReset());
    }
  }, [printPackingInvoiceLoaded]);

  /** Used to give color to header values according to panel */
  const headerValueColor = (mainKey) => {
    if (window.location.pathname.includes('/packing')) {
      return PACKING_COLOUR[mainKey];
    }
    return QC_COLOUR[mainKey];
  };

  const showPrintOptions = () => {
    const isBulkOrder = shipmentHeaders?.navChannel?.toLowerCase()?.includes('bulk');
    const isSBRTOrder = shipmentHeaders?.navChannel?.toLowerCase()?.includes('dtc');
    let isValidFR0 = false;
    if (isFr0Order) {
      if (!isBulkOrder) {
        isValidFR0 = true;
      }
      if (orderDetailStatus !== 201 || isQCSuperVisor) {
        isValidFR0 = true;
      }
    }
    const showPrintShipmentForQC = isQCSummary && isQCPassed && isValidFR0;
    const isPrintInvoiceEnabled =
      (isPackingSummary && (packingDetailStatus !== DONE || role === USER_SUPERVISOR)) ||
      (isQCSummary && (isFR1order || isValidFR0 || isSBRTOrder));
    const isPrintShipmentEnabled = (isPackingSummary || showPrintShipmentForQC) && shipmentHeaders;
    return (
      <div className="display-flex  align-items-center bg-white">
        {isPrintInvoiceEnabled && (
          <Button
            variant="outlined"
            color="primary"
            disabled={printPackingInvoiceLoading || shipmentHeaders?.navChannel === 'MPDTC'}
            onClick={() => printPackingInvoiceFun(true)}
            className={classes.btnRadius}
          >
            Print Invoice
          </Button>
        )}
        {isPrintShipmentEnabled && (
          <PrintShipmentNew
            enableAutoPrint={autoPrintShipment && manualRetryCount.current === 0}
            disableAutoPrint={setAutoPrintShipment}
            showPrintButton
            incrementId={shipmentHeaders.incrementId}
            shippingPackageId={shipmentHeaders.shippingPackageId}
            navChannel={shipmentHeaders.navChannel}
            unicomOrderCode={shipmentHeaders.unicomOrderCode}
            isWebB2BOrder={isWebB2BOrder}
            role={role}
            packingDetailStatus={packingDetailStatus}
          />
        )}
      </div>
    );
  };
  const fromPackingPage = url.includes('/packing');

  return (
    <div className="display-flex justify-content-space-between wd-100 align-items-center">
      <div className="display-flex  align-items-center bg-white">
        {data.map(
          ({ main_key: mainKey, onClick, className = '', flag = '', key, value, star = '' }) => (
            <div key={key} className="display-flex flex-direction-column mr-r45">
              <div className="display-flex">
                <Typography
                  variant="body1"
                  style={{ color: mainKey === 'DO_NOT_PRINT_INVOICE' ? '#ff0000' : '' }}
                >
                  {key}
                </Typography>
              </div>
              {key === 'Order ID' &&
                (isQCSuperVisor || isPackingSuperVisor ? (
                  <Link
                    className={`fs20 fw-bold mr-t8 ${className}`}
                    target="_blank"
                    to={`/order/${value}/overview`}
                  >
                    {value}
                  </Link>
                ) : (
                  <Box sx={{ color: 'gray' }} className={`fw-bold mr-t8 ${className}`}>
                    {value}
                  </Box>
                ))}
              {key === 'Shipment ID' &&
                (isQCSuperVisor || isPackingSuperVisor ? (
                  <Link
                    className={`fs20 fw-bold mr-t8 ${className}`}
                    target="_blank"
                    to={`/order/${value}/overview`}
                  >
                    {value}
                  </Link>
                ) : (
                  <Box sx={{ color: 'gray' }} className={`fw-bold mr-t8 ${className}`}>
                    {value}
                  </Box>
                ))}
              {key !== 'Order ID' && key !== 'Shipment ID' ? (
                <div
                  onClick={onClick}
                  style={{
                    color: headerValueColor(mainKey) ? headerValueColor(mainKey) : ''
                  }}
                  className={`fs16 fw-bold ${className}
                  ${
                mainKey === 'ORDER_SOURCE' && !fromPackingPage
                  ? classes.orderSourceImgDiv
                  : 'mr-t8'
                }
                ${
                (key === 'Dispatch' && value === 'Delayed') ||
                  (mainKey === 'ORDER_TYPE' &&
                    value &&
                    value.toLowerCase().split(' ').join('') === 'shiptocustomer')
                  ? classes.delayedColor
                  : ''
                }`}
                >
                  {flag} {value && `${value}` !== '0' ? value : 'NA'}{' '}
                  {star && <StarIcon className={classes.star} />}
                </div>
              ) : (
                ''
              )}
            </div>
          )
        )}
      </div>
      {showPrintOptions()}
    </div>
  );
};
export default WMInfoStrip;
