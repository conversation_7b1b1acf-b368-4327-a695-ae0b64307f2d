import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Field } from 'react-final-form';
import { makeStyles } from 'tss-react/mui';

import { isEmpty } from 'utils/validation';
import LkInput from 'components/MaterialUi/LkInput';
import { LOCALISATION } from 'redux/reducers/localisation';

import LkModal from '../Modal/Modal';
import QCForm from './QCForm';
import CLQCForm from './CLQCForm';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  table: {
    minWidth: 700
  },
  tableContainer: {
    maxHeight: 270
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },
  tableBulkqc: {
    marginTop: 73,
    marginBottom: 57
  }
}));

const BulkInvoiceModal = (props) => {
  const { classes } = useStyles();
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const { open, close, confirmBulkQc, vendorInvoiceData, clFailData } = props;
  const [bulkQCData, setBulkQCData] = useState([]);

  useEffect(() => {
    const { invoiceData, bulkQcData } = props;
    const bulkQC = [];
    if (vendorInvoiceData.invoice_level === 'SUMMARY') {
      invoiceData.invoiceData.forEach((row) => {
        const { quantity_original, accepted_quantity, rejected_quantity, product_id } = row;
        const short_qty =
          Number(quantity_original) - Number(accepted_quantity) - Number(rejected_quantity);

        const index = bulkQcData.findIndex((x) => Number(x.product_id) === Number(product_id));
        bulkQC.push({
          ...row,
          bulk_qc_failed_qty: index > -1 ? bulkQcData[index].bulk_qc_failed_qty : null,
          reason: index > -1 ? bulkQcData[index].reason : null,
          short_qty
        });

        return null;
      });
    } else {
      invoiceData.invoiceData.forEach((row) => {
        const { quantity_original, accepted_quantity, rejected_quantity, product_id } = row;
        const short_qty =
          Number(quantity_original) - Number(accepted_quantity) - Number(rejected_quantity);
        if (short_qty > 0) {
          const index = bulkQcData.findIndex((x) => Number(x.product_id) === Number(product_id));
          bulkQC.push({
            ...row,
            bulk_qc_failed_qty: index > -1 ? bulkQcData[index].bulk_qc_failed_qty : null,
            reason: index > -1 ? bulkQcData[index].reason : null,
            short_qty
          });
        }
        return null;
      });
    }
    setBulkQCData(bulkQC);
  }, []);

  const onSubmit = () => {};

  const maxGrnNumber = (max) => (value) => {
    if (!isEmpty(value) && value > max) {
      return INVOICE.QTY_CANNOT_EXCEED_SHORT_QTY;
    }
    return null;
  };

  const createTextField = (name, field, validate, className) => (
    <Field className="form-control" name={`${name}.${field}`} validate={validate}>
      {({ input, meta }) => (
        <div className="material-group">
          <LkInput className={classes[className]} {...input} variant="outlined" />
          <span style={{ marginTop: '0px' }} className="input-error">
            {meta.error && meta.touched ? meta.error : ''}
          </span>
        </div>
      )}
    </Field>
  );

  const confirm = (value) => {
    confirmBulkQc(value.invoiceData);
    close();
  };

  const renderForm = () => {
    if (vendorInvoiceData.invoice_level === 'SUMMARY') {
      return (
        <CLQCForm
          bulkQCData={bulkQCData}
          clFailData={clFailData}
          classes={classes}
          confirm={confirm}
          confirmBulkQc={confirmBulkQc}
          createTextField={createTextField}
          maxGrnNumber={maxGrnNumber}
          onSubmit={onSubmit}
          vendorInvoiceData={vendorInvoiceData}
        />
      );
    }

    if (bulkQCData.length) {
      return (
        <QCForm
          bulkQCData={bulkQCData}
          classes={classes}
          confirm={confirm}
          confirmBulkQc={confirmBulkQc}
          createTextField={createTextField}
          maxGrnNumber={maxGrnNumber}
          onSubmit={onSubmit}
        />
      );
    }

    return <div className="text-center mr-b40">{INVOICE.NO_PID_FOR_BULK_QC_FAIL}</div>;
  };

  return (
    <LkModal
      modalHeight="510px"
      modalWidth={
        vendorInvoiceData.items.length > 0 || vendorInvoiceData.invoice_level === 'SUMMARY'
          ? null
          : '520px'
      }
      upperHeading={INVOICE.BULK_QC_FAIL}
      open={open}
      handleClose={() => close()}
      showActionButton={false}
    >
      {renderForm()}
    </LkModal>
  );
};

export default BulkInvoiceModal;
