import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import {
  LocationDetailsStatusType
} from 'views/CycleCount/CycleCountHelper';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({
  status: {
    fontWeight: 600
  }
}));

const LocationDetailsTable = () => {
  const { classes } = useStyles();
  const [tableHeight] = useWindowResize(window.innerHeight - 285);
  const { data, isLoading, totalCount } = useSelector(
    (state) => state.cycleCount.locationDetails
  );
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CYCLE_COUNT);

  const LOCATION_DETAILS_HEADER_CONFIG = {
    BARCODE: { key: 'barcode', name: CONTENT.ITEM_BARCODES },
    BOXBARCODE: { key: 'boxBarcode', name: CONTENT.BOX_BARCODES },
    STATUS: { key: 'status', name: CONTENT.STATUS }
  };
  const [locationDetails, setLocationDetails] = useState([]);
  const [appliedFilter, setAppliedFilter] = useState([]);
  const [listTotalCount, setListTotalCount] = useState(totalCount);

  useEffect(() => {
    if (appliedFilter.length > 0) {
      const locationDetailsData = data.items;
      const filterData = locationDetailsData.filter((items) =>
        items[appliedFilter[0].key].includes(appliedFilter[0].value)
      );
      setListTotalCount(filterData.length);
      setLocationDetails(filterData);
    } else {
      setListTotalCount(totalCount);
      setLocationDetails(data.items);
    }
  }, [data, appliedFilter]);

  const filters = (filterData) => {
    if (filterData.length > 0) {
      setAppliedFilter(() => {
        const filter = {
          key: filterData[0].key,
          label: filterData[0].name,
          value: filterData[0].value,
          disableDelete: false
        };

        return [filter];
      });
    }
  };

  const sortTable = (type, key) => {
    if (type) {
      setLocationDetails((prevBody) => {
        let sortArray;
        if (type === 'ASC') {
          sortArray = prevBody.slice().sort((firstValue, secondValue) =>
            firstValue[key].localeCompare(secondValue[key])
          );
        } else {
          sortArray = prevBody.slice().sort((firstValue, secondValue) =>
            secondValue[key].localeCompare(firstValue[key])
          );
        }
        return [...sortArray];
      });
    }
  };

  const LOCATION_DETAILS_HEADER = [
    {
      key: LOCATION_DETAILS_HEADER_CONFIG.BARCODE.key,
      name: LOCATION_DETAILS_HEADER_CONFIG.BARCODE.name,
      supportSort: true,
      align: 'center',
      columnName: LOCATION_DETAILS_HEADER_CONFIG.BARCODE.key,
      filterData: {
        type: 'input',
        columnName: LOCATION_DETAILS_HEADER_CONFIG.BARCODE.key
      }
    },
    {
      key: LOCATION_DETAILS_HEADER_CONFIG.BOXBARCODE.key,
      name: LOCATION_DETAILS_HEADER_CONFIG.BOXBARCODE.name,
      supportSort: true,
      columnName: LOCATION_DETAILS_HEADER_CONFIG.BOXBARCODE.key,
      align: 'center',
      filterData: {
        type: 'input',
        columnName: LOCATION_DETAILS_HEADER_CONFIG.BOXBARCODE.key
      }
    },
    {
      key: LOCATION_DETAILS_HEADER_CONFIG.STATUS.key,
      name: LOCATION_DETAILS_HEADER_CONFIG.STATUS.name,
      align: 'center',
      supportSort: true,
      columnName: LOCATION_DETAILS_HEADER_CONFIG.STATUS.key,
      filterData: {
        type: 'singleSelect',
        columnName: LOCATION_DETAILS_HEADER_CONFIG.STATUS.key,
        listData: LocationDetailsStatusType,
        selectedFilterList: appliedFilter
      },
      formatBody: useCallback(({ status }) => <Box className={classes.status}>{status}</Box>, [])
    }
  ];

  return (
    <LkTable
      tableHeight={tableHeight}
      headerConfig={LOCATION_DETAILS_HEADER}
      isDataFetching={isLoading}
      tableData={locationDetails}
      dataRequestFunction={sortTable}
      totalRowsCount={listTotalCount}
      rowKey="barcode"
      setFilters={filters}
    />
  );
};

export default LocationDetailsTable;
