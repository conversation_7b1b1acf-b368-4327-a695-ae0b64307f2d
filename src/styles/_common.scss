@use './variables' as *;

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: none;
}

:after,
:before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

body {
  font: #{$base-font-size}/#{$line-height-base} $base-font-family;
  color: $font-color-base;
  // opacity: $opacity-base;
  margin: 0;
  height: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol,
dl,
p,
h1,
h2,
h3,
h4,
h5,
h6,
address,
form,
table,
blockquote,
applet,
embed,
object,
iframe,
frameset {
  margin: 0;
}

.wd-100 {
  width: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

.hover-grey:hover {
  background-color: lightgrey;
}

.hover-link:hover {
  text-decoration: underline;
}
.pointer-events {
  pointer-events: none;
}

* [role='tooltip'] {
  z-index: 1202;
}

#release-version {
  position: fixed;
  bottom: 10px;
  right: 30px;
  z-index: 30;
  color: #030303;
  opacity: 0.6;
}

.tabselected {
  border: 1px solid #dddddd !important;
  border-top-left-radius: 8px !important;
  border-top-right-radius: 8px !important;
  border-bottom: none !important;
  position: relative !important;
  background-color: #ffffff !important;
}

.tabs {
  min-width: 160px !important;
  max-width: 160px !important;
  font-size: 11px !important;
}

.tabselected::after {
  content: '' !important;
  position: absolute !important;
  width: 100% !important;
  height: 10px !important;
  background-color: white !important;
  bottom: -10px !important;
}

.height90vh {
  height: 90vh;
}
