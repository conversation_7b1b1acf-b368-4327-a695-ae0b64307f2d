import { MarkerType } from 'reactflow';
import { SOURCES, TARGETS, EDGE_CONFIG } from './flowChartConfig';
import { FR0_EDGES, FR0_NODES } from './fr0WorkflowConfig';

const MONITOR_NODES = [
  {
    id: 'monitor-node-1',
    type: 'custom',
    position: { x: 80, y: 10 },
    data: {
      id: 1,
      label: 'Synced',
      key: 'Synced',
      sources: [SOURCES.RIGHT.id],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-2',
    type: 'custom',
    position: { x: 300, y: 10 },
    data: {
      id: 2,
      label: 'JIT Processing',
      key: 'JIT Processing',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-3',
    type: 'custom',
    position: { x: 520, y: 10 },
    data: {
      id: 3,
      label: 'Pending Picking',
      key: 'Pending Picking',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-4',
    type: 'custom',
    position: { x: 740, y: 10 },
    data: {
      id: 4,
      label: 'In Picking',
      key: 'In Picking',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-5',
    type: 'custom',
    position: { x: 960, y: 10 },
    data: {
      id: 5,
      label: 'Lens In Picking',
      key: 'Lens In Picking',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-6',
    type: 'custom',
    position: { x: 1180, y: 10 },
    data: {
      id: 6,
      label: 'Tray Making',
      key: 'Tray Making',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-7',
    type: 'custom',
    position: { x: 1400, y: 10 },
    data: {
      id: 7,
      label: 'Lens Tray Making',
      key: 'Lens Tray Making',
      sources: [SOURCES.BOTTOM.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-8',
    type: 'custom',
    position: { x: 80, y: 205 },
    data: {
      id: 8,
      label: 'MEI',
      key: 'MEI',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-9',
    type: 'custom',
    position: { x: 300, y: 205 },
    data: {
      id: 9,
      label: 'Fitting',
      key: 'Fitting',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-10',
    type: 'custom',
    position: { x: 520, y: 205 },
    data: {
      id: 10,
      label: 'Order QC',
      key: 'Order QC',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM_LEFT.id],
      targets: [TARGETS.LEFT.id, TARGETS.BOTTOM_RIGHT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-11',
    type: 'custom',
    position: { x: 740, y: 205 },
    data: {
      id: 11,
      label: 'Packing',
      key: 'Packing',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-12',
    type: 'custom',
    position: { x: 960, y: 205 },
    data: {
      id: 12,
      label: 'Manifest',
      key: 'Manifest',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-23',
    type: 'custom',
    position: { x: 1180, y: 205 },
    data: {
      id: 12,
      label: 'Ready To Ship',
      key: 'Ready To Ship',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-13',
    type: 'custom',
    position: { x: 80, y: 400 },
    data: {
      id: 13,
      label: 'JIT Processing',
      key: 'QC Fail:::JIT Processing',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-14',
    type: 'custom',
    position: { x: 300, y: 400 },
    data: {
      id: 14,
      label: 'Pending Picking',
      key: 'QC Fail:::Pending Picking',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-15',
    type: 'custom',
    position: { x: 520, y: 400 },
    data: {
      id: 15,
      key: 'QC Fail:::In Picking',
      label: 'In Picking',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-16',
    type: 'custom',
    position: { x: 740, y: 400 },
    data: {
      id: 16,
      key: 'QC Fail:::Lens In Picking',
      label: 'Lens In Picking',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-17',
    type: 'custom',
    position: { x: 960, y: 400 },
    data: {
      id: 17,
      key: 'QC Fail:::Tray Making',
      label: 'Tray Making',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-18',
    type: 'custom',
    position: { x: 1180, y: 400 },
    data: {
      id: 18,
      key: 'QC Fail:::Lens Tray Making',
      label: 'Lens Tray Making',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-19',
    type: 'custom',
    position: { x: 1400, y: 400 },
    data: {
      id: 19,
      key: 'QC Fail:::MEI',
      label: 'MEI',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-20',
    type: 'custom',
    position: { x: 1620, y: 400 },
    data: {
      id: 20,
      key: 'QC Fail:::Fitting',
      label: 'Fitting',
      nodeType: 'failed',
      sources: [SOURCES.TOP.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-21',
    type: 'custom',
    position: { x: 1610, y: 10 },
    data: {
      id: 21,
      key: 'Un Classified',
      label: 'Un Classified',
      nodeType: 'unclassified',
      sources: [],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'monitor-node-22',
    type: 'custom',
    position: { x: 1820, y: 10 },
    data: {
      id: 16,
      key: 'Shipment Not Generated',
      label: 'Shipment Not Generated',
      sources: [],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  }
];

const MONITOR_EDGES = [
  {
    id: 'monitor-edge-1',
    source: 'monitor-node-1',
    target: 'monitor-node-2',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-2',
    source: 'monitor-node-2',
    target: 'monitor-node-3',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-3',
    source: 'monitor-node-3',
    target: 'monitor-node-4',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-4',
    source: 'monitor-node-4',
    target: 'monitor-node-5',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-5',
    source: 'monitor-node-5',
    target: 'monitor-node-6',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-6',
    source: 'monitor-node-6',
    target: 'monitor-node-7',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-7',
    source: 'monitor-node-7',
    target: 'monitor-node-8',
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-8',
    source: 'monitor-node-8',
    target: 'monitor-node-9',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-9',
    source: 'monitor-node-9',
    target: 'monitor-node-10',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-10',
    source: 'monitor-node-10',
    target: 'monitor-node-13',
    sourceHandle: SOURCES.BOTTOM_LEFT.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-11',
    source: 'monitor-node-10',
    target: 'monitor-node-11',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-12',
    source: 'monitor-node-11',
    target: 'monitor-node-12',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-13',
    source: 'monitor-node-13',
    target: 'monitor-node-14',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-14',
    source: 'monitor-node-14',
    target: 'monitor-node-15',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-15',
    source: 'monitor-node-15',
    target: 'monitor-node-16',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-16',
    source: 'monitor-node-16',
    target: 'monitor-node-17',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-17',
    source: 'monitor-node-17',
    target: 'monitor-node-18',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-18',
    source: 'monitor-node-18',
    target: 'monitor-node-19',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-19',
    source: 'monitor-node-19',
    target: 'monitor-node-20',
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-21',
    source: 'monitor-node-20',
    target: 'monitor-node-10',
    sourceHandle: SOURCES.TOP.id,
    targetHandle: TARGETS.BOTTOM_RIGHT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'monitor-edge-22',
    source: 'monitor-node-12',
    target: 'monitor-node-23',
    ...EDGE_CONFIG
  }
];

const LENS_LAB_NODES = [
  {
    id: 'lens-lab-node-1',
    type: 'custom',
    position: { x: 80, y: 10 },
    data: {
      id: 1,
      label: 'Synced',
      key: 'SYNCED',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM.id],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-2',
    type: 'custom',
    position: { x: 300, y: 10 },
    data: {
      id: 2,
      label: 'OMA Synced',
      key: 'OMA SYNCED',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-3',
    type: 'custom',
    position: { x: 520, y: 10 },
    data: {
      id: 3,
      label: 'Blank PID Received',
      key: 'BLANK PID RECEIVED',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM_LEFT.id],
      targets: [TARGETS.LEFT.id, TARGETS.BOTTOM_RIGHT.id, TARGETS.LEFT_BOTTOM.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-4',
    type: 'custom',
    position: { x: 740, y: 10 },
    data: {
      id: 4,
      label: 'Blank In Picking',
      key: 'BLANK PICKING',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-5',
    type: 'custom',
    position: { x: 960, y: 10 },
    data: {
      id: 5,
      label: 'Lens Lab',
      key: 'LENS LAB',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-6',
    type: 'custom',
    position: { x: 1180, y: 10 },
    data: {
      id: 6,
      label: 'Tray Making',
      key: 'TRAY MAKING',
      sources: [SOURCES.BOTTOM_LEFT.id, SOURCES.BOTTOM.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-7',
    type: 'custom',
    position: { x: 80, y: 200 },
    data: {
      id: 7,
      label: 'OMA Synced failed',
      key: 'OMA SYNCED FAILED',
      sources: [],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-8',
    type: 'custom',
    position: { x: 300, y: 200 },
    data: {
      id: 8,
      label: 'rxu Rejected (OU)',
      key: 'RXU REJECTED',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.TOP.id, TARGETS.BOTTOM_LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-9',
    type: 'custom',
    position: { x: 520, y: 200 },
    data: {
      id: 9,
      label: 'Inventory Not Available',
      key: 'INVENTORY NOT AVAILABLE',
      sources: [SOURCES.TOP_RIGHT.id],
      targets: [TARGETS.TOP_LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-10',
    type: 'custom',
    position: { x: 740, y: 200 },
    data: {
      id: 10,
      nodeType: 'failed',
      label: 'QC_FAIL/LENSLAB BREAKAGE',
      key: 'QC_FAIL',
      sources: [SOURCES.BOTTOM.id],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-11',
    type: 'custom',
    position: { x: 1180, y: 200 },
    data: {
      id: 11,
      label: 'OUT OF LENS LAB',
      key: 'OUT OF LENSLAB',
      sources: [],
      targets: [TARGETS.TOP.id, TARGETS.BOTTOM.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-12',
    type: 'custom',
    position: { x: 300, y: 400 },
    data: {
      id: 12,
      nodeType: 'failed',
      label: 'OMA SYNCED',
      key: 'QC_FAIL:::OMA SYNCED',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM.id],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-13',
    type: 'custom',
    position: { x: 520, y: 400 },
    data: {
      id: 13,
      nodeType: 'failed',
      label: 'BLANK PID RECEIVED',
      key: 'QC_FAIL:::BLANK PID RECEIVED',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-14',
    type: 'custom',
    position: { x: 740, y: 400 },
    data: {
      id: 14,
      nodeType: 'failed',
      label: 'BLANK PICKING',
      key: 'QC_FAIL:::BLANK PICKING',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-15',
    type: 'custom',
    position: { x: 960, y: 400 },
    data: {
      id: 15,
      nodeType: 'failed',
      label: 'LENS LAB',
      key: 'QC_FAIL:::LENS LAB',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-16',
    type: 'custom',
    position: { x: 1180, y: 400 },
    data: {
      id: 16,
      nodeType: 'failed',
      label: 'TRAY MAKING',
      key: 'QC_FAIL:::TRAY MAKING',
      sources: [SOURCES.TOP.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-17',
    type: 'custom',
    position: { x: 1400, y: 10 },
    data: {
      id: 16,
      key: 'Un Classified',
      label: 'Un Classified',
      nodeType: 'unclassified',
      sources: [],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-18',
    type: 'custom',
    position: { x: 1620, y: 10 },
    data: {
      id: 17,
      label: 'Shipment Not Generated',
      key: 'Shipment Not Generated',
      sources: [],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-19',
    type: 'custom',
    position: { x: 300, y: 580 },
    data: {
      id: 18,
      nodeType: 'failed',
      label: 'rxu Rejected (OU)',
      key: 'QC_FAIL:::RXU REJECTED',
      targets: [TARGETS.TOP.id],
      sources: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'lens-lab-node-20',
    type: 'custom',
    position: { x: 520, y: 580 },
    data: {
      id: 19,
      nodeType: 'failed',
      label: 'Inventory Not Available',
      key: 'QC_FAIL:::INVENTORY NOT AVAILABLE',
      sources: [],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  }
];

const LENS_LAB_EDGES = [
  {
    id: 'lens-lab-edge-1',
    source: 'lens-lab-node-1',
    target: 'lens-lab-node-2',
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-2',
    source: 'lens-lab-node-2',
    target: 'lens-lab-node-3',
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-3',
    source: 'lens-lab-node-3',
    target: 'lens-lab-node-4',
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-4',
    source: 'lens-lab-node-4',
    target: 'lens-lab-node-5',
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-5',
    source: 'lens-lab-node-5',
    target: 'lens-lab-node-6',
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-6',
    source: 'lens-lab-node-1',
    target: 'lens-lab-node-7',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-8',
    source: 'lens-lab-node-2',
    target: 'lens-lab-node-8',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-9',
    source: 'lens-lab-node-3',
    target: 'lens-lab-node-9',
    sourceHandle: SOURCES.BOTTOM_LEFT.id,
    targetHandle: TARGETS.TOP_LEFT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-10',
    source: 'lens-lab-node-9',
    target: 'lens-lab-node-3',
    sourceHandle: SOURCES.TOP_RIGHT.id,
    targetHandle: TARGETS.BOTTOM_RIGHT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-11',
    source: 'lens-lab-node-8',
    target: 'lens-lab-node-3',
    targetHandle: TARGETS.LEFT_BOTTOM.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-12',
    source: 'lens-lab-node-6',
    target: 'lens-lab-node-10',
    sourceHandle: SOURCES.BOTTOM_LEFT.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-13',
    source: 'lens-lab-node-6',
    target: 'lens-lab-node-11',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-14',
    source: 'lens-lab-node-10',
    target: 'lens-lab-node-12',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP_RIGHT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-15',
    source: 'lens-lab-node-12',
    target: 'lens-lab-node-19',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-16',
    source: 'lens-lab-node-13',
    target: 'lens-lab-node-20',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },

  {
    id: 'lens-lab-edge-17',
    source: 'lens-lab-node-12',
    target: 'lens-lab-node-13',
    sourceHandle: SOURCES.RIGHT.id,
    targetHandle: TARGETS.LEFT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-18',
    source: 'lens-lab-node-13',
    target: 'lens-lab-node-14',
    sourceHandle: SOURCES.RIGHT.id,
    targetHandle: TARGETS.LEFT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-19',
    source: 'lens-lab-node-14',
    target: 'lens-lab-node-15',
    sourceHandle: SOURCES.RIGHT.id,
    targetHandle: TARGETS.LEFT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-20',
    source: 'lens-lab-node-15',
    target: 'lens-lab-node-16',
    sourceHandle: SOURCES.RIGHT.id,
    targetHandle: TARGETS.LEFT.id,
    ...EDGE_CONFIG
  },
  {
    id: 'lens-lab-edge-21',
    source: 'lens-lab-node-16',
    target: 'lens-lab-node-11',
    sourceHandle: SOURCES.TOP.id,
    targetHandle: TARGETS.BOTTOM.id,
    ...EDGE_CONFIG
  }
];

const EXTERNAL_VENDOR_NODES = [
  {
    id: 'external-vendor-node-1',
    type: 'custom',
    position: { x: 80, y: 10 },
    data: {
      id: 1,
      label: 'Synced',
      key: 'SYNCED',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-2',
    type: 'custom',
    position: { x: 300, y: 10 },
    data: {
      id: 2,
      label: 'Po Generated',
      key: 'PO GENERATED',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-3',
    type: 'custom',
    position: { x: 520, y: 10 },
    data: {
      id: 2,
      label: 'Po Raised',
      key: 'PO RAISED',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-4',
    type: 'custom',
    position: { x: 740, y: 10 },
    data: {
      id: 3,
      label: 'Inwarded',
      key: 'INWARDED',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-5',
    type: 'custom',
    position: { x: 960, y: 10 },
    data: {
      id: 4,
      label: 'Tray Making',
      key: 'TRAY MAKING',
      sources: [SOURCES.RIGHT.id, SOURCES.BOTTOM.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-6',
    type: 'custom',
    position: { x: 1180, y: 10 },
    data: {
      id: 4,
      label: 'Out Of Manual JIT',
      key: 'OUT OF MANUAL JIT',
      sources: [SOURCES.BOTTOM.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-7',
    type: 'custom',
    position: { x: 960, y: 220 },
    data: {
      id: 5,
      label: 'Synced',
      key: 'QC_FAIL:::SYNCED',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.TOP.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-8',
    type: 'custom',
    position: { x: 1180, y: 220 },
    data: {
      id: 6,
      label: 'PO Generated',
      key: 'QC_FAIL:::PO GENERATED',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-9',
    type: 'custom',
    position: { x: 1400, y: 220 },
    data: {
      id: 2,
      label: 'Po Raised',
      key: 'QC_FAIL:::PO RAISED',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-10',
    type: 'custom',
    position: { x: 1620, y: 220 },
    data: {
      id: 7,
      label: 'Inwarded',
      key: 'QC_FAIL:::INWARDED',
      nodeType: 'failed',
      sources: [SOURCES.RIGHT.id],
      targets: [TARGETS.LEFT.id]
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  },
  {
    id: 'external-vendor-node-11',
    type: 'custom',
    position: { x: 1400, y: 10 },
    data: {
      id: 7,
      label: 'Un Classified',
      key: 'Un Classified',
      nodeType: 'failed',
      sources: [],
      targets: []
    },
    markerEnd: { type: MarkerType.ArrowClosed }
  }
];

const EXTERNAL_VENDOR_EDGES = [
  {
    id: 'external-vendor-edge-1',
    source: 'external-vendor-node-1',
    target: 'external-vendor-node-2',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-2',
    source: 'external-vendor-node-2',
    target: 'external-vendor-node-3',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-3',
    source: 'external-vendor-node-3',
    target: 'external-vendor-node-4',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-4',
    source: 'external-vendor-node-4',
    target: 'external-vendor-node-5',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-5',
    source: 'external-vendor-node-5',
    target: 'external-vendor-node-6',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-6',
    source: 'external-vendor-node-5',
    target: 'external-vendor-node-7',
    sourceHandle: SOURCES.BOTTOM.id,
    targetHandle: TARGETS.TOP.id,
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-7',
    source: 'external-vendor-node-7',
    target: 'external-vendor-node-8',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-8',
    source: 'external-vendor-node-8',
    target: 'external-vendor-node-9',
    ...EDGE_CONFIG
  },
  {
    id: 'external-vendor-edge-9',
    source: 'external-vendor-node-9',
    target: 'external-vendor-node-10',
    ...EDGE_CONFIG
  }
];

export const MONITOR_NODE_CONFIG = {
  'LENS LAB': LENS_LAB_NODES,
  'EXTERNAL VENDOR': EXTERNAL_VENDOR_NODES,
  'DO LENS LAB': LENS_LAB_NODES,
  DEFAULT: MONITOR_NODES,
  FR0: FR0_NODES,
  BULK: FR0_NODES,
  CL: FR0_NODES
};

export const MONITOR_EDGE_CONFIG = {
  'LENS LAB': LENS_LAB_EDGES,
  'EXTERNAL VENDOR': EXTERNAL_VENDOR_EDGES,
  'DO LENS LAB': LENS_LAB_EDGES,
  DEFAULT: MONITOR_EDGES,
  FR0: FR0_EDGES,
  BULK: FR0_EDGES,
  CL: FR0_EDGES
};
