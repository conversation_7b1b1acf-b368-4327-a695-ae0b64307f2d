import React from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box, Button } from '@mui/material';
import SelectBoxes from './SelectBoxes';
import { manualOverrideLoad } from '../../redux/actionCreators/grnListing';

const styles = makeStyles()((theme) => ({
  button: {
    borderRadius: theme.spacing(1),
    marginLeft: theme.spacing(2)
  }
}));

const BlockedTabActions = ({
  selectBox,
  grnCode,
  reset,
  inputBox,
  handleSelectBox,
  handleInputBox,
  actionValues,
  clearSelection,
  dispatch
}) => {
  const { classes } = styles();
  const confirmFun = () => {
    const payload = {
      payload: actionValues,
      filter: {
        key: selectBox,
        value: inputBox,
        grnCode
      }
    };
    dispatch(manualOverrideLoad(payload));
  };
  const enable =
    actionValues.manual_override_allowed.length === 0 &&
    actionValues.manual_override_rejected.length === 0;
  return (
    <div className={classes.root}>
      <Box p={2} display="flex" justifyContent="space-between" alignItems="center">
        <Box display="flex">
          <SelectBoxes
            selectBox={selectBox}
            inputBox={inputBox}
            handleSelectBox={handleSelectBox}
            handleInputBox={handleInputBox}
          />
          {selectBox !== 0 && inputBox !== '' && (
            <Box display="flex" alignItems="center">
              <Button
                variant="outlined"
                color="primary"
                size="medium"
                className={classes.button}
                onClick={reset}
              >
                RESET
              </Button>
            </Box>
          )}
        </Box>

        <Box>
          <Button
            variant="outlined"
            disabled={enable}
            color="primary"
            onClick={clearSelection}
            size="medium"
            className={classes.button}
          >
            CANCEL
          </Button>
          <Button
            disabled={enable}
            variant="outlined"
            onClick={confirmFun}
            color="primary"
            size="medium"
            className={classes.button}
          >
            CONFIRM
          </Button>
        </Box>
      </Box>
    </div>
  );
};

export default BlockedTabActions;
