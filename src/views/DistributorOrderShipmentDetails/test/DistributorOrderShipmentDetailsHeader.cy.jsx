/* eslint-disable cypress/no-unnecessary-waiting */
import React from 'react';
import { getPickingScanSummarySuccess } from 'redux/reducers/pickingScan';
import { setUserDetails } from 'redux/reducers/login';
import {
  createPickingSummarySuccess,
  updateDOShipmentDetailsListSuccess
} from 'redux/reducers/doShipmentList';
import { getConsulKeyValueSuccess } from 'redux/reducers/consul';
import DistributorOrderShipmentDetailsHeader from '../DistributorOrderShipmentDetailsHeader';
import CONTENT from '../../../../public/localisation/lang_en.json';
import response from './data/mock.json';
import pickingSummaryMock from './data/pickingSummary.json';
import userDetails from './data/userDetails.json';
import printShipmentData from './data/printShipmentData.json';

import config from '../../../config';

describe('DistributorOrderShipmentDetailsHeader', () => {
  let onSearchSpy;
  let onDownloadFunSpy;
  let onDownloadFun;
  let shippingPackageID = '';

  beforeEach(() => {
    cy.viewport('macbook-15');
    onSearchSpy = cy.spy().as('onSearch');
    onDownloadFunSpy = cy.spy().as('onDownloadFun');
    onDownloadFun = cy.spy().as('onDownloadFun');
    shippingPackageID = 121;
  });

  it('Initial render of DistributorOrderShipmentDetailsHeader', () => {
    const data = [];

    cy.mount(
      <DistributorOrderShipmentDetailsHeader
        onSearch={onSearchSpy}
        onDownloadFun={onDownloadFunSpy}
        CONTENT={CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS}
        shippingPackageID={shippingPackageID}
        headerData={null}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!data.length}
      />
    );

    cy.dispatch(
      getConsulKeyValueSuccess({
        values: [
          {
            createIndex: 19911,
            value:
              // eslint-disable-next-line max-len
              '{\n  "enableBarcodeHistoryOperationId": true,\n  "qcCaseImageFacilityList": [\n    "NXS2",\n    "SGNXS1"\n  ],\n  "sensieUsers": [\n    "LSP03661"\n  ],\n  "enableOwnDaysCard": [\n    "NXS1"\n  ],\n  "markAsBadBarcode": true,\n  "forcePrintEnabled": [\n    "NXS2"\n  ],\n  "showCompletePutAwayFacilities": {\n    "SGNXS1": {\n      "executionUserId": "SGNXS1_PUTAWAY",\n      "sources": [\n        "NEXS",\n        "ATHENA"\n      ]\n    },\n    "NXS2": {\n      "executionUserId": "NXS2_RETURNS",\n      "sources": [\n        "ATHENA"\n      ],\n      "types": [\n        "PUTAWAY_GATEPASS_ITEM",\n        "PUTAWAY_INVENTORY_ADJUSTMENT"\n      ]\n    }\n  },\n  "markDamageStatus": [\n    "IN_TRAY",\n    "PICKED",\n    "EDGING",\n    "PENDING_CUSTOMIZATION"\n  ],\n  "stockOutFallbackFacilities": [\n    "NXS2"\n  ],\n  "stockOutFallbackErrorMessages": [\n    "PicklistOrderItem not found",\n    "Unable to fetch response from unicom"\n  ],\n  "stockOutErrorMessageToShowAddInventory": [\n    "Unable to scan barcode"\n  ],\n  "showStockOut": {\n    "NXS2": [\n      "CREATED",\n      "JIT_PROCESSING",\n      "JIT_PR_RAISED",\n      "JIT_PR_GENERATED",\n      "IN_PICKING"\n    ],\n    "defaultStatus": [\n      "IN_PICKING"\n    ]\n  },\n  "showMarkFullFillableForUff": {\n    "NXS2": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    },\n    "NXS1": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    }\n  },\n  "wareHouseRecieve": {\n    "NXS2": {\n      "vendors": [\n        "Lenskart Eyetech Solutions Pvt. Ltd."\n      ]\n    }\n  },\n  "autoToManualConditions": {\n    "QNXS2": {\n      "status": [\n        "CREATED",\n        "JIT_PROCESSING",\n        "IN_PICKING"\n      ],\n      "email": [\n        "<EMAIL>",\n        "<EMAIL>"\n      ]\n    }\n  },\n  "inventoryExportBtn": [\n    "NXS2"\n  ],\n  "notFound": {\n    "SGNXS1": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    },\n    "NXS2": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    }\n  },\n  "doPicking": {\n    "printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"],\n    "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"],\n    "channelsToExclude": ["INT_BULKTOVENDOR"]\n  },\n  "qcLensPowerDetailsFacilityList": ["NXS2"]\n}\n',
            key: 'nexsconfig/nexs-ui,preprod-k8s/featureToggle',
            modifyIndex: 3368064
          }
        ],
        keys: ['featureToggle']
      })
    );

    cy.get('#DoShipmentDetailsglobalSearchId').should('exist');
    cy.get('#DoShipmentDetailsglobalSearchId').should(
      'have.attr',
      'placeholder',
      CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS.SEARCH
    );
    cy.get('#DoShipmentDetailsglobalSearchId').type(121);
    cy.get('@onSearch').should('have.been.calledOnce');

    cy.getByCy('arraw-back').should('exist').click();
    cy.getByCy('info-bar-items').should('exist');
    cy.getByCy('start-scanning').should('not.exist');
    cy.getByCy('close-picking').should('not.exist');
    cy.getByCy('print-shipping-label').should('not.exist');
    cy.getByCy('print-invoice').should('not.exist');
    cy.getByCy('export-csv').should('exist');
    cy.getByCy('export-csv').should('be.disabled');
  });

  it('render DistributorOrderShipmentDetailsHeader with status in_picking', () => {
    response.data.status = 'IN_PICKING';
    cy.mount(
      <DistributorOrderShipmentDetailsHeader
        onSearch={onSearchSpy}
        onDownloadFun={onDownloadFunSpy}
        CONTENT={CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS}
        shippingPackageID={shippingPackageID}
        headerData={response.data}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!response.data.pidListing.length}
      />
    );

    cy.dispatch(
      getConsulKeyValueSuccess({
        values: [
          {
            createIndex: 19911,
            value:
              // eslint-disable-next-line max-len
              '{\n  "enableBarcodeHistoryOperationId": true,\n  "qcCaseImageFacilityList": [\n    "NXS2",\n    "SGNXS1"\n  ],\n  "sensieUsers": [\n    "LSP03661"\n  ],\n  "enableOwnDaysCard": [\n    "NXS1"\n  ],\n  "markAsBadBarcode": true,\n  "forcePrintEnabled": [\n    "NXS2"\n  ],\n  "showCompletePutAwayFacilities": {\n    "SGNXS1": {\n      "executionUserId": "SGNXS1_PUTAWAY",\n      "sources": [\n        "NEXS",\n        "ATHENA"\n      ]\n    },\n    "NXS2": {\n      "executionUserId": "NXS2_RETURNS",\n      "sources": [\n        "ATHENA"\n      ],\n      "types": [\n        "PUTAWAY_GATEPASS_ITEM",\n        "PUTAWAY_INVENTORY_ADJUSTMENT"\n      ]\n    }\n  },\n  "markDamageStatus": [\n    "IN_TRAY",\n    "PICKED",\n    "EDGING",\n    "PENDING_CUSTOMIZATION"\n  ],\n  "stockOutFallbackFacilities": [\n    "NXS2"\n  ],\n  "stockOutFallbackErrorMessages": [\n    "PicklistOrderItem not found",\n    "Unable to fetch response from unicom"\n  ],\n  "stockOutErrorMessageToShowAddInventory": [\n    "Unable to scan barcode"\n  ],\n  "showStockOut": {\n    "NXS2": [\n      "CREATED",\n      "JIT_PROCESSING",\n      "JIT_PR_RAISED",\n      "JIT_PR_GENERATED",\n      "IN_PICKING"\n    ],\n    "defaultStatus": [\n      "IN_PICKING"\n    ]\n  },\n  "showMarkFullFillableForUff": {\n    "NXS2": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    },\n    "NXS1": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    }\n  },\n  "wareHouseRecieve": {\n    "NXS2": {\n      "vendors": [\n        "Lenskart Eyetech Solutions Pvt. Ltd."\n      ]\n    }\n  },\n  "autoToManualConditions": {\n    "QNXS2": {\n      "status": [\n        "CREATED",\n        "JIT_PROCESSING",\n        "IN_PICKING"\n      ],\n      "email": [\n        "<EMAIL>",\n        "<EMAIL>"\n      ]\n    }\n  },\n  "inventoryExportBtn": [\n    "NXS2"\n  ],\n  "notFound": {\n    "SGNXS1": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    },\n    "NXS2": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    }\n  },\n  "doPicking": {\n    "printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"],\n    "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"],\n    "channelsToExclude": ["INT_BULKTOVENDOR"]\n  },\n  "qcLensPowerDetailsFacilityList": ["NXS2"]\n}\n',
            key: 'nexsconfig/nexs-ui,preprod-k8s/featureToggle',
            modifyIndex: 3368064
          }
        ],
        keys: ['featureToggle']
      })
    );

    cy.getByCy('info-bar-items').should('exist');
    cy.getByCy('print-shipping-label').should('not.exist');
    cy.getByCy('print-invoice').should('not.exist');
    cy.getByCy('start-scanning').should('exist').click();
    cy.getByCy('navigationDrawer').should('not.exist');
    cy.getByCy('close-picking').should('exist').click();
    cy.getByCy('go-back-to-scanning').should('exist');
    cy.getByCy('go-back-to-scanning').should('be.disabled');

    cy.dispatch(getPickingScanSummarySuccess({ productQuantityDetailsList: [] }));

    cy.getByCy('navigationDrawer').should('exist');
    cy.getByCy('confirm-btn').should('exist');
    cy.getByCy('confirm-btn').should('be.disabled');
    cy.getByCy('go-back-to-scanning').should('exist');
    cy.wait(1000);
  });

  it('render DistributorOrderShipmentDetailsHeader with status in_picking with Product', () => {
    response.data.status = 'IN_PICKING';
    cy.mount(
      <DistributorOrderShipmentDetailsHeader
        onSearch={onSearchSpy}
        onDownloadFun={onDownloadFunSpy}
        CONTENT={CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS}
        shippingPackageID={shippingPackageID}
        headerData={response.data}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!response.data.pidListing.length}
      />
    );

    cy.dispatch(
      getConsulKeyValueSuccess({
        values: [
          {
            createIndex: 19911,
            value:
              // eslint-disable-next-line max-len
              '{\n  "enableBarcodeHistoryOperationId": true,\n  "qcCaseImageFacilityList": [\n    "NXS2",\n    "SGNXS1"\n  ],\n  "sensieUsers": [\n    "LSP03661"\n  ],\n  "enableOwnDaysCard": [\n    "NXS1"\n  ],\n  "markAsBadBarcode": true,\n  "forcePrintEnabled": [\n    "NXS2"\n  ],\n  "showCompletePutAwayFacilities": {\n    "SGNXS1": {\n      "executionUserId": "SGNXS1_PUTAWAY",\n      "sources": [\n        "NEXS",\n        "ATHENA"\n      ]\n    },\n    "NXS2": {\n      "executionUserId": "NXS2_RETURNS",\n      "sources": [\n        "ATHENA"\n      ],\n      "types": [\n        "PUTAWAY_GATEPASS_ITEM",\n        "PUTAWAY_INVENTORY_ADJUSTMENT"\n      ]\n    }\n  },\n  "markDamageStatus": [\n    "IN_TRAY",\n    "PICKED",\n    "EDGING",\n    "PENDING_CUSTOMIZATION"\n  ],\n  "stockOutFallbackFacilities": [\n    "NXS2"\n  ],\n  "stockOutFallbackErrorMessages": [\n    "PicklistOrderItem not found",\n    "Unable to fetch response from unicom"\n  ],\n  "stockOutErrorMessageToShowAddInventory": [\n    "Unable to scan barcode"\n  ],\n  "showStockOut": {\n    "NXS2": [\n      "CREATED",\n      "JIT_PROCESSING",\n      "JIT_PR_RAISED",\n      "JIT_PR_GENERATED",\n      "IN_PICKING"\n    ],\n    "defaultStatus": [\n      "IN_PICKING"\n    ]\n  },\n  "showMarkFullFillableForUff": {\n    "NXS2": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    },\n    "NXS1": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    }\n  },\n  "wareHouseRecieve": {\n    "NXS2": {\n      "vendors": [\n        "Lenskart Eyetech Solutions Pvt. Ltd."\n      ]\n    }\n  },\n  "autoToManualConditions": {\n    "QNXS2": {\n      "status": [\n        "CREATED",\n        "JIT_PROCESSING",\n        "IN_PICKING"\n      ],\n      "email": [\n        "<EMAIL>",\n        "<EMAIL>"\n      ]\n    }\n  },\n  "inventoryExportBtn": [\n    "NXS2"\n  ],\n  "notFound": {\n    "SGNXS1": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    },\n    "NXS2": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    }\n  },\n  "doPicking": {\n    "printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"],\n    "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"],\n    "channelsToExclude": ["INT_BULKTOVENDOR"]\n  },\n  "qcLensPowerDetailsFacilityList": ["NXS2"]\n}\n',
            key: 'nexsconfig/nexs-ui,preprod-k8s/featureToggle',
            modifyIndex: 3368064
          }
        ],
        keys: ['featureToggle']
      })
    );

    cy.getByCy('close-picking').should('exist').click();

    cy.dispatch(getPickingScanSummarySuccess(pickingSummaryMock.data));
    cy.getByCy('navigationDrawer').should('exist');
    cy.getByCy('confirm-btn').should('not.exist');
  });

  it('verify supervisor role flow in  DistributorOrderShipmentDetailsHeader ', () => {
    response.data.status = 'IN_PICKING';
    cy.mount(
      <DistributorOrderShipmentDetailsHeader
        onSearch={onSearchSpy}
        onDownloadFun={onDownloadFunSpy}
        CONTENT={CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS}
        shippingPackageID={shippingPackageID}
        headerData={response.data}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!response.data.pidListing.length}
      />
    );

    cy.dispatch(
      getConsulKeyValueSuccess({
        values: [
          {
            createIndex: 19911,
            value:
              // eslint-disable-next-line max-len
              '{\n  "enableBarcodeHistoryOperationId": true,\n  "qcCaseImageFacilityList": [\n    "NXS2",\n    "SGNXS1"\n  ],\n  "sensieUsers": [\n    "LSP03661"\n  ],\n  "enableOwnDaysCard": [\n    "NXS1"\n  ],\n  "markAsBadBarcode": true,\n  "forcePrintEnabled": [\n    "NXS2"\n  ],\n  "showCompletePutAwayFacilities": {\n    "SGNXS1": {\n      "executionUserId": "SGNXS1_PUTAWAY",\n      "sources": [\n        "NEXS",\n        "ATHENA"\n      ]\n    },\n    "NXS2": {\n      "executionUserId": "NXS2_RETURNS",\n      "sources": [\n        "ATHENA"\n      ],\n      "types": [\n        "PUTAWAY_GATEPASS_ITEM",\n        "PUTAWAY_INVENTORY_ADJUSTMENT"\n      ]\n    }\n  },\n  "markDamageStatus": [\n    "IN_TRAY",\n    "PICKED",\n    "EDGING",\n    "PENDING_CUSTOMIZATION"\n  ],\n  "stockOutFallbackFacilities": [\n    "NXS2"\n  ],\n  "stockOutFallbackErrorMessages": [\n    "PicklistOrderItem not found",\n    "Unable to fetch response from unicom"\n  ],\n  "stockOutErrorMessageToShowAddInventory": [\n    "Unable to scan barcode"\n  ],\n  "showStockOut": {\n    "NXS2": [\n      "CREATED",\n      "JIT_PROCESSING",\n      "JIT_PR_RAISED",\n      "JIT_PR_GENERATED",\n      "IN_PICKING"\n    ],\n    "defaultStatus": [\n      "IN_PICKING"\n    ]\n  },\n  "showMarkFullFillableForUff": {\n    "NXS2": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    },\n    "NXS1": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    }\n  },\n  "wareHouseRecieve": {\n    "NXS2": {\n      "vendors": [\n        "Lenskart Eyetech Solutions Pvt. Ltd."\n      ]\n    }\n  },\n  "autoToManualConditions": {\n    "QNXS2": {\n      "status": [\n        "CREATED",\n        "JIT_PROCESSING",\n        "IN_PICKING"\n      ],\n      "email": [\n        "<EMAIL>",\n        "<EMAIL>"\n      ]\n    }\n  },\n  "inventoryExportBtn": [\n    "NXS2"\n  ],\n  "notFound": {\n    "SGNXS1": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    },\n    "NXS2": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    }\n  },\n  "doPicking": {\n    "printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"],\n    "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"],\n    "channelsToExclude": ["INT_BULKTOVENDOR"]\n  },\n  "qcLensPowerDetailsFacilityList": ["NXS2"]\n}\n',
            key: 'nexsconfig/nexs-ui,preprod-k8s/featureToggle',
            modifyIndex: 3368064
          }
        ],
        keys: ['featureToggle']
      })
    );

    cy.dispatch(setUserDetails(userDetails));

    cy.intercept('POST', `${config.doShipment.closePicking}`, {
      statusCode: 200,
      body: {}
    }).as('closePicking');
    cy.intercept('GET', `${config.pickingScan.pickingSummary}**`, {
      statusCode: 200,
      body: pickingSummaryMock
    }).as('pickingScanSummary');
    cy.getByCy('close-picking').click();
    cy.getByCy('navigationDrawer').should('exist');

    cy.getByCy('confirm-btn').should('exist').click();
    cy.wait('@closePicking').its('response.statusCode').should('eq', 200);
    // waiting 12 sec to update status after closing picking
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(12000);
    cy.dispatch(updateDOShipmentDetailsListSuccess({ ...response.data, status: 'AWB_CREATED' }));
  });

  it('verify continue scanning  flow  DistributorOrderShipmentDetailsHeader ', () => {
    response.data.status = 'IN_PICKING';
    const pickingScanData = { ...pickingSummaryMock };
    pickingScanData.data.assignedTo = 'LSP03661';
    cy.mount(
      <DistributorOrderShipmentDetailsHeader
        onSearch={onSearchSpy}
        onDownloadFun={onDownloadFunSpy}
        CONTENT={CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS}
        shippingPackageID={shippingPackageID}
        headerData={response.data}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!response.data.pidListing.length}
      />
    );

    cy.dispatch(
      getConsulKeyValueSuccess({
        values: [
          {
            createIndex: 19911,
            value:
              // eslint-disable-next-line max-len
              '{\n  "enableBarcodeHistoryOperationId": true,\n  "qcCaseImageFacilityList": [\n    "NXS2",\n    "SGNXS1"\n  ],\n  "sensieUsers": [\n    "LSP03661"\n  ],\n  "enableOwnDaysCard": [\n    "NXS1"\n  ],\n  "markAsBadBarcode": true,\n  "forcePrintEnabled": [\n    "NXS2"\n  ],\n  "showCompletePutAwayFacilities": {\n    "SGNXS1": {\n      "executionUserId": "SGNXS1_PUTAWAY",\n      "sources": [\n        "NEXS",\n        "ATHENA"\n      ]\n    },\n    "NXS2": {\n      "executionUserId": "NXS2_RETURNS",\n      "sources": [\n        "ATHENA"\n      ],\n      "types": [\n        "PUTAWAY_GATEPASS_ITEM",\n        "PUTAWAY_INVENTORY_ADJUSTMENT"\n      ]\n    }\n  },\n  "markDamageStatus": [\n    "IN_TRAY",\n    "PICKED",\n    "EDGING",\n    "PENDING_CUSTOMIZATION"\n  ],\n  "stockOutFallbackFacilities": [\n    "NXS2"\n  ],\n  "stockOutFallbackErrorMessages": [\n    "PicklistOrderItem not found",\n    "Unable to fetch response from unicom"\n  ],\n  "stockOutErrorMessageToShowAddInventory": [\n    "Unable to scan barcode"\n  ],\n  "showStockOut": {\n    "NXS2": [\n      "CREATED",\n      "JIT_PROCESSING",\n      "JIT_PR_RAISED",\n      "JIT_PR_GENERATED",\n      "IN_PICKING"\n    ],\n    "defaultStatus": [\n      "IN_PICKING"\n    ]\n  },\n  "showMarkFullFillableForUff": {\n    "NXS2": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    },\n    "NXS1": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    }\n  },\n  "wareHouseRecieve": {\n    "NXS2": {\n      "vendors": [\n        "Lenskart Eyetech Solutions Pvt. Ltd."\n      ]\n    }\n  },\n  "autoToManualConditions": {\n    "QNXS2": {\n      "status": [\n        "CREATED",\n        "JIT_PROCESSING",\n        "IN_PICKING"\n      ],\n      "email": [\n        "<EMAIL>",\n        "<EMAIL>"\n      ]\n    }\n  },\n  "inventoryExportBtn": [\n    "NXS2"\n  ],\n  "notFound": {\n    "SGNXS1": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    },\n    "NXS2": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    }\n  },\n  "doPicking": {\n    "printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"],\n    "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"],\n    "channelsToExclude": ["INT_BULKTOVENDOR"]\n  },\n  "qcLensPowerDetailsFacilityList": ["NXS2"]\n}\n',
            key: 'nexsconfig/nexs-ui,preprod-k8s/featureToggle',
            modifyIndex: 3368064
          }
        ],
        keys: ['featureToggle']
      })
    );

    cy.dispatch(setUserDetails(userDetails));
    cy.intercept('GET', `${config.pickingScan.pickingSummary}**`, {
      statusCode: 200,
      body: pickingScanData
    }).as('pickingScanSummary');
    cy.getByCy('close-picking').click();

    cy.getByCy('navigationDrawer').should('exist');
    cy.getByCy('go-back-to-scanning').click();
    cy.dispatch(createPickingSummarySuccess({ id: 12 }));
  });
});

describe('render DistributorOrderShipmentDetailsHeader with status AWB_CREATED', () => {
  let onSearchSpy;
  let onDownloadFunSpy;
  let onDownloadFun;
  let shippingPackageID = '';
  beforeEach(() => {
    cy.viewport('macbook-15');
    onSearchSpy = cy.spy().as('onSearch');
    onDownloadFunSpy = cy.spy().as('onDownloadFun');
    onDownloadFun = cy.spy().as('onDownloadFun');
    shippingPackageID = 121;

    response.data.status = 'AWB_CREATED';
    cy.mount(
      <DistributorOrderShipmentDetailsHeader
        onSearch={onSearchSpy}
        onDownloadFun={onDownloadFunSpy}
        CONTENT={CONTENT.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS}
        shippingPackageID={shippingPackageID}
        headerData={response.data}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!response.data.pidListing.length}
      />
    );
    cy.dispatch(
      getConsulKeyValueSuccess({
        values: [
          {
            createIndex: 19911,
            value:
              // eslint-disable-next-line max-len
              '{\n  "enableBarcodeHistoryOperationId": true,\n  "qcCaseImageFacilityList": [\n    "NXS2",\n    "SGNXS1"\n  ],\n  "sensieUsers": [\n    "LSP03661"\n  ],\n  "enableOwnDaysCard": [\n    "NXS1"\n  ],\n  "markAsBadBarcode": true,\n  "forcePrintEnabled": [\n    "NXS2"\n  ],\n  "showCompletePutAwayFacilities": {\n    "SGNXS1": {\n      "executionUserId": "SGNXS1_PUTAWAY",\n      "sources": [\n        "NEXS",\n        "ATHENA"\n      ]\n    },\n    "NXS2": {\n      "executionUserId": "NXS2_RETURNS",\n      "sources": [\n        "ATHENA"\n      ],\n      "types": [\n        "PUTAWAY_GATEPASS_ITEM",\n        "PUTAWAY_INVENTORY_ADJUSTMENT"\n      ]\n    }\n  },\n  "markDamageStatus": [\n    "IN_TRAY",\n    "PICKED",\n    "EDGING",\n    "PENDING_CUSTOMIZATION"\n  ],\n  "stockOutFallbackFacilities": [\n    "NXS2"\n  ],\n  "stockOutFallbackErrorMessages": [\n    "PicklistOrderItem not found",\n    "Unable to fetch response from unicom"\n  ],\n  "stockOutErrorMessageToShowAddInventory": [\n    "Unable to scan barcode"\n  ],\n  "showStockOut": {\n    "NXS2": [\n      "CREATED",\n      "JIT_PROCESSING",\n      "JIT_PR_RAISED",\n      "JIT_PR_GENERATED",\n      "IN_PICKING"\n    ],\n    "defaultStatus": [\n      "IN_PICKING"\n    ]\n  },\n  "showMarkFullFillableForUff": {\n    "NXS2": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    },\n    "NXS1": {\n      "status": ["CREATED", "JIT_PROCESSING", "JIT_PR_RAISED", "JIT_PR_GENERATED", "IN_PICKING"],\n      "empCodes": ["141874", "LSP03661", "LSP06662"]\n    }\n  },\n  "wareHouseRecieve": {\n    "NXS2": {\n      "vendors": [\n        "Lenskart Eyetech Solutions Pvt. Ltd."\n      ]\n    }\n  },\n  "autoToManualConditions": {\n    "QNXS2": {\n      "status": [\n        "CREATED",\n        "JIT_PROCESSING",\n        "IN_PICKING"\n      ],\n      "email": [\n        "<EMAIL>",\n        "<EMAIL>"\n      ]\n    }\n  },\n  "inventoryExportBtn": [\n    "NXS2"\n  ],\n  "notFound": {\n    "SGNXS1": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    },\n    "NXS2": {\n      "status": [\n        "IN_PICKING"\n      ],\n      "NFStatus": {\n        "NOT_FOUND": "NF",\n        "TEMP_NOT_FOUND": "TNF"\n      }\n    }\n  },\n  "doPicking": {\n    "printStatus": ["AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED", "PARTIALLY_SHIPPED"],\n    "startScanningStatus": ["IN_PICKING", "PICKED", "PROCESSING"],\n    "channelsToExclude": ["INT_BULKTOVENDOR"]\n  },\n  "qcLensPowerDetailsFacilityList": ["NXS2"]\n}\n',
            key: 'nexsconfig/nexs-ui,preprod-k8s/featureToggle',
            modifyIndex: 3368064
          }
        ],
        keys: ['featureToggle']
      })
    );
    cy.getByCy('info-bar-items').should('exist');
    cy.getByCy('start-scanning').should('not.exist');
    cy.getByCy('close-picking').should('not.exist');

    cy.intercept('GET', `${config.apiPath.printShipmentNew}**`, {
      statusCode: 200,
      body: printShipmentData
    }).as('printShippingLabel');
    cy.getByCy('print-invoice').should('exist').click();
  });

  it('printShipment with same box value ', () => {
    cy.getByCy('print-shipping-label').should('exist').click();
    cy.wait('@printShippingLabel').its('response.statusCode').should('eq', 200);

    cy.getByCy('modal-upperHeading').should('exist');
    cy.get('#boxUpdateInput').should('exist');

    cy.get('#boxUpdateInput').type(1212);

    cy.intercept('PUT', `${config.doShipment.updateBoxCount}`, {
      statusCode: 400
    }).as('boxUpdateApi');
    cy.getByCy('primary-btn').click();

    cy.get('#boxUpdateInput').clear();
    cy.get('#boxUpdateInput').type(1);
    cy.getByCy('primary-btn').click();
  });

  it('printShipment with BoxUpdate success', () => {
    cy.getByCy('print-shipping-label').should('exist').click();
    cy.wait('@printShippingLabel').its('response.statusCode').should('eq', 200);

    cy.getByCy('modal-upperHeading').should('exist');
    cy.get('#boxUpdateInput').should('exist');

    cy.get('#boxUpdateInput').type(1212);

    cy.intercept('PUT', `${config.doShipment.updateBoxCount}`, {
      statusCode: 200
    }).as('boxUpdateApi');
    cy.getByCy('primary-btn').click();
  });
});
