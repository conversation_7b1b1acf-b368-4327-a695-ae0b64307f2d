import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { Box } from '@mui/material';

import Spinner from 'components/Spinner/Spinner';
import useFilterHook from 'components/common/useFilterHook';

import { getConsulKeyValue } from 'redux/reducers/consul';
import { LOCALISATION } from 'redux/reducers/localisation';
import { getStockTakeCycleListLoad } from 'redux/reducers/stockTake';

import useShowStockTakePage from 'common/useShowStockTakePage';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';

import StockTakeListBody from './StockTakeListBody';

const PAGE_SIZE = 35;

const defaultRequest = {
  sortBy: 'createdAt',
  sortOrder: 'DESC',
  page: 0,
  pageSize: PAGE_SIZE
};
const StockPickList = () => {
  const initialReq = useRef(0);
  const location = useLocation();
  const dispatch = useDispatch();
  const { showPage, showButton, isLoading } = useShowStockTakePage();

  const [requestBody, setRequestBody] = useState(defaultRequest);
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.STOCK_TAKE);

  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter } = useFilterHook();

  useEffect(() => {
    dispatch(getConsulKeyValue(['stockTakeFacility']));
  }, []);

  useEffect(() => {
    if (initialReq.current >= 1) {
      dispatch(getStockTakeCycleListLoad(requestBody));
    }
  }, [requestBody]);

  useEffect(() => {
    if (showPage) {
      initialReq.current += 1;

      const result = getAllQueryParam(location.search);

      const payload = generatePayloadForSearchAPI(result, {}, mapSearchFilterKey);

      setRequestBody((prevReqBody) => {
        const newState = {
          page: 0,
          pageSize: PAGE_SIZE,
          sortBy: prevReqBody.sortBy,
          sortOrder: prevReqBody.sortOrder,
          facilityCode: selectedFacility,
          ...payload
        };
        return newState;
      });
    }
  }, [location.search, showPage]);

  const fetchMoreListItems = (sortOrder, sortBy, page) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      sortBy,
      sortOrder,
      page
    }));
  };

  if (isLoading) {
    return <Spinner textAlign="center" />;
  }

  if (!showPage) {
    return (
      <Box
        component="h4"
        textAlign="center"
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100%"
      >
        {CONTENT.STOCK_TAKE_MUST_BE_ENABLED}
      </Box>
    );
  }

  return (
    <StockTakeListBody
      selectedFilterList={selectedFilterList}
      onChangeFilterList={onChangeFilterList}
      fetchMoreListItems={fetchMoreListItems}
      deleteSelectedFilter={deleteSelectedFilter}
      requestBody={requestBody}
      showButton={showButton}
    />
  );
};

export default StockPickList;
