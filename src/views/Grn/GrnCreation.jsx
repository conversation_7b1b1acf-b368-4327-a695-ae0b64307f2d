import React, { useEffect, useCallback, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, Button, Typography } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';

import './GrnCreation.scss';
import { makeStyles } from 'tss-react/mui';
import { Form, Field } from 'react-final-form';
import { required } from 'utils/validation';
import { useDispatch, useSelector } from 'react-redux';
import { convertDateFormat, debounce, getQueryParam } from 'utils/helpers';
import { purchaseOrderListLoad } from 'redux/actionCreators/purchaseOrder';
import { LOCALISATION } from 'redux/reducers/localisation';
import localStorageHelper from '../../utils/localStorageHelper';
import { toastOpen } from '../../redux/actionCreators/toast';
import Spinner from '../../components/Spinner/Spinner';

import {
  grnListLoad,
  grnCreateLoad,
  getInvoicesLoad,
  getInvoiceDetailLoad,
  resetGrnCreate,
  getInvoicesRESET,
  grnCreateReset
} from '../../redux/actionCreators/grn';
import {
  searchVendorList,
  resetInvoiceReferenceCreation,
  searchVendorListReset
} from '../../redux/actionCreators/invoiceReferenceCreation';
import LkInput from '../../components/MaterialUi/LkInput';

const mutatorsObj = {
  setVendor: (args, state, utils) => utils.changeValue(state, 'vendor', () => args[0]),
  setPo: (args, state, utils) => utils.changeValue(state, 'invoice', () => args[0]),
  setInvoice: (args, state, utils) => utils.changeValue(state, 'invoice', () => args[0]),
  setGrn: (args, state, utils) => utils.changeValue(state, 'grn', () => args[0])
};

const useStyles = makeStyles()((theme) => ({
  root: {
    padding: theme.spacing(3),
    height: '100%',
    display: 'flex',
    justifyContent: 'center'
  },
  card: {
    width: 550,
    margin: 'auto'
  },
  horizontalCenter: {
    display: 'grid',
    placeItems: 'center'
  },
  cardComponent: {
    paddingTop: 40,
    paddingBottom: 30,
    borderRadius: 8,
    transition: '0.3s',
    boxShadow: '0 8px 16px rgba(0,0,0,0.05)'
  },
  field: {
    width: 270
  }
}));

let poNumParam = null;
let invoiceParam = null;
let invoiceNameParam = null;
let vendorCode = null;
let grnCodeParam = null;

const GrnCreation = (props) => {
  let mutators;
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  
  const [vendorNameParam, setVendorNameParam] = useState(null);
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);

  const {
    grnCreateLoading,
    grnCreateSuccess,
    grnCreateError,
    invoiceList,
    invoiceDetail,
    invoiceDetailFail
  } = useSelector((state) => state.grn);
  const { purchaseOrderList, purchaseListError } = useSelector((state) => state.purchaseOrder);
  const { vendorList, vendorListError } = useSelector((state) => state.invoiceReferenceCreation);
  const { grnList, invoiceListError } = useSelector((state) => state.grn);

  const searchPO = useCallback(
    (value) => dispatch(purchaseOrderListLoad({ po_num: value })),
    [dispatch]
  );
  const searchVendor = useCallback((value) => dispatch(searchVendorList(value)), [dispatch]);

  const delayedQueryPO = useCallback(
    debounce((q) => q.length > 2 && searchPO(q), 500),
    []
  );
  const delayedQueryVendor = useCallback(
    debounce((q) => q.length > 2 && searchVendor(q), 500),
    []
  );

  useEffect(() => {
    if (purchaseListError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: purchaseListError?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [purchaseListError, dispatch]);

  useEffect(() => {
    if (invoiceDetailFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: invoiceDetailFail || '',
          severity: 'error'
        })
      );
    }
  }, [dispatch, invoiceDetailFail]);

  useEffect(() => {
    if (vendorListError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: vendorListError?.meta?.displayMessage,
          severity: 'error'
        })
      );
      dispatch(searchVendorListReset());
    }
    if (invoiceListError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: invoiceListError?.meta?.displayMessage,
          severity: 'error'
        })
      );
      dispatch(getInvoicesRESET());
    }
  }, [vendorListError, invoiceListError, dispatch]);

  useEffect(() => {
    if (grnCreateSuccess) {
      localStorageHelper.setItem('grn', grnCreateSuccess);
      dispatch(resetGrnCreate());
      dispatch(resetInvoiceReferenceCreation());
      navigate('/grn/home');
    }
    if (grnCreateError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading:
            grnCreateError?.response?.data?.display_message ||
            grnCreateError?.response?.data?.meta.displayMessage,
          severity: 'error'
        })
      );
      dispatch(grnCreateReset());
    }
  }, [grnCreateSuccess, grnCreateError, dispatch, props]);

  const submitGrn = (values) => {
    if (invoiceDetail) {
      let selectedGrnValue;
      if (values.grn) {
        selectedGrnValue = grnList?.result?.data?.find((t) => t.grn_code === values.grn);
      }
      let payload = {
        req: invoiceDetail?.data,
        selectedGrn: selectedGrnValue
      };
      if (values.batchNo) {
        payload = {
          ...payload,
          req: {
            ...invoiceDetail?.data,
            invoice: { ...invoiceDetail?.data?.invoice, batch_no: values.batchNo }
          }
        };
      }
      dispatch(grnCreateLoad(payload));
    }
  };

  const removeParam = () => {
    poNumParam = null;
    mutators.setPo(null);
    invoiceParam = null;
    mutators.setInvoice('');
    if (grnList?.result?.data) {
      grnList.result.data = undefined;
    }
    if (invoiceList.length > 0) {
      invoiceList.length = 0;
    }
  };
  const removeGRNParam = () => {
    grnCodeParam = null;
    mutators.setGrn(null);
  };

  useEffect(() => {
    const tempVendorNameParam = getQueryParam(location.search.replace('?', ''), 'vendorName');
    const tempVendorId = getQueryParam(location.search.replace('?', ''), 'vendorId');
    poNumParam = getQueryParam(location.search.replace('?', ''), 'poNumber');
    invoiceParam = getQueryParam(location.search.replace('?', ''), 'invoiceRefNum');
    invoiceNameParam = getQueryParam(location.search.replace('?', ''), 'invoiceName');
    grnCodeParam = getQueryParam(location.search.replace('?', ''), 'grnCode');
    setVendorNameParam(tempVendorNameParam);
    if (grnCodeParam) {
      mutators.setGrn(grnCodeParam);
    }
    if (invoiceParam) {
      mutators.setInvoice(invoiceParam);
      dispatch(grnListLoad({ invoiceNumber: invoiceParam }));
      dispatch(getInvoiceDetailLoad({ invoiceNum: invoiceParam }));
      dispatch(getInvoicesLoad({ po_num: poNumParam, vendor_code: tempVendorId }));
    } else if (poNumParam) {
      searchPO(poNumParam);
      // change vednorCode to poNumParam once it supports from backend
      dispatch(getInvoicesLoad({ po_num: poNumParam, vendor_code: tempVendorId }));
    }
    return () => {
      poNumParam = null;
      invoiceParam = null;
      invoiceNameParam = null;
      vendorCode = null;
      grnCodeParam = null;
      dispatch(resetGrnCreate());
      dispatch(resetInvoiceReferenceCreation());
    };
  }, [dispatch, props, mutators, searchPO]);

  const removeInvoice = () => {
    removeGRNParam();
    invoiceParam = null;
    if (grnList?.result?.data) {
      grnList.result.data = undefined;
    }
    mutators.setInvoice(null);
  };

  const changeHandlerPO = (newValue) => {
    mutators.setInvoice(null);
    invoiceList.length = 0;
    if (grnList?.result?.data) {
      grnList.result.data = undefined;
    }
    if (newValue) {
      removeInvoice();
      dispatch(
        getInvoicesLoad({
          po_num: newValue.po_num,
          vendor_code: vendorCode
        })
      );
    }
  };

  return (
    <div className="invoice-container display-flex justify-content-space-around">
      <div className={classes.root}>
        <Form
          onSubmit={submitGrn}
          mutators={mutatorsObj}
          render={({ handleSubmit, values, form }) => {
            mutators = form.mutators;
            return (
              <form onSubmit={handleSubmit} name="otp" className="otp-form" noValidate>
                <div className={classes.card}>
                  <Card className={classes.cardComponent}>
                    <CardContent>
                      <Typography className="bold mrB16 text-center" variant="h3">
                        {createGrnLocal.NEXS_GRN}
                      </Typography>

                      <Field name="vendor">
                        {({ input, meta, ...rest }) =>
                          !vendorNameParam ? (
                            <div className={classes.horizontalCenter}>
                              <div className="material-group mr-t40">
                                <Autocomplete
                                  {...rest}
                                  className={classes.field}
                                  options={vendorList}
                                  onChange={(event, newValue) => {
                                    mutators.setInvoice(null);
                                    removeInvoice();
                                    vendorCode = newValue?.code || null;
                                    dispatch(getInvoicesLoad({ vendor_code: vendorCode }));
                                  }}
                                  onInputChange={(event, newInputValue) => {
                                    poNumParam = null;
                                    mutators.setVendor(newInputValue);
                                  }}
                                  onKeyUp={(e) => delayedQueryVendor(e.target.value)}
                                  getOptionLabel={(option) => option?.name ?? option}
                                  filterOptions={(options) => options}
                                  renderInput={(params) => (
                                    <LkInput
                                      {...params}
                                      {...input}
                                      className="input-box"
                                      fullWidth
                                      label={createGrnLocal.ENTER_VENDOR}
                                    />
                                  )}
                                />
                                <span className="input-error">
                                  {(meta.touched && meta.error) || ''}
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div
                              className="
                              display-flex justify-content-space-between fs14 mr-t20 param-content"
                            >
                              <div>{vendorNameParam} </div>
                              <Box
                                onClick={() => setVendorNameParam()}
                                className="text-turquioise_surf cursor-pointer"
                              >
                                {createGrnLocal.REMOVE}
                              </Box>
                            </div>
                          )
                        }
                      </Field>

                      <Field name="PO">
                        {({ input, meta, ...rest }) =>
                          !poNumParam ? (
                            <div className={classes.horizontalCenter}>
                              <div className="material-group mr-t20">
                                <Autocomplete
                                  {...rest}
                                  className={classes.field}
                                  options={purchaseOrderList}
                                  onChange={(event, newValue) => changeHandlerPO(newValue)}
                                  onInputChange={(event, newInputValue) => {
                                    poNumParam = null;
                                    mutators.setPo(newInputValue);
                                  }}
                                  onKeyUp={(e) => delayedQueryPO(e.target.value)}
                                  getOptionLabel={(option) => option.po_num}
                                  filterOptions={(options) => options}
                                  renderInput={(params) => (
                                    <LkInput
                                      {...params}
                                      {...input}
                                      className="input-box"
                                      fullWidth
                                      label={createGrnLocal.ENTER_PO}
                                    />
                                  )}
                                />
                                <span className="input-error">
                                  {(meta.touched && meta.error) || ''}
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div
                              className="
                              display-flex justify-content-space-between fs14 mr-t20 param-content"
                            >
                              <div>{poNumParam} </div>
                              <Box
                                onClick={() => removeParam()}
                                className="text-turquioise_surf cursor-pointer"
                              >
                                {createGrnLocal.REMOVE}
                              </Box>
                            </div>
                          )
                        }
                      </Field>
                      <Field name="invoice" validate={required}>
                        {({ input, meta, ...rest }) => (
                          <div className={classes.horizontalCenter}>
                            {invoiceParam ? (
                              <div
                                style={{ width: '100%' }}
                                className="
                                  display-flex justify-content-space-between fs14 
                                  mr-t20 param-content"
                              >
                                <div>{invoiceNameParam} </div>
                                <Box
                                  onClick={removeInvoice}
                                  className="text-turquioise_surf cursor-pointer"
                                >
                                  {createGrnLocal.REMOVE}
                                </Box>
                              </div>
                            ) : (
                              <div className="material-group mr-t20">
                                <Autocomplete
                                  {...rest}
                                  options={invoiceList}
                                  className={classes.field}
                                  onChange={(event, newValue) => {
                                    const invoiceNumber = newValue?.invoice_ref_number;
                                    const vendorInvoiceNumber = newValue?.vendor_invoice_number;

                                    if (newValue) {
                                      dispatch(grnListLoad({ invoiceNumber }));
                                      dispatch(getInvoiceDetailLoad({ invoiceNum: invoiceNumber }));
                                      mutators.setGrn('');
                                      mutators.setInvoice(vendorInvoiceNumber);
                                    } else {
                                      if (grnList?.result?.data) {
                                        grnList.result.data = undefined;
                                      }
                                      mutators.setGrn('');
                                    }
                                  }}
                                  onInputChange={(event, newInputValue) => {
                                    mutators.setInvoice(newInputValue);
                                    mutators.setGrn('');
                                  }}
                                  getOptionLabel={(option) => option.vendor_invoice_number}
                                  renderOption={(renderProps, option) => (
                                    <li {...renderProps} key={option.vendor_invoice_number}>
                                      <div
                                        className="
                                      display-flex justify-content-space-between 
                                      align-items-center wd-100"
                                      >
                                        {option.vendor_invoice_number}
                                      </div>
                                    </li>
                                  )}
                                  disabled={!invoiceList.length}
                                  renderInput={(params) => (
                                    <LkInput
                                      {...params}
                                      {...input}
                                      className="input-box"
                                      fullWidth
                                      label={createGrnLocal.SELECT_INVOICE}
                                    />
                                  )}
                                />
                                <span className="input-error">
                                  {(meta.touched && meta.error) || ''}
                                </span>
                              </div>
                            )}
                          </div>
                        )}
                      </Field>
                      {grnList?.result?.data && (
                        <Field name="grn">
                          {({ input, ...rest }) => (
                            <div className=" mr-t20">
                              {grnCodeParam ? (
                                <div>
                                  <div
                                    className="
                                    display-flex justify-content-space-between fs14 param-content"
                                  >
                                    <div>{grnCodeParam} </div>
                                    <Box
                                      onClick={removeGRNParam}
                                      className="text-turquioise_surf cursor-pointer"
                                    >
                                      {createGrnLocal.REMOVE}
                                    </Box>
                                  </div>
                                </div>
                              ) : (
                                <div className={classes.horizontalCenter}>
                                  <Autocomplete
                                    {...rest}
                                    options={grnList?.result?.data || []}
                                    className={classes.field}
                                    getOptionLabel={(option) => option?.grn_code ?? ''}
                                    renderOption={(renderProps, option) => (
                                      <li {...renderProps} key={option.grn_code}>
                                        <div
                                          className="
                                        display-flex justify-content-space-between 
                                        align-items-center wd-100"
                                        >
                                          <div>
                                            <div className="fs12 text-66">
                                              {convertDateFormat(option.invoice_date, 'shortDate')}
                                            </div>
                                            <div className="fs16 text-33 text-left">
                                              {option.grn_code}
                                            </div>
                                            <div className="fs-12 text-99 mr-t10">
                                              {createGrnLocal.CREATED_BY}: {option.created_by}
                                            </div>
                                          </div>
                                          <div>
                                            <div className="tag tag-turquioise_surf">
                                              {option.total_scanned} {createGrnLocal.ITEMS}
                                            </div>
                                          </div>
                                        </div>
                                      </li>
                                    )}
                                    onInputChange={(event, newInputValue) => {
                                      mutators.setGrn(newInputValue);
                                    }}
                                    renderInput={(params) => (
                                      <LkInput
                                        {...params}
                                        {...input}
                                        className="input-box"
                                        fullWidth
                                        label="Select GRN"
                                      />
                                    )}
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        </Field>
                      )}
                      {invoiceDetail?.data?.invoice?.contains_batch_no && (
                        <Field name="batchNo">
                          {({ input, meta, ...rest }) => (
                            <div className={classes.horizontalCenter}>
                              <div className="material-group mr-t20">
                                <LkInput
                                  select
                                  {...rest}
                                  {...input}
                                  className={classes.field}
                                  label={createGrnLocal.BATCH_NUMBER}
                                >
                                  {invoiceDetail?.data?.invoice?.batch_no_list
                                    ?.split(',')
                                    ?.map((ap) => (
                                      <MenuItem key={ap} value={ap}>
                                        {ap}
                                      </MenuItem>
                                    ))}
                                </LkInput>
                                <span className="input-error">
                                  {(meta.touched && meta.error) || ''}
                                </span>
                              </div>
                            </div>
                          )}
                        </Field>
                      )}
                      <div className="text-center mr-t40">
                        {!grnCreateLoading ? (
                          <Button
                            type="submit"
                            className="btn-primary"
                            color="primary"
                            variant="contained"
                          >
                            {values.grn ? createGrnLocal.START_GRN : createGrnLocal.CREATE_GRN}
                          </Button>
                        ) : (
                          <Button
                            disabled
                            type="button"
                            className="btn-primary"
                            color="primary"
                            variant="contained"
                          >
                            <Spinner />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </form>
            );
          }}
        />
      </div>
    </div>
  );
};

export default GrnCreation;
