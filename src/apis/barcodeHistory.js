import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getBarcodeHistoryApi = (payload) =>
  tokenInstance().post(config.barcodeHistory.getbarcodeHistory, payload);

export const printBarcodeHistoryApi = (payload) =>
  tokenInstance().post(config.barcodeHistory.exportbarcodeHistory, payload);

export const markInventoryBadApi = (params) =>
  tokenInstance().post(config.barcodeHistory.markInventoryBad, null, {
    params: { barcode: params.barcode }
  });

export const getOrderIdListApi = (payload) =>
  tokenInstance().post(config.barcodeHistory.getOrderIdList, payload);
