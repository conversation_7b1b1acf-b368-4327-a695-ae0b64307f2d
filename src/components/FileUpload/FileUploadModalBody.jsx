import React from 'react';
import classNames from 'clsx';

import Box from '@mui/material/Box';
import ButtonBase from '@mui/material/ButtonBase';
import Button from '@mui/material/Button';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Spinner from 'components/Spinner/Spinner';
import { fileDownload } from 'utils/helpers';

const FileUploadModalBody = ({
  uploadCsvPass,
  closeModal,
  showMessage,
  failDataMessage,
  downloadReport,
  selectedFile,
  importLoading,
  cancelFileSelection,
  sampleFileName,
  showSampleArray,
  sampleCSV,
  checkedItemLabel,
  checkedItems,
  setCheckedItems,
  CONTENT
}) => {
  const csvData = sampleCSV.map((value) => value.join()).join('\n');
  const file = new Blob([csvData], { type: 'application/csv' });

  const renderSampleDataTable = () => {
    if (showSampleArray && sampleCSV.length) {
      return sampleCSV.map((sc, i) => {
        if (i === 0) {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <Box key={i} className="display-flex pd-12 fs12 fw-bold bg-f5">
              {sc.map((s) => (
                <Box key={s} className="text-left" style={{ width: '120px' }}>
                  {s}
                </Box>
              ))}
            </Box>
          );
        }
        return (
          // eslint-disable-next-line react/no-array-index-key
          <Box key={i} className="csv-row display-flex pd-12 fs12">
            {sc.map((s) => (
              <Box key={s} className="text-left" style={{ width: '120px' }}>
                {s}
              </Box>
            ))}
          </Box>
        );
      });
    }
    return null;
  };

  if (uploadCsvPass) {
    return (
      <Box className="text-center">
        <Button
          data-cy="upload-success-ok-button"
          style={{ borderRadius: 8 }}
          variant="contained"
          color="primary"
          onClick={closeModal}
        >
          {CONTENT.OK}
        </Button>
      </Box>
    );
  }

  if (showMessage) {
    return (
      <Box
        className={classNames(
          'mt-40',
          'mb-40',
          'display-flex pd-12',
          'border-grey5-radiusbase',
          'justify-content-space-between'
        )}
      >
        <Box sx={{ marginRight: 2 }} className="fs12">
          {failDataMessage}
        </Box>
        <ButtonBase color="primary" alt="img">
          <Box
            data-cy="download-error-report"
            className=" display-flex fs14 text-turquioise_surf align-items-center"
            onClick={() => downloadReport()}
          >
            <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/download.svg`} alt="img" />
            <Box className="mr-l8">{CONTENT.DOWNLOAD_ERR_REPORT}</Box>
          </Box>
        </ButtonBase>
      </Box>
    );
  }
  if (selectedFile) {
    return (
      <Box
        className={classNames(
          'file-container',
          'display-flex',
          'justify-content-space-between',
          'border-grey5-radiusbase',
          'pd-12'
        )}
      >
        <Box className="pos-rel">
          <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/file.svg`} alt="img" />
          <img
            className="tick-mark-img pos-abs bg-white"
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/check-circle.svg`}
            alt="img"
          />
        </Box>
        <Box className="fs12 align-self-center mr-l12 mr-r20">{selectedFile?.name}</Box>
        <Box
          data-cy="cancel-file-selection"
          className="cursor-pointer"
          onClick={cancelFileSelection}
        >
          {importLoading ? (
            <Spinner />
          ) : (
            <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/cancel.svg`} alt="img" />
          )}
        </Box>
      </Box>
    );
  }
  return (
    <>
      <Box className="table-conatiner mr-t40 border-grey5-radiusbase">
        <Box className="display-flex fs14 justify-content-space-between pd-12">
          <Box>{CONTENT.SAMPLE_CSV_FILE}</Box>
          <Box onClick={() => fileDownload(file, sampleFileName)}>
            <Box className="text-turquioise_surf" style={{ cursor: 'pointer' }}>
              <img
                className="mr-r8"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/download.svg`}
                alt="img"
              />
              {CONTENT.DOWNLOAD}
            </Box>
          </Box>
        </Box>
        {renderSampleDataTable()}
      </Box>
      {checkedItemLabel && (
        <Box className="mr-t40 mr-b40">
          <FormControlLabel
            control={
              <Checkbox
                checked={checkedItems}
                name="checkedItems"
                onChange={() => setCheckedItems(!checkedItems)}
                color="primary"
              />
            }
            label={checkedItemLabel}
          />
        </Box>
      )}
    </>
  );
};

export default FileUploadModalBody;
