import React, { useCallback, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { isEmpty } from 'utils/lodash';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import {
  orderDetailsInvoiceDataRequest,
  orderDetailsItemsDataRequest,
  orderDetailsOverviewDataRequest,
  orderDetailsShipmentsDataRequest
} from 'redux/actionCreators/orderDetails';
import ItemsTable from '../OrderDetailsTable/Items/ItemsTable';
import OrderOverviewTable from '../OrderDetailsTable/Overview/OrderOverviewTable';
import ShipmentTable from '../OrderDetailsTable/Shipments/ShipmentTable';
import InvoiceTable from '../OrderDetailsTable/Invoice/InvoiceTable';

const useStyles = makeStyles()(() => ({
  container: {
    position: 'relative'
  },
  customTabRoot: {
    color: '#000000',
    margin: 'auto 24px'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  },
  divider: {
    background: '#ffffff',
    borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
  },
  tabActive: {
    color: '#00B9C6'
  },
  table: {
    margin: '24px'
  }
}));

const TabPanel = ({ children, value, index }) => value === index && children;

const OrderDetailsTabs = ({ CONTENT }) => {
  const { classes, cx } = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { orderId, tab } = useParams();
  const [tabValue, setTabValue] = useState(tab);

  const { data: headerData } = useSelector((state) => state.orderDetails.header);

  useEffect(() => {
    if (headerData.incrementId) {
      if (tab === 'overview') {
        dispatch(orderDetailsOverviewDataRequest(headerData.incrementId));
      }
      if (tab === 'items') {
        dispatch(orderDetailsItemsDataRequest(headerData.incrementId));
      }
      if (tab === 'shipments') {
        dispatch(orderDetailsShipmentsDataRequest(headerData.incrementId));
      }
      if (tab === 'invoices') {
        dispatch(orderDetailsInvoiceDataRequest(headerData.incrementId));
      }
      setTabValue(tab);
    }
  }, [tab, headerData.incrementId]);

  useEffect(() => {
    if (!isEmpty(headerData) && headerData?.incrementId !== orderId) {
      navigate(`/order/${headerData.incrementId}/${tab}`);
    }
  }, [headerData]);

  const handleChange = useCallback(
    (_event, newValue) => {
      navigate(`/order/${orderId}/${newValue}`);
      setTabValue(newValue);
    },
    [orderId]
  );

  return (
    <Box className={classes.container}>
      <Box className={classes.divider}>
        <Tabs
          value={tabValue}
          onChange={handleChange}
          aria-label="secondary tabs example"
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          <Tab
            className={cx({ [classes.tabActive]: tabValue === 'overview' })}
            value="overview"
            label={CONTENT.OVERVIEW}
          />
          <Tab
            className={cx({ [classes.tabActive]: tabValue === 'items' })}
            value="items"
            label={CONTENT.ITEMS}
          />
          <Tab
            className={cx({ [classes.tabActive]: tabValue === 'shipments' })}
            value="shipments"
            label={CONTENT.SHIPMENTS}
          />
          <Tab
            className={cx({ [classes.tabActive]: tabValue === 'invoices' })}
            value="invoices"
            label={CONTENT.INVOICES}
          />
        </Tabs>
      </Box>
      <TabPanel value={tabValue} index="overview">
        <Box className={classes.table}>
          <OrderOverviewTable CONTENT={CONTENT} />
        </Box>
      </TabPanel>
      <TabPanel value={tabValue} index="items">
        <Box className={classes.table}>
          <ItemsTable CONTENT={CONTENT} />
        </Box>
      </TabPanel>
      <TabPanel value={tabValue} index="shipments">
        <Box className={classes.table}>
          <ShipmentTable CONTENT={CONTENT} />
        </Box>
      </TabPanel>
      <TabPanel value={tabValue} index="invoices">
        <Box className={classes.table}>
          <InvoiceTable CONTENT={CONTENT} />
        </Box>
      </TabPanel>
    </Box>
  );
};

export default React.memo(OrderDetailsTabs);
