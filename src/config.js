/* eslint-disable max-len */
// eslint-disable-next-line no-undef
// const __BASE_URL__ = (import.meta.env.VITE_API_BASE_URL || 'https://nexs-dev.lenskart.com/');

// const generateServiceBasedUrl = (service, version) => 'nexs/api/${service}/${version}/';

const {
  VITE_PROD_URL,
  VITE_PRE_PROD_URL,
  VITE_FINANCE_SCM_PROD_URL,
  VITE_FINANCE_SCM_PREPROD_URL,
  VITE_ORDER_INTERCEPTOR_PROD_URL,
  VITE_ORDER_INTERCEPTOR_PRE_PROD_URL,
  VITE_AUDIT_INVENTORY_PRE_PROD_URL,
  VITE_AUDIT_INVENTORY_PROD_URL
} = import.meta.env;

const FINANCE_SCM_ALL_ENV_URLS = {
  [VITE_PROD_URL]: VITE_FINANCE_SCM_PROD_URL,
  [VITE_PRE_PROD_URL]: VITE_FINANCE_SCM_PREPROD_URL
};

const ORDER_INTERCEPTOR_ALL_ENV_URLS = {
  [VITE_PROD_URL]: VITE_ORDER_INTERCEPTOR_PROD_URL,
  [VITE_PRE_PROD_URL]: VITE_ORDER_INTERCEPTOR_PRE_PROD_URL
};

const AUDIT_INVENTORY_ALL_ENV_URLS = {
  [VITE_PROD_URL]: VITE_AUDIT_INVENTORY_PROD_URL,
  [VITE_PRE_PROD_URL]: VITE_AUDIT_INVENTORY_PRE_PROD_URL
};

const FINANCE_SCM_CURRENT_ENV_URL =
  FINANCE_SCM_ALL_ENV_URLS[window.location.host] ?? VITE_FINANCE_SCM_PREPROD_URL;

const ORDER_INTERCEPTOR_CURRENT_ENV_URL =
  ORDER_INTERCEPTOR_ALL_ENV_URLS[window.location.host] ?? VITE_ORDER_INTERCEPTOR_PRE_PROD_URL;

const AUDIT_INVENTORY_CURRENT_ENV_URL =
  AUDIT_INVENTORY_ALL_ENV_URLS[window.location.host] ?? VITE_AUDIT_INVENTORY_PRE_PROD_URL;

const config = {
  apiPath: {
    vendor: '/nexs/api/vendor/v1/search?q=',
    invoice: '/nexs/api/invoice/v1/',
    packingDetails: '/nexs/packing/getItemDetailById',
    orderDetails: '/nexs/api/orderqc/fetch/order/entityType',
    packingByBarcode: '/packing/getOrderInfo',
    orderByBarcode: '/nexs/api/orderqc/fetch/barcode',
    unicomComments: '/nexs/wms/api/v1/comment/get',
    packingDone: '/nexs/packing/completePacking',
    lsmShipmentsHostPreProd: 'http://lsm-shipments.preprod.lms.lenskart.cloud',
    lsmShipmentsHostProd: 'http://lsm-shipments.prod.lms.lenskart.technology',
    lsmShipments: '/nexs/wms/api/v1/',
    packingPrintInvoice: '/nexs/wms/api/v1/exportPdf',
    qcEnable: `${ORDER_INTERCEPTOR_CURRENT_ENV_URL}/inbound/order-mgmt/sbrt/isEligibleForQc`,
    locale: '/localisation',
    qcHoldReasons: '/nexs/wms/api/v1/reason/group/QC',
    qcDone: '/nexs/api/orderqc/completeQc',
    qcFailDamage: '/nexs/wms/api/v1/qcFail/status/update',
    markFullFillable: '/nexs/wms/api/v1/mark/swap/item/fullfillable',
    orders: '/orders',
    printShipmentNew: '/nexs/wms/api/v1/shipment/print',
    printShipmentMPDTC: '/nexs/wms/api/v1/shipment/mporder/print',
    printShipmentUAE: '/nexs/wms/api/v1/shipment/getPdf',
    printMonotCartonShipment: '/nexs/wms/api/v1/printShipment/monocarton',
    printShipmentB2B: '/orders/is-web-b2b-order/',
    lensLabPrintSticker: '/jit/print/label',
    jobCardPrint: '/jit/print/job',
    consul: '/nexs-config/get',
    newPackSlip: '/nexs/packing/packingSlip',
    cardBarcodeValid: '/nexs/packing/authenticityCard/scanCardBarcode',
    printMpShipmentDummy: '/nexs/wms/api/v1/shipment/dummy/print'
  },
  trayReassign: {
    getTrayDeatils: '/nexs/wms/api/v1/fittingDetails',
    getTrayHistory: '/nexs/wms/api/v1/tray/history',
    reassign: '/nexs/wms/api/v1/tray/reassign',
    validateTray: '/nexs/wms/api/v1/tray/validate'
  },
  manifest: {
    list: '/nexs/manifest/api/v1/fetch/filter',
    remove: '/nexs/manifest/api/v1/discard',
    fetch: '/nexs/manifest/api/v1/fetch',
    fetchBag: '/nexs/manifest/api/v1/asrs/fetchManifest',
    add: '/nexs/manifest/api/v1/add',
    close: '/nexs/manifest/api/v1/close',
    save: '/nexs/manifest/api/v1/save',
    shippingProvider: '/nexs/manifest/api/v1/fetch/shippingProvider',
    print: '/nexs/manifest/api/v1/print',
    deleteShipment: '/nexs/manifest/api/v1/remove/shipment',
    retryManifest: '/nexs/manifest/api/v1/asrs/retryAddingShipment',
    sgTaxInvoice: '/inbound/wm/manifests/nexs/download/sgTaxInvoice',
    sgCommInvoice: '/inbound/wm/manifests/nexs/download/sgCommInvoice',
    sgSliInvoice: '/inbound/wm/manifests/nexs/download/sgSliInvoice'
  },
  purchaseOrder: {
    list: '/nexs/api/po/v1/search',
    approvedPo: '/nexs/api/po/v1/nos/vendor',
    detail: '/nexs/api/po/v1/order',
    uploadItem: '/nexs/api/po/v1/po_itemlist',
    submitPo: '/nexs/api/po/v1/submit',
    generatePoNumber: '/nexs/api/po/v1/generatePoNum',
    submitAprroval: '/nexs/api/po/v1/submitforapproval',
    hold: '/nexs/api/po/v1/hold',
    amend: '/nexs/api/po/v1/amend',
    unhold: '/nexs/api/po/v1/unhold',
    close: '/nexs/api/po/v1/close',
    export: '/nexs/api/po/v1/downloadCSV',
    processingStatus: '/nexs/api/po/v1/processingstatus',
    exportPoDetails: '/nexs/api/export/v1/poItemDetails/exportCSV',
    poApproval: '/nexs/api/po/v1/bulkApproveorreject',
    bulkSubmitforapproval: '/nexs/api/po/v1/bulkSubmitforapproval'
  },
  catalog: {
    productDetails: '/nexs/api/catalog/v1/product',
    productPrice: '/nexs/api/catalog/v1/productPrice'
  },
  invoice: {
    creationItems: '/nexs/api/vendor/v1/items/creation',
    invoiceView: '/nexs/api/invoice/v1/details',
    invoiceList: '/nexs/api/invoice/v1/search',
    close: '/nexs/api/invoice/v1/close',
    import: '/nexs/api/invoice/v1/po_itemlist',
    download: '/nexs/api/invoice/v1/downloadCSV',
    processingStatus: '/nexs/api/invoice/v1/processingstatus',
    save: '/nexs/api/invoice/v1/save',
    saveCL: '/nexs/api/invoice/v1/createPurchaseInvoiceForCl',
    exportInvoiceDetails: '/nexs/api/invoice/v1/invoiceItemDetails/exportCSV',
    getPidInfo: '/nexs/api/invoice/v1/items/details'
  },
  grnListing: {
    searchGRN: '/nexs/api/grn/v1/master/search',
    blockedPid: '/nexs/api/grn/v1/pid/master/search-blocked-pids',
    reassignGrn: '/nexs/api/grn/v1/master/assign-grn',
    manualOverride: '/nexs/api/grn/v1/pid/master/manual-override',
    exportGrnDetail: '/nexs/api/grn/v1/master/export-details'
  },
  grnDetails: {
    getGrnPids: '/nexs/api/grn/v1/pid/master/grn-pids',
    getGrnBlockedPids: '/nexs/api/grn/v1/pid/master/grn-blocked-pids',
    grnPdfDownload: '/nexs/api/grn/v1/master/grn-pdf'
  },
  grn: {
    boxScanner: '/nexs/api/grnentities/v1/box-mapping/create',
    qcDetail: '/nexs/api/grn/v1/scan/item-barcode',
    grnList: '/nexs/api/grn/v1/master/grn-list',
    grnCreate: '/nexs/api/grn/v1/master/create',
    invoiceVendor: '/nexs/api/invoice/v1/vendor/open',
    invoiceDetail: '/nexs/api/invoice/v1/items/grn',
    pidList: '/nexs/api/grn/v1/master/get/',
    pidDetails: '/nexs/api/grn/v1/pid/master/get/',
    addPid: '/nexs/api/grn/v1/master/add/grn-pid',
    convertPid: '/nexs/api/grn/v1/pid/master/get-pid',
    getSamplingQty: '/nexs/api/grn/v1/pid/master/get/sampling-quantity',
    itemSampling: '/nexs/api/grn/v1/pid/master/get/box-items/',
    editItem: '/nexs/api/grn/v1/search/grn-item/',
    deleteItem: '/nexs/api/grn/v1/delete/grn-item/',
    updateQty: '/nexs/api/grn/v1/master/update/estimated-quantity',
    grnSummary: '/nexs/api/grn/v1/master/summary/',
    getBoxItems: '/nexs/api/grn/v1/pid/master/get/box-items/',
    getExpiryDetails: '/nexs/api/grn/v1/asn/barcode',
    productMismatch: '/nexs/api/grn/v1/pid/master/product-mismatch',
    updateGrnItem: '/nexs/api/grn/v1/update/grn-item',
    barcodeSeries: '/nexs/api/grn/v1/stock-in/barcode-series',
    closeGrn: '/nexs/api/grn/v1/master/close/',
    grnDetails: '/nexs/api/grn/v1/master/grn-details',
    isGrnOpen: '/nexs/api/grn/v1/master/is-grn-open',
    closeAllOpenGrn: '/nexs/api/grn/v1/master/close-grns/'
  },
  product: {
    searchByDescLens:
      'https://developer.lenskart.com/api/v1/im/inventory/purchase-order/items/search',
    searchByDesc: '/nexs/api/po/v1/getPidsFromDescription',
    productDetails: '/nexs/api/catalog/v1/purchase-order/items/search'
  },
  debitNote: {
    list: '/nexs/api/invoice/v1/debitnote/search',
    listCsv: '/nexs/api/invoice/v1/debitnote/csv',
    detail: '/nexs/api/invoice/v1/debitnote'
  },
  vendorMaster: {
    list: '/nexs/api/vendor/v1/details',
    export: '/nexs/api/vendor/v1/export/details',
    upload: '/nexs/api/vendor/v1/price/upload',
    download: '/nexs/api/vendor/v1/price/download',
    processingStatus: '/nexs/api/vendor/v1/processingstatus',
    fileDownload: '/nexs/api/vendor/v1/price/file/download',
    statusApprove: '/nexs/api/vendor/v1/price/approveOrReject',
    pidAuditHistoryList: '/nexs/api/vendor/v1/price/history',
    exportAuditHistoryList: '/nexs/api/vendor/v1/export/vendorPidPriceMasterUpdates'
  },
  settings: {
    getFacilities: '/nexs/api/facilities/v1/'
  },
  pdfDownload: {
    getDownloadPdf: '/nexs/api/export/v1/pdf'
  },
  filters: {
    searchList: '/search/',
    exportDownload: '/export/',
    searchSuggestion: '/suggestions/'
  },
  layout: {
    define: '/layout/define',
    save: '/layout/save',
    get: '/layout',
    list: '/layouts'
  },
  user: {
    login: '/v1/user/login',
    logoutUser: '/v1/user/logout',
    forgotPassword: '/v1/forgot/password',
    changePassword: '/v1/change/password',
    register: '/v1/register/user',
    userDetails: '/v1/user/authme',
    AppId: 'nexs_search'
  },
  putaway: {
    userPutawayList: '/putaway/',
    putawayPending: '/putaway/pending/putaway',
    putawayPendingV2: '/putaway/v2/pending',
    printPutaway: '/putaway/print-putaway-list',
    createPutaway: '/nexs-ems/releaseBarcode',
    addBarcodeToPutaway: '/putaway/barcodes',
    completePutAwaySg: '/putaway/complete/putaway'
  },
  constraint: {
    constraintList: '/nexs/api/cms/rules/read',
    constraintSave: '/nexs/api/cms/rules/save',
    validateCondition: '/nexs/api/cms/rules/validateCondition',
    fetchPossibleValues: '/nexs/api/cms/rules/fetchPossibleValues',
    validateConsequence: '/nexs/api/cms/rules/validateConsequence',
    facilityAutocomplete: '/facility/suggestions',
    suggestion: '/nexs/api/cms/rules/suggestions'
  },
  facility: {
    saveFacility: '/facility/save',
    facilityList: '/facilities',
    facilityType: '/facility/facility_types',
    taxDetails: '/facility/tax_details',
    pinCode: '/v2/utility/checkpincode/',
    uploadLogo: '/facility/',
    facilityDetails: '/facility/',
    layoutSchema: '/layouts/suggestion',
    facilitySuggestion: '/facility/suggestions',
    getEntitiesList: '/facility',
    facilityAutocomplete: '/facility/suggestions',
    countryState: '/v2/utility/countrystate',
    bulkEdit: '/entity/uploadFacilityLayoutCsv',
    legalOwnerList: '/legal-owners'
  },
  transfer: {
    transferList: '/nexs/api/v1/iwt/gate-pass/search',
    gatePassScan: '/nexs/api/v1/iwt/gate-pass/get-details'
  },
  putAwayList: {
    list: '/putaway/putaway-list',
    listExport: '/putaway/putaway-list/export',
    detail: '/putaway/putaway-details',
    filterSuggestion:
      '/putaway/putaway-list/filter-suggestions?key=putaway&value=&recommendations=25',
    suggestion: '/putaway/putaway-list/filter-suggestions',
    detailExport: '/putaway/putaway-details/export'
  },
  barcodeHistory: {
    getbarcodeHistory: '/nexs/api/ims/getHistory',
    exportbarcodeHistory: '/nexs/api/ims/export',
    suggestionAPi: '/nexs/api/ims/suggestions',
    getOrderIdList: '/nexs/analytics/barcode/activity/fetchOperationDetails',
    markInventoryBad: '/nexs-ems/mark/barcode/bad'
  },
  inventory: {
    fetchBarcodeItemDetails: '/nexs/api/ims/fetchSlaveBarcodeItemDetails',
    PIDdetails: '/nexs/api/catalog/v1/product/additionalDetails',
    pidTotalCount: '/nexs/cid/api/v1/warehouseInventory/getConsolidatedInvInfo',
    warehouseList: '/nexs/cid/api/v1/warehouseInventory/search',
    warehouseDetailsList: '/nexs/api/ims/search',
    wareHouseBarcodeDetailsList: '/nexs/analytics/monitoring/v3/details',
    inventoryOrderTypeList: '/nexs/analytics/monitoring/pidOrderDetails'
  },
  fitting: {
    getFittingData: '/nexs/fitting/getFittingDetail',
    getFittingQCData: '/nexs/api/orderqc/fetch/order/fitting/entityType',
    completeFitting: '/nexs/fitting/markFittingComplete'
  },
  supervisor: {
    getUserList: '/nexs/order/picking/supervisor/getUserList',
    getCategories: '/nexs/order/picking/v2/location/categories',
    getSkippedList: '/nexs/order/picking/supervisor/getListingDetail',
    createSkippedItemSummary: '/nexs/order/picking/supervisor/createSkippedItemSummary',
    markItemPermanentlyNotFound: '/nexs/order/picking/supervisor/markItemPermanentlyNotFound',
    scan: '/nexs/order/picking/scan',
    getPickingConfig: '/nexs/order/picking/supervisor/getPickingConfig',
    getAssignmentCategories: '/nexs/order/picking/supervisor/getCategory',
    getAssignmentLocation: '/nexs/order/picking/supervisor/getLocation',
    addLocationCategoryForUser: '/nexs/order/picking/supervisor/addUserLocationAndCategory',
    getLocationCategoryUser: '/nexs/order/picking/supervisor/getAllUserLocationCategoryMapping',
    getUploadFastZonePid: '/nexs/order/picking/v2/updateFastPickingPid',
    moveFromASRSToManual: '/nexs/order/picking/supervisor/sync/manual/warehouse',
    bulkUploadDiscardedShipment: '/nexs/order/picking/supervisor/manual/warehouse/csv/upload'
  },
  storePacking: {
    packingList: '/nexs-consolidation/packing/api/v1/scan/item',
    flushStorePacking: '/nexs-consolidation/packing/api/v1/order/flush',
    printShipment: '/nexs/wms/api/v1/shipment/print',
    removeStoreShipment: '/nexs-consolidation/packing/api/v1/delete',
    storeCourierList: '/nexs/wms/api/v1/shipment',
    changeStoreCourier: '/nexs-consolidation/packing/api/v1/order/newCourierCode'
  },
  storeConsolidation: {
    list: '/nexs-consolidation/addverb/api/v1/fetchTrays',
    historyList: '/nexs-consolidation/addverb/api/v1/releaseTrayHistory',
    releaseTray: '/nexs-consolidation/addverb/api/v1/releaseTray',
    trayHistory: '/nexs-consolidation/addverb/api/v1/getHistoryShipments'
  },
  monitor: {
    getJITMonitorList: '/nexs/analytics/monitoring/jit/home',
    getJITMonitorDetail: '/nexs/analytics/monitoring/jit/detail',
    getMonitorList: '/nexs/analytics/monitoring/v2/homePage',
    getMonitorListv3: '/nexs/analytics/monitoring/v3/homePage',
    getMonitorTagDetailsList: '/nexs/analytics/monitoring/v2/details',
    getMonitorTagDetailsListV3: '/nexs/analytics/monitoring/v3/details',
    getFiltersList: '/nexs/analytics/monitoring/fetchViews',
    saveFiltersList: '/nexs/analytics/monitoring/saveViews',
    getOrdersCount: '/inbound/order-adaptor/order/getCurrentOrderCounter',
    totalOrderData: '/nexs/analytics/monitoring/summary/homePage'
  },
  consolidation: {
    getConsolidationAPI: '/nexs-consolidation/api/v1/options',
    getPigeonholeListAPI: '/nexs-consolidation/supervisor/api/v1/pigeonhole/list',
    getPigeonholeChildAPI: '/nexs-consolidation/supervisor/api/v1/pigeonhole',
    scanConsolidation: '/nexs-consolidation/api/v1/scan/v2/item'
  },
  bulkInventory: {
    getAllTransfer: '/nexs/api/v1/iwt/inventoryTransfer/getAllTransfer',
    getTransferDetails: '/nexs/api/v1/iwt/inventoryTransfer/getTransferDetails',
    getBarcodes: '/nexs/api/v1/iwt/inventoryTransfer/getPidWiseScannedBarcodes',
    createTransfer: '/nexs/api/v1/iwt/inventoryTransfer/createTransfer',
    scanBarcode: '/nexs/api/v1/iwt/inventoryTransfer/scanBarcode',
    downloadTransferCsv: '/nexs/api/v1/iwt/inventoryTransfer/downloadTransferFile',
    uploadTransferCsv: '/nexs/api/v1/iwt/inventoryTransfer/uploadTransferFile',
    deleteAbarcode: '/nexs/api/v1/iwt/inventoryTransfer/deleteScannedBarcode',
    changeTransferStatus: '/nexs/api/v1/iwt/inventoryTransfer/changeTransferStatus',
    downloadTransferSummary: '/nexs/api/v1/iwt/inventoryTransfer/downloadAllTransfer',
    downloadTransferDetails: '/nexs/api/v1/iwt/inventoryTransfer/downloadTransferDetailsFile',
    deleteBoxFromTransfer: '/nexs/api/v1/iwt/inventoryTransfer/boxBarcode/delete',
    validateTransferBox: '/nexs/api/v1/iwt/inventoryTransfer/boxBarcode/validity',
    transferPrintInvoiceTemplate: '/nexs/api/v1/iwt/inventoryTransfer/getInvoicePDF',
    replaceBarcode: '/nexs/api/v1/iwt/inventoryTransfer/replaceBarcode',
    generateAWBnumber: '/nexs/api/v1/iwt/tracking/getAwbNumberByCourierCode',
    transferPrintShippingLabel: '/nexs/api/v1/iwt/tracking/print',
    scanBarcodeV2: '/nexs/api/v1/iwt/inventoryTransfer/scanBarcodeV2',
    validateBox: '/nexs/api/grnentities/v1/validate/box/barcode'
  },
  mei: {
    meiLocationBarcode: '/nexs/wms/api/v1/location/scan',
    trayRelease: '/nexs/wms/api/v1/tray/release'
  },
  orderDetails: {
    overviewAPI: '/nexs/wms/api/v1/order/details/overview',
    headerAPI: '/nexs/wms/api/v1/order/details/header',
    itemsOrderListAPI: '/nexs/wms/api/v1/order/details/id',
    itemsAPI: '/nexs/wms/api/v1/order/details/items',
    shipmentAPI: '/nexs/wms/api/v1/order/details/shipments',
    invoiceAPI: '/nexs/wms/api/v1/exportPdf',
    invoiceTabAPI: '/nexs/wms/api/v1/order/details/invoice',
    courierList: '/nexs/wms/api/v1/shipment',
    shipmentReassign: '/nexs/wms/api/v1/shipment/reassign',
    barcodeStockOut: '/nexs/order/picking/pick/orderItemId',
    barcodeStockOutFallback: '/nexs/wms/api/v1/manual/picking/status/update',
    vmsStatus: '/nexs/wms/api/v1/order/uw/detail',
    orderHistory: '/nexs/analytics/monitoring/fetch/order-activity',
    autoToManual: '/nexs/wms/api/v1/convert/autoToManual',
    reassign: '/nexs/wms/api/v1/shipment/reassign',
    getNotFoundStatus: '/nexs/order/picking/pickingDetails/status',
    regularToJitOrder: '/nexs/wms/api/v1/convert/regularToJit'
  },
  cycleCountWeb: {
    SummaryViewAPI: '/nexs-cycle-count/api/v1/supervisor/location/summary',
    locationListAPI: '/nexs-cycle-count/api/v1/supervisor/location/list',
    locationDetailAPI: '/nexs-cycle-count/api/v1/supervisor/location/detail'
  },
  eInvoice: {
    eInvoiceListAPI: '/search/eInvoice/einvoiceList',
    exportEinvoice: '/search/eInvoice/downloadReport',
    retryEinvoice: '/search/eInvoice/retryEInvoice',
    printEinvoice: '/search/eInvoice/printeinvoice'
  },
  picking: {
    pickingList: '/nexs/order/picking/pickListDetails',
    pickingDetailsList: '/nexs/order/picking/pickingSummaryDetails',
    exportCSV: '/nexs/order/picking'
  },
  reportingTool: {
    getReportMetaData: '/scripts/nexs/api/reports/getReportMetaData',
    generateReport: '/scripts/nexs/api/reports/generateReport',
    reportHistoryList: '/scripts/nexs/api/reports/getReportData'
  },
  d365: {
    d365List: '/search/d365/syncCount',
    d365Details: '/search/d365/syncReasons',
    retry: '/search/d365/publish/'
  },
  refurb: {
    statusCount: '/nexs/api/ims/refinish/getRefurbishmentStatusCount',
    listData: '/nexs/api/ims/refinish/getRefurbishmentData',
    markGoodOrBad: '/nexs/api/ims/refinish/markGoodOrBad'
  },
  asrsDiscrepancy: {
    discrepancyReport: '/nexs/api/ims/fetchDiscrepancyReport'
  },
  userManagement: {
    getUserRoles: '/v1/userManagement/user',
    getUserList: '/v1/userManagement/roleGroup',
    getEmployeeCode: '/v1/userManagement/get/employeeData',
    createUser: '/v1/userManagement/createEdit/user',
    createBulkUser: '/v1/userManagement/bulk/createEdit/user',
    user: '/v1/userManagement/user'
  },
  monoCarton: {
    mapWithShipment: '/nexs/wms/api/v1/cartonShipmentMapping'
  },
  courierCutoff: {
    list: '/nexs/manifest/api/v1/getCourierCutoff',
    upload: '/nexs/manifest/api/v1/createUpdateCourierCutoff'
  },
  adhocASRSPicking: {
    adhocASRSPickingList: '/nexs/api/v1/iwt/adhoc/getAllTransfer',
    adhocPidDetailsList: '/nexs/api/v1/iwt/adhoc/get/level/pid',
    skuCountList: '/nexs/order/picking/addverb/get/asrsInventory'
  },
  inwardQc: {
    getIqcDetail: '/nexs/api/grn/v1/get/iqc/list',
    getBarcodeDetails: '/nexs/api/grn/v1/get/iqc/details',
    scanBarcode: '/nexs/api/grn/v1/scan/iqc/barcode',
    getFailReason: '/nexs/api/grn/v1/scan/qcreason',
    iqcComplete: '/nexs/api/grn/v1/complete/iqc',
    saveGrnProduct: '/nexs/api/grn/v1/save/grn/product',
    deleteBarcode: '/nexs/api/grn/v1/delete/iqc',
    invoiceIqcSummary: '/nexs/api/invoice/v1/details/iqcSummary',
    approveOrReject: '/nexs/api/invoice/v1/bulkApproveOrReject'
  },
  manifestShippingMethod: {
    getManifestShippingMethodsList: '/nexs/manifest/api/v1/getManifestShippingMethod',
    uploadManifestShippingMethodFile:
      '/nexs/manifest/api/v1/manifestShippingMethod/createUpdateCsv',
    trackManifestShippingIdsUploadStatus:
      '/nexs/manifest/api/v1/manifestShippingMethod/uploadStatus'
  },
  shipmentBags: {
    getShipmentBagsList: '/nexs/manifest/api/v1/getBagShipments',
    bagRelease: '/nexs/manifest/api/v1/release',
    getShipmentBagDetails: '/nexs/manifest/api/v1/getBagShipmentDetails'
  },
  startRetire: {
    list: '/nexs/api/po/v1/startRetire/homePage',
    bulkUpload: '/nexs/api/po/v1/bulk/startRetire',
    singleUpload: '/nexs/api/po/v1/single/startRetire'
  },
  addInventory: {
    list: '/nexs/api/po/v1/addInventory/homePage',
    bulkUpload: '/nexs/api/po/v1/bulk/addInventory',
    singleUpload: '/nexs/api/po/v1/single/addInventory'
  },
  priceControl: {
    list: '/nexs/api/vendor/v1/price/approverConfirmerDetails',
    confirmApprover: '/nexs/api/vendor/v1/price/upsertApproverConfirmerDetails',
    userDetails: '/v1/get/users',
    classificationsOrBrandsList: '/nexs/api/vendor/v1/price/getClassificationsOrBrandsList',
    legalEntityList: '/nexs/api/vendor/v1/legalOwners'
  },
  reactiveTransfer: {
    list: '/nexs/api/v1/iwt/transferRecommendation/statusWiseTransfer',
    headers: '/nexs/api/po/v1/bulk/startRetire',
    create: '/nexs/api/v1/iwt/transferRecommendation/createBulkTransfer'
  },
  jitOrderSearch: {
    list: '/jit/lenslab/fetch-data',
    markdamage: '/jit/lenslab/status-update'
  },
  handEdging: {
    startEdging: '/nexs/wms/api/v1/hand/edging/scan',
    getPowerDetails: '/nexs/wms/api/v1/edging/details/id'
  },
  edgingQC: {
    startEdging: '/nexs/api/orderqc/fetch/order/edging/entityType'
  },
  invoicePosting: {
    list: `${FINANCE_SCM_CURRENT_ENV_URL}/invoice-posting/api/v1/get`,
    createInvoice: `${FINANCE_SCM_CURRENT_ENV_URL}/invoice-posting/api/v1`,
    processInvoice: `${FINANCE_SCM_CURRENT_ENV_URL}/invoice-posting/api/v1/process`
  },
  distributorOrders: {
    validateCSV: '/inbound/order-sensei/oms/api/v1/distributorOrder/upload',
    createOrder: '/inbound/order-sensei/oms/api/v1/distributorOrder/create',
    cancelOrder: '/inbound/order-sensei/oms/api/v1/distributorOrder/reject',
    orderList: '/inbound/order-sensei/oms/api/v1/distributorOrder/search',
    customerDropdownList: '/inbound/order-sensei/oms/api/v1/customer/search',
    doNumberDetails: '/inbound/order-sensei/oms/api/v1/distributorOrder/details',
    return: '/inbound/order-sensei/oms/api/v1/distributorReturnOrder/returnOrder'
  },
  finance: {
    getDOList: '/inbound/order-sensei/oms/api/v1/distributorOrder/search',
    approveAndRejectDO: '/inbound/order-sensei/oms/api/v1/distributorOrder',
    getDODetails: '/inbound/order-sensei/oms/api/v1/distributorOrder/details'
  },
  pickingScan: {
    pickingSummary: '/nexs/order/picking/DO/getPickingSummary',
    pickingSummaryDetails: '/nexs/order/picking/DO/getPickingSummaryDetails',
    pickBarcode: '/nexs/order/picking/DO/pickBarcode'
  },
  doShipment: {
    closePicking: '/nexs/order/picking/DO/closePicking',
    createSummary: '/nexs/order/picking/DO/createPickingSummary',
    getShipmentList: '/scripts/api/v1/order/doOrder/details',
    getShipmentDetailsList: '/nexs/wms/api/v1/order/do/details',
    getShipmentPrintInvoice: '/inbound/finance-document/fds/api/v1/document',
    updateBoxCount: '/nexs/wms/api/v1/update/shipment/box/count',
    invoiceAPI: '/nexs/wms/api/v1/exportPdf'
  },
  customer: {
    create: '/inbound/order-sensei/oms/api/v1/customer/save',
    update: '/inbound/order-sensei/oms/api/v1/customer/update',
    getCustomer: '/inbound/order-sensei/oms/api/v1/customer/findById',
    customerListing: '/inbound/order-sensei/oms/api/v1/customer/search',
    toggleCustomer: '/inbound/order-sensei/oms/api/v1/customer/toggle'
  },
  vendorShipment: {
    poListByVendor: '/nexs/api/po/v1/nos/vendor',
    create: '/nexs/api/po/v1/shipment/upsertVendorPoShipment',
    detailsByShipment: '/nexs/api/po/v1/shipment/getShipmentById',
    uploadCsv: '/nexs/api/po/v1/shipment/file/uploadVendorPoShipmentItems',
    createShipmentItems: '/nexs/api/po/v1/shipment/upsertVendorPoShipmentItems'
  },
  vendorShipmentList: {
    list: '/nexs/api/po/v1/shipment/listShipments',
    details: '/nexs/api/po/v1/shipment/getShipmentById'
  },
  stockTake: {
    list: '/inbound/audit-inventory/api/v1/stocktake/cycle/getAllStockCycle',
    create: '/inbound/audit-inventory/api/v1/stocktake/cycle/create',
    upload: '/inbound/audit-inventory/api/v1/stocktake/cycle/fileUpload',
    consolidatedStatus: '/inbound/audit-inventory/api/v1/stocktake/cycle/getConsolidatedData',
    closed: '/inbound/audit-inventory/api/v1/stocktake/cycle/close',
    stockCycleBarcodeDetailsList: '/inbound/audit-inventory/api/v1/stocktake/cycle/getBarcodeList',
    scanBarcode: '/inbound/audit-inventory/api/v1/stocktake/scan/barcode',
    stockCycleDetails: '/inbound/audit-inventory/api/v1/stocktake/cycle/getStockCycleDetails',
    idDownload: '/inbound/audit-inventory/api/v1/stocktake/cycle/id/export',
    statusDownload: `${AUDIT_INVENTORY_CURRENT_ENV_URL}/api/v1/stocktake/cycle/status/export`
  },
  bulkOrderPicking: {
    getScanDetails: '/nexs/order/picking/partial/item',
    scan: '/nexs/order/picking/partial/pickBarcode',
    complete: '/nexs/order/picking/partial/summary'
  },
  RTVlist: {
    list: '/search/gatepass?version=v1',
    export: '/export/gatepass?version=v1',
    generateGatepassSequence: '/nexs/api/po/v1/generateGatepassNum',
    createGatepass: '/nexs/api/po/v1/gatepass/create',
    submitForApprove: '/nexs/api/po/v1/gatepass/submit',
    approveReject: '/nexs/api/po/v1/gatepass/approveorreject',
    detailsList: '/nexs/api/po/v1/gatepass/get'
  },
  RTVbarcodeScan: {
    scan: '/nexs/api/po/v1/gatepass/scan/barcode',
    delete: '/nexs/api/po/v1/gatepass/delete/barcode'
  },
  autoGrn: {
    invoiceDetails: '/inbound/order-mgmt/po-grn/fetch-invoice-summary',
    grnSummary: '/inbound/order-mgmt/po-grn/grn-summary',
    addBarcode: '/inbound/order-mgmt/po-grn/add-barcode',
    scannedBarcodes: '/inbound/order-mgmt/po-grn/scanned-barcodes',
    getGrnDetails: '/inbound/order-mgmt/po-grn/grn-detail',
    wareHouseRecieve: '/inbound/order-mgmt/po-grn/warehouse-receive-barcode'
  },
  qcLensoMeter: {
    fittingByStationCode: '/api/qc/scan',
    qcLensometerEventsApi: '/api/qc/events'
  },
  asrsManualSync: {
    getOrderCount: '/nexs/order/picking/manual/get/order/count',
    createWave: '/nexs/order/picking/manual/wave/create'
  }
};

if (import.meta.env.MODE === 'development') {
  const ignoreAPIPaths = ['locale', 'qcLensometerEventsApi', 'fittingByStationCode'];
  Object.keys(config).forEach((eachService) =>
    Object.keys(config[eachService]).forEach((apiKeyName) => {
      if (!ignoreAPIPaths.includes(apiKeyName) && config[eachService][apiKeyName].startsWith('/')) {
        config[eachService][apiKeyName] = `/local-ui${config[eachService][apiKeyName]}`;
      }
    })
  );
}

export default config;
