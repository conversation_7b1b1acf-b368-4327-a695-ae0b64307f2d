import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const getCustomerListApi = (payload) => {
  const { filters, ...rest } = payload;

  let searchTerms = '';
  if (filters.name) {
    searchTerms += `name.like:${filters.name}`;
  }

  return tokenInstance().get(config.customer.customerListing, {
    params: { ...rest, searchTerms }
  });
};
export const getCustomerById = (id) => tokenInstance().get(`${config.customer.getCustomer}/${id}`);

export const createCustomerApi = (payload) => tokenInstance().post(config.customer.create, payload);

export const updateCustomerApi = (payload) =>
  tokenInstance().put(`${config.customer.update}/${payload.id}`, payload);
