import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getPriceUpdateListAPI = (requestBody) =>
  tokenInstance().post(config.priceControl.list, requestBody);

export const confirmApproverAPI = (requestBody) =>
  tokenInstance().post(config.priceControl.confirmApprover, requestBody);

export const getUserDetailsAPI = (requestBody) =>
  tokenInstance(
    {
      'x-lenskart-app-id': 'nexs_search'
    },
    true
  ).get(config.priceControl.userDetails, { params: requestBody });

export const getClassificationsOrBrandsListAPI = (requestBody) =>
  tokenInstance().get(config.priceControl.classificationsOrBrandsList, { params: requestBody });

export const getLegalEntityListListAPI = () =>
  tokenInstance().get(config.priceControl.legalEntityList);
