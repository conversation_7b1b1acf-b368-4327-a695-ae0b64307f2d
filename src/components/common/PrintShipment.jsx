import React, { useEffect, useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import ShipmentPrintTemplate from 'components/PrintShipment/ShipmentPrintTemplate';
import { useDispatch } from 'react-redux';
import { getPrintMPShipmentDummyReset } from 'redux/actionCreators/printShipment';

const PrintShipment = ({ printShipmentData, type = '' }) => {
  const componentRef = useRef(null);
  const dispatch = useDispatch();

  const handlePrint = useReactToPrint({
    contentRef: componentRef,
    onAfterPrint: () => {
      if (type === 'MPDTC') {
        dispatch(getPrintMPShipmentDummyReset());
      }
    }
  });

  useEffect(() => {
    if (printShipmentData && componentRef.current) {
      handlePrint();
    }
  }, []);

  return <ShipmentPrintTemplate shipmentLabelData={printShipmentData} ref={componentRef} />;
};

export default PrintShipment;
