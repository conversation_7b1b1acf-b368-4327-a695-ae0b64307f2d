import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import TableContainer from '@mui/material/TableContainer';
import Table from '@mui/material/Table';
import Box from '@mui/material/Box';
import Spinner from 'components/Spinner/Spinner';
import TableCount from 'components/common/TableCount';
import LkTableHeader from './LkTableHeader/LkTableHeader';
import LkVertualizedTableBody from './LkTableBody/LkVertualizedTableBody';
import LkNonVertualizedTableBody from './LkTableBody/LkNonVertualizedTableBody';

const useStyles = makeStyles()((theme, prop) => ({
  root: {
    position: 'relative'
  },
  tableContainer: {
    overflowX: 'auto',
    overflowY: 'hidden',
    border: '1px solid #EEEEEE',
    borderRadius: 8,
    position: 'relative',
    backgroundColor: '#FFFFFF'
  },
  table: {
    height: '100%'
  },
  maxTableHeight: {
    maxHeight: prop.maxHeight
  },
  tableHeight: {
    height: prop.tableHeight
  }
}));

let customSort = false;

const LkTable = ({
  headerConfig,
  renderExtenedComponent,
  maxHeight,
  tableHeight = 500,
  isDataFetching = false,
  setFilters = null,
  tableData = [],
  initialSortBy = { order: null, id: null },
  dataRequestFunction = null,
  pageLimit = 35,
  totalRowsCount = 0,
  rowKey = null,
  selectedRows = [],
  warnedRows = [],
  pageNumber = 0,
  rowSize = 50,
  rowExtendedSize = 150,
  columnWidth = 150,
  showTableCount = true,
  onRowClick = null,
  hideHeader = false,
  plainTheme = false,
  isNonVertualizedTable = false,
  border = true,
  noDataText = 'No data...',
  enableLocalSort = false,
  dataCy = ''
}) => {
  const { classes, cx } = useStyles({ maxHeight, tableHeight });
  const tableBodyRef = useRef();
  const tableRef = useRef();
  const [sortType, setSortType] = useState(initialSortBy.order);
  const [sortKey, setSortKey] = useState(initialSortBy.id);
  const [nextPagePage, setNextPage] = useState(pageNumber);
  const [tableDataState, setTableDataState] = useState([]);

  const stableTableData = useMemo(() => tableData, [JSON.stringify(tableData)]);

  useEffect(() => {
    setTableDataState(stableTableData);
  }, [stableTableData]);

  const sortFun = useCallback(
    (type, key) => {
      // spreading array because array is frozen in strict mode need to copy the array before sortig
      const sortedArray = [...tableData].sort((obj1, obj2) => {
        if (type === 'ASC') {
          return obj1[key] > obj2[key] ? 1 : -1;
        }
        return obj2[key] > obj1[key] ? 1 : -1;
      });

      setTableDataState(sortedArray);
    },
    [tableData]
  );

  useEffect(() => {
    if (enableLocalSort) {
      if (!customSort) {
        sortFun(sortType, sortKey);
      }
    } else if (dataRequestFunction) {
      dataRequestFunction(sortType, sortKey, nextPagePage, pageLimit, enableLocalSort);
    }
  }, [sortType, sortKey]);

  useEffect(() => {
    if (dataRequestFunction && nextPagePage !== 0) {
      dataRequestFunction(sortType, sortKey, nextPagePage, pageLimit, false);
    }
  }, [nextPagePage]);

  const scrollTop = () => {
    if (tableBodyRef.current) {
      tableBodyRef.current.scrollTop = 0;
    }
  };

  useEffect(() => {
    if (pageNumber === 0 && nextPagePage !== 0) {
      setNextPage(0);
      scrollTop();
    }
  }, [pageNumber]);

  const changeSortOrder = useCallback((key, isCustomSort) => {
    customSort = isCustomSort;
    setNextPage(0);
    setSortKey(key);
    setSortType((prevSortType) => (prevSortType === 'DESC' ? 'ASC' : 'DESC'));
    scrollTop();
  }, []);

  useEffect(() => {
    setSortType(initialSortBy.order);
    setSortKey(initialSortBy.id);
  }, [initialSortBy.order, initialSortBy.id]);

  const applyFilter = useCallback((filterParams) => {
    setFilters(filterParams);
    setNextPage(0);
    scrollTop();
  }, []);

  const updateNextPage = useCallback(() => setNextPage((prevPage) => prevPage + 1), []);

  const hasMoreItemsToload = () => {
    const totalPage = Math.ceil(totalRowsCount / pageLimit);
    const currentPage = Math.ceil(tableDataState.length / pageLimit);
    return totalPage !== currentPage && currentPage < totalPage;
  };

  useEffect(() => {
    if (tableRef.current && isNonVertualizedTable && !nextPagePage) {
      tableRef.current.scrollTop = 0;
    }
  }, [nextPagePage, isNonVertualizedTable]);

  // @TODO: (Remove this comment): this is for when headerConfig is
  // empty until data comes like monitor panel details pages
  if (isDataFetching && tableData.length === 0 && headerConfig.length === 0) {
    return (
      <Box textAlign="center" pt={1}>
        <Spinner />
      </Box>
    );
  }

  return (
    <div className={classes.root} data-cy={dataCy}>
      <TableContainer
        ref={tableRef}
        className={cx({
          [classes.tableContainer]: true
        })}
        style={{ height: tableHeight, overflowY: isNonVertualizedTable ? 'auto' : 'hidden' }}
      >
        <Table
          component="div"
          stickyHeader
          className={cx({
            [classes.table]: !isNonVertualizedTable
          })}
        >
          {!hideHeader && (
            <LkTableHeader
              headerConfig={headerConfig}
              isDataFetching={isDataFetching}
              tableData={tableDataState}
              setFilters={applyFilter}
              initialSortBy={initialSortBy}
              dataRequestFunction={dataRequestFunction}
              changeSortOrder={changeSortOrder}
              sortKey={sortKey}
              sortType={sortType}
              scrollTop={scrollTop}
              columnWidth={columnWidth}
              isNonVertualizedTable={isNonVertualizedTable}
            />
          )}
          {isNonVertualizedTable ? (
            <LkNonVertualizedTableBody
              headerConfig={headerConfig}
              isDataFetching={isDataFetching}
              tableData={tableDataState}
              pageSize={pageLimit}
              totalRowsCount={totalRowsCount}
              setNextPage={updateNextPage}
              rowKey={rowKey}
              selectedRows={selectedRows}
              warnedRows={warnedRows}
              tableBodyRef={tableBodyRef}
              rowSize={rowSize}
              rowExtendedSize={rowExtendedSize}
              renderExtenedComponent={renderExtenedComponent}
              columnWidth={columnWidth}
              onRowClick={onRowClick}
              plainTheme={plainTheme}
              noDataText={noDataText}
              border={border}
              hasMoreItemsToload={hasMoreItemsToload}
            />
          ) : (
            <LkVertualizedTableBody
              headerConfig={headerConfig}
              isDataFetching={isDataFetching}
              tableData={tableDataState}
              pageSize={pageLimit}
              totalRowsCount={totalRowsCount}
              currentPage={nextPagePage}
              setNextPage={updateNextPage}
              rowKey={rowKey}
              selectedRows={selectedRows}
              warnedRows={warnedRows}
              tableBodyRef={tableBodyRef}
              rowSize={rowSize}
              rowExtendedSize={rowExtendedSize}
              renderExtenedComponent={renderExtenedComponent}
              columnWidth={columnWidth}
              onRowClick={onRowClick}
              plainTheme={plainTheme}
              noDataText={noDataText}
              border={border}
            />
          )}
        </Table>
      </TableContainer>
      {tableDataState.length > 0 && showTableCount && (
        <TableCount count={tableDataState.length} totalCount={totalRowsCount} />
      )}
    </div>
  );
};

export default React.memo(LkTable);

LkTable.propTypes = {
  tableHeight: PropTypes.number,
  headerConfig: PropTypes.arrayOf(PropTypes.instanceOf(Object).isRequired).isRequired,
  isDataFetching: PropTypes.bool,
  tableData: PropTypes.instanceOf(Array),
  setFilters: PropTypes.func,
  initialSortBy: PropTypes.shape({
    id: PropTypes.string.isRequired,
    order: PropTypes.string.isRequired
  }),
  dataRequestFunction: PropTypes.func,
  pageLimit: PropTypes.number,
  totalRowsCount: PropTypes.number,
  rowKey: PropTypes.string,
  selectedRows: PropTypes.instanceOf(Array),
  warnedRows: PropTypes.instanceOf(Array),
  pageNumber: PropTypes.number,
  rowSize: PropTypes.number,
  rowExtendedSize: PropTypes.number,
  columnWidth: PropTypes.number,
  showTableCount: PropTypes.bool,
  onRowClick: PropTypes.func,
  hideHeader: PropTypes.bool,
  plainTheme: PropTypes.bool,
  isNonVertualizedTable: PropTypes.bool,
  border: PropTypes.bool,
  noDataText: PropTypes.string,
  enableLocalSort: PropTypes.bool,
  dataCy: PropTypes.string
};
