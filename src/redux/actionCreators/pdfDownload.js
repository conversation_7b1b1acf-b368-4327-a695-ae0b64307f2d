import * as action from '../actionTypes/pdfDownload'

export function pdfDownloadLoad(data) {
    return {
      type: action.PDF_DOWNLOAD_LOAD,
      payload: data
    };
  }
  
  export function pdfDownloadSuccess(data) {
    return {
      type: action.PDF_DOWNLOAD_SUCCESS,
      payload: data
    };
  }
  export function pdfDownloadFail(data) {
    return {
      type: action.PDF_DOWNLOAD_FAIL,
      payload: data
    };
  }

  export function pdfDownloadReset(data) {
    return {
      type: action.PDF_DOWNLOAD_RESET,
      payload: data
    };
  }
  
  