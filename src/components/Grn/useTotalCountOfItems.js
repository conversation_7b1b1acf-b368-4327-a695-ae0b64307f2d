/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const useTotalCountOfItems = (QCData) => {
  const [countInfo, setCountInfo] = useState([]);
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);

  useEffect(() => {
    if (QCData) {
      const { qc_passed, qc_failed, others } = QCData;
      let qc_pass_count = 0;
      for (const i in qc_passed) {
        qc_pass_count += qc_passed[i].length;
      }
      const infoData = [
        { [createGrnLocal.SCANNED]: qc_pass_count + qc_failed.length },
        { [createGrnLocal.QC_PASS]: qc_pass_count },
        { [createGrnLocal.QC_FAIL]: qc_failed.length },
        { [createGrnLocal.OTHERS]: others.length }
      ];
      setCountInfo(infoData);
    }
  }, [QCData]);
  return countInfo;
};

export default useTotalCountOfItems;
