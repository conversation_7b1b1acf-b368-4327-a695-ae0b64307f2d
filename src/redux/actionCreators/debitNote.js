import * as action from '../actionTypes/debitNote'

export function debitNoteListLoad(data) {
  return {
    type: action.DEBIT_NOTE_LIST_LOAD,
    payload: data
  };
}

export function debitNoteListSuccess(data) {
  return {
    type: action.DEBIT_NOTE_LIST_SUCCESS,
    payload: data
  };
}

export function debitNoteListFail(data) {
  return {
    type: action.DEBIT_NOTE_LIST_FAIL,
    payload: data
  };
}

export function debitNoteListCsvLoad(data) {
  return {
    type: action.DEBIT_NOTE_LIST_CSV_LOAD,
    payload: data
  };
}
export function debitNoteListCsvFail(data) {
  return {
    type: action.DEBIT_NOTE_LIST_CSV_FAIL,
    payload: data
  };
}

export function debitNoteListCsvSuccess() {
  return {
    type: action.DEBIT_NOTE_LIST_CSV_SUCCESS,
  };
}

export function debitNoteDetailLoad(data) {
  return {
    type: action.DEBIT_NOTE_DETAIL_LOAD,
    payload: data
  };
}
export function debitNoteDetailSuccess(data) {
  return {
    type: action.DEBIT_NOTE_DETAIL_SUCCESS,
    payload: data
  };
}

export function debitNoteDetailFail(data) {
  return {
    type: action.DEBIT_NOTE_DETAIL_FAIL,
    payload: data
  };
}
