import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Tab from '@mui/material/Tab';
import { makeStyles } from 'tss-react/mui';
import { Box, Button, Switch, InputAdornment } from '@mui/material';
import { StyledTabs } from 'components/common/StyledTabs';
import './constraintTabs.scss';
import LkChip from 'components/MaterialUi/LkChip';
import LkInput from 'components/MaterialUi/LkInput';
import { useDispatch, useSelector } from 'react-redux';

import SearchIcon from '@mui/icons-material/Search';
import { debounce } from 'utils/helpers';
import { toastOpen } from 'redux/actionCreators/toast';
import {
  cmsMetaDataLoad,
  cmsMetaDataReset,
  saveConstraintReset,
  getConstraintListReset
} from 'redux/actionCreators/constraint';
import Spinner from 'components/Spinner/Spinner';
import useFilterHook from 'components/common/useFilterHook';
import CloseIcon from '@mui/icons-material/Close';
import { LOCALISATION } from 'redux/reducers/localisation';
import ConstraintList from './ConstraintList';

const styles = makeStyles()((theme) => ({
  positionRelative: { position: 'relative' },
  tab1Absolute: { position: 'absolute', background: 'white', top: 48, left: 1 },
  tab2Absolute: { position: 'absolute', background: 'white', top: 48, left: 121 },
  tab3Absolute: { position: 'absolute', background: 'white', top: 48, left: 321 },
  container: {
    height: '91vh',
    backgroundColor: '#f5f5f5',
    padding: theme.spacing(3),
    paddingTop: theme.spacing(1),
    paddingBottom: 0
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },
  root: { backgroundColor: '#FFFFFF' },
  doneIcon: { fontSize: '1rem', color: '#07949B' },
  LkChip: { borderRadius: 16, marginLeft: 10 }
}));

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box> {children} </Box>}
    </div>
  );
};

const ConstraintTabs = () => {
  const navigate = useNavigate();
  const [value, setValue] = useState(0);
  const [globalSearch, setGlobalSearch] = useState('');
  const [rulesEnabled, setEnabledRules] = useState(false);
  const { selectedFacility } = useSelector((state) => state.settings);
  const [ruleStatus, setRuleStatus] = useState([]);
  const { classes } = styles();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CONSTRAINTS);

  const { constraintListLoad, constraintListFail, cmsMetaData, cmsMetaDataLoading, cmsMetaError } =
    useSelector((state) => state.constraint);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const dispatch = useDispatch();

  const resetData = () => {
    dispatch(cmsMetaDataReset());
    dispatch(saveConstraintReset());
    dispatch(getConstraintListReset());
  };

  useEffect(
    () => () => {
      resetData();
    },
    [dispatch]
  );

  useEffect(() => {
    const obj = {
      facility: selectedFacility,
      fetchDataTypeList: ['RULE_TYPE']
    };
    dispatch(cmsMetaDataLoad(obj));
  }, [dispatch]);

  useEffect(() => {
    if (constraintListFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: constraintListFail?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [dispatch, constraintListFail]);
  const onChangeFun = useCallback(
    debounce((e) => setGlobalSearch(e), 500),
    []
  );

  const handleChange = (event, newValue) => {
    setValue(newValue);
  }; // setValue(newValue);

  const toogleEnabled = () => {
    setEnabledRules(!rulesEnabled);
    setRuleStatus([]);
  };

  const goToConstraintCreate = () => {
    resetData();
    navigate('/constraint/create');
  };

  if (cmsMetaError) {
    return (
      <Box pt={4} textAlign="center">
        {cmsMetaError?.meta?.displayMessage}
      </Box>
    );
  }

  if (cmsMetaDataLoading || !cmsMetaData) {
    return <Spinner className="text-center" />;
  }

  return (
    <div className={classes.container}>
      <div>
        <div className={classes.mainBar}>
          <Box width="320px">
            <LkInput
              placeholder={CONTENT.SEARCH}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                  </InputAdornment>
                )
              }}
              variant="outlined"
              className={classes.root}
              onChange={(e) => onChangeFun(e.target.value)}
            />
          </Box>
          <div>
            {' '}
            <Button variant="contained" color="primary" onClick={() => goToConstraintCreate()}>
              {' '}
              {CONTENT.CREATE_NEW_RULE}
            </Button>{' '}
          </div>
        </div>
        <Box width="100%" overflow="hidden" margin="0 auto" className={classes.positionRelative}>
          <StyledTabs value={value} onChange={handleChange}>
            {cmsMetaData?.fetchPossibleValuesMap?.RULE_TYPE.map((name, index) => (
              <Tab
                label={name}
                key={name}
                className={`${value === index ? 'tabselected' : ''} constraintTabs`}
              />
            ))}
          </StyledTabs>
          <Box
            className={`border-grey5-radiusbase overflow-hidden ${
              value === 0 && 'border-left-no-radius'
            }`}
          >
            {cmsMetaData?.fetchPossibleValuesMap?.RULE_TYPE.map((name, index) => (
              <TabPanel value={value} index={index} key={name}>
                <Box className="bg-white" pl={3} display="flex" alignItems="center">
                  <Box
                    pr={3}
                    height="55px"
                    className="fs10 text-33 border-right-dd"
                    display="flex"
                    alignItems="center"
                  >
                    <Box width="70px" lineHeight="10px">
                      {' '}
                      {CONTENT.ENABLED_ONLY}{' '}
                    </Box>
                    <Switch
                      checked={rulesEnabled}
                      onChange={() => !constraintListLoad && toogleEnabled()}
                      name="checkedB"
                      color="primary"
                      size="small"
                    />
                  </Box>
                  <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
                    {selectedFilterList.map(({ key, value: filterValue }) => (
                      <Box key={key + filterValue} mr={2} mb={1}>
                        <LkChip
                          label={`${key}: ${filterValue}`}
                          type="filter"
                          deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                          size="small"
                          onDelete={() => deleteSelectedFilter(key, filterValue)}
                        />
                      </Box>
                    ))}
                  </Box>
                </Box>
                <ConstraintList
                  ruleType={cmsMetaData?.fetchPossibleValuesMap?.RULE_TYPE[index]}
                  globalSearch={globalSearch}
                  isRulesEnabled={rulesEnabled}
                  ruleStatus={ruleStatus}
                  selectedFilterList={selectedFilterList}
                  onChangeFilterList={onChangeFilterList}
                  deleteSelectedFilter={deleteSelectedFilter}
                  resetFilters={resetFilters}
                  CONTENT={CONTENT}
                />
              </TabPanel>
            ))}
          </Box>
        </Box>
      </div>
    </div>
  );
};

export default ConstraintTabs;
