// get list
export const GET_VENDOR_MASTER_LIST_LOAD = 'GET_VENDOR_MASTER_LIST_LOAD';
export const GET_VENDOR_MASTER_LIST_SUCCESS = 'GET_VENDOR_MASTER_LIST_SUCCESS';
export const GET_VENDOR_MASTER_LIST_FAIL = 'GET_VENDOR_MASTER_LIST_FAIL';
export const GET_VENDOR_MASTER_LIST_RESET = 'GET_VENDOR_MASTER_LIST_RESET';

// export vendor master
export const EXPORT_VENDOR_MASTER_LOAD = 'EXPORT_VENDOR_MASTER_LOAD';
export const EXPORT_VENDOR_MASTER_SUCCESS = 'EXPORT_VENDOR_MASTER_SUCCESS';
export const EXPORT_VENDOR_MASTER_FAIL = 'EXPORT_VENDOR_MASTER_FAIL';
export const EXPORT_VENDOR_MASTER_RESET = 'EXPORT_VENDOR_MASTER_RESET';

// upload vendor master
export const UPLOAD_VENDOR_MASTER_LOAD = 'UPLOAD_VENDOR_MASTER_LOAD';
export const UPLOAD_VENDOR_MASTER_SUCCESS = 'UPLOAD_VENDOR_MASTER_SUCCESS';
export const UPLOAD_VENDOR_MASTER_FAIL = 'UPLOAD_VENDOR_MASTER_FAIL';
export const UPLOAD_VENDOR_MASTER_RESET = 'UPLOAD_VENDOR_MASTER_RESET';

// download vendor master
export const DOWNLOAD_VENDOR_MASTER_LOAD = 'DOWNLOAD_VENDOR_MASTER_LOAD';
export const DOWNLOAD_VENDOR_MASTER_SUCCESS = 'DOWNLOAD_VENDOR_MASTER_SUCCESS';
export const DOWNLOAD_VENDOR_MASTER_FAIL = 'DOWNLOAD_VENDOR_MASTER_FAIL';
export const DOWNLOAD_VENDOR_MASTER_RESET = 'DOWNLOAD_VENDOR_MASTER_RESET';

// vendor proccess status api
export const VENDOR_PROCCESS_STATUS_LOAD = 'VENDOR_PROCCESS_STATUS_LOAD';
export const VENDOR_PROCCESS_STATUS_SUCCESS = 'VENDOR_PROCCESS_STATUS_SUCCESS';
export const VENDOR_PROCCESS_STATUS_FAIL = 'VENDOR_PROCCESS_STATUS_FAIL';
export const VENDOR_PROCCESS_STATUS_RESET = 'VENDOR_PROCCESS_STATUS_RESET';

// file download api
export const FILE_DOWNLOAD_VENDOR_LOAD = 'FILE_DOWNLOAD_VENDOR_LOAD';
export const FILE_DOWNLOAD_VENDOR_SUCCESS = 'FILE_DOWNLOAD_VENDOR_SUCCESS';
export const FILE_DOWNLOAD_VENDOR_FAIL = 'FILE_DOWNLOAD_VENDOR_FAIL';
export const FILE_DOWNLOAD_VENDOR_RESET = 'FILE_DOWNLOAD_VENDOR_RESET';

// price update
export const VENDOR_MASTER_APPROVE_LOAD = 'VENDOR_MASTER_APPROVE_LOAD';
export const VENDOR_MASTER_APPROVE_SUCCESS = 'VENDOR_MASTER_APPROVE_SUCCESS';
export const VENDOR_MASTER_APPROVE_FAIL = 'VENDOR_MASTER_APPROVE_FAIL';
export const VENDOR_MASTER_APPROVE_RESET = 'VENDOR_MASTER_APPROVE_RESET';

// get history list
export const GET_PID_AUDIT_HISTORY_LIST_LOAD = 'GET_PID_AUDIT_HISTORY_LIST_LOAD';
export const GET_PID_AUDIT_HISTORY_LIST_SUCCESS = 'GET_PID_AUDIT_HISTORY_LIST_SUCCESS';
export const GET_PID_AUDIT_HISTORY_LIST_FAIL = 'GET_PID_AUDIT_HISTORY_LIST_FAIL';
export const GET_PID_AUDIT_HISTORY_LIST_RESET = 'GET_PID_AUDIT_HISTORY_LIST_RESET';


// export history list
export const EXPORT_PID_AUDIT_HISTORY_LIST_LOAD = 'EXPORT_PID_AUDIT_HISTORY_LIST_LOAD';
export const EXPORT_PID_AUDIT_HISTORY_LIST_SUCCESS = 'EXPORT_PID_AUDIT_HISTORY_LIST_SUCCESS';
export const EXPORT_PID_AUDIT_HISTORY_LIST_FAIL = 'EXPORT_PID_AUDIT_HISTORY_LIST_FAIL';
export const EXPORT_PID_AUDIT_HISTORY_LIST_RESET = 'EXPORT_PID_AUDIT_HISTORY_LIST_RESET';
