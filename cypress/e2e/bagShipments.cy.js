import { genericDateFormatted } from '../../src/utils/helpers';
import config from '../../src/config';

// eslint-disable-next-line max-len
const bagShipmentUrl = `${config.shipmentBags.getShipmentBagsList}?sortBy=updatedAt&sortOrder=DESC&page=0&size=35`;

const manifestUrl = (bagId, manifestId) =>
  `${config.manifest.fetchBag}?bagId=${bagId}&manifestId=${manifestId}`;

const valueOrDefault = (val) => val ?? '-';

const checkTableCell = (index, value) => {
  cy.getByCy(`table-cell-0-${index}`).should('contain.text', value);
};

describe('Bag Shipment', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');

    cy.getByCy('main-navigation').click();
    cy.getByCy('DC Sorting').click();
    cy.intercept('GET', bagShipmentUrl).as('getBagShipments');
    cy.getByCy('Bag Shipments').click();
  });

  after(() => {
    cy.logout();
  });

  it('Visits the Bag Shipment Page from app bar and table is populated', () => {
    cy.fixture('bagShipment/bagShipmentHeader').as('headers');

    // check if the url is correct
    cy.url().should('include', '/bagShipments');

    // check if we are on correct page
    cy.findByText(/bag shipments/i).should('exist');

    cy.wait('@getBagShipments').then(({ response }) => {
      // Capturing the API response body
      const {
        bagId,
        manifestId,
        manifestShippingMethod,
        shipmentCount,
        bagLocation,
        bagStatus,
        dropLocationType,
        createdAt,
        updatedAt,
        createdBy,
        updatedBy
      } = response.body.data.bagShipmentsList[0];

      const firstRowData = [
        bagId,
        manifestId,
        manifestShippingMethod,
        shipmentCount,
        bagLocation,
        bagStatus,
        dropLocationType,
        createdAt,
        updatedAt,
        createdBy,
        updatedBy
      ];

      // Checking first row of the Bag shipment table is properly populated
      cy.getByCy('table-cell-0-0')
        .should('contain.text', valueOrDefault(bagId))
        .find('a')
        .should('have.attr', 'href')
        .and('not.be.empty');

      cy.get('@headers').then((headers) => {
        headers.forEach((header, index) => {
          cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
          if (index === 7 || index === 8) {
            checkTableCell(index, valueOrDefault(genericDateFormatted(firstRowData[index])));
          } else {
            checkTableCell(index, valueOrDefault(firstRowData[index]));
          }
        });
      });
    });
  });

  it('will click first row bagId and navigate to manifest details page', () => {
    cy.wait('@getBagShipments').then(({ response }) => {
      const { bagId, manifestId } = response.body.data.bagShipmentsList[0];
      cy.intercept('GET', manifestUrl(bagId, manifestId)).as('manifestDetails');
      cy.getByCy('table-cell-0-0').should('exist').click();
      cy.url().should(
        'include',
        `/manifest-detail?bagId=${bagId}&manifestId=${manifestId}&bagSearch=true&bagShipment=true`
      );
      cy.wait('@manifestDetails').then((manifestResponse) => {
        const manifestDetails = manifestResponse.response.body.data;
        cy.findByText(/Manifest Details/i).should('exist');
        cy.findByText(/NEXS COUNT/).should('exist');
        cy.findByText(/BAG COUNT/).should('exist');
        cy.getByCy('enter awb shipment').should('not.exist');
        if (manifestDetails.bagStatus === 'Attached') {
          cy.getByCy('bag status').should('have.css', 'color').and('equal', 'rgb(255, 0, 0)');
        } else if (manifestDetails.bagStatus === 'Detached') {
          cy.getByCy('bag status').should('have.css', 'color').and('equal', 'rgb(0, 255, 0)');
        }
        if (manifestDetails.status === 'CLOSED') {
          cy.getByCy('close manifest').should('not.exist');
          cy.getByCy('retry').should('not.exist');
          cy.getByCy('print summary').should('exist');
          cy.getByCy('print pdf').should('exist');
          cy.getByCy('print csv').should('exist');
        }
        cy.getByCy('scan barcode').should('not.exist');
        cy.getByCy('back').click();
        cy.url().should('include', '/bagShipments');
        cy.findByText(/bag shipments/i).should('exist');
      });
    });
  });
});
