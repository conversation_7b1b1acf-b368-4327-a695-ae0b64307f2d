import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const constraintListApi = payload => {
    return tokenInstance().post(config.constraint.constraintList, payload)
        .then(response => ({ response }))
        .catch(error => { throw error });
}


export const constraintSaveApi = payload => {
    return tokenInstance().post(config.constraint.constraintSave, payload)
        .then(response => ({ response }))
        .catch(error => { throw error });
}
export const validateConditionApi = payload => {
    return tokenInstance(null, null, true).post(config.constraint.validateCondition, payload)
        .then(response => response)
        .catch(error => { throw error });
};

export const cmsMetaDataApi = payload => {
    return tokenInstance().post(config.constraint.fetchPossibleValues, payload)
        .then(response => response)
        .catch(error => { throw error });
};

export const validateConsequenceApi = payload => {
    return tokenInstance().post(config.constraint.validateConsequence, payload)
        .then(response => response)
        .catch(error => { throw error });
};

export const facilityAutoCompleteApi = payload => {
    const { value } = payload;
    return tokenInstance().get(`${config.constraint.facilityAutocomplete}?key=facility_info&value=${value}&recommendations=100`)
        .then(response => response)
        .catch(error => { throw error });
};



