import React from 'react';
import { useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import CircularProgress from '@mui/material/CircularProgress';

const useStyles = makeStyles()(() => ({
  loaderImg: {
    height: '70px',
    width: '70px',
    zIndex: '99999999999',
    position: 'fixed',
    top: '48%',
    left: '48%'
  },
  backgroundGray: {
    position: 'fixed',
    zIndex: 9999,
    backgroundColor: 'gray',
    opacity: 0.4,
    top: 0,
    left: 0
  }
}));

const Loader = () => {
  const { classes } = useStyles();
  const { isLoading } = useSelector(state => state.loader);
  if (isLoading)
  {return (
    <div>
      <div
        className={classes.backgroundGray}
        style={{
          width: window.screen.width,
          height: window.screen.height
        }}
      />
      <CircularProgress className={classes.loaderImg} />
    </div>
  );}
  return '';
};
export default Loader;
