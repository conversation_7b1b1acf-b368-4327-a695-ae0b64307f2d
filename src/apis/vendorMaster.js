import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { convertToQueryString } from '../utils/helpers';

export const getVendorMasterApi = (payload) => {
  const f = convertToQueryString(payload.apiObj);
  return tokenInstance()
    .get(`${config.vendorMaster.list}?${f}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const exportVendorMasterApi = (payload) => {
  const f = convertToQueryString(payload);
  return tokenInstance()
    .get(`${config.vendorMaster.export}?${f}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const downloadReportVendorMasterApi = (payload) =>
  tokenInstance()
    .get(`${config.vendorMaster.download}/${payload}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const uploadVendorMasterApi = (payload) =>
  tokenInstance()
    .post(`${config.vendorMaster.upload}`, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const vendorProccessStatusApi = (payload) =>
  tokenInstance()
    .get(`${config.vendorMaster.processingStatus}?reqid=${payload}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const fileDownloadApi = (payload) =>
  tokenInstance()
    .get(`${config.vendorMaster.fileDownload}?file=${payload}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const statusApproveApi = (payload) =>
  tokenInstance().post(config.vendorMaster.statusApprove, payload);

export const getPidAuditHistoryListApi = ({ payload }) =>
  tokenInstance().get(config.vendorMaster.pidAuditHistoryList, { params: payload });

export const exportPidAuditHistoryListApi = () =>
  tokenInstance().get(config.vendorMaster.exportAuditHistoryList);
