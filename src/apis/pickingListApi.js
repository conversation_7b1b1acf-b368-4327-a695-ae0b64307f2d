import { tokenInstance } from 'utils/axiosInstance';

import config from '../config';

export const pickingListApi = (payload) =>
  tokenInstance().post(config.picking.pickingList, payload);

export const pickingDetailsListApi = (payload) =>
  tokenInstance().post(
    `${config.picking.pickingDetailsList}/${payload.pickingId}`,
    payload.pageRequest
  );

export const exportCsvApi = (id) => tokenInstance().get(`${config.picking.exportCSV}/${id}`);
