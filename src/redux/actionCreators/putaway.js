import * as action from '../actionTypes/putaway';

// putaway list based on user
export function getUserPutawayLoad(data) {
  return { type: action.USER_PUTAWAY_LIST_LOAD, payload: data };
}

export function getUserPutawaySuccess(data) {
  return { type: action.USER_PUTAWAY_LIST_SUCCESS, payload: data };
}

export function getUserPutawayFail(data) {
  return { type: action.USER_PUTAWAY_LIST_FAIL, payload: data };
}

export function getUserPutawayReset(data) {
  return { type: action.USER_PUTAWAY_LIST_RESET, payload: data };
}

// update putaway to pending
export function updatePutawayToPendingLoad(data) {
  return { type: action.UPDATE_PUTAWAY_TO_PENDING_LOAD, payload: data };
}

export function updatePutawayToPendingSuccess(data) {
  return { type: action.UPDATE_PUTAWAY_TO_PENDING_SUCCESS, payload: data };
}

export function updatePutawayToPendingFail(data) {
  return { type: action.UPDATE_PUTAWAY_TO_PENDING_FAIL, payload: data };
}

export function updatePutawayToPendingReset(data) {
  return { type: action.UPDATE_PUTAWAY_TO_PENDING_RESET, payload: data };
}

// Print Putaway
export function printPutawayLoad(data) {
  return { type: action.PRINT_PUTAWAY_LOAD, payload: data };
}

export function printPutawaySuccess(data) {
  return { type: action.PRINT_PUTAWAY_SUCCESS, payload: data };
}

export function printPutawayFail(data) {
  return { type: action.PRINT_PUTAWAY_FAIL, payload: data };
}

export function printPutawayReset(data) {
  return { type: action.PRINT_PUTAWAY_RESET, payload: data };
}

// Create putaway for release barcode

export const putawayCreationLoad = (data) => ({ type: action.CREATE_PUTAWAY_LOAD, payload: data });

export const putawayCreationSuccess = (data) => ({
  type: action.CREATE_PUTAWAY_SUCCESS,
  payload: data
});

export const putawayCreationFail = (data) => ({ type: action.CREATE_PUTAWAY_FAIL, payload: data });

export const putawayCreationReset = (data) => ({
  type: action.CREATE_PUTAWAY_RESET,
  payload: data
});

export const updatePutawayBarcodeStatusList = (data) => ({
  type: action.UPDATE_PUTAWAY_BARCODE_LIST,
  payload: data
});

export const resetPutawayBarcodeStatusList = () => ({
  type: action.RESET_PUTAWAY_BARCODE_LIST
});

export const completePutAwaySGLoad = (data) => ({
  type: action.COMPLETE_PUTAWAY_SG_LOAD,
  payload: data
});

export const completePutAwaySGSuccess = (data) => ({
  type: action.COMPLETE_PUTAWAY_SG_SUCCESS,
  payload: data
});

export const completePutAwaySGFail = (data) => ({
  type: action.COMPLETE_PUTAWAY_SG_FAIL,
  payload: data
});

export const completePutAwaySGReset = (data) => ({
  type: action.COMPLETE_PUTAWAY_SG_RESET,
  payload: data
});

export const printMultiplePutawayLoad = (data) => ({
  type: action.PRINT_MULTIPLE_PUTAWAY_LOAD,
  payload: data
});

export const printMultiplePutawaySuccess = (data) => ({
  type: action.PRINT_MULTIPLE_PUTAWAY_SUCCESS,
  payload: data
});

export const printMultiplePutawayFail = (data) => ({
  type: action.PRINT_MULTIPLE_PUTAWAY_FAIL,
  payload: data
});

export const printMultiplePutawayReset = (data) => ({
  type: action.PRINT_MULTIPLE_PUTAWAY_RESET,
  payload: data
});
