import { createSlice, createDraftSafeSelector } from '@reduxjs/toolkit';
import { CURRENT_CONSUL_FOLDER } from 'apis/consul';
import FALLBACK_CONFIG from '../../config/consul';

const initialState = {
  currency: {
    isLoading: true,
    data: {}
  },
  permissions: {
    isLoading: true,
    data: {}
  },
  navigation: {
    isLoading: true,
    data: {}
  },
  JITLensMarkDamageReasons: {
    isLoading: true,
    data: {}
  },
  idleSessionTimer: {
    isLoading: true,
    data: {}
  },
  storePackingConsolidationFacility: {
    isLoading: true,
    data: {}
  },
  adhocASRSPicking: {
    isLoading: true,
    data: {}
  },
  featureToggle: {
    isLoading: true,
    data: {}
  },
  traySeries: {
    isLoading: true,
    data: []
  },
  autoFocusTimer: {
    isLoading: true,
    data: {
      time: 300
    }
  },
  classification: {
    isLoading: true,
    data: {}
  },
  monitorPanelFilter: {
    isLoading: true,
    data: {}
  },
  stockTakeFacility: {
    isLoading: true,
    data: []
  },
  vendorName: {
    isLoading: true,
    data: {}
  },
  doCountry: {
    isLoading: true,
    data: []
  }
};

const registerSlice = createSlice({
  name: 'consul',
  initialState,
  reducers: {
    getConsulKeyValue: (state, action) => {
      action.payload.forEach((eachKey) => {
        state[eachKey].isLoading = true;
      });
    },
    getConsulKeyValueSuccess: (state, action) => {
      const responseKeys = [];
      action.payload.values.forEach((eachKeyValue) => {
        const key = eachKeyValue.key.replace(CURRENT_CONSUL_FOLDER, '');
        const value = JSON.parse(eachKeyValue.value);
        if (state[key]) {
          state[key].isLoading = false;
          state[key].data = value;
          responseKeys.push(key);
        }
      });

      const notFoundKeys = action.payload.keys.filter((itemA) => !responseKeys.includes(itemA));
      notFoundKeys.forEach((eachKey) => {
        if (state[eachKey]) {
          [eachKey].isLoading = false;
          state[eachKey].data = FALLBACK_CONFIG[eachKey];
        }
      });
    },
    getConsulKeyValueError: (state, action) => {
      action.payload.forEach((eachKey) => {
        state[eachKey].isLoading = false;
        state[eachKey].data = FALLBACK_CONFIG[eachKey];
      });
    }
  }
});

export const CONSUL = registerSlice.name;
export const { getConsulKeyValue, getConsulKeyValueSuccess, getConsulKeyValueError } =
  registerSlice.actions;

export default registerSlice.reducer;

const filterNavigationData = (data, selectedFacility) => {
  const isProduction = window.location.host === import.meta.env.VITE_PROD_URL;
  return data.reduce((updatedListData, currentData) => {
    if (selectedFacility && currentData.facility?.includes?.(selectedFacility)) {
      return updatedListData; // Skip this item
    }
    if (currentData.children?.length > 0) {
      const filteredChildren = filterNavigationData(currentData.children, selectedFacility);

      if (filteredChildren.length > 0) {
        updatedListData.push({ ...currentData, children: filteredChildren });
      }
    } else if (currentData.live !== false || !isProduction) {
      updatedListData.push(currentData);
    }

    return updatedListData;
  }, []);
};

// Selectors
const navigationState = (state) => state.consul.navigation;
const facility = (state) => state.settings;

export const selectNavigationData = createDraftSafeSelector(
  [navigationState, facility],
  ({ data, isLoading }, { selectedFacility }) => {
    let updatedListData = [];

    if (!isLoading) {
      updatedListData = filterNavigationData(data, selectedFacility);
    }

    return { navigationDataLoading: isLoading, navigationData: updatedListData };
  }
);

const getTheDisabledKeys = (data) => {
  const isProduction = window.location.host === import.meta.env.VITE_PROD_URL;
  let disabledKeys = [];
  data.forEach((currentData) => {
    if (currentData?.children?.length > 0) {
      const filteredChildrenIds = getTheDisabledKeys(currentData?.children);
      disabledKeys = [...disabledKeys, ...filteredChildrenIds];
    } else if (isProduction && currentData.live === false) {
      disabledKeys = [...disabledKeys, currentData.id];
    }
  });
  return disabledKeys;
};

export const selectFlatNavigationData = createDraftSafeSelector(
  navigationState,
  ({ data, isLoading }) => {
    let disabledKeys = [];
    if (!isLoading) {
      disabledKeys = getTheDisabledKeys(data);
    }

    return { navigationDataLoading: isLoading, disabledKeys };
  }
);
