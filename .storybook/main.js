const config = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-docs',
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-actions'
  ],
  framework: '@storybook/react-vite',
  core: {
    builder: '@storybook/builder-vite',
    disableTelemetry: true // 👈 Used to ignore update notifications.
  },
  docs: {
    autodocs: true
  },
  staticDirs: ['../public']
};
export default config;
