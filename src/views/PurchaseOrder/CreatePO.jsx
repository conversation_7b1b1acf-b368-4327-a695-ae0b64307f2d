import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { Form, Field } from 'react-final-form';
import { FieldArray } from 'react-final-form-arrays';
import arrayMutators from 'final-form-arrays';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import AddCircle from '@mui/icons-material/AddCircle';

import LkToolTip from 'components/ToolTip/ToolTip';
import InfoStrip from 'components/common/InfoStrip';
import UploadCsv from 'components/UploadCsv/UploadCsv';
import LkModal from 'components/Modal/Modal';
import { UploadCsvError } from 'components/UploadCsv/component';
import SubtitleInfoStrip from 'components/common/SubtitleInfoStrip';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import { PurchaseOrderStatus } from 'config/PurchaseOrderStatus';
import { convertDateFormat, roundUptoDigits, fileDownload, getCurrencySumbol } from 'utils/helpers';
import PurchaseOrderDetailItems from 'model/PurchaseOrderDetailItems';
import PurchaseOrderDetailsModel from 'model/PurchaseOrderDetailsModel';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  amendPoDraft,
  editPo,
  getUploadItemErrorReportLoad,
  getPidDetailsLoad,
  submitPurchaseOrderLoad,
  processingStatusLoad,
  processingStatusSuccess,
  submitPurchaseOrderReset,
  resetUploadCSV,
  purchaseOrderDetailLoad,
  processingStatusReset,
  uploadItemLoad
} from 'redux/actionCreators/purchaseOrder';
import ShortCut from 'components/shortCut/shortCut';
import POModal from './components/POModal';
import CreatePOTable from './components/CreatePOTable';

const mutatorObj = {
  ...arrayMutators,
  setTotal: (args, state, utils) => {
    utils.changeValue(state, 'total', () => args);
  },
  setPid: (args, state, utils) => {
    // if(visitedPid.has(parseInt(args[1]))) {
    //   window.alert('PID already present');
    // } else {
    //   getPidDetails(args[0]);
    utils.changeValue(state, `${args[0]}`, () => args[1]);
    // }
  }
};
const sampleCSV = [
  ['product_id', 'qty', 'price'],
  [687789, 120, 280.0],
  [687789, 120, 280.0],
  [687789, 120, 280.0]
];
let fieldArray = null;
let formValues = null;
let formObj = null;
const row = (key, value, className = '') => (
  <div key={key} className={`display-flex justify-content-space-between mr-b15 fs12 ${className}`}>
    <div>{key}</div>
    <div className="pd-l12">{value}</div>
  </div>
);

const handleEnterClicked = (e) => {
  const key = e.keyCode || e.which;
  if (key === 13) {
    e.preventDefault();
  }
};

const submitPoReq = (data) => {
  const {
    parent_po_num,
    po_num,
    vendor_id,
    shipping_date,
    expiry_date,
    version,
    items,
    reference_no,
    po_type,
    season,
    shipment_type,
    filename,
    procurement_type,
    reference_number
  } = data;
  const reqItemFields = [
    'id',
    'product_id',
    'quantity',
    'vendor_unit_cost_price',
    'cgst_rate',
    'igst_rate',
    'sgst_rate',
    'ugst_rate',
    'price_with_taxes',
    'enabled'
  ];
  const reqItemFieldsValue = [
    'id',
    'product_id',
    'quantity',
    'vendor_unit_cost_price',
    'cgst.rate',
    'igst.rate',
    'sgst.rate',
    'ugst.rate',
    'total',
    'enabled'
  ];
  const obj = {
    parent_po_num,
    po_num,
    po_type,
    vendor_id,
    shipment_type,
    shipping_date,
    expiry_date,
    version,
    reference_no,
    season,
    procurement_type,
    reference_number,
    current_timestamp: new Date().getTime()
  };
  if (items) {
    const newItems = [...items];
    newItems.forEach((item, index) => {
      const p = {};
      Object.keys(reqItemFields).forEach((it, i) => {
        p[`${reqItemFields[it]}`] = item[`${reqItemFieldsValue[i]}`];
      });
      p.id = index;
      p.enabled = true;
      p.cgst_rate = item.cgst.rate;
      p.igst_rate = item.igst.rate;
      p.sgst_rate = item.sgst.rate;
      p.ugst_rate = item.ugst.rate;
      p.hsn_classification = item.hsn_classification;
      newItems[index] = p;
    });
    obj.items = newItems;
  } else {
    obj.filename = filename;
  }
  return obj;
};
let visitedPid = {};
let subtitleInfoStripData = [];

const CreatePO = (props) => {
  const navigate = useNavigate();
  const {
    value,
    poNum,
    handleCancel
  } = props;
  const {
    vendor,
    shippingDate,
    shipment_type,
    season,
    expiryDate,
    data = {},
    status,
    reason,
    procurementType,
    referenceDetails
  } = value;

  const [initialValue, setInitialValue] = useState(
    new PurchaseOrderDetailsModel({
      ...data,
      procurement_type: procurementType && procurementType.toUpperCase(),
      season,
      reference_details: referenceDetails || [],
      expiryDate,
      shippingDate,
      shipment_type
    })
  );
  const [resetState, setResetState] = useState(false);
  const [openDetailModal, setOpenDetailModal] = useState(false);
  const [openErrorCSV, setOpenErrorCsv] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadCsvModal, setUploadCsvModal] = useState(false);
  const [errorPid, setErrorPid] = useState(false);

  const dispatch = useDispatch();

  const {
    uploadItemsData,
    uploadItemsFailData,
    csvErrorSuccess,
    pidDetailsSuccess,
    submitPoData,
    processingStatus,
    processingStatusFail,
    pidDetailsFail,
    purchaseOrderDetail,
    orderVersion
  } = useSelector((state) => state.purchaseOrder, shallowEqual);
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const PO = useSelector((state) => state[LOCALISATION].localeData.PO);

  let poStatus = 'DRAFT';
  if (status) {
    if (status === 'Amend') {
      poStatus = 'Amending';
    }
  }

  const handleSubmit = useCallback((e) => {
    const n = e?.items?.length;
    if (e?.type !== 'file' && !e.items[n - 1]?.tax_type) {
      window.alert('Waiting for Data to be load');
      return;
    }
    const apiData = {
      shipping_date: e?.shippingDate || shippingDate,
      expiry_date: e?.expiryDate || expiryDate,
      // facility_code: '08',
      po_num: poNum,
      shipment_type,
      season: e?.season || season,
      vendor_id: vendor.code,
      procurement_type: e?.procurement_type || procurementType,
      reference_number: e?.reference_number?.join(',') || referenceDetails?.join(','),
      version: orderVersion,
      po_type: 'BULK'
    };

    dispatch(editPo(undefined));

    if (status === 'Amend') {
      poStatus = 'Amending';
      const r = { data: { ...data, ...apiData, items: e }, reason };
      dispatch(amendPoDraft(r));
      navigate(`/po/detail?poNum=${poNum}&fromPo=true`);
    } else if (e?.type === 'file') {
      apiData.filename = e.uploadItemsDataProp.data.file;
      dispatch(submitPurchaseOrderLoad(submitPoReq(apiData)));
    } else {
      apiData.items = e.items;
      dispatch(submitPurchaseOrderLoad(submitPoReq(apiData)));
    }
  }, []);

  const handleFileSubmit = (uploadItemsDataProp) =>
    handleSubmit({ type: 'file', uploadItemsDataProp });

  useEffect(() => {
    visitedPid = {};
    window.addEventListener('keypress', handleEnterClicked, true);
    return () => {
      window.removeEventListener('keypress', handleEnterClicked, true);
    };
  }, []);

  const addPid = () => {
    const len = fieldArray.value.length;
    const tmp = fieldArray.value[len - 1];
    if (tmp?.product_id && tmp?.quantity && tmp?.tax_type) {
      fieldArray.push(initialValue.newItem());
    }
  };

  // Alt + D -> Open PO Detail Modal
  useKeyboardShortcut([18, 68], () => {
    setOpenDetailModal(true);
  });

  // Alt + N -> Add Item
  useKeyboardShortcut([18, 78], () => {
    addPid();
  });

  // Ctrl + I -> Import CSV
  useKeyboardShortcut([17, 73], () => {
    setUploadCsvModal(true);
  });
  // Alt + s -> Save
  useKeyboardShortcut([18, 83], () => {
    formObj.submit();
  });

  const openPoDModal = () => {
    setOpenDetailModal(true);
  };
  const infoStripData = [
    {
      key: PO.PO,
      value: poNum,
      status: PurchaseOrderStatus[poStatus]?.text || poStatus,
      type: PurchaseOrderStatus[poStatus]?.type,
      className: 'fs16 fw-bold text-turquioise_surf cursor-pointer',
      onClick: () => openPoDModal()
    },
    { key: PO.DATE, value: convertDateFormat(shippingDate, 'shortDate') },
    { key: PO.VENDOR, value: vendor.name }
  ];

  const handleRemovePid = (fields, i) => {
    const len = fields.value.length;
    delete visitedPid[`${fields.value[i].product_id}`];
    formValues.items[i] = initialValue.newItem();
    initialValue.calculateVal(formValues, formValues.items);
    if (len === 1) {
      fields.update(i, initialValue.newItem());
    } else {
      fields.remove(i);
    }
  };

  const handleQtyChange = (e, i) => {
    let val = e.target.value;
    if (Number.isNaN(val) || !val) {
      val = 1;
    }
    const x = formValues.items[i];
    const ite = initialValue.newItem({
      ...x,
      cgst_per: x.cgst.percent,
      sgst_per: x.sgst.percent,
      igst_per: x.igst.percent,
      ugst_per: x.ugst.percent,
      quantity: val
    });
    formValues.items[i] = ite;
    initialValue.calculateVal(formValues, formValues.items);
    fieldArray.update(i, ite);
  };

  // const firstUppercase = val => val[0].toUpperCase() + val.slice(1);
  const priceTooltip = (currency) => {
    // width='400px'
    const { logistics, insurance, forwarder, cha } = formValues.total_overheads_rate;
    return (
      <div className="pd-20">
        {row(PO.SUBTOTAL, `${currency} ${roundUptoDigits(formValues.subtotal, 2)} `)}
        {row(PO.DUTY, `${currency} ${roundUptoDigits(formValues.duty, 2)} `)}
        {row(
          PO.TOTAL_TAX_ON_SALES,
          `${currency} ${roundUptoDigits(formValues.taxes, 2)} `,
          'pd-b15 border-bottom-dd-dashed'
        )}
        {
          Object.keys(formValues.total_overheads_rate).length > 0 && (
            <div>
              {row(PO.LOGISTICS_CHARGES, `+${currency}${roundUptoDigits(logistics, 2)}`)}
              {row(PO.INSURANCE_CHARGES, `+${currency}${roundUptoDigits(insurance, 2)}`)}
              {row(PO.FORWADER_CHARGES, `+${currency}${roundUptoDigits(forwarder, 2)}`)}
              {row(
                PO.CHA_CHARGES,
                `+${currency}${roundUptoDigits(cha, 2)}`,
                'pd-b15 border-bottom-dd-dashed'
              )}
            </div>
          )
          // .map(tor => row(`${firstUppercase(tor)} Charges`,
          // `+ ${currency} ${roundUptoDigits(formValues.total_overheads_rate[`${tor}`], 2)} `))
        }
        {row(
          PO.TOTAL_AMOUNT,
          `${currency} ${roundUptoDigits(formValues.total, 2)} `,
          'fw-bold fs18'
        )}
      </div>
    );
  };

  useEffect(() => {
    if (submitPoData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: submitPoData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [submitPoData, dispatch]);

  useEffect(() => {
    if (uploadItemsData?.data) {
      setUploadProgress(100);
    }
    if (uploadItemsData?.response?.data) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: uploadItemsData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
      setUploadProgress(0);
    }
  }, [dispatch, uploadItemsData]);

  useEffect(() => {
    if (pidDetailsFail) {
      const len = fieldArray.value.length;
      if (fieldArray.value[len - 1].product_id !== pidDetailsFail.pid) {
        setErrorPid(false);
        return;
      }
      delete visitedPid[`${fieldArray.value[len - 1].product_id}`];
      const ite = initialValue.newItem();
      formValues.items[len - 1] = ite;
      initialValue.calculateVal(formValues, formValues.items);
      fieldArray.update(len - 1, ite);
      setErrorPid('PID not found');
    } else {
      setErrorPid(false);
    }
  }, [pidDetailsFail]);

  useEffect(() => {
    if (pidDetailsSuccess) {
      const len = fieldArray.value.length;
      if (fieldArray.value[len - 1].product_id === `${pidDetailsSuccess.product_id}`) {
        const ite = initialValue.newItem({
          ...pidDetailsSuccess,
          ...pidDetailsSuccess.hsn_tax_info
        });
        formValues.items[len - 1] = ite;
        initialValue.calculateVal(formValues, formValues.items);
        fieldArray.update(len - 1, ite);
      }
    }
  }, [pidDetailsSuccess]);
  const selectFiles = (file) => {
    for (let i = 1; i < 100; i += 1) {
      setTimeout(() => setUploadProgress(i), 100);
    }
    const formData = new FormData();
    formData.set('po_num', poNum);
    formData.set('file', file);
    dispatch(uploadItemLoad(formData));
  };
  const downloadErrorReport = () => {
    if (csvErrorSuccess) {
      fileDownload(csvErrorSuccess, 'poError');
      return;
    }
    if (uploadItemsFailData?.data?.file) {
      dispatch(getUploadItemErrorReportLoad(uploadItemsFailData?.data?.file));
    }
  };
  useEffect(() => {
    if (csvErrorSuccess) {
      fileDownload(csvErrorSuccess, 'poError');
    }
  }, [csvErrorSuccess]);
  // const getPidDetails = val => useCallback(dispatch(getPidDetailsLoad(val)),[]);
  const getPidDetails = useCallback((val) => dispatch(getPidDetailsLoad(val), []), []);
  const handlePidChange = (e, index, fields) => {
    const val = e.target.value;
    if (visitedPid[`${val}`]) {
      if (visitedPid[`${val}`] === index + 1) {
        return;
      }
      fields.update(index, new PurchaseOrderDetailItems({}));
    } else {
      visitedPid = {};
      // eslint-disable-next-line no-unused-expressions, no-return-assign
      fields.value && fields.value.forEach((id, i) => (visitedPid[`${id.product_id}`] = i + 1));
      getPidDetails({
        reqObj: {
          product_ids: [parseInt(val, 10)],
          state_code: vendor.state_code,
          supplier_id: vendor.code
        },
        params: { procurementType },
        currency: vendor.currency
      });
    }
    e.preventDefault();
  };
  const closeCsvModal = () => {
    dispatch(resetUploadCSV());
    setResetState(!resetState);
    setUploadCsvModal(false);
  };

  useEffect(() => {
    closeCsvModal();
  }, [submitPoData?.data]);

  useEffect(() => {
    if (processingStatus?.status === 'done' && processingStatus?.error_msg) {
      if (processingStatus?.invalidPIDs?.details) {
        setOpenErrorCsv(
          new Blob([processingStatus?.invalidPIDs?.details], { type: 'application/csv' })
        );
        // fileDownload(new Blob([processingStatus?.invalidPIDs?.details],
        // { type: 'application/csv' }),'PO_CSV_FAIL');
      } else {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: processingStatus?.error_msg[0],
            severity: 'error'
          })
        );
      }
    }
  }, [processingStatus?.error_msg]);

  // useEffect(() => {
  //   dispatch(processingStatusSuccess(null));
  //   dispatch(submitPurchaseOrderReset(null));
  //   closeCsvModal();
  // }, [processingStatus,processingStatusFail]);

  useEffect(() => {
    if (processingStatusFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: processingStatusFail,
          severity: 'error'
        })
      );
      dispatch(processingStatusReset());
    }
  }, [processingStatusFail]);

  useEffect(() => {
    if (processingStatus?.status === 'done' && processingStatus?.error_msg) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: processingStatus?.error_msg[0],
          severity: 'error'
        })
      );
    } else if (processingStatus?.status === 'done') {
      navigate(`/po/detail?poNum=${poNum}&fromPo=true`);
    }

    dispatch(processingStatusSuccess(null));
    dispatch(submitPurchaseOrderReset(null));
    closeCsvModal();
  }, [processingStatus, processingStatusFail]);
  const infoTableData = [
    { key: 'PO', value: poNum },
    { key: 'Vendor Code', value: vendor.code, header: vendor.name },
    { key: 'PO Status', value: PurchaseOrderStatus[poStatus]?.text || poStatus },
    { key: 'Type', value: 'Manual' },
    { key: 'From party', value: null },
    { key: 'Approved By', value: null },
    { key: 'Approved On', value: null },
    { key: 'Created By', value: null }
    // { key: 'Expiry Date', value: expiryDate && convertDateFormat(expiryDate, 'shortDateTime') },
    // { key: 'Shipping Date', value: shippingDate ?
    // convertDateFormat(shippingDate, 'shortDateTime') : null },
  ];
  const convertAddressString = (addressArray) => {
    let add;
    addressArray.forEach((address, index) => {
      const con = index === addressArray.length - 1 ? '.' : ', ';
      if (address) {
        add = add ? add + address + con : address + con;
      }
    });
    return add;
  };
  const {
    primary_contact_name,
    billing_address_line1,
    billing_address_line2,
    billing_address_city,
    billing_address_state,
    billing_address_pincode,
    shipping_address_line1,
    shipping_address_line2,
    shipping_address_city,
    shipping_address_state,
    shipping_address_pincode
  } = data;
  const addressBoxData = [
    {
      superTitle: 'Billing Address',
      title: primary_contact_name || '-',
      text: primary_contact_name
        ? convertAddressString([
          billing_address_line1,
          billing_address_line2,
          billing_address_city,
          billing_address_state,
          billing_address_pincode
        ])
        : '-'
    },
    {
      superTitle: 'Shipping Address',
      title: primary_contact_name || '-',
      text: primary_contact_name
        ? convertAddressString([
          shipping_address_line1,
          shipping_address_line2,
          shipping_address_city,
          shipping_address_state,
          shipping_address_pincode
        ])
        : '-'
    }
  ];
  const handleCSVErrorClose = () => {
    dispatch(purchaseOrderDetailLoad({ ponum: poNum, items: true }));
  };
  useEffect(() => {
    if (openErrorCSV && purchaseOrderDetail) {
      setOpenErrorCsv(false);
      setInitialValue(
        new PurchaseOrderDetailsModel({ ...initialValue, items: purchaseOrderDetail.data.items })
      );
    }
  }, [purchaseOrderDetail]);

  const convertedCurrency = getCurrencySumbol(vendor.currency);

  const handleFieldRender = useCallback(
    ({ fields }) => {
      const priceStripData = [
        {
          title: PO.SUBTOTAL,
          subtitle: `${convertedCurrency}${roundUptoDigits(formValues.subtotal, 2)}`,
          titleClassName: 'fs12',
          subTitleClassName: 'fs16 mr-t8'
        },
        {
          title: PO.TAXES_DUTY,
          subtitle: `${convertedCurrency}${roundUptoDigits(formValues.taxes_duty, 2)}`,
          titleClassName: 'fs12',
          subTitleClassName: 'fs16 mr-t8'
        },
        {
          title: PO.OVERHEADS,
          subtitle: `${convertedCurrency}${roundUptoDigits(formValues.overheads || 0, 2)}`,
          titleClassName: 'fs12',
          subTitleClassName: 'fs16 mr-t8'
        },
        {
          title: PO.TOTAL,
          subtitle: `${convertedCurrency}${roundUptoDigits(formValues.total, 2)}`,
          titleClassName: 'fs12',
          subTitleClassName: 'fs16 mr-t8 fw-bold'
        }
      ];
      subtitleInfoStripData = [
        {
          title: formValues.total_quantity,
          subtitle: PO.TOTAL_ORDER,
          titleClassName: 'fs20',
          subTitleClassName: 'fs14 text-99 mr-t5'
        },
        {
          title: formValues.accepted_quantity,
          subtitle: PO.ACCEPTED,
          titleClassName: 'fs20',
          subTitleClassName: 'fs14 text-99 mr-t5'
        },
        {
          title: formValues.rejected_quantity,
          subtitle: PO.REJECTED,
          titleClassName: 'fs20 text-red',
          subTitleClassName: 'fs14 text-99 mr-t5'
        },
        {
          title: formValues.pending_quantity,
          subtitle: PO.PENDING,
          titleClassName: 'fs20 text-bright-yellow',
          subTitleClassName: 'fs14 text-99 mr-t5'
        }
      ];

      if (fields !== fieldArray) {
        fieldArray = fields;
      }
      return (
        <Box bgcolor="white" height="100%">
          <InfoStrip data={infoStripData}>
            <div className="display-flex align-items-center">
              <LkToolTip
                placement="bottom"
                title={<ShortCut name="Ctrl+I" />}
                open={isShowShortCuts}
              >
                <Button
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="outlined"
                  disabled={status === 'Amend'}
                  onClick={() => setUploadCsvModal(true)}
                  startIcon={
                    <img
                      src={`${import.meta.env.VITE_STATIC_SERVER}/images/import.svg`}
                      alt="Import"
                    />
                  }
                >
                  {PO.IMPORT_CSV}
                </Button>
              </LkToolTip>
              <LkToolTip
                placement="bottom"
                title={<ShortCut name="Alt+N" />}
                open={isShowShortCuts}
              >
                <Button
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, width: 115, height: 36 }}
                  variant="outlined"
                  startIcon={<AddCircle />}
                  onClick={addPid}
                >
                  {PO.ADD_PID}
                </Button>
              </LkToolTip>
              <Button
                color="primary"
                style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                variant="outlined"
                onClick={handleCancel}
              >
                {PO.CANCEL}
              </Button>
              <Button
                color="primary"
                style={{ borderRadius: 8, height: 36 }}
                type="submit"
                variant="contained"
              >
                {PO.DONE}
              </Button>
              {/* <Shortcuts data={shortcuts} /> */}
            </div>
          </InfoStrip>

          <Box p={3} pt={2}>
            <div className="display-flex justify-content-space-between">
              <SubtitleInfoStrip data={subtitleInfoStripData} />
              <LkToolTip title={priceTooltip(convertedCurrency)} width="400">
                <SubtitleInfoStrip data={priceStripData} />
              </LkToolTip>
            </div>
            <Box mt={2}>
              <CreatePOTable
                handleRemovePid={(i) => handleRemovePid(fields, i)}
                purchaseOrderDetail={fields}
                purchaseOrderDetailProccessing={false}
                Field={Field}
                currency={convertedCurrency}
                formValues={formValues}
                handlePidChange={(e, i) => handlePidChange(e, i, fields)}
                handleQtyChange={handleQtyChange}
                errorPid={errorPid}
              />
              <Box m="auto" width="fit-content" mt={3}>
                <Button
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="outlined"
                  startIcon={<AddCircle />}
                  onClick={addPid}
                >
                  {PO.ADD_PID}
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      );
    },
    [errorPid, isShowShortCuts]
  );
  const handleRender = useCallback(
    ({ handleSubmit: onSubmit, form, values, valid }) => {
      if (values !== formValues) {
        formValues = values;
      }
      if (form !== formObj) {
        formObj = form;
      }
      return (
        <form id="myForm" onSubmit={onSubmit}>
          <FieldArray initialValue={values.items} name="items">
            {handleFieldRender}
          </FieldArray>
          <UploadCsv
            open={uploadCsvModal}
            onClose={closeCsvModal}
            selectFiles={selectFiles}
            sampleCSV={sampleCSV}
            uploadProgress={uploadProgress}
            checkedItemLabel={PO.OVERWRITE_EXISTING_ROWS_IN_PO}
            failDataMessage={uploadItemsFailData?.data?.file}
            downloadErrorReport={downloadErrorReport}
            uploadCsvPass={uploadItemsData}
            handleSubmit={() => handleFileSubmit(uploadItemsData)}
            resetState={resetState}
            // errorFileUrl={`${config.purchaseOrder.uploadItem}?file=
            // ${uploadItemsFailData?.data?.file}`}
            sampleFileName="Sample_Po_Item_Upload"
          />
          <LkModal
            open={!!(submitPoData?.data && !processingStatus?.data)}
            title={`${PO.PO} ${poNum}`}
            subTitle={PO.PO_PROCESSING_STATE_CHECK_STATUS}
            primaryBtn={() => dispatch(processingStatusLoad(submitPoData?.data))}
            primaryBtnText={PO.CHECK_STATUS}
            showClose={false}
          />
          <POModal
            open={openDetailModal}
            handleClose={() => setOpenDetailModal(false)}
            infoTableData={infoTableData}
            subtitleInfoStripData={subtitleInfoStripData}
            Field={Field}
            addressBoxData={addressBoxData}
            values={values}
            valid={valid}
          />
          <UploadCsvError
            message={PO.UPLOAD_FAILED}
            open={openErrorCSV}
            handleClose={handleCSVErrorClose}
            downloadReport={() => fileDownload(openErrorCSV, 'PO_CSV_FAIL')}
          />
        </form>
      );
    },
    [
      initialValue,
      uploadCsvModal,
      uploadItemsFailData,
      uploadItemsData,
      csvErrorSuccess,
      processingStatus,
      submitPoData,
      errorPid,
      openDetailModal,
      openErrorCSV,
      isShowShortCuts
    ]
  );
  return (
    <Form
      initialValues={initialValue}
      mutators={mutatorObj}
      onSubmit={handleSubmit}
      render={handleRender}
    />
  );
};

export default React.memo(CreatePO);
