import React, { useState, useEffect, useMemo, useCallback } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import { makeStyles } from 'tss-react/mui';
import { Box, Button } from '@mui/material';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import { DateRangePicker } from 'react-date-range';
import './dateSelectFilter.scss';
import { convertDateFormat, getDateDifference, mergeContentValues } from 'utils/helpers';
import dayjs from 'dayjs';
import { useDispatch, useSelector } from 'react-redux';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import FilterWrapper from './FilterWrapper';
import LkTimePicker from './TimePicker';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  container: {
    paddingTop: '20px'
  },
  listItem: {
    padding: 6,
    margin: 0,
    paddingLeft: '15px'
  },
  listItemIcon: {
    minWidth: '30px'
  },
  submitBtn: {
    borderRadius: 8,
    padding: 4
  },
  active: {
    background: '#F5F5F5'
  },
  listBorder: {
    borderBottom: '1px solid #F2F2F2',
    borderRight: '1px solid #F2F2F2',
    marginTop: '-15px'
  },
  dateBorder: {
    position: 'relative',
    borderBottom: '1px solid #F2F2F2',
    '& .rdrCalendarWrapper': {
      height: '310px !important'
    },
    '& .rdrMonthPicker': {
      marginTop: '-50px'
    },
    '& .rdrYearPicker': {
      marginTop: '-50px'
    }
  },
  formatdiff: {
    margin: '0 5px'
  }
}));

const DATE_RANGE_OPTIONS = [
  { key: 'All Period', value: 'ALL PERIOD' },
  { key: 'Today', value: 'TODAY' },
  { key: 'Last 7 Days', value: 'LAST 7 DAYS' },
  { key: 'Last 30 Days', value: 'LAST 30 DAYS' },
  { key: 'Custom Range', value: 'Custom Range' }
];

const initialDate = [
  {
    startDate: new Date(),
    endDate: new Date(),
    key: 'selection',
    color: '#00BAC6'
  }
];

const minimumDate = new Date(0);
const maximunDate = new Date();

const DateSelectFilter = ({
  onSubmit,
  marginLeft,
  selectedFilterList,
  columnName,
  monitorPhasefilter,
  showTimePicker = false,
  maxDaysAllowed = 0,
  dateOptions = DATE_RANGE_OPTIONS,
  minDate = minimumDate
}) => {
  const { classes, cx } = useStyles();
  const [dateType, setDateType] = useState(0);
  const [open, setOpen] = useState(false);
  const [customRange, setCustomRange] = useState(initialDate);
  const [timeValues, setTimeValues] = useState({
    startTime: dayjs(`${convertDateFormat(new Date(), 'yyyy-mm-dd')} 00:00:00`),
    endTime: dayjs(`${convertDateFormat(new Date(), 'yyyy-mm-dd')} 23:59:59`)
  });
  const dispatch = useDispatch();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.COMMON_COMPONENTS);

  const setOpenFun = useCallback((value) => setOpen(value), [setOpen]);
  const onChange = (value) => () => {
    setDateType(value);
    if (value !== 'Custom Range') {
      setOpen(false);
      setCustomRange(initialDate);
      onSubmit([{ key: columnName, value }]);
    }
  };

  const getFormatedTime = (newTime) => dayjs.utc(newTime).local().format('HH:mm:ss');

  const onSubmitCustomDate = () => {
    let value = '';
    if (showTimePicker) {
      value = `${convertDateFormat(customRange[0].startDate, 'shortDate')} ${getFormatedTime(
        timeValues.startTime
      )} - ${convertDateFormat(customRange[0].endDate, 'shortDate')} ${getFormatedTime(
        timeValues.endTime
      )}`;
    } else {
      if (maxDaysAllowed) {
        const daysdifference =
          getDateDifference(customRange[0].endDate, customRange[0].startDate, 'day') + 1;
        if (daysdifference > maxDaysAllowed) {
          return dispatch(
            toastOpen({
              isToastOpen: true,
              heading: mergeContentValues(
                CONTENT.THE_SELECTED_DATE_RANGE_MUST_BE_LESS_THAN,
                maxDaysAllowed
              ),
              severity: 'error',
              autoHideDuration: 3000
            })
          );
        }
      }
      value = `${convertDateFormat(customRange[0].startDate, 'shortDate')} - ${convertDateFormat(
        customRange[0].endDate,
        'shortDate'
      )}`;
    }
    onSubmit([{ key: columnName, value }]);
    return setOpen(false);
  };
  const isCustomRange = useMemo(() => dateType === 'Custom Range', [dateType]);

  const updateFilterChanges = useCallback(() => {
    const temp = selectedFilterList.filter((t) => t.key === columnName);
    if (!temp.length) {
      setCustomRange(initialDate);
      setDateType(0);
    } else {
      // to update custom date from url
      const t = temp[0].value.split(' - ');
      if (t.length === 2) {
        setCustomRange([
          {
            key: 'selection',
            color: '#00BAC6',
            startDate: new Date(t[0]),
            endDate: new Date(t[1])
          }
        ]);
        setDateType('Custom Range');
      } else {
        setCustomRange(initialDate);
        setDateType(temp[0].value);
      }
    }
  }, [setDateType, selectedFilterList, setCustomRange, columnName]);

  useEffect(() => {
    updateFilterChanges();
  }, [selectedFilterList, updateFilterChanges]);

  const handleTimeChangeFun = (newTime, key) => {
    setTimeValues((prevValue) => ({
      ...prevValue,
      [key]: newTime
    }));
  };

  return (
    <FilterWrapper
      disabled={false}
      width={isCustomRange ? '600px' : '180px'}
      onSubmit={() => null}
      hideSubmit
      monitorPhasefilter={monitorPhasefilter}
      onCloseUpdateFilter={updateFilterChanges}
      open={open}
      marginLeft={marginLeft}
      setOpen={setOpenFun}
      dataCy={columnName}
    >
      <Box className={cx({ [classes.container]: isCustomRange })}>
        <Box display="flex">
          <Box
            width={isCustomRange ? '100px' : '180px'}
            className={isCustomRange ? classes.listBorder : ''}
          >
            <List>
              {dateOptions.map(({ key, value }) => {
                const labelId = `checkbox-list-label-${value}`;
                const active = value === dateType;
                return (
                  <ListItem
                    data-cy={`date-filter-${value}`}
                    key={value}
                    role={undefined}
                    className={cx({
                      [classes.listItem]: true,
                      [classes.active]: active
                    })}
                    button
                    onClick={onChange(value)}
                  >
                    <ListItemText id={labelId} primary={<div className="fs12">{key}</div>} />
                  </ListItem>
                );
              })}
            </List>
          </Box>
          {isCustomRange && (
            <Box>
              <Box width="500px" className={classes.dateBorder}>
                <DateRangePicker
                  ranges={customRange}
                  onChange={(item) => setCustomRange([item.selection])}
                  moveRangeOnFirstSelection={false}
                  months={2}
                  minDate={minDate}
                  maxDate={maximunDate}
                  direction="horizontal"
                  showDateDisplay={false}
                  rangeColors={['#F2F2F2']}
                />
              </Box>
              {showTimePicker && (
                <Box className="display-flex justify-content-space-around">
                  <LkTimePicker
                    label="Start Time"
                    value={timeValues.startTime}
                    name="startTime"
                    handleChange={handleTimeChangeFun}
                  />
                  <LkTimePicker
                    label="End Time"
                    value={timeValues.endTime}
                    name="endTime"
                    handleChange={handleTimeChangeFun}
                  />
                </Box>
              )}
            </Box>
          )}
        </Box>
        {isCustomRange && (
          <Box display="flex" justifyContent="flex-end" p={2} pr={4}>
            <Box className="fs14 text-66" pt={1} pr={2}>
              {convertDateFormat(customRange[0].startDate, 'date')}{' '}
              {getFormatedTime(timeValues.startTime).slice(0, -3)}
              <span className={classes.formatdiff}> - </span>
              {convertDateFormat(customRange[0].endDate, 'date')}{' '}
              {getFormatedTime(timeValues.endTime).slice(0, -3)}
            </Box>
            <Box mr={1.5}>
              <Button
                color="primary"
                size="small"
                variant="outlined"
                onClick={() => {
                  setDateType(0);
                  setOpenFun(false);
                }}
              >
                {' '}
                Cancel{' '}
              </Button>
            </Box>
            <Button
              color="primary"
              size="small"
              variant="contained"
              className="test"
              onClick={onSubmitCustomDate}
            >
              {' '}
              Apply{' '}
            </Button>
          </Box>
        )}
      </Box>
    </FilterWrapper>
  );
};

export default DateSelectFilter;
