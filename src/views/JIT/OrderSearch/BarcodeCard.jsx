import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import { makeStyles } from 'tss-react/mui';
import CloseIcon from '@mui/icons-material/Close';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

import LkCard from 'components/MaterialUi/LkCard';
import LkInput from 'components/MaterialUi/LkInput';
import Spinner from 'components/Spinner/Spinner';
import localStorageHelper from 'utils/localStorageHelper';
import { getJitTrayListLoad, getJitTrayListReset } from 'redux/reducers/jitOrderSearch';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({
  cardHeader: {
    textAlign: 'center',
    padding: 0
  },
  cardHeaderContent: {
    fontWeight: 700,
    fontSize: 22
  },
  cardContent: {
    width: 365,
    marginInline: 'auto',
    display: 'flex',
    gap: 5,
    paddingInline: 0
  },
  iconContainer: {
    display: 'flex',
    alignItems: 'center'
  },
  input: {
    '& input::placeholder': {
      fontSize: 13
    }
  },
  button: {
    width: 40
  }
}));

const BarcodeCard = ({ jitTrayScanLoading, jitOrderStatusDetailsList }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { barcode = '' } = useParams();
  const [inputValue, setInputValue] = useState('');
  const facilityCode = localStorageHelper.getItem('facility-code');
  const getJitTrayListLoadFn = (barcodeVal) => {
    navigate(`/autojit/ordersearch/${barcodeVal}`);
  };

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.JIT_ORDER_SEARCH);

  useEffect(() => () => dispatch(getJitTrayListReset()), []);

  useEffect(() => {
    if (barcode && String(jitOrderStatusDetailsList?.[0]?.fittingId) !== barcode) {
      dispatch(getJitTrayListLoad({ barcode, facilityCode }));
    }
    setInputValue(barcode);
  }, [barcode]);

  const resetJitTrayList = () => {
    setInputValue('');
    navigate('/autojit/ordersearch');
    dispatch(getJitTrayListReset());
    document.getElementById('barcode-input').focus();
  };

  const renderInputAdornment = () => (
    <InputAdornment position="end">
      {jitTrayScanLoading && <Spinner />}
      {jitOrderStatusDetailsList && (
        <Box className={classes.iconContainer}>
          <IconButton data-cy="close-icon" onClick={resetJitTrayList}>
            <CloseIcon />
          </IconButton>
          <CheckCircleIcon color="primary" data-cy="spinner" />
        </Box>
      )}
    </InputAdornment>
  );
  return (
    <LkCard
      width={500}
      elevation={5}
      marginX="auto"
      marginTop={10}
      marginBottom={13}
      paddingX={4.25}
      paddingY={5}
    >
      <CardHeader
        className={classes.cardHeader}
        data-cy="barcode-card-header"
        title={
          <Typography variant="h1" className={classes.cardHeaderContent}>
            {CONTENT.SCAN_FITTING_ID_TRAY_ID}
          </Typography>
        }
      />
      <CardContent data-cy="barcode-card-body" className={classes.cardContent}>
        <LkInput
          id="barcode-input"
          data-cy="barcode-input"
          className={classes.input}
          autoFocus
          placeholder={CONTENT.SCAN_FITTING_ID_TRAY_ID}
          fullWidth
          value={inputValue}
          onKeyDown={(event) => {
            if (event.key === 'Enter') {
              getJitTrayListLoadFn(event.target.value.trim());
            }
          }}
          onChange={(e) => setInputValue(e.target.value)}
          InputProps={{
            endAdornment: renderInputAdornment()
          }}
        />
        <Button
          color="primary"
          data-cy="barcode-submit"
          variant="contained"
          disabled={inputValue.trim().length === 0 || jitTrayScanLoading}
          className={classes.button}
          onClick={() => getJitTrayListLoadFn(inputValue.trim())}
        >
          <ArrowForwardIcon />
        </Button>
      </CardContent>
    </LkCard>
  );
};

export default BarcodeCard;
