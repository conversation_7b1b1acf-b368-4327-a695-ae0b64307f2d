import { Box, Button, Typography } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import LkInput from 'components/MaterialUi/LkInput';
import LkModal from 'components/Modal/Modal';
import Spinner from 'components/Spinner/Spinner';
import React from 'react';

const useStyles = makeStyles()(() => ({
  modalContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  },
  modalTitle: {
    fontWeight: 700,
    marginBottom: 16,
    marginTop: -25
  },
  modalSubHeading: {
    fontSize: 16,
    color: '#3c3c3c',
    marginBottom: 24,
    textAlign: 'center',
    width: 239
  },
  buttonContainer: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-around'
  },
  button: {
    width: 130
  },
  stockOutTitle: {
    marginTop: -15,
    fontWeight: 700
  },
  input: {
    width: 310,
    marginTop: '32px',
    boxShadow: '0px 8px 16px rgba(0, 0, 0, 0.05)'
  }
}));

const OrdeDetailsModal = ({
  markOpen,
  markclose,
  barcode,
  btnClick,
  stockOutOpen,
  stockOutClose,
  productId,
  stockOut,
  inputRef,
  handleKeyDown,
  onClick
}) => {
  const { classes } = useStyles();

  const renderInputIcon = () => {
    if (stockOut.isLoading) {
      return <Spinner />;
    }
    if (stockOut.status) {
      return (
        <img
          className="success-icon"
          src={`${import.meta.env.VITE_STATIC_SERVER}/images/tick-green.svg`}
          alt="success icon"
        />
      );
    }
    return (
      <img
        className="mr-r8"
        src={`${import.meta.env.VITE_STATIC_SERVER}/images/qrCodeScan.svg`}
        alt="img"
      />
    );
  };

  return (
    <>
      <LkModal
        disableBackdrop
        modalHeight="221px"
        modalWidth="394px"
        open={markOpen}
        handleClose={markclose}
      >
        <Box className={classes.modalContainer}>
          <Typography variant="h3" className={classes.modalTitle}>
            Are you sure?
          </Typography>
          <Typography variant="body1" className={classes.modalSubHeading}>
            You want to mark barcode (<b>{barcode}</b>) as damaged.
          </Typography>
          <Box component="div" className={classes.buttonContainer}>
            <Button
              onClick={markclose}
              className={classes.button}
              variant="outlined"
              color="primary"
            >
              CANCEL
            </Button>
            <Button
              onClick={btnClick}
              className={classes.button}
              variant="contained"
              color="primary"
            >
              YES
            </Button>
          </Box>
        </Box>
      </LkModal>

      <LkModal
        disableBackdrop
        modalHeight="201px"
        modalWidth="467px"
        open={stockOutOpen}
        handleClose={stockOutClose}
      >
        <Box className={classes.modalContainer}>
          <Typography variant="h3" className={classes.stockOutTitle} onClick={onClick}>
            Scan barcode of PID ({productId})
          </Typography>
          <LkInput
            variant="outlined"
            size="medium"
            autoFocus
            label="Scan Item Barcode"
            className={classes.input}
            InputProps={{
              endAdornment: renderInputIcon()
            }}
            placeholder="Scan Item Barcode"
            inputRef={inputRef}
            onKeyDown={handleKeyDown}
          />
        </Box>
      </LkModal>
    </>
  );
};

export default OrdeDetailsModal;
