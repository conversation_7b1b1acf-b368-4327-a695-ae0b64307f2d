
// Manifest List

export const GET_MANIFEST_LIST_LOAD = 'GET_MANIFEST_LIST_LOAD';
export const GET_MANIFEST_LIST_SUCCESS = 'GET_MANIFEST_LIST_SUCCESS';
export const GET_MANIFEST_LIST_FAIL = 'GET_MANIFEST_LIST_FAIL';

// Manifest REMOVE

export const REMOVE_MANIFEST_LOAD = 'REMOVE_MANIFEST_LOAD';
export const REMOVE_MANIFEST_SUCCESS = 'REMOVE_MANIFEST_SUCCESS';
export const REMOVE_MANIFEST_FAIL = 'REMOVE_MANIFEST_FAIL';
export const REMOVE_MANIFEST_RESET = 'REMOVE_MANIFEST_RESET';

// Fetch Manifest channel (Order Type) list

export const FETCH_CHANNEL_LOAD = 'FETCH_CHANNEL_LOAD';
export const FETCH_CHANNEL_SUCCESS = 'FETCH_CHANNEL_SUCCESS';
export const FETCH_CHANNEL_FAIL = 'FETCH_CHANNEL_FAIL';


// Get Manifest Details
export const MANIFEST_DETAILS_LOAD = 'MANIFEST_DETAILS_LOAD';
export const MANIFEST_DETAILS_SUCCESS = 'MANIFEST_DETAILS_SUCCESS';
export const MANIFEST_DETAILS_FAIL = 'MANIFEST_DETAILS_FAIL';

// Delete Shipment From Manifest
export const MANIFEST_DELETE_SHIPPING_REQUEST = 'MANIFEST_DELETE_SHIPPING_REQUEST';
export const MANIFEST_DELETE_SHIPPING_SUCCESS = 'MANIFEST_DELETE_SHIPPING_SUCCESS';
export const MANIFEST_DELETE_SHIPPING_FAIL = 'MANIFEST_DELETE_SHIPPING_FAIL';

// Search AWB
export const SEARCH_AWB_LOAD = 'SEARCH_AWB_LOAD';
export const SEARCH_AWB_SUCCESS = 'SEARCH_AWB_SUCCESS';
export const SEARCH_AWB_FAIL = 'SEARCH_AWB_FAIL';

// Close Manifest
export const CLOSE_MANIFEST_LOAD = 'CLOSE_MANIFEST_LOAD';
export const CLOSE_MANIFEST_SUCCESS = 'CLOSE_MANIFEST_SUCCESS';
export const CLOSE_MANIFEST_FAIL = 'CLOSE_MANIFEST_FAIL';
export const CLOSE_MANIFEST_RESET = 'CLOSE_MANIFEST_RESET';


// Get Shipping Provider List

export const GET_PROVIDER_LOAD = 'GET_PROVIDER_LOAD';
export const GET_PROVIDER_SUCCESS = 'GET_PROVIDER_SUCCESS';
export const GET_PROVIDER_FAIL = 'GET_PROVIDER_FAIL';

// Save Manifest
export const SAVE_MANIFEST_LOAD = 'SAVE_MANIFEST_LOAD';
export const SAVE_MANIFEST_SUCCESS = 'SAVE_MANIFEST_SUCCESS';
export const SAVE_MANIFEST_FAIL = 'SAVE_MANIFEST_FAIL';


// Print Manifest
export const PRINT_MANIFEST_LOAD = 'PRINT_MANIFEST_LOAD';
export const PRINT_MANIFEST_SUCCESS = 'PRINT_MANIFEST_SUCCESS';
export const PRINT_MANIFEST_FAIL = 'PRINT_MANIFEST_FAIL';
export const PRINT_MANIFEST_RESET = 'PRINT_MANIFEST_RESET';


// retry Manifest
export const RETRY_MANIFEST_LOAD = 'RETRY_MANIFEST_LOAD';
export const RETRY_MANIFEST_SUCCESS = 'RETRY_MANIFEST_SUCCESS';
export const RETRY_MANIFEST_FAIL = 'RETRY_MANIFEST_FAIL';
export const RETRY_MANIFEST_RESET = 'RETRY_MANIFEST_RESET';

// Print SG Invoices
export const PRINT_SG_INVOICES_LOAD = 'PRINT_SG_INVOICES_LOAD';