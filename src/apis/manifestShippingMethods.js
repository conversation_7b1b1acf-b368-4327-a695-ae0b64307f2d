import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const getManifestShippingMethodsApi = ({ payload }) =>
  tokenInstance().get(config.manifestShippingMethod.getManifestShippingMethodsList, {
    params: { ...payload }
  });

export const uploadManifestShippingMethodsApi = (payload) =>
  tokenInstance().post(config.manifestShippingMethod.uploadManifestShippingMethodFile, payload);

export const trackManifestShippingIdsUploadStatusApi = (payload) =>
  tokenInstance().get(
    `${config.manifestShippingMethod.trackManifestShippingIdsUploadStatus}/${payload}`
  );