/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import React, { forwardRef } from 'react';
import { NavLink as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { List, ListItem, Button, colors } from '@mui/material';

const useStyles = makeStyles()((theme, _props, classes) => ({
  root: {},
  rootIcon: {
    display: 'inline-block'
  },
  rootText: {
    display: (props) => (props.open ? 'inline-block' : 'none'),
    width: 'calc(100% - 72px)'
  },
  item: {
    display: 'flex',
    paddingTop: 0,
    paddingBottom: 0
  },
  iconButton: {
    color: colors.blueGrey[800],
    paddingTop: '10px',
    paddingBottom: '10px',
    // paddingLeft: '10px',
    // justifyContent: 'flex-start',
    // textTransform: 'none',
    // letterSpacing: 0,
    width: theme.spacing(2)
    // fontWeight: theme.typography.fontWeightMedium
  },
  textButton: {
    paddingTop: '10px',
    paddingBottom: '10px',
    justifyContent: 'flex-start',
    textTransform: 'none',
    letterSpacing: 0,
    width: '100%',
    fontWeight: theme.typography.fontWeightMedium
  },
  icon: {
    color: theme.palette.icon,
    width: 24,
    height: 24,
    display: 'flex',
    alignItems: 'center'
    // marginRight: theme.spacing(1)
  },
  active: {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
    [`& .${classes.icon}`]: {
      color: theme.palette.primary.main
    }
  }
}));

const CustomRouterLink = forwardRef((props, ref) => (
  <div ref={ref} style={{ flexGrow: 1 }}>
    <RouterLink {...props} />
  </div>
));

const SidebarNav = (props) => {
  const { pages, className, open, ...rest } = props;

  const { classes, cx } = useStyles(props);

  return (
    <div>
      <List {...rest} className={cx(classes.root, className, classes.rootIcon)}>
        {pages.map((page) => (
          <ListItem className={classes.item} disableGutters key={page.title}>
            <Button
              activeClassName={classes.active}
              className={classes.iconButton}
              component={CustomRouterLink}
              to={page.href}
            >
              {/* <div className={classes.icon}>{page.icon}</div> */}
              {page.icon}
            </Button>
          </ListItem>
        ))}
      </List>
      <List {...rest} className={cx(classes.root, className, classes.rootText)}>
        {pages.map((page) => (
          <ListItem className={classes.item} disableGutters key={page.title}>
            <Button
              activeClassName={classes.active}
              className={classes.textButton}
              component={CustomRouterLink}
              to={page.href}
            >
              {/* <div className={classes.icon}>{page.icon}</div> */}
              {page.title}
            </Button>
          </ListItem>
        ))}
      </List>
    </div>
  );
};

SidebarNav.propTypes = {
  className: PropTypes.string,
  pages: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      href: PropTypes.string.isRequired,
      icon: PropTypes.elementType.isRequired
    })
  ).isRequired
};

export default SidebarNav;
