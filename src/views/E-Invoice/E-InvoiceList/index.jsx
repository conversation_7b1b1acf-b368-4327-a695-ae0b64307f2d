import React, { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import useFilterHook from 'components/common/useFilterHook';
import { useDispatch, useSelector } from 'react-redux';
import {
  eInvoiceListDataRequest,
  exportEinvoiceDataRequest,
  printEinvoiceRequest,
  retryEInvoiceRequest
} from 'redux/reducers/e-invoice';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';
import EInvoiceFilters from './EInvoiceFilters';
import EInvoiceTable from './EInvoiceTable';
import HeaderInfo from './HeaderInfo';

const defaultpayload = {
  limit: 35,
  pageNo: 0,
  status: ['Failure', 'Processing'],
  sortKey: 'createdAt',
  sortOrder: 'DESC',
  globalSearchField: ''
};

let isAPICalled = false;
const EInvoiceList = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter } = useFilterHook();
  const [selectedShipmentId, setselectedShipmentId] = useState([]);

  const [payload, setPayload] = useState(defaultpayload);
  const {
    eInvoicelist: { data, headerData, isLoading, totalCount },
    exportEInvoice: { isloading: isExportLoading }
  } = useSelector((state) => state.eInvoice);

  useEffect(() => {
    if (isAPICalled) {
      dispatch(eInvoiceListDataRequest(payload));
    }
  }, [dispatch, payload]);

  useEffect(
    () => () => {
      isAPICalled = false;
    },
    []
  );

  const onChangeSelectedShipmentId = useCallback((newShipmenst) => {
    setselectedShipmentId(newShipmenst);
  }, []);

  const retryFun = useCallback(
    (retryPayload = selectedShipmentId) => {
      dispatch(retryEInvoiceRequest({ selectedShipmentId: retryPayload, payload }));
    },
    [selectedShipmentId, payload]
  );

  const printFun = useCallback(
    (retryPayload = selectedShipmentId) => {
      dispatch(printEinvoiceRequest(retryPayload));
    },
    [selectedShipmentId, payload]
  );

  useEffect(() => {
    const result = getAllQueryParam(location.search);
    const filterPayload = generatePayloadForSearchAPI(result);
    let tempPayload = { ...payload, pageNo: 0 };
    if (filterPayload.CREATEDfrom) {
      tempPayload = {
        ...tempPayload,
        createdAtRange: {
          startDate: filterPayload.CREATEDfrom,
          endDate: filterPayload.CREATEDto
        }
      };
    } else {
      delete tempPayload.createdAtRange;
    }

    if (filterPayload.UPDATEDfrom) {
      tempPayload = {
        ...tempPayload,
        updatedAtRange: {
          startDate: filterPayload.UPDATEDfrom,
          endDate: filterPayload.UPDATEDto
        }
      };
    } else {
      delete tempPayload.updatedAtRange;
    }

    if (filterPayload.status) {
      const { status } = filterPayload;
      tempPayload = {
        ...tempPayload,
        status: Array.isArray(status) ? status : [status]
      };
    } else {
      tempPayload = {
        ...tempPayload,
        status: defaultpayload.status
      };
    }
    setPayload(tempPayload);
    isAPICalled = true;
  }, [location.search]);

  const onExportFun = useCallback(() => {
    dispatch(exportEinvoiceDataRequest(payload));
  }, []);

  return (
    <div data-cy="e-invoice-container" className=" pd-t10 height90vh ">
      <div className="border-grey5-radiusbase mr-r20 mr-l20  bg-white">
        <HeaderInfo data={{ ...headerData }} onExportFun={onExportFun} loading={isExportLoading} />
        <EInvoiceFilters
          selectedFilterList={selectedFilterList}
          deleteSelectedFilter={deleteSelectedFilter}
          value={payload.globalSearchField}
          onChangeFun={setPayload}
          generateFun={retryFun}
          selectedShipmentId={selectedShipmentId}
          printFun={printFun}
        />
        <EInvoiceTable
          data={data}
          selectedShipmentId={selectedShipmentId}
          setselectedShipmentId={onChangeSelectedShipmentId}
          isLoading={isLoading}
          totalCount={totalCount}
          payload={payload}
          setPayload={setPayload}
          selectedFilterList={selectedFilterList}
          onChangeFilterList={onChangeFilterList}
          generateFun={retryFun}
          printFun={printFun}
        />
      </div>
    </div>
  );
};

export default EInvoiceList;
