import React, { useMemo } from 'react';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';
import { useSelector } from 'react-redux';
import { getTotalOrdersListSelector } from 'selectors/monitorSelector';
import Box from '@mui/material/Box';

const MonitorBodyTotalOrder = ({ totalOrderExportFun }) => {
  const [tableHeight] = useWindowResize(window.innerHeight - 250);
  const { isLoading, data, headersList } = useSelector(getTotalOrdersListSelector);
  const { isLoading: exportDataLoading } = useSelector(
    (state) => state.monitor.monitorTagDetailsExportList
  );

  const renderCell = (column, rowData) => (
    <Box
      onClick={() =>
        !exportDataLoading &&
        rowData[column] &&
        totalOrderExportFun(column, rowData.pickingPriority, rowData[column])
      }
      className={`${rowData[column] && !exportDataLoading ? 'text-primary fw-bold' : 'text-cc'}`}
    >
      {rowData[column] ?? '-'}
    </Box>
  );

  const headerConfig = useMemo(() => {
    const dynamicHeaders = headersList.map((column) => ({
      name: column,
      key: column,
      formatBody: (rowData) => renderCell(column, rowData)
    }));

    return [{ name: 'Picking Priority', key: 'pickingPriority' }, ...dynamicHeaders];
  }, [headersList, exportDataLoading, totalOrderExportFun]);

  return (
    <Box p={2} bgcolor="#ffff">
      <LkTable
        headerConfig={headerConfig}
        isDataFetching={isLoading || exportDataLoading}
        tableData={data}
        totalRowsCount={data.length}
        tableHeight={tableHeight}
        showTableCount={false}
      />
    </Box>
  );
};
export default MonitorBodyTotalOrder;
