import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getConsolidationListApi = (payload) =>
  tokenInstance().post(config.storeConsolidation.list, payload);

export const getConsolidationHistoryListApi = (payload) =>
  tokenInstance().post(config.storeConsolidation.historyList, payload);

export const storeConsolidationTrayHistoryApi = (payload) =>
  tokenInstance().get(config.storeConsolidation.trayHistory, {
    params: payload
  });

const releaseTrayApi = ({ trayId, storeCode, parentId }) =>
  tokenInstance().put(config.storeConsolidation.releaseTray, { trayId, storeCode, parentId });

export const releaseTraysApi = (selectedTrays) =>
  Promise.allSettled(selectedTrays.map((trayData) => releaseTrayApi(trayData)));
