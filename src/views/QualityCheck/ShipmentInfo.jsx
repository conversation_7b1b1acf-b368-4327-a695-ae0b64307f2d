import React from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';

import WMInfoStrip from 'components/common/WMInfoStrip';
import { convertDateFormat, getDatesDifference } from 'utils/helpers';
import { LOCALISATION } from 'redux/reducers/localisation';

// Note: below keys should be the same keys in orderInfo file like country, source, incrementId
const QC_LABELS = {
  country: 'ORDER_ORIGIN',
  incrementId: 'ORDER_ID',
  shippingPackageId: 'SHIPMENT_ID',
  orderType: 'ORDER_TYPE',
  shipmentType: 'ORDER_TYPE',
  navChannel: 'CHANNEL',
  isFranchiseOrderShipToCustomer: 'ORDER_TYPE',
  // expectedDispatchDate: 'DISPATCH',
  highValue: 'HIGH_VALUE_ORDER',
  doNotPrintInvoice: 'DO_NOT_PRINT_INVOICE',
  expectedDeliveryDate: 'EXPECTED_DELIVERY_DATE'
};
const PP_LABELS = {
  country: 'ORDER_ORIGIN',
  source: 'ORDER_SOURCE',
  incrementId: 'ORDER_ID',
  shippingPackageId: 'SHIPMENT_ID',
  // expectedDispatchDate: 'DISPATCH',
  isFranchiseOrderShipToCustomer: 'ORDER_TYPE',
  lkFacilityCode: 'FACILITY_CODE',
  doNotPrintInvoice: 'DO_NOT_PRINT_INVOICE',
  expectedDeliveryDate: 'EXPECTED_DELIVERY_DATE'
};

const ORDER_PRIORITY = {
  1: 'Next Day Delivery',
  2: 'Next Day Delivery',
  10: 'Normal'
};

// ISO 3166-1 alpha-2
const COUNTRY_TO_FLAG = (isoCode) => {
  if (isoCode) {
    return (
      <img
        style={{ width: '20px' }}
        src={`${
          import.meta.env.VITE_STATIC_SERVER
          // eslint-disable-next-line react/destructuring-assignment
        }/media/desktop/nexs/flags/${isoCode.toLowerCase()}.svg`}
        alt="flag new"
      />
    );
  }
  return null;
};

const ShipmentInfo = () => {
  const infoData = [];
  let LABELS_TO_SHOW = [];

  const location = useLocation();

  const {
    orderBarcodeInfo = {},
    oldOrderBarcodeInfo = {},
    shipmentHeaders = {},
    orderPriority
  } = useSelector((state) => state.order);
  const localeData = useSelector((state) => state[LOCALISATION].localeData.QC);

  const orderBarcodeInfoToShow = orderBarcodeInfo?.country ? orderBarcodeInfo : oldOrderBarcodeInfo;

  if (location?.pathname?.includes('/qc')) {
    LABELS_TO_SHOW = QC_LABELS;
  } else if (location?.pathname?.includes('/packing')) {
    LABELS_TO_SHOW = PP_LABELS;
  }
  let dataItem;

  Object.keys(LABELS_TO_SHOW).forEach((label) => {
    const infoLabel = orderBarcodeInfoToShow[label] || shipmentHeaders[label];
    if (infoLabel) {
      dataItem = {
        key: localeData[LABELS_TO_SHOW[label]],
        value: infoLabel,
        main_key: LABELS_TO_SHOW[label]
      };

      if (label === 'country') {
        dataItem.flag = COUNTRY_TO_FLAG(orderBarcodeInfoToShow[label]);
      }

      if (label === 'highValue') {
        if (dataItem.value && dataItem.value.toLowerCase() === 'no') {
          dataItem.star = false;
        } else if (dataItem.value && dataItem.value.toLowerCase() === 'yes') {
          dataItem.star = true;
        }
      }

      if (label === 'lkFacilityCode') {
        const orderType = orderBarcodeInfoToShow?.isFranchiseOrderShipToCustomer;
        if (orderType && orderType.toLowerCase().split(' ').join('') === 'shiptocustomer') {
          dataItem.value = 'NA';
        }
        if (orderBarcodeInfo.lkFacilityCode) {
          dataItem.value = orderBarcodeInfo.lkFacilityCode;
        }
      }

      /** For order source image */
      if (label === 'source') {
        if (dataItem?.value?.toLowerCase()?.includes('flipkart')) {
          dataItem.value = (
            <img
              style={{ width: '80px' }}
              alt="order-source"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/flipkart.svg`}
            />
          );
        } else if (dataItem?.value?.toLowerCase()?.includes('amazon')) {
          dataItem.value = (
            <img
              style={{ width: '80px' }}
              alt="order-source"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/amazon.svg`}
            />
          );
        } else if (dataItem?.value?.toLowerCase()?.includes('nykaa')) {
          dataItem.value = (
            <img
              style={{ width: '80px' }}
              alt="order-source"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/nykaa.svg`}
            />
          );
        } else if (dataItem?.value?.toLowerCase()?.includes('myntra')) {
          dataItem.value = (
            <img
              style={{ width: '80px' }}
              alt="order-source"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/Myntra.png`}
            />
          );
        } else if (
          dataItem?.value?.toLowerCase()?.includes('jj') ||
          dataItem?.value?.toLowerCase()?.includes('john')
        ) {
          dataItem.value = (
            <img
              style={{ width: '50px' }}
              alt="order-source"
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/JJ.svg`}
            />
          );
        }
      }
      if (label === 'expectedDeliveryDate') {
        dataItem.value = convertDateFormat(infoLabel, 'abbDate');
      }
      infoData.push(dataItem);
    }
    if (label === 'doNotPrintInvoice' && orderBarcodeInfoToShow?.franchiseShipToCustomerNoInvoice) {
      dataItem = {
        key: localeData[LABELS_TO_SHOW[label]],
        value: localeData.FRANCHISE_ORDER,
        main_key: LABELS_TO_SHOW[label]
      };
      infoData.push(dataItem);
    }
  });

  if (location.pathname === '/qc' && shipmentHeaders.expiryDate) {
    const { expiryDate } = shipmentHeaders;
    const diffDate = getDatesDifference(new Date(expiryDate), new Date());

    infoData.push({
      key: 'Expiry Date',
      value: convertDateFormat(expiryDate, 'shortDate'),
      main_key: 'Expiry Date'
    });
    if (diffDate > 0) {
      infoData.push({
        key: 'Expires In',
        value: `${diffDate} days`,
        main_key: 'Expires In'
      });
    } else if (diffDate === 0) {
      infoData.push({
        key: 'Expires ',
        value: 'Today',
        main_key: 'Expires In'
      });
    } else {
      infoData.push({
        key: 'Expired ',
        value: <div className="text-red"> {Math.abs(diffDate)} days ago</div>,
        main_key: 'Expired from'
      });
    }
  }

  if (location.pathname === '/qc' && orderPriority) {
    infoData.push({
      key: 'Priority',
      value: ORDER_PRIORITY[orderPriority] ?? '-',
      main_key: 'Priority'
    });
  }

  return <WMInfoStrip data={infoData} />;
};

export default ShipmentInfo;
