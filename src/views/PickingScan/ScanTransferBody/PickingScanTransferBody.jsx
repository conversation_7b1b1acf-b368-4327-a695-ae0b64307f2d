import React, { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import { IconButton } from '@mui/material';
import SystemUpdateAltIcon from '@mui/icons-material/SystemUpdateAlt';

import ScannedItems from 'components/ProductsScanner/ScannedItems';
import ProductsScannerTextField from 'components/ProductsScanner/ProductsScannerTextField';

import { exportCsvLoad } from 'redux/reducers/pickingListReducer';
import { CONSUL, getConsulKeyValue } from 'redux/reducers/consul';
import {
  pickingScanBarcodeLoad,
  pickingScanToogleBoxwiseScanner
} from 'redux/reducers/pickingScan';
import { getPickingScannedBarcodesSelector } from 'selectors/pickingScan';
import PickingScanProductsTable from './PickingScanProductsTable';

const useStyles = makeStyles()(() => ({
  container: {
    margin: '16px 20px 20px 20px',
    backgroundColor: '#fff',
    height: 'calc(100vh - 185px)',
    padding: 24,
    boxSizing: 'border-box',
    display: 'flex',
    justifyContent: 'space-between'
  },
  scanContainer: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1.5,
    marginRight: 40,
    gap: 24
  }
}));

const PickingScanTransferBody = ({ CONFIG, productList, isLoading, pickingSummaryId }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const location = useLocation();

  const { scanApiEndPoint, hideEnableBoxOption, disableItemDelete } = CONFIG;
  const {
    inProgressBoxes,
    isLoading: barcodeScanLoading,
    savedScannedBarcodes,
    isBoxWiseTransfer
  } = useSelector((state) => state.pickingScan.barcodeScan);
  const loadingCsv = useSelector((state) => state.pickingList.exportCsv.isLoading);
  const featureToggle = useSelector((state) => state[CONSUL].featureToggle.data);
  const ScannedBarcodeDetails = useSelector(getPickingScannedBarcodesSelector);

  const [activeBoxBarcode, setActiveBoxBarcode] = useState('');
  const isAllowDuplicateScan = featureToggle?.pickingScanAllowDuplicates?.some((path) =>
    location.pathname?.startsWith(path)
  );

  useEffect(() => {
    dispatch(getConsulKeyValue(['featureToggle']));
  }, []);

  const scanBarcode = (barcode) => {
    const isBarcodeExist = savedScannedBarcodes.some(
      ({ barcode: itembarcode, status }) =>
        barcode === itembarcode.toUpperCase() && status !== 'failed'
    );

    if (!isBarcodeExist || isAllowDuplicateScan) {
      dispatch(
        pickingScanBarcodeLoad({
          barcode,
          box_barcode: activeBoxBarcode,
          api: scanApiEndPoint,
          pickingSummaryId,
          isAllowDuplicateScan
        })
      );
    }
  };

  const deleteBarcode = () => {
    // will be added in Future
  };

  const getBoxDetails = (boxBarcode) => {
    if (isBoxWiseTransfer) {
      dispatch(
        pickingScanBarcodeLoad({
          box_barcode: boxBarcode,
          enableBoxBarcode: isBoxWiseTransfer,
          isAllowDuplicateScan
        })
      );
    }
    setActiveBoxBarcode(boxBarcode);
  };

  const deleteBox = (boxBarcode) => {
    if (isBoxWiseTransfer && ScannedBarcodeDetails.boxes[boxBarcode]?.length) {
      // will be added in future
    }
    setActiveBoxBarcode('');
  };

  useEffect(() => {
    if (!inProgressBoxes.includes(activeBoxBarcode) && isBoxWiseTransfer) {
      setActiveBoxBarcode('');
    }
  }, [inProgressBoxes]);

  const disableInputFiled = useMemo(
    () =>
      !productList.some(({ pickedQuantity, totalQuantity }) => pickedQuantity !== totalQuantity),
    [productList]
  );

  return (
    <Box className={classes.container}>
      <Box className={classes.scanContainer} data-cy="scan-container">
        <ProductsScannerTextField
          activeBoxBarcode={activeBoxBarcode}
          disableInputField={disableInputFiled || barcodeScanLoading}
          setActiveBoxBarcode={getBoxDetails}
          savedScannedBarcodes={savedScannedBarcodes}
          ScannedBarcodeDetails={ScannedBarcodeDetails}
          scanBarcode={scanBarcode}
          setBoxWiseTransfer={() => dispatch(pickingScanToogleBoxwiseScanner())}
          isBoxWiseTransfer={isBoxWiseTransfer}
          enableBoxTransfers={
            !(ScannedBarcodeDetails.totalScanned - ScannedBarcodeDetails.failedItems.length)
          }
          scannedBoxes={Object.keys(ScannedBarcodeDetails.boxes)}
          hideEnableBoxOption={hideEnableBoxOption}
          isAllowDuplicateScan={isAllowDuplicateScan}
        />
        <ScannedItems
          useCompleWidth
          activeBoxBarcode={activeBoxBarcode}
          deleteActiveBox={deleteBox}
          deleteBarcode={deleteBarcode}
          ScannedBarcodeDetails={ScannedBarcodeDetails}
          inProgressBoxes={inProgressBoxes.concat(ScannedBarcodeDetails.pendingBoxes)}
          disableItemDelete={disableItemDelete ? true : isBoxWiseTransfer}
          disableBoxDelete={!isBoxWiseTransfer}
        />
      </Box>
      <Box width="50%" data-cy="products-table-container">
        <Box marginBottom="72px" className="display-flex justify-content-flex-end">
          <IconButton
            color="primary"
            className="mr-r10"
            onClick={() => dispatch(exportCsvLoad(pickingSummaryId))}
            disabled={loadingCsv[pickingSummaryId] ?? false}
            data-cy="export-csv-btn"
          >
            <SystemUpdateAltIcon />
          </IconButton>
        </Box>
        <PickingScanProductsTable tableData={productList} isLoading={isLoading} />
      </Box>
    </Box>
  );
};

export default PickingScanTransferBody;
