import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import LkInput from 'components/MaterialUi/LkInput';
import { MenuItem } from '@mui/material';
import TextField from '@mui/material/TextField';

import { toastOpen } from 'redux/actionCreators/toast';
import { supervisorPickingCategoriesRequest } from 'redux/actionCreators/supervisorPickingSkipped';
import { CONSUL, getConsulKeyValue } from 'redux/reducers/consul';
import Spinner from 'components/Spinner/Spinner';
import DateSelectFilter from 'components/common/DateSelectFilter';
import { generatePayloadForSearchAPI } from 'utils/helpers';
import { keys } from 'utils/lodash';
import SkippedList from '../SkippedList/SkippedList';

const useStyles = makeStyles()(() => ({
  container: {
    position: 'relative'
  },
  customTabRoot: {
    color: '#00B9C6'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  },
  divider: {
    borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
  },
  filters: {
    position: 'absolute',
    top: 5,
    right: 0
  },
  field: {
    width: 150,
    background: '#fff',
    marginLeft: 16
  }
}));

const rePickStatusList = [
  {
    key: 'QC_FAILED',
    value: 'QC Fail'
  },
  { key: 'ANY', value: 'ANY' }
];

const DATE_RANGE_OPTIONS = [
  { key: 'All Period', value: 'ALL PERIOD' },
  { key: 'Today', value: 'TODAY' },
  { key: 'Last 2 Days', value: 'LAST 2 DAYS' },
  { key: 'Custom Range', value: 'Custom Range' }
];

const TabPanel = ({ children, value, index }) => value === index && children;

const SupervisorPickingTabs = ({ CONTENT }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { innerTab } = useParams();
  const navigate = useNavigate();
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedRepickStatus, setSelectedRepickStatus] = useState(null);
  const [selectedFilterList, setSelectedFilterList] = useState([DATE_RANGE_OPTIONS[0]]);
  const [dateFilter, setDateFilter] = useState({
    startDate: '',
    endDate: ''
  });
  const [isDateSelected, setIsSelected] = useState(false);

  const { data: categories } = useSelector((state) => state.supervisorPickingSkipped.categories);
  const notification = useSelector((state) => state.supervisorPickingSkipped.notification);
  const { isLoading, data: featureToggle } = useSelector((state) => state[CONSUL].featureToggle);
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);

  const showDiscarded = featureToggle?.superVisorPickingSkipped?.[selectedFacility]?.showDiscarded;

  useEffect(() => {
    dispatch(supervisorPickingCategoriesRequest());
    dispatch(getConsulKeyValue(['featureToggle']));
  }, []);

  useEffect(() => {
    if (notification) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: notification.heading,
          subHeading: notification.subHeading,
          severity: notification.severity
        })
      );
    }
  }, [notification]);

  const handleChange = useCallback((_event, newValue) => {
    navigate(`/supervisor/picking/skipped/${newValue}`);
  }, []);

  const locationsList = useMemo(
    () => categories.map((eachCategory) => eachCategory.location),
    [categories]
  );
  const categoriesList = useMemo(() => {
    let result = [];
    categories.some((eachCategory) => {
      if (eachCategory.location === selectedLocation) {
        result = eachCategory.categories;
        return true;
      }
      result = [...result, ...eachCategory.categories];
      return false;
    });
    return result;
  }, [categories, selectedLocation]);

  const changeLocation = (e) => {
    setSelectedLocation(e.target.value);
    setSelectedCategory(null);
  };

  const onChangeFilterList = (data) => {
    const payload = generatePayloadForSearchAPI(data, {}, 'Date_Range');

    const selectedDate = {
      start: keys(payload).length !== 0 ? payload.Date_Rangefrom : '',
      end: keys(payload).length !== 0 ? payload.Date_Rangeto : ''
    };
    setDateFilter({
      startDate: selectedDate.start ? selectedDate.start : '',
      endDate: selectedDate.end ? selectedDate.end : ''
    });
    setSelectedFilterList(data);
    setIsSelected(true);
  };

  const renderFilters = useMemo(
    () => (
      <Box className={classes.filters}>
        <TextField
          type="text"
          size="small"
          label={CONTENT.SORT_BY_DATE}
          value={selectedFilterList[0].value}
          variant="outlined"
          InputProps={{
            readOnly: true,
            startAdornment: (
              <DateSelectFilter
                onSubmit={onChangeFilterList}
                columnName="Date_Range"
                selectedFilterList={selectedFilterList}
                monitorPhasefilter
                dateOptions={DATE_RANGE_OPTIONS}
                marginLeft="100px"
                maxDaysAllowed={2}
              />
            )
          }}
        />
        <LkInput
          select
          label={CONTENT.SELECT_LOCATION}
          className={classes.field}
          onChange={changeLocation}
          value={selectedLocation || ''}
        >
          {locationsList.map((location) => (
            <MenuItem key={location} value={location}>
              {location}
            </MenuItem>
          ))}
        </LkInput>
        <LkInput
          select
          label={CONTENT.SELECT_CATEGORY}
          className={classes.field}
          onChange={(e) => setSelectedCategory(e.target.value)}
          value={selectedCategory || ''}
          disabled={!selectedLocation}
        >
          {categoriesList.map((category) => (
            <MenuItem key={category} value={category}>
              {category}
            </MenuItem>
          ))}
        </LkInput>
        <LkInput
          select
          label={CONTENT.SELECT_RE_PICK_STATUS}
          className={classes.field}
          onChange={(e) => setSelectedRepickStatus(e.target.value)}
          value={selectedRepickStatus || ''}
          disabled={!selectedLocation}
        >
          {rePickStatusList.map(({ key, value }) => (
            <MenuItem key={key} value={key}>
              {value}
            </MenuItem>
          ))}
        </LkInput>
      </Box>
    ),
    [
      selectedLocation,
      selectedCategory,
      locationsList,
      categoriesList,
      selectedRepickStatus,
      selectedFilterList
    ]
  );

  const handleDateFilterValue = () => {
    setSelectedFilterList([DATE_RANGE_OPTIONS[0]]);
  };

  if (isLoading) {
    return <Spinner className="display-grid-center" />;
  }

  return (
    <Box className={classes.container}>
      <Box className={classes.divider}>
        <Tabs
          value={innerTab}
          onChange={handleChange}
          aria-label="skipped list tab"
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          <Tab value="skipped" label={CONTENT.SKIPPED} />
          {showDiscarded && <Tab value="discarded" label={CONTENT.DISCARD} />}
          <Tab value="hold" label={CONTENT.HOLD} disabled />
        </Tabs>
      </Box>
      <TabPanel value={innerTab} index="skipped">
        <SkippedList
          onHold={false}
          selectedLocation={selectedLocation}
          selectedCategory={selectedCategory}
          CONTENT={CONTENT}
          selectedRepickStatus={selectedRepickStatus}
          dateFilter={dateFilter}
          isDateSelected={isDateSelected}
          handleDateFilterValue={handleDateFilterValue}
        />
      </TabPanel>
      {showDiscarded && (
        <TabPanel value={innerTab} index="discarded">
          <SkippedList
            onHold={false}
            isDiscarded
            selectedLocation={selectedLocation}
            selectedCategory={selectedCategory}
            CONTENT={CONTENT}
            selectedRepickStatus={selectedRepickStatus}
            dateFilter={dateFilter}
            isDateSelected={isDateSelected}
            handleDateFilterValue={handleDateFilterValue}
          />
        </TabPanel>
      )}
      <TabPanel value={innerTab} index="hold">
        <SkippedList
          onHold
          selectedLocation={selectedLocation}
          selectedCategory={selectedCategory}
          CONTENT={CONTENT}
          selectedRepickStatus={selectedRepickStatus}
        />
      </TabPanel>
      {renderFilters}
    </Box>
  );
};

export default React.memo(SupervisorPickingTabs);
