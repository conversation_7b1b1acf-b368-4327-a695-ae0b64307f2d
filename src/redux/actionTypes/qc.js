// load qc hold reasons
export const QC_HOLD_REASONS_LOAD = 'QC_HOLD_REASONS_LOAD';
export const QC_HOLD_REASONS_SUCCESS = 'QC_HOLD_REASONS_SUCCESS';
export const QC_HOLD_REASONS_FAIL = 'QC_HOLD_REASONS_FAIL';

// create QC Data
export const CREATE_QC_DATA = 'CREATE_QC_DATA';

// update QC Data
export const UPDATE_QC_DATA = 'UPDATE_QC_DATA';

// mark QC Done
export const MARK_QC_DONE_LOAD = 'MARK_QC_DONE_LOAD';
export const MARK_QC_DONE_SUCCESS = 'MARK_QC_DONE_SUCCESS';
export const MARK_QC_DONE_FAIL = 'MARK_QC_DONE_FAIL';
export const MARK_QC_DONE_RESET = 'MARK_QC_DONE_RESET';

// Damage Order Id
export const MARK_PRODUCT_DAMAGED_REQUEST = 'MARK_PRODUCT_DAMAGED_REQUEST';
export const MARK_PRODUCT_DAMAGED_SUCCESS = 'MARK_PRODUCT_DAMAGED_SUCCESS';
export const MARK_PRODUCT_DAMAGED_FAIL = 'MARK_PRODUCT_DAMAGED_FAIL';
export const MARK_PRODUCT_DAMAGED_RESET = 'MARK_PRODUCT_DAMAGED_RESET';

// Fitting by station code
export const FITTING_BY_STATION_CODE_LOAD = 'FITTING_BY_STATION_CODE_LOAD';
export const FITTING_BY_STATION_CODE_SUCCESS = 'FITTING_BY_STATION_CODE_SUCCESS';
export const FITTING_BY_STATION_CODE_FAIL = 'FITTING_BY_STATION_CODE_FAIL';

// Mark Full FIllable for UnFF order
export const MARK_FULL_FILLABLE_LOAD = 'MARK_FULL_FILLABLE_LOAD';
