import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';

import { LOCALISATION } from 'redux/reducers/localisation';
import { markQcDoneReset, updateQCData, markQcDone } from 'redux/actionCreators/qc';
import { getOrderDetailsReset, setLensFocus } from 'redux/actionCreators/order';
import usePermissions from 'common/usePermissions';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOGIN } from 'redux/reducers/login';
import { verifyPartialOrders } from 'utils/helpers';
import { getConsulKeyValue } from 'redux/reducers/consul';
import ShipmentHeader from './ShipmentHeader';
import BarcodesContainer from './BarcodesContainer';
import ProductCardsContainer from './ProductCardsContainer';

const styles = makeStyles()(() => ({
  container: {
    backgroundColor: '#fff'
  },
  barcodesContainer: {
    padding: '24px 24px 15px 24px'
  },
  submitBtn: {
    borderRadius: 8,
    position: 'absolute',
    right: 0,
    marginRight: '20px'
  },
  caseImageContainer: {
    border: '4px solid #FAA61A',
    height: '480px',
    width: '600px',
    marginRight: '24px'
  },
  sharkTankImageContainer: {
    width: '600px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column'
  },
  caseImage: {
    height: '100%',
    width: '100%'
  },
  sharkTankImage: {
    borderRadius: '8px',
    width: '75%'
  },
  sharkTankTitle: {
    width: 'fit-content',
    marginBottom: '13px',
    fontSize: '20px',
    fontWeight: '700',
    color: 'red'
  }
}));

function unloadListener(e) {
  const confirmationMessage = 'Changes will be lost if you reload.';
  (e || window.event).returnValue = confirmationMessage;
  return confirmationMessage;
}

const QualityCheckProcess = () => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const QC = useSelector((state) => state[LOCALISATION].localeData.QC);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const { orderDetails, qccheck, isFr0Order, additionalImageRequired } = useSelector(
    (state) => state.order
  );
  const consul = useSelector((state) => state.consul.featureToggle.data);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);

  const { qcData } = useSelector((state) => state.qc);

  const { isQCSuperVisor } = usePermissions(userDetail?.permission);

  // State for expiry warning modal
  const [showExpiryWarning, setShowExpiryWarning] = useState(false);
  const [expiryWarningData, setExpiryWarningData] = useState({ daysToExpiry: 0 });

  const { clExpiryCheckFacilityList } = useSelector((state) => state.consul.featureToggle.data);

  const [shipmentHeaderHeight, setShipmentHeaderHeight] = useState(0);
  const [isScanningComplete, setIsScanningComplete] = useState(false);
  const [unicomOrderCode, setUnicomOrderCode] = useState(null);
  const [renderData, setRenderData] = useState([]);
  const [isHoldedOrder, setIsHoldedOrder] = useState(false);
  const [caseImage, setCaseImage] = useState('');

  useEffect(() => {
    /** Used to determine height of shipmentHeader to give paddingTop to content */
    if (document.getElementById('shipment_header') && !shipmentHeaderHeight) {
      setShipmentHeaderHeight(document.getElementById('shipment_header').offsetHeight);
    }
    /** End */

    window.addEventListener('beforeunload', unloadListener);
    return () => {
      dispatch(markQcDoneReset());
      // dispatch(getOrderDetailsReset());
      window.removeEventListener('beforeunload', unloadListener);
    };
  }, []);

  useEffect(() => {
    if (qcData.length && !isHoldedOrder) {
      const isQCHoldOrder = qcData.some(({ status }) => status === 'QC_HOLD');
      setIsHoldedOrder(isQCHoldOrder);
    }
  }, [qcData, isHoldedOrder]);

  useEffect(() => {
    if (isScanningComplete) {
      const validBarCodes = qcData.filter((x) => !x.isAccessories).length;
      if (
        qcData.filter((x) => !x.isAccessories && x.isScanningDone === true).length === validBarCodes
      ) {
        /** API request to MarkQCDone. Final call of submission. */
        dispatch(
          markQcDone({
            salesOrderId: unicomOrderCode,
            qcData
          })
        );
      }
      setIsScanningComplete(false);
    }
  }, [qcData]);

  useEffect(() => {
    if (qccheck === 10) {
      dispatch(getOrderDetailsReset());
      navigate('/qc');
    }
    if (orderDetails?.length) {
      const caseImageTemp = orderDetails.find(({ order }) => order.caseImage)?.order?.caseImage;
      if (caseImageTemp) {
        setCaseImage(caseImageTemp);
      }
    }
  }, [orderDetails]);

  useEffect(() => {
    dispatch(getConsulKeyValue(['featureToggle']));
  }, []);

  const showCaseImage = useMemo(
    () => caseImage && isFr0Order && consul.qcCaseImageFacilityList?.includes(facilityCode),
    [consul, facilityCode, isFr0Order, caseImage]
  );

  if (!orderDetails || !orderDetails.length) {
    navigate(-1);
    return null;
  }

  const handleSubmission = () => {
    if (isQCSuperVisor) {
      // Verify partial operation has performed
      const count = verifyPartialOrders(qcData);
      if (count > 1) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: 'Error',
            subHeading: QC.PARTIAL_OPERATIONS_CANT_BE_PERFORMED,
            severity: 'error',
            autoHideDuration: 7000
          })
        );
        return;
      }
    }

    // Check for CL product expiry before proceeding
    // const productData = shipment?.[0];
    // if (productData) {
    //   const expiryCheck = checkCLProductExpiry(
    //     productData,
    //     facilityCode,
    //     clExpiryCheckFacilityList
    //   );
    //   if (expiryCheck.isExpired) {
    //     setExpiryWarningData({ daysToExpiry: expiryCheck.daysToExpiry });
    //     setShowExpiryWarning(true);
    //     return;
    //   }
    // }

    qcData.forEach((data) => {
      const { uwItemId, lensType } = data;
      dispatch(setLensFocus(lensType));
      dispatch(
        updateQCData(
          {
            isScanningDone: true
          },
          uwItemId
        )
      );
    });
    setIsScanningComplete(true);
  };

  // const handleQCFail = () => {
  //   // Mark the current product as QC failed
  //   qcData.forEach((data) => {
  //     const { uwItemId } = data;
  //     dispatch(
  //       updateQCData(
  //         {
  //           isScanningDone: false,
  //           status: 'QCFailed'
  //         },
  //         uwItemId
  //       )
  //     );
  //   });

  //   dispatch(
  //     toastOpen({
  //       isToastOpen: true,
  //       heading: 'QC Failed',
  //       subHeading: 'Product marked as QC FAIL due to expiry. Please repick.',
  //       severity: 'warning',
  //       autoHideDuration: 7000
  //     })
  //   );
  // };

  return (
    <div className={classes.container}>
      <Box>
        <ShipmentHeader />
      </Box>
      <div style={{ paddingTop: shipmentHeaderHeight }}>
        <Box className={classes.barcodesContainer}>
          <BarcodesContainer
            setUnicomOrderCode={setUnicomOrderCode}
            setRenderData={setRenderData}
            renderData={renderData}
          />
        </Box>
        <Box className="display-flex">
          <ProductCardsContainer />
          {/* (Confirmed with Sreeram to add only condition for 
            caseImage if it is Shark Tank Order) */}
          {showCaseImage && (
            <Box className={classes.caseImageContainer}>
              <img src={caseImage} alt="product case" className={classes.caseImage} />
            </Box>
          )}
          {additionalImageRequired?.length > 0 && (
            <Box className={classes.sharkTankImageContainer}>
              <Box className={classes.sharkTankTitle}>
                {additionalImageRequired?.[0]?.sharkTankImageAlt ?? 'SHARK TANK'} ORDER
              </Box>
              <img
                className={classes.sharkTankImage}
                src={additionalImageRequired?.[0]?.sharkTankImage}
                alt={additionalImageRequired?.[0]?.sharkTankImageAlt}
              />
            </Box>
          )}
        </Box>

        {isHoldedOrder && (
          <Button
            onClick={handleSubmission}
            className={classes.submitBtn}
            variant="contained"
            color="primary"
          >
            {QC.SUBMIT}
          </Button>
        )}
      </div>
      {/* <ExpiryWarningModal
        open={showExpiryWarning}
        onClose={() => setShowExpiryWarning(false)}
        // onFail={handleQCFail}
        daysToExpiry={expiryWarningData.daysToExpiry}
      /> */}
    </div>
  );
};

export default QualityCheckProcess;
