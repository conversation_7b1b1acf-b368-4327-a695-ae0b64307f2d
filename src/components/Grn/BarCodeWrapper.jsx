import React from 'react';
import { Box, Typography } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import theme from 'theme';

const styles = makeStyles()((themeParam) => ({
  title: {
    color: '#FFFFFF',
    marginBottom: themeParam.spacing(0.6),
    fontWeight: 700
  },
  disableTitle: {
    color: '#CCCCCC'
  },

  root: {
    background: 'white',
    borderRadius: themeParam.spacing(0.5)
  }
}));

const BarCodeWrapper = ({ children, width, disable, title, padding = 3, ...rest }) => {
  const bgColor = disable ? theme.palette.disabled : theme.palette.primary.main;

  const { classes, cx } = styles();

  return (
    <Box
      style={{ backgroundColor: bgColor, flexBasis: `${width}%` }}
      borderRadius="8px"
      padding={padding}
      {...rest}
    >
      <Typography
        className={cx({
          [classes.title]: true,
          [classes.disableTitle]: disable
        })}
      >
        {title}
      </Typography>
      {children}
    </Box>
  );
};

export default BarCodeWrapper;
