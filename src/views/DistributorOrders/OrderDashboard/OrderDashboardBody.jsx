import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import CircleIcon from '@mui/icons-material/Circle';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';

import MoreItem from 'components/common/MoreItem';
import { splitDate } from 'views/Order/helper';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkModal from 'components/Modal/Modal';
import {
  DISTRIBUTOR_ORDERS,
  getCancelOrderLoad,
  getCancelOrderReset,
  getDoNumberDetailsLoad
} from 'redux/reducers/distributorOrders';
import { LOGIN } from 'redux/reducers/login';
import { LOCALISATION } from 'redux/reducers/localisation';
import { Link } from 'react-router-dom';
import useWindowResize from '../../../common/useWindowResize';

const useStyles = makeStyles()(() => ({
  statusContainer: {
    border: '1px solid #dddddd',
    borderRadius: '16px',
    display: 'flex',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    padding: '3px 2px'
  },
  statusActions: {
    display: 'flex',
    justifyContent: 'flex-start',
    padding: '14px 15px',
    fontWeight: '700',
    fontSize: '14px',
    letterSpacing: '0.2px',
    cursor: 'pointer'
  },
  cancelContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    margin: '0px',
    color: '#666'
  },
  modalHeading: {
    fontSize: '28px',
    fontWeight: 700,
    color: '#e31a0d'
  },
  subHeading: {
    fontSize: '14px',
    padding: '14px 0'
  },
  modalSoNumber: {
    fontSize: '14px',
    fontWeight: '500'
  },
  modalSoNumberContainer: {
    fontSize: '12px',
    border: '1px solid #ddd',
    padding: '10px 6px',
    borderRadius: '8px',
    display: 'flex',
    justifyContent: 'space-evenly',
    marginBottom: '14px'
  },
  actionButtons: {
    marginTop: '39px',
    display: 'flex',
    justifyContent: 'space-evenly'
  },
  goBackBtn: {
    width: '150px',
    padding: '5px 22px'
  },
  cancelButton: {
    width: '150px',
    padding: '5px 22px'
  }
}));

const initialSortBy = { id: 'createdAt', order: 'DESC' };

const OrderDashboardBody = ({
  pageNumber,
  totalCount,
  tableData,
  fetchMoreData,
  isLoading,
  selectedFilterList,
  onChangeFilterList,
  setRequestBody,
  defaultPayload
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 230);
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const [rowData, setRowData] = useState({});

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.DO_ORDER_MANAGEMENT);
  const { autoSuggestionListpoNumber, autoSuggestionListincrementId } = useSelector(
    (state) => state.filters
  );
  const { isSuccess: isCancelSuccess, isLoading: isCancelLoading } = useSelector(
    (state) => state[DISTRIBUTOR_ORDERS].cancelData
  );
  const { data: doNumbersListData } = useSelector(
    (state) => state[DISTRIBUTOR_ORDERS].doNumberDetails
  );
  const empCode = useSelector((state) => state[LOGIN].user.userDetail?.empCode);

  const uniquePoNumbers = autoSuggestionListpoNumber
    ?.map((item) => item.poNumber)
    .filter((value, index, self) => self.indexOf(value) === index);

  const suggetionS0NumbersData = autoSuggestionListincrementId
    ?.map((item) => item?.incrementId)
    .filter((item) => item);

  const statusIcon = (status) => {
    const lowerStatus = status.toLowerCase();
    const statusMap = {
      pending: 'warning',
      rejected: 'error',
      cancel: 'error',
      approved: 'success'
    };

    const foundKey = Object.keys(statusMap).find((key) => lowerStatus.includes(key));
    return foundKey ? statusMap[foundKey] : 'warning';
  };

  const toggleModal = useCallback(() => setOpenCancelModal((previousData) => !previousData), []);

  const getOrderDetailsData = (incrementId) => {
    if (incrementId && !doNumbersListData[incrementId]) {
      dispatch(getDoNumberDetailsLoad({ userName: empCode, incrementId }));
    }
  };

  const cancelOrder = (data) => {
    setRowData(data);
    getOrderDetailsData(data?.incrementId);
    toggleModal();
  };

  useEffect(() => {
    if (isCancelSuccess && !openCancelModal) {
      setRequestBody(defaultPayload);
      dispatch(getCancelOrderReset());
    }
  }, [isCancelSuccess, openCancelModal]);

  const actionOptions = (rowDetails) => (
    <Box
      data-cy="cancel-order-button"
      onClick={() => cancelOrder(rowDetails)}
      className={classes.statusActions}
    >
      {CONTENT.CANCEL_ORDER_SMALL}
    </Box>
  );

  const onSubmitFun = () => {
    const cancelPayload = {
      userName: empCode,
      id: rowData?.id,
      status: 'CANCELLED',
      cancellationReason: ''
    };
    dispatch(getCancelOrderLoad(cancelPayload));
  };
  const headerConfig = [
    {
      name: CONTENT.DO_NUMBER,
      key: 'incrementId',
      columnName: 'incrementId',
      align: 'center',
      enableExpander: true,
      supportFilter: true,
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' },
      filterData: {
        type: 'autoSelect',
        listData: suggetionS0NumbersData,
        selectedFilterList,
        apiName: 'doOrders',
        columnName: 'incrementId'
      },
      formatBody: useCallback(({ incrementId }) => <Box className="mr-l15">{incrementId}</Box>, [])
    },
    {
      name: CONTENT.PO_NUMBER,
      key: 'poNumber',
      columnName: 'id',
      supportFilter: true,
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' },
      filterData: {
        type: 'autoSelect',
        listData: uniquePoNumbers,
        selectedFilterList,
        apiName: 'doOrders',
        columnName: 'poNumber'
      }
    },
    {
      name: CONTENT.ENTITY,
      key: 'customer',
      columnName: 'id',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' },
      formatBody: useCallback((data) => data?.customer?.name, [])
    },
    {
      name: CONTENT.SO_VALUE,
      key: 'soValue',
      columnName: 'id',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' },
      formatBody: useCallback(
        (data) =>
          data?.orderItems?.reduce(
            (acc, item) => acc + (item?.quantity && item?.price ? item.quantity * item.price : 0),
            0
          ) || 0,
        []
      )
    },
    {
      name: CONTENT.DO_TYPE,
      key: 'doType',
      columnName: 'doType',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' }
    },
    {
      name: CONTENT.PLACED_BY,
      key: 'createdBy',
      columnName: 'id',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' }
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      columnName: 'id',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' },
      formatBody: useCallback(
        ({ status }) => (
          <Box data-cy="status-col-container" className={classes.statusContainer}>
            <CircleIcon data-cy="status-color-icon" fontSize="18px" color={statusIcon(status)} />
            {status}
          </Box>
        ),
        []
      ),
      filterData: {
        type: 'status',
        listData: ['CREATED', 'APPROVED', 'REJECTED', 'UPLOADED', 'CANCELLED'],
        columnName: 'status',
        selectedFilterList
      }
    },
    {
      name: CONTENT.CREATED_AT,
      key: 'createdAt',
      columnName: 'createdAt',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' },
      formatBody: useCallback(
        ({ createdAt }) => (
          <Box className="fw-bold">{createdAt && splitDate(createdAt, 'utcTOlocalDate')}</Box>
        ),
        []
      ),
      supportSort: true,
      filterData: {
        type: 'dateRange',
        selectedFilterList,
        apiName: 'doOrders',
        columnName: 'createdAt'
      }
    },
    {
      name: CONTENT.FACILITY,
      key: 'facility',
      columnName: 'id',
      align: 'center',
      style: { minWidth: 130, maxWidth: 130, fontSize: '14px' }
    },
    {
      name: CONTENT.ACTION,
      key: 'ACTION',
      align: 'center',
      formatBody: useCallback(
        (data) => (
          <MoreItem
            key={data.id}
            itemsToShow={actionOptions(data)}
            disabled={data.status === 'CANCELLED'}
          />
        ),
        []
      ),
      style: { minWidth: '60px', maxWidth: '60px' }
    }
  ];

  const extendedHeaderConfig = [
    {
      name: 'Shipment ID',
      key: 'shippingPackageId',
      align: 'center'
    },
    {
      name: 'Invoice Value',
      key: 'totalPrice',
      align: 'center'
    },
    {
      name: 'Invoice Number',
      key: 'invoiceNumber',
      align: 'center',
      formatBody: useCallback(({ invoiceNumber, documentLink }) => {
        if (documentLink) {
          return (
            <Link target="_blank" to={documentLink}>
              {invoiceNumber}
            </Link>
          );
        }
        return invoiceNumber;
      }, [])
    },
    {
      name: 'Carrier',
      key: 'courierCode',
      align: 'center'
    },
    {
      name: 'AWB',
      key: 'awbNumber',
      align: 'center'
    }
  ];

  const renderExtenedComponent = ({ incrementId }) => (
    <LkTable
      tableHeight={240}
      isDataFetching={doNumbersListData[incrementId]?.isLoading}
      headerConfig={extendedHeaderConfig}
      tableData={doNumbersListData[incrementId]?.data}
      totalRowsCount={doNumbersListData[incrementId]?.length}
      dataRequestFunction={() => getOrderDetailsData(incrementId)}
      pageNumber={0}
      showTableCount={false}
      isNonVertualizedTable
    />
  );

  return (
    <Box data-cy="table-body-container" className={classes.bodyContainer}>
      <Box data-cy="table-container" className={classes.tableContainer}>
        <LkTable
          tableHeight={tableHeight}
          isDataFetching={isLoading}
          headerConfig={headerConfig}
          tableData={tableData}
          totalRowsCount={totalCount}
          pageNumber={pageNumber}
          isNonVertualizedTable
          rowSize={60}
          renderExtenedComponent={renderExtenedComponent}
          dataRequestFunction={fetchMoreData}
          initialSortBy={initialSortBy}
          setFilters={onChangeFilterList}
        />
      </Box>
      {openCancelModal && (
        <LkModal
          modalWidth="500px"
          showActionButton
          open={openCancelModal}
          handleClose={toggleModal}
        >
          {!isCancelSuccess ? (
            <>
              <Box data-cy="modal-container" width="100%" className={classes.cancelContainer}>
                <Box data-cy="modal-heading" className={classes.modalHeading}>
                  {CONTENT.CANCEL_ORDER_SMALL}?
                </Box>
                <Box data-cy="modal-sub-heading" className={classes.subHeading}>
                  {CONTENT.MODAL_INFO_MESSAGE}
                </Box>
                <Box
                  data-cy="modal-so-number"
                  width="50%"
                  className={classes.modalSoNumberContainer}
                >
                  {CONTENT.SO_NUMBER}{' '}
                  <span className={classes.modalSoNumber}>{rowData?.incrementId}</span>
                </Box>
                <Box className="fs14 text-center">
                  with{' '}
                  <span className={classes.modalSoNumber}>
                    Invoice Value:{' '}
                    {doNumbersListData?.[rowData?.incrementId]?.isLoading ? (
                      <CircularProgress color="inherit" size="13px" />
                    ) : (
                      doNumbersListData?.[rowData?.incrementId]?.data?.totalPrice
                    )}
                  </span>
                  <br />
                  <span data-cy="raised-against">{CONTENT.RAISED_AGAINST} </span>
                  <span data-cy="po-number" className={classes.modalSoNumber}>
                    PO: {rowData?.poNumber}{' '}
                  </span>
                  {CONTENT.BY}
                  <span data-cy="customer-name" className={classes.modalSoNumber}>
                    {' '}
                    {rowData?.customer?.name}
                  </span>
                </Box>
              </Box>
              <Box width="100%" className={classes.actionButtons}>
                <Button
                  className={classes.cancelButton}
                  onClick={onSubmitFun}
                  color="primary"
                  variant="contained"
                  data-cy="modal-cancel-order-button"
                >
                  {isCancelLoading ? (
                    <CircularProgress color="inherit" size="20px" />
                  ) : (
                    CONTENT.CANCEL_ORDER
                  )}
                </Button>
                <Button
                  className={classes.goBackBtn}
                  onClick={toggleModal}
                  color="primary"
                  variant="outlined"
                  data-cy="go-back-button"
                >
                  {CONTENT.GO_BACK}
                </Button>
              </Box>
            </>
          ) : (
            <Box className={classes.cancelContainer}>
              <img
                data-cy="success-image"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/success-icon.png`}
                alt="success-icon.png"
              />
              <Box data-cy="success-message-heading" className={`${classes.modalHeading} mr-t40`}>
                {CONTENT.ORDER_CANCELLED}!
              </Box>
              <Box data-cy="success-message-subHeading" className="fs14 mr-10 text-center">
                {CONTENT.ORDER_CANCELLED_FOR_SO_NO}{' '}
                <span className={classes.modalSoNumber}>{rowData?.incrementId}</span>
              </Box>
            </Box>
          )}
        </LkModal>
      )}
    </Box>
  );
};

export default OrderDashboardBody;
