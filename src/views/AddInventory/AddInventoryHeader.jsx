import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';

import LkInput from 'components/MaterialUi/LkInput';
import FileUpload from 'components/FileUpload/FileUpload';
import {
  addBarcodeToInventoryReset,
  uploadAddInventoryLoad,
  uploadAddInventoryReset
} from 'redux/reducers/addInventory';
import { fileDownload, debounce } from 'utils/helpers';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import AddInventorySinglePidUpload from './AddInventorySinglePidUpload';

// import SinglePidUploadModal from './SinglePidUploadModal';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 4
  },

  button: {
    marginLeft: 10,
    borderRadius: 12,
    width: 'fit-content'
  },
  modalSubTitle: {
    width: 500,
    paddingBottom: '10px',
    lineHeight: '18px'
  }
}));

const defaultUploadModalState = { single: false, bulk: false };
const sampleCSV = [['BARCODE', 'PID']];

const AddInventoryHeader = ({
  fetchMoreListItems,
  onSearch,
  selectedFilterList,
  deleteSelectedFilter,
  CONTENT
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [openUploadModal, setOpenUploadModal] = useState(defaultUploadModalState);
  const [failData, setFailData] = useState('');

  const {
    uploadAddInventory: { isSuccess: isSuccessBulkUpload, errorFile, isLoading },
    addBarcodeToInventory: { isSuccess: singleUploadSuccess, isLoading: isLoadingSingleUpload }
  } = useSelector((state) => state.addInventory);
  const closeModal = useCallback(() => {
    setFailData('');
    dispatch(uploadAddInventoryReset());
    setOpenUploadModal(defaultUploadModalState);
  }, []);

  useEffect(() => {
    if (isSuccessBulkUpload) {
      setFailData('');
      fetchMoreListItems();
    }
  }, [dispatch, isSuccessBulkUpload]);

  useEffect(() => {
    if (singleUploadSuccess) {
      fetchMoreListItems();
      setOpenUploadModal(defaultUploadModalState);
      dispatch(addBarcodeToInventoryReset());
    }
  }, [singleUploadSuccess]);

  useEffect(() => {
    if (errorFile) {
      setFailData(CONTENT.INVALID_CONTENT);
    }
  }, [errorFile]);

  const modalSubTitle = () => (
    <p className={classes.modalSubTitle}>
      {CONTENT.PLEASE_SELECT_THE_CSV_FILE_TO_IMPORT_PID_DETAILS}
    </p>
  );

  const openModalFun = (name) => setOpenUploadModal((oldState) => ({ ...oldState, [name]: true }));

  const upLoadSelectedFileFun = useCallback((file) => {
    if (file) {
      const formData = new FormData();
      formData.set('csvFile', file);
      dispatch(uploadAddInventoryLoad(formData));
    }
  }, []);

  const downloadreportFun = () => {
    fileDownload(errorFile, 'CourierCutoffUploaddError');
  };

  const delayedQuery = useCallback(
    debounce(({ target: { value } }) => onSearch(value), 500),
    []
  );

  return (
    <>
      <Box className={classes.container}>
        <Box display="flex" alignItems="flex-end" gap={1.25}>
          <Box width={380}>
            <LkInput
              fullWidth
              onChange={delayedQuery}
              variant="outlined"
              id="globalSearchId"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                  </InputAdornment>
                )
              }}
              placeholder={CONTENT.SEARCH_PID_HERE}
            />
          </Box>
          <FilterChipsV2 filters={selectedFilterList} deleteData={deleteSelectedFilter} />
        </Box>
        <Box>
          <Button
            color="primary"
            className={classes.button}
            type="submit"
            variant="contained"
            data-cy="singlePidBtn"
            onClick={() => openModalFun('single')}
          >
            {CONTENT.SINGLE_PID}
          </Button>
          <Button
            color="primary"
            className={classes.button}
            type="submit"
            name="bulk"
            data-cy="bulkUploadBtn"
            variant="contained"
            onClick={() => openModalFun('bulk')}
          >
            {CONTENT.BULK_UPLOAD}
          </Button>
        </Box>
      </Box>
      <FileUpload
        open={openUploadModal.bulk}
        close={closeModal}
        subTitle={modalSubTitle()}
        selectFiles={upLoadSelectedFileFun}
        sampleCSV={sampleCSV}
        checkedItemLabel=""
        sampleFileName="sample_add_inventory_upload"
        showSampleArray={false}
        failDataMessage={failData}
        importLoading={isLoading}
        uploadCsvPass={isSuccessBulkUpload}
        downloadReport={downloadreportFun}
      />
      {openUploadModal.single && (
        <AddInventorySinglePidUpload
          isLoading={isLoadingSingleUpload}
          open={openUploadModal.single}
          close={closeModal}
          CONTENT={CONTENT}
        />
      )}
    </>
  );
};

export default AddInventoryHeader;
