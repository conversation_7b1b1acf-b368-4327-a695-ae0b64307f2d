import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const stockCycleBarcodeDetailsApi = (id) =>
  tokenInstance().get(`${config.stockTake.stockCycleBarcodeDetailsList}/${id}`);

export const stockCycleScanBarcodeApi = (payload) =>
  tokenInstance().post(config.stockTake.scanBarcode, payload);

export const stockCycleDetailsLoadSagaApi = (id) =>
  tokenInstance().get(`${config.stockTake.stockCycleDetails}/${id}`);
