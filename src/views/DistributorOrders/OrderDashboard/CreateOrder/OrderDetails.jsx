import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Autocomplete from '@mui/material/Autocomplete';
import { MenuItem } from '@mui/material';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import AutoStoriesTwoToneIcon from '@mui/icons-material/AutoStoriesTwoTone';
import MoveDownIcon from '@mui/icons-material/MoveDown';
import CircularProgress from '@mui/material/CircularProgress';

import { debounce } from 'utils/helpers';
import { keys } from 'utils/lodash';

import LkInput from 'components/MaterialUi/LkInput';
import LkSelect from 'components/MaterialUi/LkSelectBox';

import { DISTRIBUTOR_ORDERS, getValidateCSVReset } from 'redux/reducers/distributorOrders';
import { DO_ORDER_TYPES } from 'config/DOorder';

const useStyles = makeStyles()(() => ({
  orderDetails: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    padding: '25px 15px',
    paddingRight: '40px',
    border: '1px solid #939393',
    borderRadius: '12px',
    marginBottom: '20px'
  },
  customer: {
    color: 'black'
  },
  changeDetailsButton: {
    borderRadius: 6,
    marginRight: '20px'
  }
}));

const OrderDetails = ({
  setActiveStep,
  orderDetails,
  setOrderDetails,
  activeStep,
  isSuccess,
  CONTENT,
  searchSuggestion
}) => {
  const dispatch = useDispatch();
  const { classes } = useStyles();

  const { isLoading: dropDownListLoading, data: customerDropDownList } = useSelector(
    (state) => state[DISTRIBUTOR_ORDERS].customerDropDownList
  );
  const facilityCode = useSelector((state) => state.settings.selectedFacility);

  const handleOrderDetails = () => {
    if (orderDetails.customer && orderDetails.poNumber) {
      setActiveStep(1);
    }
  };

  const changeOrderDetails = () => {
    setActiveStep(0);
    dispatch(getValidateCSVReset());
  };

  const delayedQueryCustomerList = useCallback(
    debounce((q) => searchSuggestion(q), 500),
    []
  );

  const getShippingAddress = useMemo(
    () =>
      orderDetails?.customer?.customerAddressDetails?.find(
        (item) => item?.addressType === 'SHIPPING'
      ),
    [orderDetails]
  );

  if (activeStep > 0) {
    return (
      <Box width="250px">
        <Box className={classes.orderDetails} width="100%">
          <Box className={classes.customer} margin="auto">
            {orderDetails.customer.name}
          </Box>
          <Box className="fs12 pd-t15 display-flex">
            <AutoStoriesTwoToneIcon className="fs16" />
            <span data-cy="facility-code-details" className="pd-l10">
              {CONTENT.FACILITY}: {facilityCode}
            </span>
          </Box>
          <Box width="100%" marginBlock={2} className="fs12 display-flex">
            <ViewInArIcon className="fs16" />
            <span data-cy="po-number-details" className="pd-l10">
              {CONTENT.PO}: {orderDetails.poNumber}
            </span>
          </Box>
          <Box width="100%" className="fs12 display-flex">
            <MoveDownIcon className="fs16" />
            <Box
              component="span"
              sx={{ wordBreak: 'break-all' }}
              data-cy="do-type-details"
              className="pd-l10"
            >
              {CONTENT.DO_TYPE}: {orderDetails.doType}
            </Box>
          </Box>
        </Box>

        <Button
          data-cy="change-details-btn"
          fullWidth
          color="primary"
          className={classes.changeDetailsButton}
          type="submit"
          variant="outlined"
          onClick={changeOrderDetails}
          disabled={isSuccess}
        >
          {CONTENT.CHANGE_DETAILS}
        </Button>
      </Box>
    );
  }

  const renderOptionLabel = (props, option, { index }) => {
    if (keys(option).length === 0) {
      return '';
    }
    const shippingAddressCity = option.customerAddressDetails.find(
      (address) => address.addressType === 'SHIPPING'
    )?.city;

    const isOdd = parseInt(index, 10) % 2 === 1;

    return (
      <Box
        component="li"
        {...props}
        key={option.id}
        sx={{
          backgroundColor: isOdd ? '#F5F7FA' : '#FFFFFF',
          '&:hover': {
            backgroundColor: '#EAEAEA' // Your gray hover color
          },
          '&[aria-selected="true"]': {
            backgroundColor: '#E6F7FB' // Your cyan selected color
          }
        }}
        display="flex"
        borderBottom="1px solid #E0E0E0"
        flexDirection="column"
        alignItems="flex-start !important"
      >
        <Box fontWeight="bolder">{option.name}</Box>
        <Box display="flex" gap="6px" justifyContent="space-between" width="100%">
          <Box component="span" sx={{ fontSize: '12px' }} aria-label={shippingAddressCity}>
            {shippingAddressCity}
          </Box>
          <Box component="span" sx={{ fontSize: '10px' }} aria-label={option.code}>
            {option.code}
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <>
      <Box width="270px">
        <Autocomplete
          data-cy="auto-complete-input"
          clearIcon={false}
          options={customerDropDownList || []}
          onChange={(_event, newValue) =>
            setOrderDetails((prev) => ({
              ...prev,
              customer: newValue
            }))
          }
          onKeyUp={(e) => delayedQueryCustomerList(e.target.value)}
          getOptionKey={(option) => option.id}
          noOptionsText={dropDownListLoading ? <CircularProgress size="20px" /> : 'No options'}
          getOptionLabel={(option) => (typeof option === 'string' ? option : option.name)}
          filterOptions={(options) => options.filter((option) => option.customerEnabled)}
          renderOption={renderOptionLabel}
          renderInput={(params) => (
            <LkInput
              {...params}
              label={CONTENT.SELECT_CUSTOMER}
              className="input-box wd-200 mr-t5"
              size="small"
            />
          )}
        />
        <LkInput
          sx={{ marginBlock: 2 }}
          id="po-number"
          fullWidth
          label={CONTENT.PO_NUMBER}
          value={orderDetails.poNumber}
          onChange={(e) => setOrderDetails((prev) => ({ ...prev, poNumber: e.target.value }))}
          size="small"
          placeholder={CONTENT.ENTER_PO_NUMBER}
        />

        <LkSelect
          fullWidth
          value={orderDetails.doType}
          label="DO Type"
          data-cy="do-type-select"
          labelValue="DO Type"
          variant="outlined"
          size="small"
          onChange={(e) => setOrderDetails((prev) => ({ ...prev, doType: e.target.value }))}
        >
          {DO_ORDER_TYPES.map(({ value, label }) => (
            <MenuItem data-cy={`do-type-${value}`} value={value} key={label}>
              {label}
            </MenuItem>
          ))}
        </LkSelect>
      </Box>
      <Box data-cy="all-fields-are-required" width="250px" className="display-flex fs10 pd-t15">
        *{CONTENT.ALL_FIELDS_ARE_REQUIRED}
      </Box>
      <Box className="mr-t32" width="250px">
        <Button
          data-cy="confirm-button"
          fullWidth
          color="primary"
          className={classes.changeDetailsButton}
          type="submit"
          variant="contained"
          onClick={handleOrderDetails}
          disabled={!(orderDetails.customer && orderDetails.poNumber && orderDetails.doType)}
        >
          {CONTENT.CONFIRM}
        </Button>
      </Box>
      <Box marginTop={5} fontWeight="400" color="black">
        {getShippingAddress?.addressLine1 ? (
          <Box className="display-flex flex-direction-column gap10 fs14" width="250px">
            <Box fontSize="16px" fontWeight="500">
              Address:
            </Box>
            <Box>{getShippingAddress?.addressLine1}</Box>
            <Box>{getShippingAddress?.addressLine2}</Box>
            <Box>
              {getShippingAddress?.city}-{getShippingAddress?.state}, {getShippingAddress?.pincode}
            </Box>
            <br />
          </Box>
        ) : null}
      </Box>
    </>
  );
};

export default OrderDetails;
