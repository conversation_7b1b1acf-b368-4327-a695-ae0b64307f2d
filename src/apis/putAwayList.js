import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getPutAwayListApi = (payload) =>
  tokenInstance().post(config.putAwayList.list, payload);

export const exportPutawayListApi = (payload = {}) => tokenInstance()
  .post(`${config.putAwayList.listExport}`, payload)
  .then((response) => response.data)
  .catch((error) => {
    throw error;
  });

export const getPutawayDetailApi = (payload) => {
  delete payload.isReset;
  return tokenInstance()
    .post(`${config.putAwayList.detail}`, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const putawayFilterSuggestionApi = () =>
  // const f = convertToQueryString(payload)
  tokenInstance()
    .get(config.putAwayList.filterSuggestion)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    })
;

export const exportPutawayDetailAPi = (payload = {}) => tokenInstance()
  .post(`${config.putAwayList.detailExport}`, payload)
  .then((response) => response.data)
  .catch((error) => {
    throw error;
  });
