/* The Modal (background) */
.modal-custom {
    display: block; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 60%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    // background-color: rgb(0, 0, 0); /* Fallback color */
    // background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
    /* The Close Button */
    .close {
        color: #aaaaaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .close:hover,
    .close:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }
    /* Modal Content */
    .modal-custom-content {
        background-color: #fefefe;
        margin: auto;
        padding: 20px;
        width: 80%;
        border: none !important;
        border-radius: 8px;
        box-shadow: 0px 16px 32px rgba(0, 0, 0, 0.1);
    }
}
