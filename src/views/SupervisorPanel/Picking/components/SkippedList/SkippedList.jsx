import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { has, keys, omit } from 'utils/lodash';
import {
  getExportSkippedDataLoad,
  getExportSkippedDataReset,
  supervisorPickingHoldDataRequest,
  supervisorPickingHoldDataReset,
  supervisorPickingSkippedDataRequest,
  supervisorPickingSkippedDataReset,
  supervisorPickingUsersRequest
} from 'redux/actionCreators/supervisorPickingSkipped';

import { fileDownload, formatDateDashedSeparated, listToCSVString } from 'utils/helpers';
import SkippedListHeader from './SkippedListHeader';
import SkippedListTable from './SkippedListTable';

const PAGE_SIZE = 25;
// @TODO: Move these to constants
const FILTER_LABELS = {
  location_barcode: 'Location',
  product_id: 'PID',
  increment_id: 'Order ID',
  location_qty: 'Warehouse Quantity',
  facility_qty: 'Bin Quantity',
  skipped_by: 'Skipped By',
  skipped_date: 'Skipped Date',
  skipped_reason: 'Skipped Reason',
  channel: 'Channel',
  location: 'Location',
  category: 'Category',
  repickStatus: 'Repick Status'
};

let totalApiCall = 0;

const getStatus = (onHold, isDiscarded) => {
  if (onHold) {
    return 'HOLD';
  }
  if (isDiscarded) {
    return 'DISCARDED';
  }
  return 'TEMP_NOT_FOUND';
};

const defaultRequest = (
  onHold,
  isDiscarded,
  selectedLocation,
  selectedCategory,
  selectedRepickStatus
) => ({
  page: 0,
  pageSize: PAGE_SIZE,
  status: getStatus(onHold, isDiscarded),
  locationSearchFields: {
    location: selectedLocation,
    category: selectedCategory,
    repickStatus: selectedRepickStatus
  },
  searchFields: {},
  orderByFields: {
    key: 'skipped_date',
    order: 'ASC'
  },
  dateRange: {
    startDate: '',
    endDate: ''
  },
  reset: true
});

const exportLabels = [
  'Location',
  'PID',
  'Order ID',
  'Warehouse Quantity',
  'Bin Quantity',
  'Skipped By',
  'Skipped Date',
  'Skipped Reason',
  'Channel'
];

const exportKeys = [
  'locationBarcode',
  'productId',
  'incrementId',
  'locationQty',
  'facilityQty',
  'skippedBy',
  'skippedDate',
  'skippedReason',
  'channel'
];

const SkippedList = ({
  onHold,
  isDiscarded = false,
  selectedLocation,
  selectedCategory,
  CONTENT,
  selectedRepickStatus,
  dateFilter,
  isDateSelected,
  handleDateFilterValue
}) => {
  const dispatch = useDispatch();
  const { isLoading, totalRecords, data } = useSelector((state) =>
    onHold ? state.supervisorPickingSkipped.hold : state.supervisorPickingSkipped.skipped
  );
  const stockOut = useSelector((state) => state.supervisorPickingSkipped.stockOut);

  const { isLoading: exportDataLoading, data: exportSkippedData } = useSelector(
    (state) => state.supervisorPickingSkipped.exportSkippedData
  );

  const { isSuccess } = useSelector((state) => state.supervisorPickingSkipped.asrsToManual);

  const [sortType, setSortType] = useState({});
  const [sortKey, setSortKey] = useState('skipped_date');
  const [selectedItems, setSelectedItems] = useState([]);
  const [requestBody, setRequestBody] = useState(
    defaultRequest(onHold, isDiscarded, selectedLocation, selectedCategory, selectedRepickStatus)
  );
  const [globalSearch, setGlobalSearch] = useState('');

  const getData = () => {
    if (onHold) {
      dispatch(supervisorPickingHoldDataRequest(requestBody));
    } else {
      dispatch(supervisorPickingSkippedDataRequest(requestBody));
    }
  };

  useEffect(
    () => () => {
      if (onHold) {
        dispatch(supervisorPickingHoldDataReset());
      } else {
        totalApiCall = 0;
        dispatch(supervisorPickingSkippedDataReset());
      }
    },
    []
  );

  useEffect(() => {
    totalApiCall = 1;
    getData();
  }, [requestBody, stockOut]);

  useEffect(() => {
    if (isSuccess) {
      dispatch(supervisorPickingSkippedDataRequest(requestBody));
      setSelectedItems([]);
    }
  }, [isSuccess]);

  useEffect(() => {
    if (selectedLocation || selectedCategory) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        locationSearchFields: {
          location: selectedLocation,
          category: selectedCategory,
          repickStatus: selectedRepickStatus === 'ANY' ? null : selectedRepickStatus
        },
        page: 0,
        reset: true
      }));
    }
  }, [selectedLocation, selectedCategory, selectedRepickStatus]);

  useEffect(() => {
    if (selectedItems.length && !isDiscarded) {
      dispatch(supervisorPickingUsersRequest());
    }
  }, [selectedItems]);

  useEffect(() => {
    if (isDateSelected) {
      const { startDate, endDate } = dateFilter;
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        dateRange: {
          startDate,
          endDate
        }
      }));
    }
  }, [dateFilter]);

  const exportToCSV = (exportDataDetails) => {
    const exportData = exportDataDetails.map((item) => ({
      ...item,
      ...item?.pickingDetail
    }));
    const fileName = 'supervisor-picking-list';
    const csvString = listToCSVString(exportData, exportLabels, exportKeys);
    fileDownload(csvString, fileName);
  };

  useEffect(() => {
    if (exportSkippedData?.length > 0) {
      exportToCSV(exportSkippedData);
    }
    return () => {
      dispatch(getExportSkippedDataReset());
    };
  }, [exportSkippedData]);

  const fetchMoreListItems = (order, key, nextPagePage) => {
    if (totalApiCall) {
      setRequestBody((preReqBody) => ({
        ...preReqBody,
        orderByFields: {
          key,
          order
        },
        page: nextPagePage,
        reset: !nextPagePage
      }));
    }
  };

  const reset = useCallback((key, st) => {
    setGlobalSearch('');
    setRequestBody((prevReqBody) => {
      let updatedReq = { ...prevReqBody, page: 0, searchFields: {}, reset: true };
      if (st) {
        updatedReq = {
          ...updatedReq,
          orderByFields: {
            key,
            order: st
          }
        };
      } else {
        updatedReq = {
          ...updatedReq,
          orderByFields: {
            key: 'skipped_date',
            order: 'ASC'
          }
        };
      }
      return updatedReq;
    });
  }, []);

  const searchData = useCallback(([{ key, value }]) => {
    setRequestBody((prevReqBody) => {
      let updatedSearchFields = prevReqBody.searchFields;
      if (!value && has(updatedSearchFields, key)) {
        updatedSearchFields = omit(prevReqBody.searchFields, [key]);
      } else if (value) {
        updatedSearchFields = {
          ...updatedSearchFields,
          [key]: [value]
        };
      }
      return {
        ...prevReqBody,
        page: 0,
        searchFields: updatedSearchFields,
        reset: true
      };
    });
  }, []);

  const deleteFilter = (key) => {
    setRequestBody((prevReqBody) => {
      const updatedSearchFields = omit(prevReqBody.searchFields, [key]);
      return {
        ...prevReqBody,
        page: 0,
        searchFields: updatedSearchFields,
        reset: true
      };
    });
    if (key === 'dateRange') {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        dateRange: {
          startDate: '',
          endDate: ''
        }
      }));
      handleDateFilterValue();
    }
  };

  const sortTable = useCallback(
    (key) => {
      const st = sortType[key] === 'ASC' ? 'DESC' : 'ASC';
      const obj = {};
      obj[key] = st;
      setSortKey(key);
      setSortType({ ...sortType, ...obj });
      reset(key, st);
    },
    [sortType, setSortType, setSortKey, reset]
  );

  const selectItem = useCallback(
    (event, item) => {
      let updatedSelectedItems = [];
      if (event.target.checked) {
        updatedSelectedItems = [...selectedItems, item];
      } else {
        updatedSelectedItems = selectedItems.filter((eachItem) => eachItem.id !== item.id);
      }
      if (selectedItems.length === 0 && updatedSelectedItems.length === 1 && !isDiscarded) {
        dispatch(supervisorPickingUsersRequest());
      }
      setSelectedItems(updatedSelectedItems);
    },
    [selectedItems, setSelectedItems]
  );

  const handleSelectAllClick = useCallback(() => {
    if (selectedItems.length === data.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(() => data.map((eachData) => eachData.pickingDetail));
    }
  }, [selectedItems, setSelectedItems, data]);

  const appliedFilter = useMemo(() => {
    const filters = [];
    keys({
      ...requestBody.locationSearchFields,
      ...requestBody.searchFields
    }).forEach((eachFilterKey) => {
      const value =
        requestBody.locationSearchFields[eachFilterKey] || requestBody.searchFields[eachFilterKey];
      if (value) {
        filters.push({
          key: eachFilterKey,
          keyValue: FILTER_LABELS[eachFilterKey],
          values: value,
          disableDelete: has(requestBody.locationSearchFields, eachFilterKey)
        });
      }
    });
    if (requestBody.dateRange.startDate) {
      filters.push({
        key: 'dateRange',
        keyValue: 'Date Range',
        values: `${formatDateDashedSeparated(
          requestBody.dateRange.startDate
        )} - ${formatDateDashedSeparated(requestBody.dateRange.endDate)}`
      });
    }
    return filters;
  }, [requestBody.locationSearchFields, requestBody.searchFields, requestBody.dateRange]);

  const getDataForExport = useCallback(() => {
    if (data.length === totalRecords) {
      exportToCSV(data);
    } else {
      dispatch(getExportSkippedDataLoad({ ...requestBody, pageSize: totalRecords }));
    }
  }, [data, totalRecords]);

  return (
    <>
      <SkippedListHeader
        selectedItems={selectedItems}
        setSelectedItems={setSelectedItems}
        onGlobalSearch={searchData}
        onHold={onHold}
        globalSearch={globalSearch}
        setGlobalSearch={setGlobalSearch}
        enableReset={Object.keys(requestBody.searchFields).length}
        reset={reset}
        isDiscarded={isDiscarded}
        appliedFilter={appliedFilter}
        deleteFilter={deleteFilter}
        CONTENT={CONTENT}
        getDataForExport={getDataForExport}
        exportDataLoading={exportDataLoading}
        totalRecords={totalRecords}
      />
      <SkippedListTable
        onHold={onHold}
        selectItem={selectItem}
        selectedItems={selectedItems}
        handleSelectAllClick={handleSelectAllClick}
        sortTable={sortTable}
        sortType={sortType}
        sortKey={sortKey}
        fetchMoreListItems={fetchMoreListItems}
        tableData={data}
        totalRecords={totalRecords}
        isLoading={isLoading}
        searchData={searchData}
        CONTENT={CONTENT}
      />
    </>
  );
};

export default React.memo(SkippedList);
