import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import { setPageTitle } from 'redux/reducers/settings';
import Spinner from 'components/Spinner/Spinner';
import useReducerCodeSplit from 'common/useReducerCodeSplit';

const RenderRoute = ({
  route: { title, hideGlobalTopBar, component: Component, reducers = [], sagas = [] }
}) => {
  const dispatch = useDispatch();
  const { PAGE_TITLE: CONTENT, isLoading: localeLoading } = useSelector(
    (state) => state[LOCALISATION].localeData
  );
  const reducersLoaded = useReducerCodeSplit(reducers, sagas);

  useEffect(() => {
    if (!localeLoading) {
      dispatch(setPageTitle({ title: CONTENT?.[title] || '', hide: !!hideGlobalTopBar }));
    }
    return () => dispatch(setPageTitle({ title: '', hide: true }));
  }, [localeLoading, hideGlobalTopBar, title]);

  if (!reducersLoaded) {
    return <Spinner className="display-grid-center" />;
  }

  return <Component />;
};

export default RenderRoute;
