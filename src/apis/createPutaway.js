import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

// LOCATION LIST API
export const addBarcodeToPutaway = ({
  facility,
  type,
  empCode,
  barcode,
  pid,
  boxBarcode,
  grnAndPoNumber
}) => {
  const URL = config.putaway.addBarcodeToPutaway;
  const request = {
    facilityCode: facility,
    type,
    user: empCode,
    ...grnAndPoNumber,
    barcode: [
      {
        barcodeNo: barcode,
        pid,
        boxBarcode,
        action: 'create',
        condition: 'PASS'
      }
    ]
  };
  return tokenInstance()
    .post(URL, request)
    .then((response) => response.data);
};
