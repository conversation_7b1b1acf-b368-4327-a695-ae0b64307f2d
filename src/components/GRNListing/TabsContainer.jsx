import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Tab from '@mui/material/Tab';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import './TabsContainer.scss';
// import BlockedTab from './BlockedTab';
import { useSelector, useDispatch } from 'react-redux';
import {
  getAllQueryParam,
  generatePayloadForSearchAPI,
  getQueryParamsVersion
} from 'utils/helpers';
import useFilterHook from 'components/common/useFilterHook';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { toastOpen } from 'redux/actionCreators/toast';
import { StyledTabs } from 'components/common/StyledTabs';
import { LOCALISATION } from 'redux/reducers/localisation';
import { grnSearchLoad, searchBlockedPidsLoad } from '../../redux/actionCreators/grnListing';
import GRNTab from './GRNTab';

const styles = makeStyles()(() => ({
  positionRelative: {
    position: 'relative'
  },
  tab1Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 0
  },
  tab2Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 160
  }
}));

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const PAGE_SIZE = 35;
const defaultObj = (name = 'grn?version=v1', sortKey = 'created_at', type = 'grn') => ({
  isReset: true,
  path: name,
  payload: {
    type,
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey,
      sortOrder: 'DESC'
    }
  }
});
let objPayload = defaultObj();

const blockedObjPayload = defaultObj('grnProduct', 'grn_code');

const sortingData = {
  key: 'created_at',
  order: 'DESC'
};

// const blockedSortingData = {
//     key: 'grn_code',
//     order: 'DESC',
// }

const TabsContainer = ({ mainGrnSearch }) => {
  const location = useLocation();
  const { classes } = styles();
  const dispatch = useDispatch();
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_LIST);
  const [sortKey, setSortKey] = useState('created_at');
  const { grnSearchSuccess, manualOverrideFail, manualOverrideSuccess } = useSelector(
    (state) => state.grnListing
  );
  // const [blockedSortKey, setBlockedSortKey] = useState('grn_code')

  useEffect(() => {
    if (manualOverrideFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.ERROR,
          subHeading: manualOverrideFail.display_message,
          severity: 'error'
        })
      );
    }
    if (manualOverrideSuccess) {
      dispatch(searchBlockedPidsLoad(blockedObjPayload));
    }
  }, [manualOverrideFail, manualOverrideSuccess, dispatch]);

  useEffect(() => {
    const result = getAllQueryParam(window.location.search);
    objPayload = defaultObj();
    let { payload } = objPayload;
    const { path } = objPayload;
    payload.pageRequest.sortKey = sortingData.key;
    payload.pageRequest.sortOrder = sortingData.order;
    payload.global_grn_code = mainGrnSearch;
    objPayload.path = getQueryParamsVersion(location, path);
    payload = generatePayloadForSearchAPI(
      result,
      payload,
      { ...mapSearchFilterKey, poNum: 'po_num' },
      'GRN_'
    );
    objPayload.payload = payload;
    dispatch(grnSearchLoad(objPayload));
  }, [location.search, dispatch, mainGrnSearch]);

  // blocked pids call

  // useEffect(() => {
  //     let result = getAllQueryParam(window.location.search)
  //     blockedObjPayload = defaultObj("grnProduct", "grn_code")
  //     let { payload } = blockedObjPayload;
  //     payload.pageRequest.sortKey = blockedSortingData.key
  //     payload.pageRequest.sortOrder = blockedSortingData.order
  //     payload.global_grn_code = mainGrnSearch
  //     payload = generatePayloadForSearchAPI(result, payload, mapSearchFilterKey, "BLOCKED_")
  //     dispatch(searchBlockedPidsLoad(blockedObjPayload));
  // }, [location.search, dispatch, mainGrnSearch])

  return (
    <div className={classes.root}>
      <Box width="100%" margin="0 auto">
        <StyledTabs value={0}>
          <Tab
            label={`${CONTENT.GRN_LISTING} (${grnSearchSuccess?.length || 0} )`}
            className="tabselected tabs"
          />

          {/* removing Blocked tabs for now */}
          {/* <Tab label={`Blocked Products (${blockedPids?.length || 0})`}
           className={`${value === 1 ? "tabselected" : ''} tabs`} /> */}
        </StyledTabs>
        <Box className={`border-grey5-radiusbase ${'border-left-no-radius'}`}>
          <TabPanel value={0} index={0} className={classes.positionRelative}>
            <Box width={158} component="div" height={2} className={classes.tab1Absolute}>
              {' '}
            </Box>
            <GRNTab
              mainGrnSearch={mainGrnSearch}
              sortKey={sortKey}
              setSortkey={setSortKey}
              selectedFilterList={selectedFilterList}
              onChangeFilterList={onChangeFilterList}
              deleteSelectedFilter={deleteSelectedFilter}
              resetFilters={resetFilters}
              sortingData={sortingData}
              objPayload={objPayload}
            />
          </TabPanel>
          {/* <TabPanel value={value} index={1} className={classes.positionRelative}>
                    <Box width={158} component="div" height={2} cl
                    assName={classes.tab2Absolute}> </Box>
                    <BlockedTab
                        grnSelectedFromListing={grnSelectedFromListing}
                        mainGrnSearch={mainGrnSearch}
                        sortKey={blockedSortKey}
                        setSortkey={setBlockedSortKey}
                        selectedFilterList={selectedFilterList}
                        onChangeFilterList={onChangeFilterList}
                        deleteSelectedFilter={deleteSelectedFilter}
                        resetFilters={resetFilters}
                        sortingData={blockedSortingData}
                        objPayload={blockedObjPayload}
                    />
                </TabPanel > */}
        </Box>
      </Box>
    </div>
  );
};

export default TabsContainer;
