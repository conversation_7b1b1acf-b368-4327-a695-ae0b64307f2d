import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { makeStyles } from 'tss-react/mui';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import StoreConsolidationHistory from './StoreConsolidationHistory';
import StoreConsolidationListing from './StoreConsolidationListing';

const useStyles = makeStyles()(() => ({
  container: {
    backgroundColor: '#f0f0f0',
    height: 'calc(100vh - 64px)',
    display: 'flex',
    flexDirection: 'column'
  },
  dataContainer: {
    borderRadius: 4,
    backgroundColor: '#ffffff',
    flexGrow: 1
  },
  customTabRoot: {
    color: '#00B9C6'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  }
}));

const TabPanel = ({ children, value, index }) => value === index && children;

const HISTORY = 'HISTORY';
const LISTING = 'LISTING';

const StoreConsolidationTabs = () => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.TRAY_RELEASE);

  const [value, setValue] = useState(location.pathname.includes('/history') ? HISTORY : LISTING);

  const handleChange = (_event, report) => {
    if (report === LISTING) {
      navigate('/trayRelease');
    } else {
      navigate('/trayRelease/history');
    }
    setValue(report);
  };

  return (
    <Box className={classes.container}>
      <Box>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="secondary tabs example"
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          <Tab data-cy={`${LISTING}-tab`} value={LISTING} label={CONTENT.LISTING} />
          <Tab data-cy={`${HISTORY}-tab`} value={HISTORY} label={CONTENT.HISTORY} />
        </Tabs>
      </Box>
      <Box className={classes.dataContainer}>
        <TabPanel value={value} index={LISTING}>
          <StoreConsolidationListing CONTENT={CONTENT} />
        </TabPanel>
        <TabPanel value={value} index={HISTORY}>
          <StoreConsolidationHistory CONTENT={CONTENT} />
        </TabPanel>
      </Box>
    </Box>
  );
};

export default StoreConsolidationTabs;
