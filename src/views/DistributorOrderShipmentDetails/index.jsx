import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { omit } from 'utils/lodash';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { getConsulKeyValue } from 'redux/reducers/consul';
import { LOCALISATION } from 'redux/reducers/localisation';

import DistributorOrderShipmentDetailsHeader from './DistributorOrderShipmentDetailsHeader';
import DistributorOrderShipmentDetailsBody from './DistributorOrderShipmentDetailsBody';
import {
  DO_SHIPMENT_LIST,
  getDOShipmentDetailsListExportLoad,
  getDOShipmentDetailsListExportReset,
  getDOShipmentDetailsListLoad,
  getDOshipmentDetailsListReset
} from '../../redux/reducers/doShipmentList';
import { convertDateFormat, fileDownload, listToCSVString } from '../../utils/helpers';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    height: 'calc(100vh - 65px)',
    background: '#F8F8F8'
  }
}));

const PAGE_SIZE = 35;

const requestPayload = {
  offset: 0,
  pageSize: PAGE_SIZE,
  sortKey: 'updatedAt',
  sortOrder: 'ASC'
};

const DistributorOrderShipmentDetails = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { shippingPackageID } = useParams();

  const { data, headerData, isLoading, totalCount } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].shipmentDetailsList
  );

  const { data: exportData, isLoading: isExportLoading } = useSelector(
    (state) => state[DO_SHIPMENT_LIST].exportShipmentDetailsList
  );

  const CONTENT = useSelector(
    (state) => state[LOCALISATION].localeData.DISTRIBUTOR_ORDER_SHIPMENT_DETAILS
  );
  const initialReq = useRef(false);

  const [requestBody, setRequestBody] = useState(requestPayload);

  const dispatchListLoadFn = () =>
    dispatch(getDOShipmentDetailsListLoad({ ...requestBody, id: shippingPackageID }));

  const fetchMoreData = (sortOrder, sortKey, offset) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      offset,
      sortKey,
      sortOrder
    }));
  };

  const onGlobalSearch = (pid) => {
    if (pid) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        pageNumber: 0,
        pid
      }));
    } else {
      setRequestBody((prevReqBody) => ({
        ...omit(prevReqBody, ['pid'])
      }));
    }
  };

  useEffect(() => {
    if (initialReq.current) {
      dispatchListLoadFn();
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current = true;
    dispatch(getConsulKeyValue(['featureToggle']));
    return () => dispatch(getDOshipmentDetailsListReset());
  }, []);

  const exportToCSV = (exportDataDetails) => {
    const columns = [
      CONTENT.PRODUCT_ID,
      CONTENT.REQUIRED_QTY,
      CONTENT.QTY_SCANNED,
      CONTENT.PID_DESC,
      CONTENT.UPDATED_ON,
      CONTENT.UPDATED_BY
    ];
    const exportKeys = [
      'productId',
      'requiredQuantity',
      'quantityScanned',
      'description',
      'updatedOn',
      'updatedBy'
    ];
    const csvString = listToCSVString(exportDataDetails, columns, exportKeys);
    fileDownload(csvString, `shipmentDetailsList-${convertDateFormat(new Date(), 'export')}`);
  };

  useEffect(() => {
    if (exportData.length) {
      exportToCSV(exportData);
      dispatch(getDOShipmentDetailsListExportReset());
    }
  }, [exportData]);

  const onDownloadFun = () => {
    if (data.length === totalCount) {
      exportToCSV(data);
    } else {
      dispatch(
        getDOShipmentDetailsListExportLoad({
          ...requestBody,
          offset: 0,
          pageSize: totalCount,
          id: shippingPackageID
        })
      );
    }
  };

  return (
    <Box className={classes.root}>
      <DistributorOrderShipmentDetailsHeader
        onSearch={onGlobalSearch}
        CONTENT={CONTENT}
        shippingPackageID={shippingPackageID}
        headerData={headerData}
        downloadCSVfun={onDownloadFun}
        disableDownloadFun={!data.length || isExportLoading}
        pidListData={data}
      />
      <DistributorOrderShipmentDetailsBody
        pageLimit={PAGE_SIZE}
        pageNumber={requestBody.pageNumber}
        fetchMoreListItems={fetchMoreData}
        CONTENT={CONTENT}
        data={data}
        isLoading={isLoading}
        totalCount={totalCount}
      />
    </Box>
  );
};

export default DistributorOrderShipmentDetails;
