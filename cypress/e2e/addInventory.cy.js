// eslint-disable-next-line import/no-extraneous-dependencies
import 'cypress-file-upload';
import config from '../../src/config';

describe('Add Inventory', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
    cy.navigate(['main-navigation', 'DC Sorting', 'Add Inventory']);
  });

  afterEach(() => {
    cy.logout();
  });

  it('Visits the Add Inventory Page from app bar and table is populated', () => {
    // TODO replace url when u add apis

    cy.fixture('addInventory/addInventoryHeader').as('headers');

    cy.intercept('GET', config.addInventory.list).as('getAddInventoryList');

    cy.get('@headers').then((headers) => {
      headers.forEach((header, index) => {
        cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
      });
    });

    cy.wait('@getAddInventoryList').then(({ response }) => {
      const apiData = response.body.data?.courierCutOffs; // Capturing the API response body
      if (apiData.length) {
        cy.getByCy(`table-cell-${0}-0`).should('exist');

        // Your assertions for this item's data
        cy.getByCy(`table-cell-${0}-0`).should('contain.text', apiData[0].manifestShippingMethod);
      }
    });
  });

  it('File Upload', () => {
    const fileName = 'addInventory/sampleFileAddInventory.csv';

    cy.intercept('POST', config.addInventory.upload).as('uploadAddInventory');

    cy.getByCy('upload cutoff').click();
    cy.get('#upload-items-file').attachFile(fileName);

    cy.wait('@uploadAddInventory').then(({ response }) => {
      const apiData = response.body; // Capturing the API response body
      if (apiData?.meta?.displayMessage) {
        // uploading failed error is thrown from API
        cy.getByCy('cancel-fileupload-modal').click();
      } else if (apiData) {
        // Downloading error file
        cy.findByText(/Problems with CSV File/i).should('exist');
        cy.getByCy('ok_button').click();
      } else {
        cy.findByText(/Import Processed Successfully/i).should('exist');
        cy.getByCy('upload-success-ok-button').click();
      }
    });
  });
});
