import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getStockTakeCycleListingApi = (payload) => {
  const { page, pageSize, sortBy, sortOrder } = payload;

  const params = {
    page,
    size: pageSize,
    sort: `${sortBy},${sortOrder}`
  };

  if (payload.name) {
    params.name = payload.name;
  }

  if (payload.status) {
    params.status = payload.status;
  }

  if (payload.facilityCode) {
    params.facilityCode = payload.facilityCode;
  }

  if (payload.stockTakeId) {
    params.stockTakeId = payload.stockTakeId;
  }

  return tokenInstance().get(config.stockTake.list, { params });
};

export const createStockTakeCycleApi = (payload) =>
  tokenInstance().post(config.stockTake.create, payload);

export const uploadStockTakeCycleApi = (payload) => {
  const { id, formData } = payload;
  return tokenInstance().post(`${config.stockTake.upload}/${id}`, formData);
};

export const getStockTakeCycleConsolidatedStatusApi = (id) =>
  tokenInstance().get(`${config.stockTake.consolidatedStatus}/${id}`);

export const closeStockTakeCycleApi = (payload) =>
  tokenInstance().post(config.stockTake.closed, payload);

export const stockTakeStatusDownloadApi = (params) =>
  tokenInstance().get(config.stockTake.statusDownload, { params });

export const stockTakeIdDownloadApi = (params) =>
  tokenInstance().get(config.stockTake.idDownload, { params });
