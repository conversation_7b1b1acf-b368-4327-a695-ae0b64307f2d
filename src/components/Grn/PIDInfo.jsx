/* eslint-disable react/no-array-index-key */
/* eslint-disable max-len */
import React, { useState, useMemo, useEffect } from 'react';
import Slider from 'react-slick';
import './PIDInfo.scss';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Box from '@mui/material/Box';
// import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
// import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { useSelector } from 'react-redux';
import { getCurrencySumbol, roundUptoFixedDigits } from 'utils/helpers';
import { CATEGORY_MAPPING } from '../../config/CategoryMapping';
import CarouselModal from '../CarouselModal/CarouselModal';

const PIDInfo = (props) => {
  const { pidStatus, pid, createGrnLocal } = props;
  const [openCarousleModal, setOpenCarousleModal] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const { productSeachByDesc, pidDetail, meta } = useSelector((state) => state.grn);
  const currency = pidDetail?.result?.data?.currency;
  const [data, setData] = useState({});
  useEffect(() => {
    if (productSeachByDesc && meta) {
      const { product_details } = productSeachByDesc;
      const { po_items } = meta.po;
      const temp = po_items.filter(
        (t) => t.product_id.toString() === product_details[0]?.product_id?.toString()
      );
      if (temp.length > 0) {
        const { price_with_taxes, total_vendor_cost_price, quantity } = temp[0];
        product_details[0].unitTaxes = Math.ceil(
          (price_with_taxes - total_vendor_cost_price) / quantity
        );
      }
      setData(product_details[0]);
    }
  }, [productSeachByDesc, meta]);

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    beforeChange: (prev, next) => {
      setCurrentSlide(next);
    }
    // nextArrow: <ArrowForwardIosIcon fontSize="small" style={{ display: "block", background: "green" }} />,
    // prevArrow: <ArrowBackIosIcon fontSize="small" style={{ display: "block", background: "green" }} />
  };

  const toggleCarouselModal = (value) => setOpenCarousleModal(value);

  const categoryType = useMemo(() => {
    let result = 'other';

    const { categoryId } = pidDetail.result.data;
    result = categoryId === 11354 || categoryId === 11356 ? CATEGORY_MAPPING[categoryId] : 'other';

    return result;
  }, [pidDetail]);

  const pidStatusMemo = useMemo(() => {
    let temp = 'bg-f5';
    if (pidStatus === 'PASSED') {
      temp = 'bg-76';
    }
    if (pidStatus === 'FAILED') {
      temp = 'bg-red';
    }
    return temp;
  }, [pidStatus]);

  const totalCostValue = useMemo(() => {
    const unitTaxes = data?.unitTaxes || 0;
    const price = pidDetail?.result?.data?.price || 0;
    return roundUptoFixedDigits(unitTaxes + price);
  }, [data, pidDetail]);

  const convertedCurrency = getCurrencySumbol(currency);

  return (
    <Box className="pid-card border-radius-base border-ee" height="fit-content" position="relative">
      {/* bg-76 */}
      <div className={`pid-header   ${pidStatusMemo}`}>
        <div className={`text-b4 ${pidStatus !== 'PENDING' ? 'text-white' : ''}`}>
          {createGrnLocal.PID}
        </div>
        <a
          href={data?.product_url}
          className={`text-b3 text-link ${pidStatus !== 'PENDING' ? 'text-white' : ''}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          {pid}
        </a>
      </div>
      <div className="pid-body bg-white">
        {categoryType !== 'prescription-lens' && (
          <div className="carousel-container  pd-l20 pd-r20">
            <Slider {...settings}>
              {data?.image_urls &&
                data.image_urls.map((imgpath) => (
                  <div
                    key={imgpath}
                    onClick={() => toggleCarouselModal(true)}
                    className="image-container"
                  >
                    <img src={imgpath} alt="img" style={{ height: '100px', width: '100%' }} />
                  </div>
                ))}
            </Slider>
          </div>
        )}
        {categoryType !== 'other' && (
          <div className="display-flex justify-content-space-between  mr-b20 mr-l16 mr-r16 mr-t8 border-grey5-radiusbase pd-10">
            <div>
              <div className="fs12 text-66">{createGrnLocal.SPH}</div>
              <div className="fs14 text-a2 fw-bold">{data?.sph}</div>
            </div>
            <div>
              <div className="fs12 text-66">{createGrnLocal.CYL}</div>
              <div className="fs14 text-94 fw-bold">{data?.cyl}</div>
            </div>
            <div>
              <div className="fs12 text-66">{createGrnLocal.AP}</div>
              <div className="fs14 text-22 fw-bold">{data?.ap}</div>
            </div>
            <div>
              <div className="fs12 text-66">{createGrnLocal.ED}</div>
              <div className="fs14 text-28 fw-bold">{data?.ed}</div>
            </div>
          </div>
        )}

        <Box className="mr-l16 mr-t4 mr-r16 fw-bold fs14" width={193} mb={2}>
          {categoryType === 'prescription-lens' ? data?.name?.split('_S')[0] : data?.name}
        </Box>

        {categoryType === 'other' && (
          <div className="pid-content text-b4 display-flex flex-direction-column justify-content-space-between text-b4">
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.MODEL_NUMBER}</div>
              <div className="h7 fw-bold">{data?.model_number}</div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.COLOR_CODE}</div>
              <div className="h7 fw-bold">{data?.product_color}</div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.MRP}</div>
              <div className="h7 fw-bold">
                {convertedCurrency}
                {roundUptoFixedDigits(data?.market_price || 0)}
              </div>
            </div>
          </div>
        )}

        {categoryType === 'prescription-lens' && (
          <div className="pid-content text-b4 display-flex flex-direction-column justify-content-space-between text-b4">
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.INDEX}</div>
              <div className="h7 fw-bold">{data?.index}</div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.PD}</div>
              <div className="h7 fw-bold">{data?.product_color}</div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.AXIS}</div>
              <div className="h7 fw-bold">{data?.axis}</div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.AXIS_TYPE}</div>
              <div className="h7 fw-bold">{data?.axis_type}</div>
            </div>
          </div>
        )}

        <Box position="absolute" bottom={0} width="100%">
          <Box className="pid-content text-b4 display-flex flex-direction-column justify-content-space-between text-b4">
            <div className="pd-t12 pd-b12 mr-l16 mr-r16  flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.UNIT_COST_PRICE}</div>
              <div className="fs14 ">
                {convertedCurrency}
                {roundUptoFixedDigits(pidDetail?.result?.data?.price || 0)}
              </div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16 border-bottom-ee flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.UNIT_TAXES}</div>
              <div className="fs14 ">
                {convertedCurrency}
                {roundUptoFixedDigits(data?.unitTaxes || 0)}
              </div>
            </div>
            <div className="pd-t12 pd-b12 mr-l16 mr-r16  flex justify-content-space-between display-flex">
              <div className="text-33 fs12">{createGrnLocal.TOTAL_COST}</div>
              <div className="fs14 fw-bold">
                {convertedCurrency}
                {totalCostValue}
              </div>
            </div>
          </Box>
        </Box>
      </div>
      {openCarousleModal && (
        <CarouselModal
          data={data?.image_urls || []}
          pid={pid}
          currentSlide={currentSlide}
          open={openCarousleModal}
          openModal={toggleCarouselModal}
        />
      )}
    </Box>
  );
};
export default PIDInfo;
