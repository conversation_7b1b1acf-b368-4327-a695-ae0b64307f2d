import { publicInstance } from 'utils/axiosInstance';
import config from '../config';

export const regsiterApi = (payload) => {
  const defaultPayload = {
    department: 'customer-support',
    userType: 'customer-support',
    accesses: {
      'nexs-analytics': {
        ALL: ['nexs-analytics-super-user', 'nexs-facilities-monitor-panel']
      }
    }
  };
  return publicInstance().post(config.user.register, { ...defaultPayload, ...payload });
};
