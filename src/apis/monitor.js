/* eslint-disable no-param-reassign */
import { BINARY_VALUES, MONITOR_TYPES } from 'config/monitor';
import { invert, keys } from 'utils/lodash';
import config from '../config';
import { tokenInstance } from '../utils/axiosInstance';

const queryParams = Object.fromEntries(new URLSearchParams(window.location.search));
export const updateFilterFormat = (filters, version) => {
  let filterPayload = {
    binaryFilter: {},
    singleSelectFilters: {},
    monitorPanelRangeFilters: {},
    multiSelectFilters: {}
  };
  const REVERSE_VALUE_MAPPING = invert(BINARY_VALUES);
  if (version === 'v2' || version === 'v3') {
    const renderFiltersBinary = keys(filters.BINARY).reduce((prev, key) => {
      if (version === 'v3') {
        if (filters.BINARY[key] !== null) {
          prev[REVERSE_VALUE_MAPPING[key]] = filters.BINARY[key];
        }
      } else if (version === 'v2') {
        if (filters.BINARY[key]) {
          if (key === 'NON_ASRS') {
            prev.isAsrsOrder = false;
          } else {
            prev[REVERSE_VALUE_MAPPING[key]] = filters.BINARY[key];
          }
        }
      }

      return prev;
    }, {});
    // const REVERSE_VALUE_MAPPING_COUNTRY = invert(COUNTRY_VALUES);
    // const renderFiltersCountry = filters.COUNTRY.reduce((prev, key) => {
    //   prev.push(REVERSE_VALUE_MAPPING_COUNTRY[key]);
    //   return prev;
    // }, []);
    filterPayload = {
      binaryFilter: renderFiltersBinary,
      singleSelectFilters: {
        errorType: filters.HOLDED_ORDERS
      },
      monitorPanelRangeFilters: {
        ageingSinceCreated: filters.AGEING,
        ageingSinceLastUpdate: filters.UPDATE_AGEING,
        date: { startValue: filters.DATE_RANGE.from, endValue: filters.DATE_RANGE.to }
      },
      multiSelectFilters: {
        // orderChannel: filters.CHANNEL,
        pickingPriority: filters.PICKING_PRIORITY,
        qcStatus: filters.QC_STATUS,
        itemType: filters.ITEM_TYPE
        // country: renderFiltersCountry,
        // lensPackageType: filters.PACKAGE
      }
    };
  }
  return filterPayload;
};

export const getMonitorDataAPI = ({ filters, version }) => {
  const filterPayload = updateFilterFormat(filters, version);
  let url = config.monitor.getMonitorList;
  if (version === 'v3') {
    url = config.monitor.getMonitorListv3;
  }
  return tokenInstance().post(
    url,
    {
      monitorPanelFilters: filterPayload
    },
    { params: { version, ...queryParams } }
  );
};

export const getJITMonitorDataAPI = ({ filters, version }) => {
  const filterPayload = updateFilterFormat(filters, version);
  const url = config.monitor.getJITMonitorList;
  return tokenInstance().post(
    url,
    {
      monitorPanelFilters: filterPayload
    },
    { params: queryParams }
  );
};

export const getMonitorTagDetailsAPI = (payload) => {
  const { filters, ...data } = payload;
  const filterPayload = updateFilterFormat(filters, payload.version);

  // @TODO refactor the urls
  let url = `${config.monitor.getMonitorTagDetailsList}?version=${payload.version}`;

  if (payload.category === MONITOR_TYPES.JIT_ORDERS) {
    url = config.monitor.getJITMonitorDetail;
  } else if (payload.version === 'v3') {
    url = `${config.monitor.getMonitorTagDetailsListV3}?version=${payload.version}`;
  }

  return tokenInstance().post(
    url,
    {
      ...data,
      monitorPanelFilters: filterPayload
    },
    { params: queryParams }
  );
};

export const getMonitorFiltersApi = () =>
  tokenInstance().get(config.monitor.getFiltersList, { params: queryParams });

export const saveMonitorFiltersApi = ({ monitorPanelSavedViews }) =>
  tokenInstance().post(config.monitor.saveFiltersList, monitorPanelSavedViews, {
    params: queryParams
  });

export const getOrdersCountApi = () => tokenInstance().get(config.monitor.getOrdersCount);

export const getTotalOrderApi = (payload) =>
  tokenInstance().post(config.monitor.totalOrderData, payload);
