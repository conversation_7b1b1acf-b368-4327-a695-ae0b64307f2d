import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getPrintShipmentUAEapi = ({ shippingPackageId }) =>
  tokenInstance().get(`${config.apiPath.printShipmentUAE}?shippingPackageId=${shippingPackageId}`, {
    responseType: 'arraybuffer'
  });

export const getPrintShipmentDetailApi = (payload) => {
  const { shippingPackageId, cartonBarcode, scanType, forcePrint = false } = payload;

  const params = new URLSearchParams({
    shippingPackageId,
    forcePrint
  });

  let URL = `${config.apiPath.printShipmentNew}?${params}`;
  if (scanType === 'carton') {
    URL = `${config.apiPath.printMonotCartonShipment}/${cartonBarcode}`;
  }

  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getPrintShipmentMPDTCApi = ({ shippingPackageId, channel }) =>
  tokenInstance().get(config.apiPath.printShipmentMPDTC, {
    responseType: 'arraybuffer',
    params: {
      shippingPackageId,
      channel
    }
  });

export const printShipmentB2BApi = (payload) => {
  const { unicomOrderCode } = payload;
  const URL = `${config.apiPath.printShipmentB2B}${unicomOrderCode}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const printLensDetailsSticker = (fittingId) =>
  tokenInstance().get(`${config.apiPath.lensLabPrintSticker}/${fittingId}`, {
    responseType: 'arraybuffer'
  });

export const printJobCardApi = ({ facilityCode, tryId }) =>
  tokenInstance().get(`${config.apiPath.jobCardPrint}/${facilityCode}/${tryId}`, {
    responseType: 'arraybuffer'
  });

export const printPackSlipApi = ({ shippingPackageId }) =>
  tokenInstance().get(config.apiPath.newPackSlip, { params: { entity: shippingPackageId } });

export const printMpShipmentDummyApi = (payload) =>
  tokenInstance().get(`${config.apiPath.printMpShipmentDummy}?shippingPackageId=${payload}`);
