import config from '../config';
import { tokenInstance } from '../utils/axiosInstance';

export const getReportMetaDataApi = ({ reportType }) =>
  tokenInstance().get(`${config.reportingTool.getReportMetaData}?reportType=${reportType}`);

export const generateReportApi = ({ payload }) =>
  tokenInstance().post(config.reportingTool.generateReport, payload);

export const getReportHistoryListApi = (payload) =>
  tokenInstance().post(config.reportingTool.reportHistoryList, payload);
