import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import Autocomplete from '@mui/material/Autocomplete';

import LkCard from 'components/MaterialUi/LkCard';
import LkInput from 'components/MaterialUi/LkInput';
import { useParams, useNavigate } from 'react-router-dom';
import { LOCALISATION } from 'redux/reducers/localisation';
import { searchVendorList } from 'redux/actionCreators/invoiceReferenceCreation';
import { formatDateDashedSeparated, debounce, numberOnly } from 'utils/helpers';
import Spinner from 'components/Spinner/Spinner';
import { toastOpen } from 'redux/actionCreators/toast';
import { Box } from '@mui/material';
import FormValidator from 'components/Validations/FormValidator';
import useForms from 'common/useForms';
import { keys, omit } from 'utils/lodash';
import {
  createVendorShipmentLoad,
  createVendorShipmentReset,
  getDetailsByShipmentLoad,
  getNewQtyPayload,
  getPoListByVendorLoad
} from 'redux/reducers/vendorShipment';
import { LOGIN } from 'redux/reducers/login';
import { AdditionalFields, ValidationRules } from './ValidationRules';

const useStyles = makeStyles()((theme) => ({
  root: {
    paddingTop: theme.spacing(5),
    display: 'flex',
    justifyContent: 'center'
  },
  card: {
    height: 750,
    margin: 'auto'
  },
  cardComponent: {
    paddingTop: 40,
    borderRadius: 12,
    transition: '0.3s',
    boxShadow: '0 8px 16px rgba(0,0,0,0.05)'
  },
  header: {
    textAlign: 'center'
  },
  horizontalCenter: {
    display: 'flex',
    justifyContent: 'center',
    marginBottom: 22
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  content: {
    marginTop: 15
  },
  field: {
    width: 300
  },
  addOnField: {
    width: 200
  },
  button: {
    textAlign: 'center',
    marginTop: 30,
    marginBottom: 40
  },
  verticalMiddle: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  },
  invoiceData: {
    width: '158px'
  },
  additionalFieldContainer: {
    margin: '20px',
    borderTop: '1px solid #EEEEEE'
  },
  additionalFieldHeading: {
    display: 'flex',
    justifyContent: 'center',
    fontSize: '16px',
    fontWeight: '700',
    margin: '20px'
  },
  additionalFields: {
    display: 'grid',
    gridTemplateColumns: 'repeat(3, 1fr)',
    gridTemplateRows: 'repeat(2, 1fr)',
    gridColumnGap: '20px',
    gridRowGap: '20px',
    paddingTop: '10px'
  }
}));

const initialFormData = {
  vendor_id: '',
  po_num: '',
  invoiceNumber: '',
  invoice_date: '',
  shipment_quantity: '',
  expected_dispatch_date: '',
  expected_arrival_date: ''
};

const initialOptionalFromData = {
  actual_dispatch_date: '',
  bill_of_entry_date: '',
  custom_clearance_date: '',
  wh_landing_date: '',
  shipment_value: '',
  shipment_type: ''
};

const CreateVendorShipment = () => {
  const { classes, cx } = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CREATE_VENDOR_SHIPMENT);
  const validator = new FormValidator(ValidationRules);
  const { type, shipmentId } = useParams();

  const [optionalForm, setOptionalForm] = useState(initialOptionalFromData);
  const empCode = useSelector((state) => state[LOGIN].user.userDetail?.empCode);
  const facility_code = useSelector((state) => state.settings.selectedFacility);
  const { vendorList } = useSelector((state) => state.invoiceReferenceCreation);

  const { data: poListByVendor, isLoading: poListByVendorLoading } = useSelector(
    (state) => state.vendorShipment.poListByVendor
  );
  const { isLoading: createVendorLoading, data: createdVendorData } = useSelector(
    (state) => state.vendorShipment.createVendorShipment
  );
  const shipmentDetails = useSelector((state) => state.vendorShipment.shipmentDetails);

  const { form, errors, onChangeForm, setForm, setErrors } = useForms(
    ValidationRules,
    initialFormData
  );

  const suggetionLists = useMemo(() => {
    const vendorListOptions = vendorList.map((item) => item?.code);
    return {
      vendor_id: vendorListOptions,
      po_num: poListByVendor
    };
  }, [vendorList, poListByVendor]);

  const handleSearch = (e, name) => {
    if (e.length < 3) {
      return;
    }
    if (name === 'vendor_id') {
      dispatch(searchVendorList(e));
    }
  };

  useEffect(() => {
    if (type === 'edit' && Object.keys(shipmentDetails).length > 0) {
      const {
        poNum,
        vendorId,
        shipmentQuantity,
        expectedDispatchDate,
        actualDispatchDate,
        billOfEntryDate,
        customClearanceDate,
        whLandingDate,
        shipmentValue,
        shipmentType,
        vendorInvoiceNumber,
        invoiceDate
      } = shipmentDetails;
      setForm((prev) => ({
        ...prev,
        vendor_id: vendorId,
        po_num: poNum,
        shipment_quantity: shipmentQuantity,
        vendor_invoice_number: vendorInvoiceNumber,
        invoice_date: invoiceDate ? formatDateDashedSeparated(invoiceDate) : '',
        expected_dispatch_date: expectedDispatchDate
          ? formatDateDashedSeparated(expectedDispatchDate)
          : '',
        expected_arrival_date: expectedDispatchDate
          ? formatDateDashedSeparated(expectedDispatchDate)
          : ''
      }));
      setOptionalForm((prev) => ({
        ...prev,
        actual_dispatch_date: actualDispatchDate
          ? formatDateDashedSeparated(actualDispatchDate)
          : '',
        bill_of_entry_date: billOfEntryDate ? formatDateDashedSeparated(billOfEntryDate) : '',
        wh_landing_date: whLandingDate ? formatDateDashedSeparated(whLandingDate) : '',
        custom_clearance_date: customClearanceDate
          ? formatDateDashedSeparated(customClearanceDate)
          : '',
        shipment_value: shipmentValue ?? '',
        shipment_type: shipmentType
      }));
    } else {
      setForm(initialFormData);
    }
  }, [shipmentDetails, type]);

  useEffect(() => {
    if (shipmentId) {
      dispatch(getDetailsByShipmentLoad({ shipment_id: shipmentId }));
    }
  }, [shipmentId]);

  useEffect(() => {
    if (Object.keys(createdVendorData).length > 0) {
      const targetPath =
        type === 'create'
          ? `/vendorShipment/details/${createdVendorData?.shipment_id}`
          : `/vendorShipmentDetails/${shipmentId}`;

      navigate(targetPath);
      dispatch(createVendorShipmentReset());
    }
  }, [createdVendorData, type]);

  const removeEmptyValues = (obj) =>
    Object.fromEntries(
      Object.entries(obj).filter(
        // eslint-disable-next-line no-unused-vars
        ([_, value]) => value !== undefined && value !== null && value !== ''
      )
    );

  const isOptionalHasValues = useMemo(
    () => keys(optionalForm).some((key) => !!optionalForm[key]),
    [optionalForm]
  );

  const handleSubmitClicked = () => {
    const validationStatus = validator.validateForm(form);
    let newValidation = { ...validationStatus };
    if (form?.po_num && !isOptionalHasValues) {
      newValidation = {
        ...newValidation,
        errors: { ...omit(newValidation?.errors, 'vendor_invoice_number') }
      };
    } else if (!form?.vendor_invoice_number || !form?.invoice_date) {
      newValidation = {
        ...newValidation,
        errors: {
          ...newValidation.errors,
          vendor_invoice_number: CONTENT.PLEASE_ENTER_INVOICE_NUMBER,
          invoice_date: CONTENT.PLEASE_ENTER_INVOICE_DATE
        }
      };
    }
    if (form?.vendor_invoice_number) {
      newValidation = {
        ...newValidation,
        errors: { ...omit(newValidation?.errors, 'po_num') }
      };
    }
    if (keys(newValidation?.errors).length > 0) {
      setErrors(validationStatus.errors);
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: `${CONTENT.PLEASE_FILL_ALL_THE_REQUIRED_FIELDS}....`,
          severity: 'error'
        })
      );
    } else {
      const removedEmptyValues = removeEmptyValues({ ...form, ...optionalForm });
      let payload = {
        ...removedEmptyValues,
        created_by: empCode,
        updated_by: empCode,
        facility_code,
        version: type === 'create' ? 0 : 1
      };
      if (type === 'edit') {
        payload = {
          ...payload,
          id: shipmentDetails?.id,
          shipment_id: shipmentDetails?.shipmentId
        };
      }
      if (
        type === 'edit' &&
        Number(form?.shipment_quantity) !== Number(shipmentDetails?.shipmentQuantity)
      ) {
        dispatch(getNewQtyPayload(payload));
        navigate(`/vendorShipmentDetails/${shipmentId}?newQty=${true}`);
      } else {
        dispatch(createVendorShipmentLoad({ createPayload: payload }));
      }
    }
  };

  const handleOptionalChange = (e, optionalFieldData) => {
    const newValue = e.target.value;
    setOptionalForm((prev) => ({
      ...prev,
      [optionalFieldData?.field]: newValue
    }));
  };

  const getPoListByVendor = (e, key) => {
    if (key === 'vendor_id') {
      dispatch(getPoListByVendorLoad({ vendorName: e.target.value }));
    }
    return 0;
  };

  const delayedQuery = useCallback(
    debounce((e, name) => e.length > 0 && handleSearch(e, name), 500),
    []
  );

  return (
    <div className={classes.root} data-cy="create-vendor-container">
      <LkCard width={750}>
        <Typography className={classes.header} variant="h2" data-cy="card-title">
          {type === 'create' ? CONTENT.CREATE_SHIPMENT : CONTENT.UPDATE_SHIPMENT}
        </Typography>
        <CardContent className={classes.content}>
          <Box className="display-flex flex-direction-column gap20 align-items-center">
            {ValidationRules.map((item, index) => {
              if (item.type === 'autoComplete') {
                return (
                  <Autocomplete
                    fullWidth
                    className={classes.field}
                    key={item.field}
                    options={suggetionLists[item.field] || []}
                    value={form[item.field]}
                    onKeyUp={(e) => delayedQuery(e.target.value, item.field)}
                    onInputChange={(event, newInputValue) => {
                      onChangeForm(null, item.field, newInputValue);
                    }}
                    disabled={item.field === 'po_num' ? !form.vendor_id : false}
                    onBlur={(e) => getPoListByVendor(e, item?.field)}
                    renderInput={(params) => (
                      <LkInput
                        {...params}
                        className="input-box"
                        label={CONTENT[item.label]}
                        error={
                          !!(item.field === 'po_num'
                            ? errors?.po_num && !form?.vendor_invoice_number
                            : !!errors[item?.field])
                        }
                        helperText={
                          item.field === 'po_num'
                            ? errors?.po_num && !form?.vendor_invoice_number && errors[item?.field]
                            : errors[item?.field]
                        }
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {poListByVendorLoading ? <Spinner size="15px" /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          )
                        }}
                      />
                    )}
                  />
                );
              }
              if (index === 2) {
                return (
                  <Box
                    key={item.field}
                    className="display-flex justify-content-space-between gap10"
                    width="300px"
                  >
                    <LkInput
                      className={classes.invoiceData}
                      name={item.field}
                      id="invoiceNumberInput"
                      value={form?.vendor_invoice_number ?? ''}
                      error={
                        !!((errors?.po_num || isOptionalHasValues) && !form?.vendor_invoice_number)
                      }
                      helperText={
                        (errors?.po_num || isOptionalHasValues) && !form?.vendor_invoice_number
                          ? CONTENT.PLEASE_ENTER_INVOICE_NUMBER
                          : ''
                      }
                      InputLabelProps={{
                        shrink: true
                      }}
                      label={CONTENT[item.label]}
                      onChange={onChangeForm}
                      variant="outlined"
                    />
                    <LkInput
                      name="invoice_date"
                      InputLabelProps={{
                        shrink: true
                      }}
                      error={!!((errors?.po_num || isOptionalHasValues) && !form?.invoice_date)}
                      helperText={
                        (errors?.po_num || isOptionalHasValues) && !form?.invoice_date
                          ? CONTENT.PLEASE_ENTER_INVOICE_DATE
                          : ''
                      }
                      id="invoiceDateInput"
                      label={CONTENT.ENTER_INV_DATE}
                      onChange={onChangeForm}
                      type="date"
                      value={form?.invoice_date || ''}
                      variant="outlined"
                    />
                  </Box>
                );
              }
              if (item.type === 'date') {
                return (
                  <Box key={item.field} width="300px">
                    <LkInput
                      name={item.field}
                      fullWidth
                      value={form[item.field] || ''}
                      InputLabelProps={{
                        shrink: true
                      }}
                      id={item.field}
                      error={!!errors[item?.field]}
                      helperText={errors[item?.field]}
                      label={CONTENT[item.label]}
                      onChange={onChangeForm}
                      type="date"
                      variant="outlined"
                    />
                  </Box>
                );
              }
              return (
                <Box key={item.field} width="300px">
                  <LkInput
                    fullWidth
                    name={item.field}
                    id={item.field}
                    value={form[item.field] || ''}
                    error={!!errors[item?.field]}
                    helperText={errors[item?.field]}
                    label={CONTENT[item.label]}
                    onKeyPress={(e) => numberOnly(e)}
                    onChange={onChangeForm}
                    variant="outlined"
                  />
                </Box>
              );
            })}
          </Box>
          <Box className={classes.additionalFieldContainer}>
            <Box className={classes.additionalFieldHeading}>
              {CONTENT.ADDITIONAL_FIELDS} ({CONTENT.OPTIONAL})
            </Box>
            <Box className={classes.additionalFields}>
              {AdditionalFields.map((item) => {
                if (item.type === 'date') {
                  return (
                    <Box key={item.field}>
                      <LkInput
                        name={item.field}
                        fullWidth
                        id={item.field}
                        InputLabelProps={{
                          shrink: true
                        }}
                        value={optionalForm[item.field]}
                        label={CONTENT[item.label]}
                        onChange={(e) => handleOptionalChange(e, item)}
                        type="date"
                        variant="outlined"
                      />
                    </Box>
                  );
                }
                return (
                  <Box key={item.field}>
                    <LkInput
                      fullWidth
                      name={item.field}
                      id={item.field}
                      label={CONTENT[item.label]}
                      value={optionalForm[item.field]}
                      onChange={(e) => handleOptionalChange(e, item)}
                      variant="outlined"
                    />
                  </Box>
                );
              })}
            </Box>
          </Box>

          <div className={cx(classes.horizontalCenter, classes.button)}>
            {createVendorLoading ? (
              <Button
                style={{ borderRadius: '8px', width: '114px' }}
                color="primary"
                disabled={createVendorLoading}
                variant="contained"
              >
                <Spinner />
              </Button>
            ) : (
              <Button
                style={{ borderRadius: '8px' }}
                color="primary"
                onClick={() => handleSubmitClicked()}
                variant="contained"
                data-cy="submit-btn"
              >
                {type === 'create' ? CONTENT.CREATE : CONTENT.UPDATE}
              </Button>
            )}
          </div>
        </CardContent>
      </LkCard>
    </div>
  );
};

export default CreateVendorShipment;
