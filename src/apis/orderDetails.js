import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';
// OVERVIEW API
export const orderDetailsOverviewAPI = (payload) => {
  const URL = `${config.orderDetails.overviewAPI}?id=${payload}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data);
};

// Auto to manual api
export const autoToManualApi = (payload) =>
  tokenInstance().post(config.orderDetails.autoToManual, payload);

// Reassign API
export const reassignAutoToManualApi = (payload) =>
  tokenInstance().post(config.orderDetails.reassign, payload);

// COURIER LIST
export const courierListApi = (payload) => {
  const { country, pincode, shipmentId } = payload;
  const url = config.orderDetails.courierList;
  return tokenInstance().get(`${url}/${shipmentId}/serviceability`, {
    params: {
      country,
      pincode
    }
  });
};

// SHIPMENT REASSIGN
export const shipmentReassignApi = (payload) =>
  tokenInstance().post(config.orderDetails.shipmentReassign, payload);

// ORDER STOCK OUT
export const orderStockOutApi = (payload) => {
  const { id, barcode } = payload;
  const url = `${config.orderDetails.barcodeStockOut}/${id}/${barcode}`;
  return tokenInstance().post(url);
};

// ORDER STOCK OUT FALLBACK
export const orderStockOutFallbackApi = (payload) => {
  const url = `${config.orderDetails.barcodeStockOutFallback}`;
  return tokenInstance().put(url, payload);
};

// ITEMS API
export const orderDetailsItemsAPI = (payload) => {
  const URL = `${config.orderDetails.itemsAPI}?id=${payload}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data);
};

// HEADER API
export const orderDetailsHeaderAPI = (payload) => {
  const URL = `${config.orderDetails.headerAPI}?id=${payload}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response);
};

// SHIPMENT ITEMS API
export const getItemsOrderList = ({ shipmentId }) =>
  tokenInstance().get(`${config.orderDetails.itemsOrderListAPI}/${shipmentId}`);

// SHIPMENT API
export const OrderDetailsShipmentAPI = (payload) => {
  const URL = `${config.orderDetails.shipmentAPI}?id=${payload}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data);
};

// PRINT INVOICE
export const OrderDetailsPrintInvoiceAPI = (payload) => {
  const URL = `${config.orderDetails.invoiceAPI}?shippingPackageId=${payload}`;
  return tokenInstance({ panel: 'orderDetails' })
    .get(URL, { responseType: 'arraybuffer' })
    .then((response) => response.data);
};

// INVOICE TAB API
export const OrderDetailsInvoiceDataAPI = (payload) => {
  const URL = `${config.orderDetails.invoiceTabAPI}?id=${payload}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data);
};

export const orderDetailsVMSStatusApi = (payload) =>
  tokenInstance().get(`${config.orderDetails.vmsStatus}/${payload.id}/${payload.shipmentId}`);

export const orderDetailsHistorysApi = (payload) =>
  tokenInstance().get(`${config.orderDetails.orderHistory}?entity=${payload}`);

export const getNotFoundStatusAPI = (payload) =>
  tokenInstance().get(`${config.orderDetails.getNotFoundStatus}/${payload}`);

export const regularToJitOrderApi = (shippingPackageId) =>
  tokenInstance().post(
    `${config.orderDetails.regularToJitOrder}?shippingPackageId=${shippingPackageId}`
  );
