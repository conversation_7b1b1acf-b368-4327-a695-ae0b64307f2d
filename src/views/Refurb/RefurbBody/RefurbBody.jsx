import React, { useCallback, useEffect, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';

import { keys, omit } from 'utils/lodash';

import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import Button from '@mui/material/Button';

import LkInput from 'components/MaterialUi/LkInput';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import { useDispatch, useSelector } from 'react-redux';
import useWindowResize from 'common/useWindowResize';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import Spinner from 'components/Spinner/Spinner';
import { genericDateFormatted } from 'utils/helpers';
import { refurbBarcodeOperationRequest, refurbListRequest } from 'redux/reducers/refurb';
// eslint-disable-next-line max-len
import DateRangeAccordion from 'views/Monitor/MonitorBody/MonitorBodyFiltersAccordion/DateRangeAccordion';
import { COMPLETED, PENDING_GOOD_PUTAWAY } from 'config/refurb';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({
  contaner: {
    height: 'calc(100vh - 219px)',
    margin: 16,
    padding: 24,
    paddingBottom: 0,
    background: '#FFFFFF'
  },
  header: {
    marginBottom: 16,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  search: {
    width: 300,
    marginRight: 10
  },
  chipsContainer: {
    flexGrow: '1'
  },
  createPutawayBtn: {
    padding: '10px 15px'
  }
}));

const PAGE_SIZE = 30;

const initialSortByData = { order: 'DESC', id: 'updatedAt' };

const defaultRequest = (selectedFacility) => ({
  status: '',
  searchFields: {},
  orderByFields: {
    key: 'updatedAt',
    order: 'DESC'
  },
  pageSize: PAGE_SIZE,
  page: 0,
  facility: selectedFacility
});

const DATE_RANGE_OPTIONS = [
  { key: 'All Period', value: 'ALL PERIOD' },
  { key: 'Today', value: 'TODAY' },
  { key: 'Last 3 Days', value: 'LAST 3 DAYS' },
  { key: 'Last 5 Days', value: 'LAST 5 DAYS' },
  { key: 'Last 10 Days', value: 'LAST 10 DAYS' },
  { key: 'Last 15 Days', value: 'LAST 15 DAYS' },
  { key: 'Last 90 Days', value: 'LAST 90 DAYS' },
  { key: 'Custom Range', value: 'Custom Range' }
];

const RefurbBody = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { type } = useParams();
  const [tableHeight] = useWindowResize(window.innerHeight - 340);
  const { isLoading, data, totalCount } = useSelector((state) => state.refurb.refurbList);
  const {
    data: {
      DATE_RANGE: { from, to }
    }
  } = useSelector((state) => state.monitor.filters.selected);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.REFURBISHMENT);
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);
  const [pageRequest, setPageRequest] = useState(defaultRequest(selectedFacility));
  const [appliedFilter, setAppliedFilter] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const { barcodeOperationSuccess } = useSelector((state) => state.refurb);

  const FILTER_KEYS = {
    itemBarcode: CONTENT.BARCODE,
    productId: CONTENT.PID,
    itemLocation: CONTENT.LOCATION,
    updatedAt: CONTENT.UPDATED_AT
  };

  useEffect(() => {
    if (pageRequest.status || barcodeOperationSuccess) {
      const updatedSearchFields = pageRequest.searchFields;
      const filters = [];
      keys(FILTER_KEYS).map((value) => {
        if (updatedSearchFields[value]) {
          const filterObj = {
            key: value,
            keyValue: FILTER_KEYS[value],
            values: updatedSearchFields[value],
            disableDelete: false
          };
          if (value === 'updatedAt') {
            filterObj.type = 'DATE_RANGE';
            filterObj.disableDelete = true;
          }
          filters.push(filterObj);
        }
        return null;
      });
      setAppliedFilter(filters);
      dispatch(refurbListRequest(pageRequest));
    }
  }, [pageRequest]);

  useEffect(() => {
    if (barcodeOperationSuccess) {
      dispatch(refurbListRequest(pageRequest));
    }
  }, [barcodeOperationSuccess]);

  useEffect(() => {
    const newReqBody = defaultRequest(selectedFacility);
    let status = 'PENDING_REFURBISHMENT';
    if (type === COMPLETED) {
      status = 'REFURBISHED';
    } else if (type === PENDING_GOOD_PUTAWAY) {
      status = 'PUTAWAY_PENDING';
    }

    setPageRequest({
      ...newReqBody,
      page: 0,
      status
    });
  }, [type]);

  useEffect(() => {
    setPageRequest((prevReqBody) => {
      let updatedSearchFields;
      if (from) {
        updatedSearchFields = {
          ...prevReqBody.searchFields,
          updatedAt: {
            from,
            to
          }
        };
      } else {
        updatedSearchFields = omit(prevReqBody.searchFields, 'updatedAt');
      }
      return {
        ...prevReqBody,
        searchFields: updatedSearchFields,
        page: 0
      };
    });
  }, [from]);

  const searchData = useCallback(([{ key, value }]) => {
    setPageRequest((prevReqBody) => ({
      ...prevReqBody,
      searchFields: {
        ...prevReqBody.searchFields,
        [key]: value
      },
      page: 0
    }));
  }, []);

  const sortTable = (sortType, sortKey, page) => {
    setPageRequest((prevReqBody) => {
      if (
        pageRequest.page !== page ||
        pageRequest.sortOrder !== sortType ||
        pageRequest.sortBy !== sortKey
      ) {
        return {
          ...prevReqBody,
          orderByFields: {
            ...prevReqBody.orderByFields,
            key: sortKey,
            order: sortType
          },
          page
        };
      }
      return prevReqBody;
    });
  };

  const deleteFilter = (key) => {
    setPageRequest((prevReqBody) => {
      const updatedSearchFields = omit(prevReqBody.searchFields, key);
      return {
        ...prevReqBody,
        searchFields: updatedSearchFields,
        page: 0
      };
    });
  };

  const headerConfig = [
    {
      name: CONTENT.BARCODE,
      key: 'itemBarcode',
      style: { minWidth: 210, maxWidth: 210 },
      formatBody: useCallback(
        ({ itemBarcode }) => (
          <Link target="_blank" to={`/historyPage/${itemBarcode}`}>
            {itemBarcode}
          </Link>
        ),
        []
      ),
      filterData: {
        type: 'input',
        selectedFilterList: appliedFilter,
        columnName: 'itemBarcode',
        submitFilter: searchData
      }
    },
    {
      name: CONTENT.PID_UPPERCASE,
      key: 'productId',
      style: { minWidth: 120, maxWidth: 120 },
      formatBody: useCallback(({ productId }) => (
        <Link target="_blank" to={`/inventory/${productId}`}>
          {productId}
        </Link>
      )),
      filterData: {
        type: 'input',
        selectedFilterList: appliedFilter,
        columnName: 'productId',
        submitFilter: searchData
      }
    },

    {
      name: CONTENT.LOCATION,
      style: { minWidth: 200, maxWidth: 200 },
      key: 'itemLocation',
      filterData: {
        type: 'input',
        selectedFilterList: appliedFilter,
        columnName: 'itemLocation',
        submitFilter: searchData
      }
    },
    {
      name: CONTENT.STATUS,
      style: { minWidth: 240, maxWidth: 240 },
      key: 'status',
      align: 'center',
      supportSort: true,
      formatBody: useCallback(
        ({ status }) => (
          <Box>{status === 'PENDING_REFURBISHMENT' ? CONTENT.IN_REFURB : status}</Box>
        ),
        []
      )
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updatedAt',
      style: { maxWidth: 200, minWidth: 200 },
      supportSort: true,
      formatBody: useCallback(({ updatedAt }) => <Box>{genericDateFormatted(updatedAt)}</Box>, [])
    },
    {
      name: CONTENT.UPDATED_BY,
      key: 'updatedBy',
      supportSort: true,
      formatBody: useCallback(({ updatedBy }) => <div>{updatedBy}</div>)
    },
    {
      name: CONTENT.ACTION,
      key: 'action',
      align: 'center',
      formatBody: useCallback(({ itemBarcode, isProcessing = false }) => {
        if (isProcessing) {
          return <Spinner size="21px" />;
        }
        return (
          <Button
            color="primary"
            variant="text"
            onClick={() =>
              dispatch(
                refurbBarcodeOperationRequest({
                  itemBarcode,
                  actionType: type === COMPLETED ? 'BAD' : 'GOOD'
                })
              )
            }
          >
            {type === COMPLETED ? CONTENT.MARK_BAD : CONTENT.MARK_GOOD}
          </Button>
        );
      })
    }
  ];

  return (
    <Box className={classes.contaner}>
      <Box className={classes.header}>
        <LkInput
          className={classes.search}
          variant="outlined"
          label={CONTENT.SEARCH_BARCODE_HERE}
          placeholder={CONTENT.SERCH_BARCODE_HERE_PLACEHOLDER}
          value={searchValue}
          margin="none"
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              searchData([{ value: e.target.value, key: 'itemBarcode' }]);
              setSearchValue('');
            }
          }}
          size="medium"
          InputProps={{
            endAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
              </InputAdornment>
            )
          }}
        />
        <Box>
          <DateRangeAccordion dateOptions={DATE_RANGE_OPTIONS} />
        </Box>
        <Box className={classes.chipsContainer}>
          {appliedFilter.length > 0 ? (
            <FilterChipsV2 filters={appliedFilter} deleteData={deleteFilter} />
          ) : null}
        </Box>
        {type === COMPLETED ? (
          <Button
            size="large"
            color="primary"
            variant="contained"
            className={classes.createPutawayBtn}
            onClick={() => navigate('/createPutaway/refurbished')}
            startIcon={
              <img
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/qr-code-scan.svg`}
                alt="create putaway"
              />
            }
          >
            {CONTENT.CREATE_PUTAWAY}
          </Button>
        ) : null}
      </Box>
      <LkTable
        tableHeight={tableHeight}
        headerConfig={
          type === PENDING_GOOD_PUTAWAY
            ? [...headerConfig.slice(0, headerConfig.length - 1)]
            : headerConfig
        }
        isDataFetching={isLoading}
        dataRequestFunction={sortTable}
        tableData={data}
        totalRowsCount={totalCount}
        pageLimit={PAGE_SIZE}
        pageNumber={pageRequest.page}
        initialSortBy={initialSortByData}
      />
    </Box>
  );
};

export default RefurbBody;
