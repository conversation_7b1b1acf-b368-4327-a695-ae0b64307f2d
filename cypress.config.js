/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig } from 'cypress';
import { loadEnv, mergeConfig } from 'vite';
import { devServer } from '@cypress/vite-dev-server';
import codeCoverageTask from '@cypress/code-coverage/task';
import cypressSonarqubeReporter from 'cypress-sonarqube-reporter/mergeReports';
import istanbul from 'vite-plugin-istanbul';
import viteConfig from './vite.config';

export default defineConfig({
  e2e: {
    baseUrl: 'https://ui.nexs.local.lenskart.com:3000',
    video: false,
    screenshotOnRunFailure: false
  },
  env: {
    USERNAME: process.env.VITE_USERNAME,
    PASSWORD: process.env.VITE_PASSWORD
  },
  reporter: 'cypress-multi-reporters',
  reporterOptions: {
    reporterEnabled: 'spec,mocha-junit-reporter,cypress-sonarqube-reporter',
    mochaJunitReporterReporterOptions: {
      mochaFile: 'coverage/junit/results-[hash].xml'
    },
    cypressSonarqubeReporterReporterOptions: {
      outputDir: 'coverage/sonarqube',
      mergeFileName: 'sonarqubeReport.xml',
      mergeOutputDir: 'coverage/sonarqube'
    },
    overwrite: true
  },
  component: {
    projectId: '7p9mrf',
    chromeWebSecurity: false,
    devServer(devServerConfig) {
      const conf = {
        define: {
          'process.env': loadEnv('development', process.cwd(), 'VITE')
        },
        plugins: [
          istanbul({
            cypress: true,
            requireEnv: false,
            exclude: ['node_modules', 'cypress', 'dist', 'build'],
            forceBuildInstrument: true
          })
        ],
        server: {
          ...viteConfig('development').server,
          port: 3003,
          https: false,
          hmr: {
            port: 3003,
            host: 'localhost',
            protocol: 'ws'
          },
          watch: {
            usePolling: true,
            interval: 500
          }
        },
        optimizeDeps: {
          esbuildOptions: {
            loader: {
              '.js': 'jsx'
            }
          }
        },
        publicDir: false
      };
      const resolvedViteConfig = mergeConfig(viteConfig('development'), conf);

      const filteredPlugins = resolvedViteConfig.plugins
        .flat()
        .filter((plugin) => plugin.name !== 'vite:basic-ssl');

      resolvedViteConfig.plugins = [filteredPlugins];

      return devServer({
        ...devServerConfig,
        framework: 'react',
        bundler: 'vite',
        viteConfig: resolvedViteConfig
      });
    },
    numTestsKeptInMemory: 1,
    specPattern: 'src/**/test/*.cy.{js,jsx}',
    supportFile: 'cypress/support/component.jsx',
    setupNodeEvents(on, config) {
      on('after:run', (results) => cypressSonarqubeReporter(results));
      codeCoverageTask(on, config);
      return config;
    }
  },
  video: false,
  screenshotOnRunFailure: false,
  viewportWidth: 1440,
  viewportHeight: 900
});
