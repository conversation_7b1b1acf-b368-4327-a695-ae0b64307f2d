import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

// export const getPickingConfig = () => tokenInstance().get(config.supervisor.getPickingConfig);

export const getCategories = () =>tokenInstance().get(config.supervisor.getAssignmentCategories);

export const getLocation = () => tokenInstance().get(config.supervisor.getAssignmentLocation);

export const getUserLocationCategory = ({payload}) =>
  tokenInstance().post(config.supervisor.getLocationCategoryUser, payload);

export const addUserLocationcategory = (payload) =>
  tokenInstance().post(config.supervisor.addLocationCategoryForUser, payload);

// ------------ Dummy API ------------
// export const getCategories = () => ({
//   data: {
//     data: [
//       { id: 1, categoryName: 'FR0' },
//       { id: 2, categoryName: 'FR1' },
//       { id: 3, categoryName: 'FR2' }
//     ],
//     meta: null
//   }
// });

// export const getLocation = () => ({
//   data: {
//     data: [
//       { id: 1, locationName: 'sunglasses' },
//       { id: 2, locationName: 'prescription lens' },
//       { id: 3, locationName: 'eyeframe' }
//     ]
//   }
// });

// export const getUserLocationCategory = () => ({
//   data: {
//     data: {
//       count: 1,
//       totalCount: 1,
//       data: [
//         {
//           user: {
//             id: 1,
//             empCode: 'LSP05067',
//             name: 'maitri',
//             facility: {
//               id: 2,
//               facilityCode: '0QNXS',
//               createdAt: '2022-11-22T06:19:06.000+00:00',
//               createdBy: null
//             },
//             email: '<EMAIL>',
//             roleName: 'agent',
//             active: true,
//             createdAt: null,
//             updatedAt: '2022-11-22 09:52:33',
//             createdBy: null,
//             updatedBy: null
//           },
//           count: 4,
//           categoryLocationMappingMap: {
//             fjerhifer: {
//               pickingCategoryResponse: {
//                 id: 3,
//                 categoryName: 'fjerhifer',
//                 active: true,
//                 facilityId: 2
//               },
//               pickingLocationResponses: [
//                 {
//                   id: 1,
//                   locationName: 'FR0',
//                   facilityId: 2,
//                   active: true,
//                   fmsLocationId: null
//                 },
//                 {
//                   id: 2,
//                   locationName: 'FR1',
//                   facilityId: 2,
//                   active: true,
//                   fmsLocationId: null
//                 }
//               ]
//             },
//             hcdjh: {
//               pickingCategoryResponse: {
//                 id: 2,
//                 categoryName: 'hcdjh',
//                 active: true,
//                 facilityId: 2
//               },
//               pickingLocationResponses: [
//                 {
//                   id: 1,
//                   locationName: 'FR0',
//                   facilityId: 2,
//                   active: true,
//                   fmsLocationId: null
//                 },
//                 {
//                   id: 2,
//                   locationName: 'FR1',
//                   facilityId: 2,
//                   active: true,
//                   fmsLocationId: null
//                 }
//               ]
//             }
//           },
//           enable: false
//         }
//       ]
//     }
//   },
//   meta: null
// });

// TODO: currently we are not moving forward with the picking config

export const getPickingConfig = () =>
  new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: [
          {
            location: 'EyeFrame',
            category: [
              { categoryName: 'FR0', isActive: true },
              { categoryName: 'FR1-JIT', isActive: false },
              { categoryName: 'FR2-JIT', isActive: true },
              { categoryName: 'FR1-NON-JIT', isActive: true },
              { categoryName: 'PL', isActive: false }
            ]
          },
          {
            location: 'Sunglasses',
            category: [
              { categoryName: 'FR0', isActive: true },
              { categoryName: 'FR1-JIT', isActive: false },
              { categoryName: 'FR2-JIT', isActive: true },
              { categoryName: 'FR2-NON-JIT', isActive: true },
              { categoryName: 'PL', isActive: true }
            ]
          },
          {
            location: 'Contact Lenses',
            category: [
              { categoryName: 'FR2-JIT', isActive: false },
              { categoryName: 'FR2-NON-JIT', isActive: true },
              { categoryName: 'PL', isActive: true }
            ]
          }
        ],
        meta: null
      });
    }, 500);
  });
