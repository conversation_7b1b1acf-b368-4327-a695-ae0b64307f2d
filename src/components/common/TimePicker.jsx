/* eslint-disable import/no-extraneous-dependencies */
import * as React from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import LkInput from 'components/MaterialUi/LkInput';
import { makeStyles } from 'tss-react/mui';
import { GlobalStyles } from 'tss-react';

const styles = makeStyles()(() => ({
  timePicker: {
    '& .MuiInputBase-input': {
      padding: 12
    }
  }
}));

const LkTimePicker = ({ label = 'small', value, handleChange, name }) => {
  const { classes } = styles();

  return (
    <>
      <GlobalStyles
        styles={{
          'div.MuiPickersPopper-root': {
            zIndex: 12000
          }
        }}
      />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <TimePicker
          ampm={false}
          label={label}
          value={value}
          className={classes.timePicker}
          onChange={(newTime) => handleChange(newTime, name)}
          renderInput={(params) => <LkInput {...params} />}
        />
      </LocalizationProvider>
    </>
  );
};

export default LkTimePicker;
