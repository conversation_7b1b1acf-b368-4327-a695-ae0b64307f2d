import React, { useCallback, useState } from 'react';
import Tab from '@mui/material/Tab';
import { makeStyles } from 'tss-react/mui';
import { Box, Button } from '@mui/material';
import { StyledTabs } from 'components/common/StyledTabs';
import './layoutList.scss';
import LkInput from 'components/MaterialUi/LkInput';
import LayoutCreateModal from 'components/LayoutCreate/CreateLayoutModal';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import LayoutList from './LayoutList';

const styles = makeStyles()((theme) => ({
  positionRelative: {
    position: 'relative'
  },
  tab1Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 0,
    zIndex: 4
  },
  tab2Absolute: {
    position: 'absolute',
    background: 'white',
    top: -2,
    left: 160
  },
  container: {
    height: '90vh',
    backgroundColor: '#f5f5f5',
    padding: theme.spacing(3)
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },
  root: {
    backgroundColor: '#FFFFFF'
  }
}));

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const LayoutListTab = () => {
  const { classes, cx } = styles();
  const [value, setValue] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [globalSearch, setGlobalSearch] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.LAYOUT_PAGE);

  const tabsConfig = [
    { label: CONTENT.ALL, value: null },
    { label: CONTENT.ACTIVE, value: 'ACTIVE' },
    { label: CONTENT.LINKED, value: 'LINKED' },
    { label: CONTENT.DRAFT, value: 'DRAFT' }
  ];

  const handleChange = (_event, newValue) => {
    setValue(newValue);
  };

  const closeLayoutModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  return (
    <div className={classes.container}>
      <div>
        <div className={classes.mainBar}>
          <Box width="320px">
            <LkInput
              fullWidth
              className={classes.root}
              label={CONTENT.SEARCH_LAYOUT}
              variant="outlined"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  setGlobalSearch(e.target.value);
                  setSearchValue('');
                }
              }}
            />
          </Box>
          <div>
            <Button
              color="primary"
              style={{ borderRadius: 8 }}
              type="submit"
              variant="contained"
              onClick={() => setShowCreateModal(true)}
            >
              {CONTENT.CREATE_NEW_LAYOUT_SCHEME}
            </Button>
          </div>
        </div>
        <Box width="100%" margin="0 auto">
          <StyledTabs value={value} onChange={handleChange}>
            {tabsConfig.map((tab, index) => (
              <Tab
                key={tab.label}
                label={tab.label}
                className={cx('tabs', { tabselected: value === index })}
              />
            ))}
          </StyledTabs>
          <Box
            className={cx('border-grey5-radiusbase overflow-hidden', {
              'border-left-no-radius': value === 0
            })}
          >
            {tabsConfig.map((tab, index) => (
              <TabPanel
                key={tab.label}
                value={value}
                index={index}
                className={classes.positionRelative}
              >
                <Box width={158} component="div" height={2} className={classes.tab1Absolute} />
                <LayoutList status={tab.value} globalSearch={globalSearch} CONTENT={CONTENT} />
              </TabPanel>
            ))}
          </Box>
        </Box>
      </div>
      {showCreateModal && (
        <LayoutCreateModal open={showCreateModal} close={() => closeLayoutModal()} />
      )}
    </div>
  );
};

export default LayoutListTab;
