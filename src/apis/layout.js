import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const layoutDefineApi = (payload) =>
  tokenInstance()
    .post(config.layout.define, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });

export const layoutSaveApi = (payload) =>
  tokenInstance()
    .post(config.layout.save, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });

export const getLayoutDetailApi = (payload) => {
  const { id } = payload;
  return tokenInstance()
    .get(`${config.layout.get}/${id}`, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const getLayoutListApi = (payload) =>
  tokenInstance().get(config.layout.list, { params: payload });
