import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

const locationData = [
  {
    id: '1',
    event: 'MEI_ENTRY'
  },
  {
    id: '2',
    event: 'MEI_EXIT'
  },
  {
    id: '3',
    event: 'TRAY_RELEASE'
  }
];

export const getLocationAPI = () => ({
  locationData
});

export const postBarcodeAPI = (payload) => {
  if (payload.event === 'TRAY_RELEASE') {
    return tokenInstance().put(config.mei.trayRelease, null, {
      params: {shippingPackageIds: payload.barcode}
    });
  }
  return tokenInstance().post(config.mei.meiLocationBarcode, payload);
};
