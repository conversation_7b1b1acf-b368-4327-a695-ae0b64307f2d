export const GET_SUPERVISOR_PICKING_SKIPPED_DATA_LOAD = 'GET_SUPERVISOR_PICKING_SKIPPED_DATA_LOAD';
export const GET_SUPERVISOR_PICKING_SKIPPED_DATA_SUCCESS =
  'GET_SUPERVISOR_PICKING_SKIPPED_DATA_SUCCESS';
export const GET_SUPERVISOR_PICKING_SKIPPED_DATA_FAIL = 'GET_SUPERVISOR_PICKING_SKIPPED_DATA_FAIL';
export const GET_SUPERVISOR_PICKING_SKIPPED_DATA_RESET =
  'GET_SUPERVISOR_PICKING_SKIPPED_DATA_RESET';

export const GET_SUPERVISOR_PICKING_USERS_LOAD = 'GET_SUPERVISOR_PICKING_USERS_LOAD';
export const GET_SUPERVISOR_PICKING_USERS_SUCCESS = 'GET_SUPERVISOR_PICKING_USERS_SUCCESS';
export const GET_SUPERVISOR_PICKING_USERS_FAIL = 'GET_SUPERVISOR_PICKING_USERS_FAIL';

export const GET_SUPERVISOR_PICKING_HOLD_DATA_LOAD = 'GET_SUPERVISOR_PICKING_HOLD_DATA_LOAD';
export const GET_SUPERVISOR_PICKING_HOLD_DATA_SUCCESS = 'GET_SUPERVISOR_PICKING_HOLD_DATA_SUCCESS';
export const GET_SUPERVISOR_PICKING_HOLD_DATA_FAIL = 'GET_SUPERVISOR_PICKING_HOLD_DATA_FAIL';
export const GET_SUPERVISOR_PICKING_HOLD_DATA_RESET = 'GET_SUPERVISOR_PICKING_HOLD_DATA_RESET';

export const GET_SUPERVISOR_PICKING_CATAGORIES_LOAD = 'GET_SUPERVISOR_PICKING_CATAGORIES_LOAD';
export const GET_SUPERVISOR_PICKING_CATAGORIES_SUCCESS =
  'GET_SUPERVISOR_PICKING_CATAGORIES_SUCCESS';
export const GET_SUPERVISOR_PICKING_CATAGORIES_FAIL = 'GET_SUPERVISOR_PICKING_CATAGORIES_FAIL';

export const SUPERVISOR_PICKING_HOLD_REQUEST = 'SUPERVISOR_PICKING_HOLD_REQUEST';
export const SUPERVISOR_PICKING_HOLD_SUCCESS = 'SUPERVISOR_PICKING_HOLD_SUCCESS';
export const SUPERVISOR_PICKING_HOLD_FAIL = 'SUPERVISOR_PICKING_HOLD_FAIL';

export const SUPERVISOR_PICKING_NOT_FOUND_REQUEST = 'SUPERVISOR_PICKING_NOT_FOUND_REQUEST';
export const SUPERVISOR_PICKING_NOT_FOUND_SUCCESS = 'SUPERVISOR_PICKING_NOT_FOUND_SUCCESS';
export const SUPERVISOR_PICKING_NOT_FOUND_FAIL = 'SUPERVISOR_PICKING_NOT_FOUND_FAIL';

export const SUPERVISOR_PICKING_UN_HOLD_REQUEST = 'SUPERVISOR_PICKING_UN_HOLD_REQUEST';
export const SUPERVISOR_PICKING_UN_HOLD_SUCCESS = 'SUPERVISOR_PICKING_UN_HOLD_SUCCESS';
export const SUPERVISOR_PICKING_UN_HOLD_FAIL = 'SUPERVISOR_PICKING_UN_HOLD_FAIL';

export const SUPERVISOR_PICKING_ASSIGN_REQUEST = 'SUPERVISOR_PICKING_ASSIGN_REQUEST';
export const SUPERVISOR_PICKING_ASSIGN_SUCCESS = 'SUPERVISOR_PICKING_ASSIGN_SUCCESS';
export const SUPERVISOR_PICKING_ASSIGN_FAIL = 'SUPERVISOR_PICKING_ASSIGN_FAIL';

export const SUPERVISOR_PICKING_SCAN_REQUEST = 'SUPERVISOR_PICKING_SCAN_REQUEST';
export const SUPERVISOR_PICKING_SCAN_SUCCESS = 'SUPERVISOR_PICKING_SCAN_SUCCESS';
export const SUPERVISOR_PICKING_SCAN_FAIL = 'SUPERVISOR_PICKING_SCAN_FAIL';

export const GET_EXPORT_SKIPPED_DATA_LOAD = 'GET_EXPORT_SKIPPED_DATA_LOAD';
export const GET_EXPORT_SKIPPED_DATA__SUCCESS = 'GET_EXPORT_SKIPPED_DATA__SUCCESS';
export const GET_EXPORT_SKIPPED_DATA__FAIL = 'GET_EXPORT_SKIPPED_DATA__FAIL';
export const GET_EXPORT_SKIPPED_DATA__RESET = 'GET_EXPORT_SKIPPED_DATA__RESET';

export const GET_UPLOAD_PID_DATA_LOAD = 'GET_UPLOAD_PID_DATA_LOAD';
export const GET_UPLOAD_PID_DATA_SUCCESS = 'GET_UPLOAD_PID_DATA_SUCCESS';
export const GET_UPLOAD_PID_DATA_FAIL = 'GET_UPLOAD_PID_DATA_FAIL';
export const GET_UPLOAD_PID_DATA_RESET = 'GET_UPLOAD_PID_DATA_RESET';

export const MOVE_FROM_ASRS_TO_MANUAL_LOAD = 'MOVE_FROM_ASRS_TO_MANUAL_LOAD';
export const MOVE_FROM_ASRS_TO_MANUAL_SUCCESS = 'MOVE_FROM_ASRS_TO_MANUAL_SUCCESS';
export const MOVE_FROM_ASRS_TO_MANUAL_FAIL = 'MOVE_FROM_ASRS_TO_MANUAL_FAIL';
export const MOVE_FROM_ASRS_TO_MANUAL_RESET = 'MOVE_FROM_ASRS_TO_MANUAL_RESET';

export const BULK_UPLOAD_DISCARDED_SHIPMENT_LOAD = 'BULK_UPLOAD_DISCARDED_SHIPMENT_LOAD';
export const BULK_UPLOAD_DISCARDED_SHIPMENT_SUCCESS = 'BULK_UPLOAD_DISCARDED_SHIPMENT_SUCCESS';
export const BULK_UPLOAD_DISCARDED_SHIPMENT_FAIL = 'BULK_UPLOAD_DISCARDED_SHIPMENT_FAIL';
export const BULK_UPLOAD_DISCARDED_SHIPMENT_RESET = 'BULK_UPLOAD_DISCARDED_SHIPMENT_RESET';
