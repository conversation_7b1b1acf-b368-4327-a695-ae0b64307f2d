import React, { useState } from 'react';

import { makeStyles } from 'tss-react/mui';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Box from '@mui/material/Box';

const useStyles = makeStyles()(() => ({
  container: {
    position: 'relative',
    padding: '0px 20px'
  },
  customTabRoot: {
    color: '#00B9C6'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  },
  divider: {
    borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
  }
}));

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`simple-tabpanel-${index}`}
    aria-labelledby={`simple-tab-${index}`}
    {...other}
  >
    {value === index && children}
  </div>
);

const LKTabs = ({ Tab1, Tab2, showTab1 = true, showTab2 = true }) => {
  const { classes } = useStyles();

  const [value, setValue] = useState(0);

  const handleChange = (_, newValue) => {
    setValue(newValue);
  };

  return (
    <Box className={classes.container}>
      <Box className={classes.divider}>
        <Tabs
          value={value}
          onChange={handleChange}
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          {showTab1 && <Tab value={0} label="Barcode List" />}
          {showTab2 && <Tab value={1} label="ASRS Discrepancy" />}
        </Tabs>
      </Box>
      <TabPanel value={value} index={0}>
        {Tab1}
      </TabPanel>
      <TabPanel value={value} index={1}>
        {Tab2}
      </TabPanel>
    </Box>
  );
};

export default LKTabs;
