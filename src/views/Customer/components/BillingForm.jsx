import React from 'react';
import { useSelector } from 'react-redux';

import { Box, MenuItem } from '@mui/material';

import { LOCALISATION } from 'redux/reducers/localisation';

const BillingForm = ({
  CustomerFormTitle,
  CustomerFieldContainer,
  CustomerTextField,
  formState,
  doCountryList,
  handleChange,
  errors
}) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);

  return (
    <Box>
      <CustomerFormTitle variant="h4">{CONTENT.BILLING_ADDRESS}</CustomerFormTitle>
      <CustomerFieldContainer>
        <CustomerTextField
          name="billing_addressLine1"
          data-cy="billing_addressLine1"
          label="Enter Address Line 1"
          value={formState.billing_addressLine1}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.billing_addressLine1}
          helperText={errors.billing_addressLine1}
          placeholder="Enter Address Line 1"
        />
        <CustomerTextField
          name="billing_addressLine2"
          value={formState.billing_addressLine2}
          onChange={handleChange}
          placeholder="Enter Address Line 2 (optional)"
        />
        <CustomerTextField
          name="billing_city"
          data-cy="billing_city"
          label="Enter City"
          value={formState.billing_city}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.billing_city}
          helperText={errors.billing_city}
          placeholder="Enter City"
        />
        <CustomerTextField
          name="billing_pincode"
          data-cy="billing_pincode"
          label="Enter PIN Code"
          value={formState.billing_pincode}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.billing_pincode}
          helperText={errors.billing_pincode}
          placeholder="Enter PIN Code"
        />

        <CustomerTextField
          name="billing_country"
          data-cy="billing_country"
          select
          label="Country"
          onChange={handleChange}
          onBlur={handleChange}
          value={formState.billing_country}
          error={!!errors.billing_country}
          helperText={errors.billing_country}
          placeholder="Select Country"
        >
          <MenuItem selected disabled value="">
            Select Country
          </MenuItem>
          {doCountryList.map(({ country_code, country_name }) => (
            <MenuItem key={country_code} value={country_code}>
              {country_name}
            </MenuItem>
          ))}
        </CustomerTextField>

        <CustomerTextField
          name="billing_state"
          data-cy="billing_state"
          label="Enter State"
          value={formState.billing_state}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.billing_state}
          helperText={errors.billing_state}
          placeholder="Enter State"
        />
        <CustomerTextField
          name="billing_phone"
          data-cy="billing_phone"
          label="Enter Contact Number"
          value={formState.billing_phone}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.billing_phone}
          helperText={errors.billing_phone}
          placeholder="Enter Contact Number"
        />
      </CustomerFieldContainer>
    </Box>
  );
};

export default BillingForm;
