import * as action from '../actionTypes/trayReassign';

export const getTrayDeatilsLoad = (barCode) => ({
  type: action.GET_TRAY_LIST_LOAD,
  payload: barCode
});
export const getTrayDeatilsSuccess = (data) => ({
  type: action.GET_TRAY_LIST_SUCCESS,
  payload: data
});
export const getTrayDeatilsFail = () => ({ type: action.GET_TRAY_LIST_FAIL });
export const resetTrayList = () => ({ type: action.TRAY_LIST_RESET });

export const getTrayHistoryLoad = (barCode) => ({
  type: action.GET_TRAY_HISTORY_LOAD,
  payload: barCode
});
export const getTrayHistorySuccess = (data) => ({
  type: action.GET_TRAY_HISTORY_SUCCESS,
  payload: data
});
export const getTrayHistoryFail = () => ({type: action.GET_TRAY_HISTORY_FAIL});

export const validateTrayLoad = (trayId) => ({ type: action.VALIDATE_TRAY_LOAD, payload: trayId });
export const validateTraySuccess = (data) => ({
  type: action.VALIDATE_TRAY_SUCCESS,
  payload: data
});
export const validateTrayFail = (data) => ({ type: action.VALIDATE_TRAY_FAIL, payload: data });
export const resetValidation = () => ({ type: action.VALIDATE_RESET });

export const reassignTrayLoad = (data) => ({ type: action.REASSIGN_TRAY_LOAD, payload: data });
export const reassignTraySuccess = (data) => ({
  type: action.REASSIGN_TRAY_SUCCESS,
  payload: data
});
export const reassignTrayFail = (data) => ({ type: action.REASSIGN_TRAY_FAIL, payload: data });
