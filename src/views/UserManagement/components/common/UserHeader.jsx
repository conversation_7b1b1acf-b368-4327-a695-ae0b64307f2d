import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Box, Button, InputAdornment } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import SearchIcon from '@mui/icons-material/Search';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import LkInput from 'components/MaterialUi/LkInput';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';
import { fileDownload, getQueryParam, listToCSVString } from 'utils/helpers';
import { getActiveApproverRoleLoad } from 'redux/reducers/userManagement';
import { toastOpen } from 'redux/actionCreators/toast';
import AddUserModal from '../UserWidgets/AddUserModal';

const useStyles = makeStyles()((theme) => ({
  header: {
    background: '#ffffff',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 26
  },
  searchIcon: {
    fontSize: '1.1rem'
  },
  searchInput: {
    minWidth: '300px'
  },
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    width: '60%'
  },
  headerButtons: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-around'
  },
  button: {
    width: 140,
    padding: '8px 22px',
    marginLeft: 8
  },
  addButton: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    boxShadow: `0px 3px 1px -2px rgba(0, 0, 0, 0.2), 
    0px 2px 2px rgba(0, 0, 0, 0.14), 0px 1px 5px rgba(0, 0, 0, 0.12)`,
    '&:hover': {
      backgroundColor: theme.palette.primary.light
    }
  },
  exportButton: {
    width: 160,
    marginLeft: 12
  }
}));

const UserHeader = () => {
  const { classes, cx } = useStyles();
  const { roles } = useParams();
  const roleGroupId = getQueryParam();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [open, setOpen] = useState(false);
  const [isExportClick, setIsExportClick] = useState(false);
  const [appliedFilter, setAppliedFilter] = useState([]);
  const [globalSearch, setGlobalSearch] = useState('');

  const userList = useSelector((state) => state.userManagement.userList);
  const approverRole = useSelector((state) => state.userManagement.approverRoles);

  const exportUserListTable = () => {
    const columns = ['Employee Id', 'Name', 'Role', 'Email Id', 'Facility'];
    const exportKeys = ['employeeCode', 'employeeName', 'roleName', 'email', 'facility'];
    const csvString = listToCSVString(userList.data, columns, exportKeys);
    fileDownload(csvString, 'User List');
  };

  const exportRoleTable = () => {
    const columns = ['Role'];
    const exportKeys = ['name'];
    const csvString = listToCSVString(approverRole?.data, columns, exportKeys);
    fileDownload(csvString, 'User Role');
  };

  useEffect(() => {
    if (roleGroupId?.role) {
      setAppliedFilter([{ key: 'role', keyValue: 'role', values: roleGroupId?.role }]);
    }
    if (roleGroupId?.roleName) {
      setAppliedFilter([{ key: 'role', keyValue: 'role', values: roleGroupId?.roleName }]);
    }
  }, [location.search]);

  useEffect(() => {
    if (isExportClick && userList?.data?.length && roles === 'lists') {
      exportUserListTable();
      setIsExportClick(false);
    }
    if (isExportClick && approverRole?.data?.length && roles === 'roles') {
      exportRoleTable();
      setIsExportClick(false);
    }
  }, [isExportClick, userList.data, approverRole?.data]);

  const handleCloseModal = () => {
    setOpen(false);
  };

  const handleOpenModal = () => {
    setOpen(true);
  };

  const removeFilter = () => {
    const queryParams = new URLSearchParams(location.search);
    setAppliedFilter([]);

    if ((queryParams.has('role') && queryParams.has('id')) || queryParams.has('roleName')) {
      queryParams.delete('role');
      queryParams.delete('id');
      queryParams.delete('roleName');
      navigate(`?${queryParams.toString()}`, { replace: true });
    }

    if (roles === 'roles') {
      dispatch(getActiveApproverRoleLoad());
    }
  };

  return (
    <>
      <Box className={classes.header}>
        <Box className={classes.searchContainer}>
          <LkInput
            className={classes.searchInput}
            variant="outlined"
            value={globalSearch}
            onChange={(e) => setGlobalSearch(e.target.value)}
            onKeyPress={(e) => {
              if (e.which === 13) {
                setAppliedFilter([{ key: 'role', keyValue: 'role', values: globalSearch }]);
                setGlobalSearch('');
                if (roles === 'lists') {
                  navigate(`?roleName=${globalSearch}`);
                } else {
                  dispatch(getActiveApproverRoleLoad({ roleGroupName: globalSearch }));
                }
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="disabled" className={classes.searchIcon} />
                </InputAdornment>
              )
            }}
            placeholder={`${
              roles === 'roles' ? 'Search by roles' : 'Search by employee Id, name, role'
            }`}
          />
          {appliedFilter.length > 0 && (
            <FilterChipsV2 filters={appliedFilter} deleteData={removeFilter} />
          )}
        </Box>
        <Box className={classes.headerButtons}>
          <Button
            startIcon={<PersonAddIcon />}
            className={cx(classes.button, classes.addButton)}
            onClick={handleOpenModal}
          >
            Add User
          </Button>
          <Button
            onClick={() => {
              setIsExportClick(true);
              if (
                (userList?.data?.length === 0 && roles === 'lists') ||
                (approverRole?.data?.length === 0 && roles === 'roles')
              ) {
                dispatch(
                  toastOpen({
                    isToastOpen: true,
                    heading: 'No data to export',
                    severity: 'error'
                  })
                );
                setIsExportClick(false);
              }
            }}
            variant="outlined"
            color="primary"
            size="large"
            className={classes.exportButton}
          >
            Export Table
          </Button>
        </Box>
      </Box>
      {open && <AddUserModal open={open} handleClose={handleCloseModal} />}
    </>
  );
};

export default UserHeader;
