/* eslint-disable max-len */
/* eslint-disable no-undef */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import {
  CardContent,
  InputAdornment,
  Button,
  MenuItem,
  Paper,
  TableContainer,
  TableBody,
  Table,
  TableHead,
  TableRow,
  IconButton
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import AddCircle from '@mui/icons-material/AddCircle';
import { makeStyles } from 'tss-react/mui';
import Autocomplete from '@mui/material/Autocomplete';

import { FieldArray } from 'react-final-form-arrays';
import { Form, Field } from 'react-final-form';
import arrayMutators from 'final-form-arrays';

import { InvoiceOrderStatus } from 'config/InvoiceOrderStatus';

import LkModal from 'components/Modal/Modal';
import Spinner from 'components/Spinner/Spinner';
import LkToolTip from 'components/ToolTip/ToolTip';
import LkInput from 'components/MaterialUi/LkInput';
import LkAlert from 'components/MaterialUi/LkAlert';
import Shortcuts from 'components/common/Shortcuts';
import ShortCut from 'components/shortCut/shortCut';
import InfoStrip from 'components/common/InfoStrip';
import AddPidModal from 'components/Grn/AddPidModal';
import UploadCsv from 'components/UploadCsv/UploadCsv';
import InvoiceDetailmodal from 'components/common/InvoiceDetailmodal';
import BulkInvoiceModal from 'components/BulkInvoiceModal/BulkInvoiceModal';
import CloseInvoiceModal from 'components/CloseInvoiceModal/CloseInvoiceModal';

import useProccessStatusHook from 'common/useProccessStatusHook';
import useKeyboardShortcut from 'common/useKeyboardShortcut';

import {
  required,
  floatNumber,
  integer,
  minGrnNumber,
  maxGrnNumber,
  validDuplicateProducId
} from 'utils/validation';
import {
  composeValidators,
  convertDateFormat,
  roundUptoFixedDigits,
  fileDownload,
  getCurrencySumbol,
  getTaxType
} from 'utils/helpers';

import {
  getVendorInvoiceCreationStatusReset,
  resetVendorInvoiceCreationData,
  closeInvoiceLoad,
  closeInvoiceReset,
  downloadErrorInvoiceLoad,
  importInvoiceReset,
  createVendorInvoiceLoad,
  getVendorInvoiceCreationStatusLoad,
  importInvoiceLoad,
  downloadErrorInvoiceReset
} from 'redux/actionCreators/invoiceReferenceCreation';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { closeAllOpenGRNLoad } from 'redux/actionCreators/grn';
import { invoiceViewReset } from 'redux/actionCreators/invoiceView';

import {
  StyledEditableTableCell as StyledTableCell,
  StyledTableRow
} from '../../theme/table.theme';
import InvoiceSummaryCL from './InvoiceSummaryCL';

const useStyles = makeStyles()((theme) => ({
  root: {
    // padding: theme.spacing(3),
  },
  content: {
    marginTop: theme.spacing(2)
  },
  table: {
    maxHeight: 'calc(100vh - 286px)'
  },
  textAlignCenter: {
    textAlign: 'center'
  },
  taxRate: {
    // paddingRight: theme.spacing(1),
    // display: 'inline-block'
  },
  input: {
    width: theme.spacing(10)
  },
  costPrice: {
    width: theme.spacing(12)
  },
  quantity: {
    width: theme.spacing(9)
  },
  taxPer: {
    width: theme.spacing(11)
  },
  cancelButton: {
    marginRight: theme.spacing(2)
  },
  contentHeader: {
    marginBottom: theme.spacing(2)
  },
  chip: {
    borderRadius: 4,
    marginLeft: 22,
    height: 24,
    color: theme.palette.success.main,
    borderColor: '#45C476'
  },
  displayInlineBlock: {
    display: 'inline-block'
  },
  verticalMiddle: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  },
  customCardHeader: {
    padding: theme.spacing(2)
  },
  bottomButton: {
    margin: 'auto',
    width: 'fit-content',
    marginTop: theme.spacing(3)
  },
  modalField: {
    width: 200,
    borderColor: '#DDDDDD'
  }
}));

let bulkQcData = [];

let poItems = {};

let isImportInvoiceUpload = false;

let mutators;

const PAGE_SIZE = 25;

const disableInputs = {
  disable: false
};

function taxCalculate(
  taxes,
  tax,
  tax_per,
  tax_rate,
  unitPrice,
  totalPrice,
  currentField,
  keyUpevent
) {
  if (taxes.includes(tax)) {
    if (!currentField[tax_per] && currentField[tax_per] !== 0 && !keyUpevent) {
      currentField[tax_per] = 0;
    }
    currentField[tax_rate] =
      (currentField[tax_per] * currentField.vendor_unit_cost_price * currentField.quantity) / 100;
    unitPrice += (currentField[tax_per] * currentField.vendor_unit_cost_price) / 100;
    totalPrice += currentField[tax_rate];
  }
  return [unitPrice, totalPrice];
}

const calculateValue = (currentField, taxes, keyUpevent) => {
  const updatedField = { ...currentField };
  updatedField.unit_price_wt = undefined;
  updatedField.price_with_taxes = undefined;
  if (
    (!updatedField.quantity && updatedField.quantity !== 0) ||
    (!updatedField.vendor_unit_cost_price && updatedField.vendor_unit_cost_price !== 0)
  ) {
    return updatedField;
  }
  let unitPrice = updatedField.vendor_unit_cost_price;
  let totalPrice = updatedField.vendor_unit_cost_price * updatedField.quantity;
  [unitPrice, totalPrice] = taxCalculate(
    taxes,
    'cgst',
    'cgst_per',
    'cgst_rate',
    unitPrice,
    totalPrice,
    updatedField,
    keyUpevent
  );
  [unitPrice, totalPrice] = taxCalculate(
    taxes,
    'igst',
    'igst_per',
    'igst_rate',
    unitPrice,
    totalPrice,
    updatedField,
    keyUpevent
  );
  [unitPrice, totalPrice] = taxCalculate(
    taxes,
    'sgst',
    'sgst_per',
    'sgst_rate',
    unitPrice,
    totalPrice,
    updatedField,
    keyUpevent
  );
  [unitPrice, totalPrice] = taxCalculate(
    taxes,
    'ugst',
    'ugst_per',
    'ugst_rate',
    unitPrice,
    totalPrice,
    updatedField,
    keyUpevent
  );
  updatedField.unit_price_wt = unitPrice;
  updatedField.price_with_taxes = totalPrice;
  return updatedField;
};

const handleChange = (fields, index, val, field, taxes, form, keyUpEvent) => {
  if (!Number.isNaN(Number(val))) {
    let currentField = { ...fields.value[index], [field]: val };
    currentField = calculateValue(currentField, taxes, keyUpEvent);
    fields.update(index, currentField);
    let totalPrice = 0;
    fields.value.forEach((f, i) =>
      !Number.isNaN(Number(f.price_with_taxes)) && i !== index && f.active
        ? (totalPrice += f.price_with_taxes)
        : null
    );
    if (!Number.isNaN(Number(currentField.price_with_taxes))) {
      totalPrice += currentField.price_with_taxes;
    }
    form.mutators.setTotal(totalPrice);
  }
};

const handleClearAll = (fields, form) => {
  const len = fields.value.length;
  if (len) {
    fields.value.forEach((value, index) => {
      fields.update(index, { id: 0, active: false });
    });
    fields.update(0, { id: 0, active: true });
    form.mutators.setTotal(0);
  }
};

const inputKeyDown = (e, field) => {
  const { keyCode } = e;
  if (
    (keyCode < 48 || keyCode > 57) &&
    keyCode !== 8 &&
    keyCode !== 9 &&
    keyCode !== 37 &&
    keyCode !== 39 &&
    field === 'quantity'
  ) {
    e.preventDefault();
  }
  if (
    (keyCode < 48 || keyCode > 57) &&
    keyCode !== 190 &&
    keyCode !== 8 &&
    keyCode !== 37 &&
    keyCode !== 39 &&
    keyCode !== 9 &&
    [
      'product_id',
      'vendor_unit_cost_price',
      'cgst_per',
      'ugst_per',
      'igst_per',
      'sgst_per'
    ].indexOf(field) > -1
  ) {
    e.preventDefault();
  }
};

const disableCloseField = (field, fields, index, closeInvoice) => {
  const disabledCloseInvoice = [
    'name',
    'ugst_per',
    'sgst_per',
    'cgst_per',
    'igst_per',
    'delete',
    'name'
  ];
  return disabledCloseInvoice.indexOf(field) > -1 && closeInvoice && fields.value[index]?.isExist;
};

const createTextField = (
  name,
  field,
  validate,
  disabled,
  className,
  classes,
  fields,
  index,
  taxes,
  form,
  closeInvoice,
  inputProps
) => (
  <Field className="form-control" name={`${name}.${field}`} validate={validate}>
    {({ input, meta }) => (
      <div className="material-group">
        <LkInput
          className={classes[className]}
          {...input}
          InputProps={inputProps}
          disabled={
            disabled ||
            disableCloseField(field, fields, index, closeInvoice) ||
            disableInputs.disable
          }
          onBlur={(e) => handleChange(fields, index, e.target.value, field, taxes, form)}
          variant="outlined"
          onKeyDown={(e) => inputKeyDown(e, field)}
          onKeyUp={(e) =>
            handleChange(fields, index, e.target.value, field, taxes, form, 'keyUpEvent')
          }
        />
        <span style={{ textAlign: 'right', marginTop: '0px' }} className="input-error">
          {meta.error ? meta.error : ''}
        </span>
      </div>
    )}
  </Field>
);

const sampleCSV = [
  ['product_id', 'qty', 'unit_price'],
  [687789, 120, 280.0],
  [687789, 120, 280.0],
  [687789, 120, 280.0]
];

let selectedImportFile;

const defaultValue = {
  total_invoice_qty: '',
  total_invoice_amount: ''
};

const initialObj = {
  product_id: '',
  product_desc: '',
  vendor_unit_cost_price: '',
  bulk_qc_failed_qty: '',
  reason: ''
};

const InvoiceSummary = (props) => {
  const navigate = useNavigate();
  const { classes } = useStyles();
  const [editRow, setEditRow] = useState(-1);
  const [loader, setLoader] = useState(false);
  const [recordsShow, setRecordsShow] = useState(PAGE_SIZE);
  const [uploadCsvModal, setUploadCsvModal] = useState(false);
  const dispatch = useDispatch();
  const [taxFieldError, setTaxFieldError] = useState(false);
  const [closeInvoiceModal, setCloseInvoiceModal] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [bulkQcFailModal, setBulkQcFailModal] = useState(false);
  const [grnErrorModal, setGrnErrorModal] = useState(true);
  const [uploadSuccessModal, setUploadSuccessModal] = useState(false);
  const [showAddByProductDescriptionModal, setShowAddByProductDescriptionModal] = useState(false);
  const { closeAllOpenGrnLoading } = useSelector((state) => state.grn);
  const [showCLTable, setShowTable] = useState(true);
  const [clData, setCLData] = useState({ ...defaultValue });
  const [clFailData, setClFailData] = useState([{ ...initialObj }]);
  const toogleClTable = useCallback(() => setShowTable((oldState) => !oldState), []);

  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const {
    closeInvoiceLoading,
    closeInvoiceData,
    importInvoiceLoading,
    importInvoiceData,
    downloadCsvErrorData,
    createVendorInvoiceData,
    createVendorInvoiceLoading,
    vendorInvoiceStatus,
    vendorInvoiceStatusLoading,
    vendorInvoiceStatusFail
  } = useSelector((state) => state.invoiceReferenceCreation);
  const {
    handleCancel,
    po,
    date,
    invoiceText,
    vendorInvoiceCreationItems,
    vendorText,
    mode,
    invoiceViewData,
    additionalFields
  } = props;

  const convertedCurrency = getCurrencySumbol(vendorText?.currency);

  const [invoiceStatus, statusSuccessFul, setInvoiceStatus] = useProccessStatusHook({
    processingStatus: vendorInvoiceStatus,
    processingStatusFail: vendorInvoiceStatusFail,
    processingStatusLoad: getVendorInvoiceCreationStatusLoad,
    processingStatusReset: getVendorInvoiceCreationStatusReset
  });
  const { closeInvoice, editMode } = mode;

  const isCLtype = useMemo(
    () => vendorInvoiceCreationItems?.invoice_level === 'SUMMARY',
    [vendorInvoiceCreationItems]
  );

  const {
    po_items,
    invoice_items,
    applicable_tax_rates,
    invoice_ref_number,
    version,
    status,
    prefilled_invoice
  } = vendorInvoiceCreationItems;
  const taxes = getTaxType(applicable_tax_rates);
  const { isGrnOpenData } = useSelector((state) => state.grn);
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);

  const infoStripData = [
    {
      key: INVOICE.VENDOR_INVOICE,
      value: invoiceText,
      className: 'text-turquioise_surf cursor-pointer',
      onClick: () => setShowModal(true),
      status: InvoiceOrderStatus[status]?.text || status,
      type: InvoiceOrderStatus[status]?.type
    },
    { key: INVOICE.VENDOR_INVOICE_DATE, value: convertDateFormat(date, 'shortDate') },
    { key: INVOICE.PO, value: po },
    { key: INVOICE.VENDOR, value: vendorText?.name }
  ];
  const poPids = Object.keys(po_items);

  const { classification } = invoice_items.length > 0 && invoice_items[0];
  const rowsTemplate = invoice_items.map((ii) => {
    const { quantity, product_id, po_pending_quantity } = ii;
    if (!closeInvoice && !editMode) {
      if (prefilled_invoice) {
        const { quantity } = po_items[product_id];
        ii = { ...ii, quantity: Number(ii.quantity), quantity_copy: Number(quantity) };
      } else {
        const { pending_quantity } = po_items[product_id];
        ii = { ...ii, quantity: Number(pending_quantity), quantity_copy: Number(pending_quantity) };
      }
    } else {
      let q;
      const invoiceDataStatus = invoiceViewData?.data?.status;
      if (invoiceDataStatus === 'PENDING') {
        q = Number(po_pending_quantity);
      } else {
        q = Number(po_pending_quantity) + Number(quantity);
      }
      ii = { ...ii, quantity_copy: q, quantity_original: Number(quantity) };
    }
    return calculateValue(ii, taxes);
  });
  let formData = { values: null, valid: null };

  let total = 0;
  rowsTemplate.forEach((rt) => {
    if (rt.price_with_taxes && !Number.isNaN(Number(rt.price_with_taxes))) {
      total += rt.price_with_taxes;
    }
    rt.isExist = true;
    rt.active = true;
  });
  // useEffect(() => {
  //   if (mutators?.setInvoiceData) {
  //     mutators.setInvoiceData(rowsTemplate);
  //   }
  // }, [mutators]);

  if (rowsTemplate.length === 0) {
    rowsTemplate.push({ id: 0, isExist: false, active: true });
  }

  const shortcuts = [
    {
      name: 'Import CSV',
      key: 'Ctrl+I',
      src: `${import.meta.env.VITE_STATIC_SERVER}/images/import.svg`,
      onClick: () => setUploadCsvModal(true),
      disabled: closeInvoice || isGrnOpenData?.isGRNOpen || isCLtype
    }
  ];

  const isInvalidTax = (id) =>
    id.active &&
    ['cgst', 'igst', 'sgst', 'ugst', 'flat'].some(
      (tax) => taxes.includes(tax) && Number.isNaN(Number(id[`${tax}_per`]))
    );

  const invoiceReq = (e) => {
    let error = false;
    if (e?.invoiceData?.some(isInvalidTax)) {
      setTaxFieldError(true);
      error = true;
    }

    if (error) {
      return null;
    }
    const items = [];
    if (e?.invoiceData) {
      e.invoiceData.forEach((id) => {
        if (id.active) {
          items.push({
            id: id.id || '',
            product_id: id.product_id,
            quantity: id.quantity,
            vendor_unit_cost_price: id.vendor_unit_cost_price,
            cgst_per: taxes.includes('cgst') ? id.cgst_per : '',
            igst_per: taxes.includes('igst') ? id.igst_per : '',
            sgst_per: taxes.includes('sgst') ? id.sgst_per : '',
            ugst_per: taxes.includes('ugst') ? id.ugst_per : '',
            flat_per: taxes.includes('flat') ? id.flat_per : ''
          });
        }
      });
    }

    return {
      items,
      invoice_ref_no: vendorInvoiceCreationItems.invoice_ref_number,
      vendor_invoice_number: invoiceText.trim(),
      po_num: po,
      invoice_date: convertDateFormat(date, 'yyyy-mm-dd'),
      version: vendorInvoiceCreationItems.version,
      quantity: e.invoiceData.quantity,
      ...additionalFields
    };
  };

  const updateItem = (items, product_id, updates) => {
    const newItems = [...items];
    const index = newItems.findIndex((x) => Number(x.product_id) === Number(product_id));
    if (index > -1) {
      newItems[index] = { ...newItems[index], ...updates };
    } else {
      newItems.push(updates);
    }
    return newItems;
  };

  const processBulkQcData = (items, e) => {
    let newItems = [...items];
    bulkQcData.forEach((data, i) => {
      const {
        product_id,
        bulk_qc_failed_qty,
        reason,
        short_qty,
        id,
        cgst_per,
        igst_per,
        ugst_per,
        quantity,
        vendor_unit_cost_price
      } = data;
      const updates = isCLtype
        ? { bulk_qc_failed_qty, reason, quantity: newItems[i]?.quantity, short_qty: 0 }
        : { bulk_qc_failed_qty, reason, short_qty: Number(short_qty) - Number(bulk_qc_failed_qty) };

      newItems = updateItem(newItems, product_id, {
        id: id || undefined,
        product_id: +product_id,
        cgst_per,
        igst_per,
        ugst_per,
        short_qty,
        quantity,
        vendor_unit_cost_price,
        ...updates
      });
    });

    if (!isCLtype) {
      newItems = newItems.map((t, i) => {
        const bulkquantity = Number(t.bulk_qc_failed_qty) || 0;
        if (!t.product_id) {
          return { ...t, product_id: 99999 };
        }
        if (!t.id) {
          return { ...t, desc: e?.invoiceData[i]?.name, out_of_po_qty: t.quantity };
        }
        return {
          ...t,
          short_qty:
            Number(t.quantity) -
            Number(invoice_items[i].accepted_quantity) -
            Number(invoice_items[i].rejected_quantity) -
            bulkquantity
        };
      });
    }

    return newItems;
  };

  const closeInvoiceSubmit = (e) => {
    const obj = invoiceReq(e);
    const items = obj?.items ?? [];
    const updatedItems = processBulkQcData(items, e);
    dispatch(closeInvoiceLoad({ ...obj, items: updatedItems }));
  };

  const quantityValidation = (fields, index) => {
    const { isExist, accepted_quantity, rejected_quantity } = fields.value[index];
    const min = Number(accepted_quantity) + Number(rejected_quantity);
    if (isExist && closeInvoice) {
      return composeValidators(required, integer, minGrnNumber(min));
    }
    const { quantity_copy } = fields.value[index];
    const max = Number(quantity_copy);
    if (editMode) {
      return composeValidators(required, integer, minGrnNumber(min), maxGrnNumber(max));
    }
    return composeValidators(required, integer, maxGrnNumber(max));
  };

  const productIdValidation = (fields, index) => {
    const { product_id } = fields.value[index];
    if (closeInvoice) {
      return composeValidators(integer, validDuplicateProducId(fields.value, product_id, index));
    }
    if (editMode) {
      return composeValidators(
        required,
        integer,
        validDuplicateProducId(fields.value, product_id, index)
      );
    }
    return null;
  };

  useKeyboardShortcut([18, 68], () => {
    setShowModal(true);
  });

  const handleSubmitFun = (e) => {
    if (isCLtype) {
      const { po_num, invoice_date } = vendorInvoiceCreationItems;
      const obj = {
        ...clData,
        vendor_invoice_number: invoiceText,
        invoice_date: invoice_date || date,
        po_num,
        type: 'CL',
        editMode
      };
      dispatch(createVendorInvoiceLoad(obj));
    } else {
      const obj = invoiceReq(e);
      dispatch(createVendorInvoiceLoad(obj));
    }
  };

  useKeyboardShortcut([18, 83], () => {
    const { valid, values } = formData;
    if (!createVendorInvoiceLoading && !isGrnOpenData?.isGRNOpen && valid && !invoiceStatus) {
      handleSubmitFun(values);
    }
  });

  useKeyboardShortcut([18, 78], () => {
    const { valid, values, fields } = formData;
    if (
      ((values?.invoiceData &&
        values.invoiceData?.filter((x) => x.active).length !== poPids.length) ||
        closeInvoice ||
        editMode) &&
      valid
    ) {
      fields.push({ id: values.length, isExist: false, active: true });
    }
  });

  useKeyboardShortcut([17, 73], () => {
    if (!(closeInvoice || isGrnOpenData?.isGRNOpen)) {
      setUploadCsvModal(true);
    }
  });

  const selectFile = (e) => {
    selectedImportFile = e;
  };

  const goToInvoiceDetail = () => {
    if (!isImportInvoiceUpload) {
      if (vendorInvoiceStatus?.infoMsg && Boolean(vendorInvoiceStatus?.infoMsg.length)) {
        navigate(`/invoice/view?invoiceRefNumber=${
          JSON.parse(vendorInvoiceStatus?.infoMsg[0])?.invoice_ref_number
        }`);
      } else if (vendorInvoiceCreationItems?.invoice_ref_number) {
        navigate(
          `/invoice/view?invoiceRefNumber=${vendorInvoiceCreationItems?.invoice_ref_number}`
        );
      }
    } else {
      isImportInvoiceUpload = false;
      if (vendorInvoiceStatus?.infoMsg && Boolean(vendorInvoiceStatus?.infoMsg.length)) {
        navigate(`/invoice/edit?invoiceRefNo=${
          JSON.parse(vendorInvoiceStatus?.infoMsg[0])?.invoice_ref_number
        }&editMode=true`);
      }
    }
  };

  const taxPercentageRateView = (taxRate, taxPercent) =>
    // eslint-disable-next-line
    (taxPercent && taxRate) || (taxPercent == 0 && taxRate == 0) ? (
      <>
        <div>{`${convertedCurrency} ${parseFloat(taxRate).toFixed(2)}`}</div>
        <div className="fs10 text-cb mr-t-10">{parseFloat(taxPercent).toFixed(2)}%</div>
      </>
    ) : null;

  // disabling the input boxed when we click on Done button
  useEffect(() => {
    if (createVendorInvoiceLoading || vendorInvoiceStatusLoading) {
      disableInputs.disable = true;
    } else if (
      vendorInvoiceStatusFail ||
      createVendorInvoiceData?.error?.response?.data?.meta?.displayMessage ||
      vendorInvoiceStatus
    ) {
      disableInputs.disable = false;
    }
  }, [
    createVendorInvoiceLoading,
    vendorInvoiceStatusLoading,
    createVendorInvoiceData,
    vendorInvoiceStatusFail,
    vendorInvoiceStatus
  ]);

  const handlePidChange = (fields, index, pid, form) => {
    if (pid) {
      let currentField = { ...poItems[pid] };
      currentField = calculateValue(currentField, taxes);
      fields.update(index, currentField);
      let totalPrice = 0;
      fields.value.forEach((f) => {
        if (!Number.isNaN(Number(f.price_with_taxes)) && f.active) {
          totalPrice += f.price_with_taxes;
        }
      });
      if (!Number.isNaN(Number(currentField.price_with_taxes))) {
        totalPrice += currentField.price_with_taxes;
      }
      form.mutators.setTotal(totalPrice);
    } else {
      fields.update(index, { id: 0, active: true });
      let totalPrice = 0;
      fields.value.forEach((f) => {
        if (!Number.isNaN(Number(f.price_with_taxes)) && f.active && f.product_id !== pid) {
          totalPrice += f.price_with_taxes;
        }
      });
      form.mutators.setTotal(totalPrice);
    }
  };

  const handleRemovePid = useCallback((fields, i, form) => {
    const len = fields.value.length;
    const activeRow = fields.value.filter((x) => x.active).length;
    if (len === 1 || activeRow === 1) {
      fields.update(i, { id: 0, active: true });
    } else {
      fields.update(i, { id: 0, active: false });
    }
    let totalPrice = 0;
    fields.value.forEach((f, index) => {
      if (!Number.isNaN(Number(f.price_with_taxes)) && index !== i && f.active) {
        totalPrice += f.price_with_taxes;
      }
    });
    form.mutators.setTotal(totalPrice);
  }, []);

  const submitInvoiceModal = useCallback(
    (e, value) => {
      const { vendorInvoiceDate, vendorInvoiceNumber, batch_no_list = [] } = value;
      const obj = invoiceReq(e);
      obj.invoice_date = vendorInvoiceDate;
      obj.vendor_invoice_number = vendorInvoiceNumber.trim();
      obj.batch_no_list = batch_no_list;
      dispatch(createVendorInvoiceLoad(obj));
    },
    [dispatch, createVendorInvoiceLoad]
  );

  const importInvoice = useCallback(() => {
    const invoiceFormData = new FormData();
    invoiceFormData.set('invoice_ref_no', invoice_ref_number);
    invoiceFormData.set('file', selectedImportFile);
    invoiceFormData.set('vendor_invoice_number', invoiceText);
    invoiceFormData.set('po_num', po);
    invoiceFormData.set('invoice_date', convertDateFormat(date, 'yyyy-mm-dd'));
    invoiceFormData.set('version', version);
    dispatch(importInvoiceLoad(invoiceFormData));
  }, [dispatch, importInvoiceLoad]);

  const resetState = useCallback(() => {
    setUploadCsvModal(false);
  }, [uploadCsvModal]);

  const downloadErrorReport = useCallback(() => {
    if (importInvoiceData?.error?.response?.data?.data?.file) {
      dispatch(downloadErrorInvoiceLoad(importInvoiceData.error.response.data.data.file));
    }
  }, [importInvoiceData, dispatch]);

  useEffect(() => {
    if (downloadCsvErrorData?.response?.data) {
      fileDownload(downloadCsvErrorData?.response?.data, 'InvoiceErrorReport');
    }
  }, [downloadCsvErrorData]);

  const getInvoiceStausObject = (refNo) => ({ refNo });

  useEffect(() => {
    if (importInvoiceData?.response?.data?.data) {
      isImportInvoiceUpload = true;
      setUploadCsvModal(false);
      setUploadSuccessModal(true);
      setInvoiceStatus(getInvoiceStausObject(importInvoiceData.response.data.data?.req_id));
    } else if (importInvoiceData?.error?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: importInvoiceData.error.response.data.meta.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [importInvoiceData]);

  useEffect(() => {
    if (statusSuccessFul) {
      goToInvoiceDetail();
    }
  }, [statusSuccessFul]);

  useEffect(() => {
    if (createVendorInvoiceData?.error?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createVendorInvoiceData.error.response.data.meta.displayMessage,
          severity: 'error'
        })
      );
    } else if (createVendorInvoiceData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createVendorInvoiceData.response.data.meta.displayMessage,
          severity: 'success'
        })
      );
      if (isCLtype) {
        navigate(
          `/invoice/view?invoiceRefNumber=${createVendorInvoiceData?.response?.data?.data?.invoiceRefNumber}`
        );
        dispatch(resetVendorInvoiceCreationData());
      } else {
        setInvoiceStatus(getInvoiceStausObject(createVendorInvoiceData.response.data?.data));
      }
    }
  }, [createVendorInvoiceData, dispatch]);

  useEffect(() => {
    if (closeInvoiceData?.error?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: closeInvoiceData.error.response.data.meta.displayMessage,
          severity: 'error'
        })
      );
    } else if (closeInvoiceData?.response?.data?.meta?.displayMessage) {
      bulkQcData = [];
      setCloseInvoiceModal(false);
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: closeInvoiceData.response.data.meta.displayMessage,
          severity: 'success'
        })
      );
      setInvoiceStatus(getInvoiceStausObject(closeInvoiceData.response.data.data));
    }
  }, [closeInvoiceData, dispatch]);

  const cancelInvoice = () => {
    if (invoice_ref_number) {
      navigate(`/invoice/view?invoiceRefNumber=${invoice_ref_number}`);
    } else {
      navigate('/invoice/dashboard');
    }
  };

  useEffect(() => {
    if (!closeInvoice && !editMode) {
      poPids.forEach((pp) => {
        if (prefilled_invoice) {
          const { quantity, product_id } = po_items[pp];
          const index = invoice_items.findIndex((x) => Number(x.product_id) === Number(product_id));
          if (index > -1) {
            poItems[pp] = {
              ...po_items[pp],
              isExist: true,
              quantity: invoice_items[index].quantity,
              quantity_copy: Number(quantity),
              active: true
            };
          }
        } else {
          const { pending_quantity } = po_items[pp];
          poItems[pp] = {
            ...po_items[pp],
            isExist: true,
            quantity: Number(pending_quantity),
            quantity_copy: Number(pending_quantity),
            active: true
          };
        }
      });
    }
    return () => {
      dispatch(closeInvoiceReset());
      dispatch(downloadErrorInvoiceReset());
      dispatch(importInvoiceReset());
      dispatch(resetVendorInvoiceCreationData());
      dispatch(invoiceViewReset());
      dispatch(getVendorInvoiceCreationStatusReset());
      bulkQcData = [];
      selectedImportFile = undefined;
      handleCancel();
      poItems = {};
      isImportInvoiceUpload = false;
    };
  }, []);

  const confirmBulkQc = (data) => {
    bulkQcData = [...data];
  };

  const addPidButton = (values, valid, fields) => (
    <LkToolTip placement="bottom" title={<ShortCut name="Alt+N" />} open={isShowShortCuts}>
      <Button
        color="primary"
        style={{ marginRight: 10, borderRadius: 8, height: 36 }}
        disabled={
          (values.invoiceData &&
            values.invoiceData.filter((x) => x.active).length === poPids.length &&
            !(closeInvoice || editMode)) ||
          !valid
        }
        onClick={() =>
          valid ? fields.push({ id: values.length, isExist: false, active: true }) : null
        }
        variant="outlined"
        startIcon={<AddCircle />}
      >
        {INVOICE.ADD_PID}
      </Button>
    </LkToolTip>
  );

  const submitAddOnlyPid = useCallback((fields, values, val, name) => {
    fields.push({
      id: values.length,
      product_id: val.scanned_id,
      name,
      isExist: false,
      active: true
    });
    setShowAddByProductDescriptionModal(false);
  }, []);

  const goToOpenGrns = () => {
    navigate(`/grn/listing?invoiceRef=${invoice_ref_number}`);
  };

  let index = 0;

  const scrollTable = (event) => {
    const tBody = event.target;
    if (
      tBody.scrollTop >= tBody.scrollHeight - 800 &&
      invoice_items.length >= recordsShow &&
      !loader
    ) {
      setLoader(true);
    }
  };

  useEffect(() => {
    if (loader) {
      setTimeout(() => {
        setRecordsShow((t) => t + PAGE_SIZE);
        setLoader(false);
      }, 300);
    }
  }, [loader]);

  function renderContent(i, row, taxType = 'ugst_per', fields, form, taxtRate, taxPer) {
    if (i === editRow) {
      return createTextField(
        row,
        taxType,
        composeValidators(required, floatNumber),
        false,
        'taxPer',
        classes,
        fields,
        i,
        taxes,
        form,
        closeInvoice,
        {
          endAdornment: <InputAdornment position="end">%</InputAdornment>
        }
      );
    }
    if (vendorText) {
      return taxPercentageRateView(fields.value[i][taxtRate], fields.value[i][taxPer]);
    }
    return null;
  }

  return (
    <>
      <Form
        initialValues={{ total, vendorInvoiceNumber: invoiceText, vendorInvoiceDate: date }}
        mutators={{
          ...arrayMutators,
          setTotal: (args, state, utils) => {
            utils.changeValue(state, 'total', () => args);
          },
          setInvoiceData: (args, state, utils) => {
            utils.changeValue(state, 'invoiceData', () => args[0]);
          }
        }}
        onSubmit={(e) => handleSubmitFun(e)}
        render={({ handleSubmit, form, values, valid }) => {
          mutators = form.mutators;
          const visitedPid = new Set();
          if (!closeInvoice && !editMode) {
            values?.invoiceData?.forEach((id) => visitedPid.add(id.product_id));
          }
          return (
            <form onSubmit={handleSubmit}>
              <FieldArray initialValue={rowsTemplate} name="invoiceData">
                {({ fields }) => {
                  formData = { ...formData, values, valid, fields };
                  index = 0;
                  return (
                    <div className={classes.root}>
                      <InfoStrip
                        data={[
                          ...infoStripData,
                          {
                            key: INVOICE.TOTAL,
                            value: `${convertedCurrency} ${roundUptoFixedDigits(
                              isCLtype ? clData.total_invoice_amount || 0 : Number(values.total),
                              2
                            )}`
                          }
                        ]}
                      >
                        {!isCLtype && addPidButton(values, valid, fields)}
                        {closeInvoice ? (
                          <>
                            <Button
                              color="primary"
                              style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                              variant="outlined"
                              onClick={() => setBulkQcFailModal(true)}
                            >
                              {INVOICE.BULK_QC_FAIL}
                            </Button>

                            <Button
                              color="primary"
                              style={{ borderRadius: 8, height: 36 }}
                              variant="contained"
                              disabled={
                                !valid ||
                                closeInvoiceLoading ||
                                invoiceStatus ||
                                !(status === 'CREATED' || status === 'IQC_COMPLETE') ||
                                closeAllOpenGrnLoading
                              }
                              onClick={() => setCloseInvoiceModal(true)}
                            >
                              {INVOICE.COMPLETE}
                            </Button>
                          </>
                        ) : (
                          <>
                            {!isCLtype && (
                              <Button
                                color="primary"
                                style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                                onClick={() => handleClearAll(fields, form)}
                                disabled={!!(closeInvoice || editMode)}
                                variant="outlined"
                              >
                                {INVOICE.CLEAR_ALL}
                              </Button>
                            )}
                            {showCLTable && (
                              <>
                                <Button
                                  className={classes.cancelButton}
                                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                                  color="primary"
                                  disabled={createVendorInvoiceLoading}
                                  onClick={() => cancelInvoice()}
                                  variant="outlined"
                                >
                                  {INVOICE.CANCEL}
                                </Button>
                                <LkToolTip
                                  placement="bottom"
                                  title={<ShortCut name="Alt+S" />}
                                  open={isShowShortCuts}
                                >
                                  <Button
                                    color="primary"
                                    style={{ borderRadius: 8, height: 36 }}
                                    type="submit"
                                    disabled={
                                      createVendorInvoiceLoading ||
                                      isGrnOpenData?.isGRNOpen ||
                                      !valid ||
                                      invoiceStatus ||
                                      (isCLtype
                                        ? !clData.total_invoice_qty || !clData.total_invoice_amount
                                        : false)
                                    }
                                    variant="contained"
                                  >
                                    {!createVendorInvoiceLoading ? INVOICE.DONE : <Spinner />}
                                  </Button>
                                </LkToolTip>
                              </>
                            )}
                          </>
                        )}
                        {editMode && (
                          <LkToolTip
                            placement="bottom"
                            title={
                              <div
                                className=" fs14 pd-12  cursor-pointer "
                                onClick={() => dispatch(closeAllOpenGRNLoad(invoice_ref_number))}
                              >
                                {INVOICE.CLOSE_GRNS}
                              </div>
                            }
                            width="200px"
                          >
                            <IconButton className="mr-l12">
                              <MoreVertIcon />
                            </IconButton>
                          </LkToolTip>
                        )}
                        {!status && <Shortcuts data={shortcuts} />}
                      </InfoStrip>
                      {isCLtype ? (
                        <InvoiceSummaryCL
                          data={vendorInvoiceCreationItems || {}}
                          vendor_invoice_number={invoiceText}
                          invoice_date={date}
                          clData={clData}
                          setData={setCLData}
                          inputKeyDown={inputKeyDown}
                          toogleClTable={toogleClTable}
                          showTable={showCLTable}
                          currency={convertedCurrency}
                        />
                      ) : (
                        <CardContent className={classes.content}>
                          <TableContainer
                            component={Paper}
                            className={classes.table}
                            onScroll={(event) => scrollTable(event)}
                          >
                            <Table stickyHeader aria-label="sticky table">
                              <TableHead>
                                <TableRow>
                                  <StyledTableCell align="left" style={{ width: '1vw' }}>
                                    {INVOICE.SNO}
                                  </StyledTableCell>
                                  <StyledTableCell align="left">{INVOICE.PID1}</StyledTableCell>
                                  <StyledTableCell align="left" style={{ width: '200px' }}>
                                    {INVOICE.ITEM_DETAILS}
                                  </StyledTableCell>
                                  <StyledTableCell align="right">
                                    {INVOICE.QUANTITY}
                                  </StyledTableCell>
                                  <StyledTableCell align="right">
                                    {INVOICE.UNIT_PRICE}
                                  </StyledTableCell>
                                  <StyledTableCell align="right">
                                    {INVOICE.TAXABLE_AMOUNT}
                                  </StyledTableCell>
                                  {taxes.includes('cgst') && (
                                    <StyledTableCell align="right">{INVOICE.CGST}</StyledTableCell>
                                  )}
                                  {taxes.includes('igst') && (
                                    <StyledTableCell align="right">{INVOICE.IGST}</StyledTableCell>
                                  )}
                                  {taxes.includes('sgst') && (
                                    <StyledTableCell align="right">{INVOICE.SGST}</StyledTableCell>
                                  )}
                                  {taxes.includes('ugst') && (
                                    <StyledTableCell align="right">{INVOICE.UGST}</StyledTableCell>
                                  )}
                                  {taxes.includes('flat') && (
                                    <StyledTableCell align="right">{INVOICE.FLAT}</StyledTableCell>
                                  )}
                                  <StyledTableCell align="right">
                                    {INVOICE.TOTAL_PRICE}
                                  </StyledTableCell>
                                  <StyledTableCell align="center" />
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {fields.map((row, i) => {
                                  if (index > recordsShow) {
                                    return null;
                                  }
                                  if (fields.value[i]?.active) {
                                    index += 1;
                                    return (
                                      <StyledTableRow key={index}>
                                        <StyledTableCell
                                          align="left"
                                          component="th"
                                          scope="row"
                                          style={{ width: '1vw' }}
                                        >
                                          <div>{index}</div>
                                        </StyledTableCell>
                                        <StyledTableCell
                                          align="left"
                                          component="th"
                                          scope="row"
                                          style={{ width: '200px' }}
                                        >
                                          {closeInvoice || editMode ? (
                                            createTextField(
                                              row,
                                              'product_id',
                                              productIdValidation(fields, i),
                                              fields.value[i]?.isExist,
                                              'product_id',
                                              classes,
                                              fields,
                                              i,
                                              taxes,
                                              form,
                                              closeInvoice
                                            )
                                          ) : (
                                            <Field
                                              name={`${row}.product_id`}
                                              validate={!closeInvoice ? required : null}
                                            >
                                              {({ input, ...rest }) =>
                                                !input.value ||
                                                values.invoiceData?.[i]?.hasFocused ? (
                                                    <Autocomplete
                                                      {...rest}
                                                      options={poPids || []}
                                                      onInputChange={(event, newInputValue) => {
                                                        values.invoiceData[i].hasFocused = true;
                                                        values.invoiceData[i].product_id =
                                                        newInputValue;
                                                        mutators.setInvoiceData(values.invoiceData);
                                                      }}
                                                      onChange={(event, newValue) => {
                                                        values.invoiceData[i].product_id = newValue;
                                                        values.invoiceData[i].hasFocused = false;
                                                        mutators.setInvoiceData(values.invoiceData);
                                                        input.onChange(newValue);
                                                        handlePidChange(fields, i, newValue, form);
                                                      }}
                                                      // onKeyUp={(e) => delayedQuery(e.target.value)}
                                                      getOptionLabel={(option) => option}
                                                      renderOption={(renderProps, option) => {
                                                        if (!visitedPid.has(parseInt(option, 10))) {
                                                          return (
                                                            <li {...renderProps} key={option}>
                                                              <MenuItem key={option} value={option}>
                                                                {option}
                                                              </MenuItem>
                                                            </li>
                                                          );
                                                        }
                                                      }}
                                                      renderInput={(params) => (
                                                        <LkInput
                                                          {...params}
                                                          {...input}
                                                          className="input-box"
                                                          fullWidth
                                                          label={INVOICE.ENTER_PID}
                                                        />
                                                      )}
                                                    />
                                                  ) : (
                                                    <div>{input.value}</div>
                                                  )
                                              }
                                            </Field>
                                          )}
                                        </StyledTableCell>
                                        <StyledTableCell
                                          align="left"
                                          component="th"
                                          scope="row"
                                          style={{ width: '200px' }}
                                        >
                                          {!closeInvoice &&
                                          !editMode &&
                                          !values.invoiceData?.[i]?.hasFocused ? (
                                              <LkToolTip
                                                placement="bottom-end"
                                                title={
                                                  <div className="pd-10">
                                                    {
                                                      po_items[values?.invoiceData?.[i]?.product_id]
                                                        ?.name
                                                    }
                                                  </div>
                                                }
                                              >
                                                <div className="ellipsis-vertical">
                                                  {
                                                    po_items[values?.invoiceData?.[i]?.product_id]
                                                      ?.name
                                                  }
                                                </div>
                                              </LkToolTip>
                                            ) : (
                                              createTextField(
                                                row,
                                                'name',
                                                composeValidators(required),
                                                fields.value[i]?.isExist,
                                                'name',
                                                classes,
                                                fields,
                                                i,
                                                taxes,
                                                form,
                                                closeInvoice
                                              )
                                            )}
                                        </StyledTableCell>
                                        <StyledTableCell align="right">
                                          {createTextField(
                                            row,
                                            'quantity',
                                            quantityValidation(fields, i),
                                            false,
                                            'quantity',
                                            classes,
                                            fields,
                                            i,
                                            taxes,
                                            form,
                                            closeInvoice
                                          )}
                                        </StyledTableCell>
                                        <StyledTableCell align="right">
                                          {createTextField(
                                            row,
                                            'vendor_unit_cost_price',
                                            composeValidators(required, floatNumber),
                                            false,
                                            'costPrice',
                                            classes,
                                            fields,
                                            i,
                                            taxes,
                                            form,
                                            closeInvoice,
                                            {
                                              startAdornment: (
                                                <InputAdornment position="start">
                                                  {vendorText ? convertedCurrency : ''}
                                                </InputAdornment>
                                              )
                                            }
                                          )}
                                        </StyledTableCell>
                                        <StyledTableCell align="right">
                                          <div>
                                            {vendorText ? convertedCurrency : ''}{' '}
                                            {(() => {
                                              const totalCost =
                                                fields.value[i].quantity *
                                                fields.value[i].vendor_unit_cost_price;
                                              return Number.isNaN(totalCost)
                                                ? 0
                                                : roundUptoFixedDigits(Number(totalCost), 2);
                                            })()}
                                          </div>
                                        </StyledTableCell>
                                        {taxes.includes('cgst') && (
                                          <StyledTableCell
                                            align="right"
                                            onClick={() => setEditRow(i)}
                                            onBlur={() => setEditRow(-1)}
                                          >
                                            {renderContent(
                                              i,
                                              row,
                                              'cgst_per',
                                              fields,
                                              form,
                                              'cgst_rate',
                                              'cgst_per'
                                            )}
                                          </StyledTableCell>
                                        )}
                                        {taxes.includes('igst') && (
                                          <StyledTableCell
                                            align="right"
                                            onClick={() => setEditRow(i)}
                                            onBlur={() => setEditRow(-1)}
                                          >
                                            {renderContent(
                                              i,
                                              row,
                                              'igst_per',
                                              fields,
                                              form,
                                              'igst_rate',
                                              'igst_per'
                                            )}
                                          </StyledTableCell>
                                        )}
                                        {taxes.includes('sgst') && (
                                          <StyledTableCell
                                            align="right"
                                            onClick={() => setEditRow(i)}
                                            onBlur={() => setEditRow(-1)}
                                          >
                                            {renderContent(
                                              i,
                                              row,
                                              'sgst_per',
                                              fields,
                                              form,
                                              'sgst_rate',
                                              'sgst_per'
                                            )}
                                          </StyledTableCell>
                                        )}
                                        {taxes.includes('ugst') && (
                                          <StyledTableCell
                                            align="right"
                                            onClick={() => setEditRow(i)}
                                            onBlur={() => setEditRow(-1)}
                                          >
                                            {renderContent(
                                              i,
                                              row,
                                              'ugst_per',
                                              fields,
                                              form,
                                              'ugst_rate',
                                              'ugst_per'
                                            )}
                                          </StyledTableCell>
                                        )}
                                        {taxes.includes('flat') && (
                                          <StyledTableCell
                                            align="right"
                                            onClick={() => setEditRow(i)}
                                            onBlur={() => setEditRow(-1)}
                                          >
                                            {renderContent(
                                              i,
                                              row,
                                              'flat_per',
                                              fields,
                                              form,
                                              'flat_rate',
                                              'flat_per'
                                            )}
                                          </StyledTableCell>
                                        )}
                                        <StyledTableCell align="right">
                                          <div className="fw-bold">
                                            {vendorText ? convertedCurrency : ''}{' '}
                                            {fields.value[i].price_with_taxes
                                              ? roundUptoFixedDigits(
                                                Number(fields.value[i].price_with_taxes),
                                                2
                                              )
                                              : 0}
                                          </div>
                                        </StyledTableCell>
                                        <StyledTableCell align="right">
                                          <IconButton
                                            disabled={disableCloseField(
                                              'delete',
                                              fields,
                                              i,
                                              closeInvoice
                                            )}
                                            onClick={() => handleRemovePid(fields, i, form)}
                                            size="large"
                                          >
                                            <DeleteOutlinedIcon />
                                          </IconButton>
                                        </StyledTableCell>
                                      </StyledTableRow>
                                    );
                                  }
                                })}
                                {loader && (
                                  <StyledTableRow>
                                    <StyledTableCell colSpan={11} align="center">
                                      <Spinner className="display-grid-center mr-t10 mr-b10" />
                                    </StyledTableCell>
                                  </StyledTableRow>
                                )}
                              </TableBody>
                            </Table>
                          </TableContainer>
                          <div className={`display-flex ${classes.bottomButton}`}>
                            {addPidButton(values, valid, fields)}
                            {!closeInvoice && (
                              <Button
                                color="primary"
                                style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                                disabled={
                                  (values.invoiceData &&
                                    values.invoiceData.filter((x) => x.active).length ===
                                      poPids.length &&
                                    !(closeInvoice || editMode)) ||
                                  !valid
                                }
                                onClick={() =>
                                  valid ? setShowAddByProductDescriptionModal(true) : null
                                }
                                variant="outlined"
                                startIcon={<AddCircle />}
                              >
                                {INVOICE.ADD_PRODUCT_BY_DESCRIPTION}
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      )}
                      {showAddByProductDescriptionModal && (
                        <AddPidModal
                          open={showAddByProductDescriptionModal}
                          openModal={() => setShowAddByProductDescriptionModal(false)}
                          disabledSearchByDescription
                          addOnlyPid
                          submitAddOnlyPid={(val, name) =>
                            submitAddOnlyPid(fields, values, val, name)
                          }
                          poNum={po}
                          classification={classification}
                        />
                      )}
                    </div>
                  );
                }}
              </FieldArray>
              {showModal && invoiceViewData && (
                <InvoiceDetailmodal
                  showModal={showModal}
                  setShowModal={() => setShowModal(false)}
                  invoiceViewData={invoiceViewData}
                  handleInvoiceSubmit={(value) => submitInvoiceModal(values, value)}
                  createVendorInvoiceLoading={createVendorInvoiceLoading}
                  invoiceStatus={invoiceStatus}
                  invoiceRefNum={vendorInvoiceCreationItems?.invoice_ref_number}
                />
              )}
              {closeInvoiceModal && (
                <CloseInvoiceModal
                  invoiceData={values}
                  open={closeInvoiceModal}
                  close={() => setCloseInvoiceModal(false)}
                  closeInvoiceSubmit={() => closeInvoiceSubmit(values)}
                  closeInvoiceLoading={closeInvoiceLoading}
                  currency={vendorText ? convertedCurrency : ''}
                />
              )}
              {bulkQcFailModal && (
                <BulkInvoiceModal
                  confirmBulkQc={(data) => {
                    setClFailData([...data]);
                    confirmBulkQc(data);
                  }}
                  invoiceData={values}
                  open={bulkQcFailModal}
                  clFailData={clFailData}
                  close={() => setBulkQcFailModal(false)}
                  bulkQcData={bulkQcData}
                  vendorInvoiceData={props?.invoiceData?.data}
                />
              )}
            </form>
          );
        }}
      />
      {uploadCsvModal && (
        <UploadCsv
          open={uploadCsvModal}
          onClose={() => setUploadCsvModal(false)}
          selectFiles={(e) => selectFile(e)}
          sampleCSV={sampleCSV}
          checkedItemLabel={INVOICE.OVERWRITE_EXISTING_ROWS_INVOICE}
          failDataMessage={importInvoiceData?.error?.response?.data?.data?.file}
          downloadErrorReport={downloadErrorReport}
          uploadCsvPass={importInvoiceData?.response?.data?.data}
          handleSubmit={() => importInvoice()}
          resetState={resetState}
          sampleFileName="Sample_Invoice_Item_Upload"
          localUpload
          importLoading={importInvoiceLoading}
        />
      )}
      {uploadSuccessModal && (
        <LkModal
          open={uploadSuccessModal}
          title={INVOICE.IMPORT_PROCESSED_SUCCESSFULLY}
          subTitle={INVOICE.FILE_HAS_BEEN_IMPORTED_SUCCESSFULLY}
          handleClose={() => setUploadSuccessModal(false)}
        >
          <div className="text-center">
            <Button
              style={{ borderRadius: 8 }}
              variant="contained"
              color="primary"
              onClick={() => setUploadSuccessModal(false)}
            >
              {INVOICE.OK}
            </Button>
          </div>
        </LkModal>
      )}
      {grnErrorModal && isGrnOpenData?.isGRNOpen && (
        <LkModal
          open={grnErrorModal}
          title={INVOICE.ERROR_OPEN_GRN_FOUND}
          subTitle={INVOICE.OPEN_GRN_FOUND_PLEASE_CLOSE_BEFORE_EDITING_INVOICE}
          handleClose={() => setGrnErrorModal(false)}
        >
          <div className="text-center">
            <Button
              style={{ borderRadius: 8 }}
              variant="contained"
              color="primary"
              onClick={() => goToOpenGrns()}
            >
              {INVOICE.CLICK_TO_SEE_OPEN_GRNS}
            </Button>
          </div>
        </LkModal>
      )}
      {taxFieldError && (
        <LkAlert
          severity="error"
          onClose={() => setTaxFieldError(false)}
          message={INVOICE.PLEASE_CORRECTLY_FILL_TAX_FIELDS}
        />
      )}
    </>
  );
};

export default InvoiceSummary;
