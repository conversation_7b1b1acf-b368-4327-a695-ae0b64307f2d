import Box from '@mui/material/Box';
import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@mui/material/Button';

import { useNavigate } from 'react-router-dom';

import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  RTV_LIST,
  approveRejectGatepassReset,
  submitGatepassForArrovalReset
} from 'redux/reducers/RTVlist.slice';
import { gatepassDetailsLoad } from 'redux/reducers/RTVbarcodeScan.slice';
import BarcodeListDrawer from './BarcodeListDrawer';

const classes = {
  listWrapper: {
    padding: '0',
    backgroundColor: 'white'
  },
  topRightbtn: {
    borderRadius: 4,
    padding: '10px 20px',
    color: '#00B9C6',
    fontWeight: '500',
    fontSize: '16px',
    letterSpacing: '0.4px'
  },
  container: {
    maxHeight: 500,
    minHeight: 500,
    borderRadius: '0',
    boxShadow: 'none'
  },
  headerRowCell: {
    fontSize: 14,
    lineHeight: '40px',
    letterSpacing: '0.15px',
    color: 'rgba(60, 60, 60, 0.54)',
    fontWeight: 500,
    backgroundColor: '#EFEFEF'
  },
  dataRowCell: {
    fontSize: '14px',
    lineHeight: '150%',
    letterSpacing: '0.15px',
    color: '#3C3C3C',
    wordBreak: 'break-word'
  },
  Black: {
    color: '#333333'
  },
  descRow: {
    minWidth: '250px',
    maxWidth: '250px',
    paddingLeft: '40px'
  },
  pdRight40: {
    paddingRight: '40px'
  },
  linkBtn: {
    fontWeight: '500',
    color: '#00bac6',
    fontSize: '14px'
  },
  headerContainer: {
    padding: '12px',
    background: '#ffffff',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'end'
  },
  searchIcon: {
    fontSize: '1.5rem'
  },
  primaryContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  secondaryContainer: {
    display: 'flex',
    alignItems: 'center'
  },

  actionsContainer: {
    padding: '0px 20px 25px',
    background: '#ffffff',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '-5px'
  },
  tableBotom: {
    minHeight: 52,
    background: '#ffffff',
    position: 'relative'
  },
  loader: {
    position: 'absolute',
    right: '150px',
    top: '12px'
  },
  button: {
    fontSize: '16px',
    borderRadius: '4px',
    letterSpacing: '0.4px',
    padding: '10px 20px',
    fontWeight: '500',
    minWidth: '200px',
    textTransform: 'uppercase',
    marginLeft: '20px'
  }
};

const PAGE_SIZE = 25;

const initialDrawerData = {
  pid: '',
  barcodeList: []
};

const RTVdetailsBody = ({ showEditTransfer, gatepassNum, data, isLoading }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [openDrawer, setOpenDrawer] = useState(false);
  const [drawerData, setdrawerData] = useState({ ...initialDrawerData });
  const approveRejectSuccess = useSelector((state) => state[RTV_LIST].approveReject.isSuccess);
  const submitFOrApprovalSuccess = useSelector(
    (state) => state[RTV_LIST].submitForApproval.isSuccess
  );

  const [tableHeight] = useWindowResize(window.innerHeight - 290);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.RTV_GATEPASS);

  const getFilteredList = () => {
    dispatch(gatepassDetailsLoad(gatepassNum));
  };

  useEffect(() => {
    if (submitFOrApprovalSuccess) {
      getFilteredList();
      dispatch(submitGatepassForArrovalReset());
    }
  }, [submitFOrApprovalSuccess]);

  useEffect(() => {
    if (approveRejectSuccess) {
      getFilteredList();
      dispatch(approveRejectGatepassReset());
    }
  }, [approveRejectSuccess]);

  const handleDrawer = (action) => {
    setOpenDrawer(action);
    if (!action) {
      setdrawerData({ ...initialDrawerData });
    }
  };
  const getBarcodesFunc = (pid) => {
    const barcodeList = data?.gatepassBarcodeDetailResponsesList?.filter(
      ({ product_id }) => Number(product_id) === pid
    );

    setdrawerData({ pid, barcodeList });
    handleDrawer(true);
  };

  const gotoScan = () => {
    navigate(`/RTVgatepass/scan/${gatepassNum}`);
  };

  const headerConfig = [
    {
      name: CONTENT.PRODUCT_ID,
      key: 'pid',
      align: 'left',
      marginLeft: '30px',
      formatBody: useCallback(
        ({ pid }, index) => (
          <Box
            sx={{ ...classes.linkBtn }}
            onClick={() => getBarcodesFunc(pid)}
            data-cy={`product-${index}`}
          >
            {pid}
          </Box>
        ),
        [data]
      )
    },

    {
      name: CONTENT.PID_DESC,
      key: 'productDesc',
      marginLeft: '0px',
      align: 'left'
    },
    {
      name: CONTENT.COUNT,
      key: 'count',
      marginLeft: '0px',
      align: 'center',
      style: { minWidth: '80px', maxWidth: '80px' }
    }
  ];

  const fetchMoreListItems = () => {
    getFilteredList();
  };
  return (
    <>
      <Box sx={{ ...classes.headerContainer }} data-cy="rtv-details-body">
        <Box className="button-wrapper" ml={2}>
          <Button
            size="small"
            variant="contained"
            sx={{ ...classes.button }}
            data-cy="go-to-scan-page"
            color="primary"
            startIcon={
              <img
                className="mr-r10"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/scan-icon.svg`}
                alt="validate"
              />
            }
            onClick={() => gotoScan()}
            disabled={!(data.status === 'CREATED' || data.status === 'REJECTED')}
          >
            {CONTENT.START_SCANNING}
          </Button>
        </Box>
      </Box>
      <Box sx={{ ...classes.listWrapper }}>
        {!showEditTransfer && (
          <LkTable
            tableHeight={tableHeight}
            headerConfig={headerConfig}
            isDataFetching={isLoading}
            tableData={data?.pidCounts || []}
            totalRowsCount={data?.pidCounts?.length}
            pageLimit={PAGE_SIZE}
            showTableCount={false}
            rowSize={60}
            dataRequestFunction={fetchMoreListItems}
          />
        )}
      </Box>
      <BarcodeListDrawer
        CONTENT={CONTENT}
        drawerData={drawerData}
        openDrawer={openDrawer}
        handleDrawer={handleDrawer}
      />
    </>
  );
};

export default RTVdetailsBody;
