import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Field, Form } from 'react-final-form';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';

import { InvoiceOrderStatus } from 'config/InvoiceOrderStatus';
import LkModal from 'components/Modal/Modal';
import { formatDateDashedSeparated, convertDateFormat } from 'utils/helpers';
import { required } from 'utils/validation';
import { LOCALISATION } from 'redux/reducers/localisation';
import { getAllGrnForAnInvoice, getAllGrnForAnInvoiceReset } from 'redux/actionCreators/grn';
import LkChip from 'components/MaterialUi/LkChip';
import CancelIcon from '@mui/icons-material/Cancel';

import FlexBox from '../core/FlexBox';
import { InfoTable } from '.';
import SubtitleInfoStrip from './SubtitleInfoStrip';
import Spinner from '../Spinner/Spinner';
import ChipBox from './ChipBox';
import LkInput from '../MaterialUi/LkInput';

const useStyles = makeStyles()((theme) => ({
  root: {
    // padding: theme.spacing(3),
  },
  content: {
    marginTop: theme.spacing(2)
  },
  table: {
    maxHeight: 'calc(100vh - 286px)'
  },
  textAlignCenter: {
    textAlign: 'center'
  },
  input: {
    width: theme.spacing(10)
  },
  chip: {
    borderRadius: 4,
    marginLeft: 22,
    height: 24,
    color: theme.palette.success.main,
    borderColor: '#45C476'
  },
  modalField: {
    width: 200,
    borderColor: '#DDDDDD'
  },
  batchNumberField: {
    width: '100%',
    borderColor: '#DDDDDD'
  }
}));

const InvoiceDetailModal = (props) => {
  const {
    invoiceViewData,
    showModal,
    setShowModal,
    invoiceRefNum,
    handleInvoiceSubmit,
    invoiceStatus,
    createVendorInvoiceLoading
  } = props;
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const { grnDetailsForInvoice, grnDetailsForInvoiceLoad } = useSelector((state) => state.grn);
  const [chipBoxData, setChipBoxData] = useState([]);
  const [batchNumbers, setBatchNumbers] = useState([]);
  const [batchNumber, setBatchNumber] = useState('');

  const {
    status,
    type,
    total_invoice_qty,
    order_rejected_quantity,
    order_pending_quantity,
    order_recieved_quantity,
    created_by,
    po_num,
    vendor_name,
    debit_note_number,
    invoice_number,
    created_at,
    contains_batch_no,
    batch_no_list
  } = invoiceViewData.data;
  const { code } = invoiceViewData.meta;

  const infoTableData = [
    { key: INVOICE.INVOICE_REF_NO, value: invoiceRefNum },
    { key: INVOICE.STATUS, value: InvoiceOrderStatus[status]?.text || status },
    { key: INVOICE.TYPE, value: type },
    { key: INVOICE.NEXS_INVOICE_NO, value: invoice_number },
    { key: INVOICE.DEBIT_INVOICE_NO, value: debit_note_number },
    { key: INVOICE.CREATED_BY, value: created_by },
    {
      key: INVOICE.CREATED_ON,
      value: `${convertDateFormat(created_at, 'shortDate')},${convertDateFormat(
        created_at,
        'time'
      )}`
    },
    {
      key: INVOICE.PURCHASE_ORDER,
      value: <div className="text-primary">{po_num}</div>,
      link: `${window.location.origin}/po/detail?poNum=${po_num}&fromPo=true`
    },
    { key: INVOICE.VENDOR_CODE, value: code }
  ];

  const subtitleInfoStripData = [
    { title: total_invoice_qty || 0, subtitle: 'Total Qty', subTitleClassName: 'text-99 mr-t5' },
    {
      title: order_recieved_quantity || 0,
      subtitle: INVOICE.ACCEPTED,
      subTitleClassName: 'text-99 mr-t5'
    },
    {
      title: order_rejected_quantity || 0,
      subtitle: INVOICE.REJECTED,
      titleColor: 'error.main',
      titleClassName: 'text-red',
      subTitleClassName: 'text-99 mr-t5'
    },
    {
      title: order_pending_quantity || 0,
      subtitle: INVOICE.PENDING,
      titleColor: 'warning.main',
      titleClassName: 'text-bright-yellow',
      subTitleClassName: 'text-99 mr-t5'
    }
  ];

  useEffect(() => {
    dispatch(getAllGrnForAnInvoice(invoiceRefNum));
    return () => {
      dispatch(getAllGrnForAnInvoiceReset());
    };
  }, []);

  const goToGrn = (grn) => {
    const { grn_code } = grn;
    window.open(`${window.location.origin}/grn/details/${grn_code}`);
  };

  useEffect(() => {
    if (grnDetailsForInvoice) {
      let c = [];
      c = grnDetailsForInvoice?.map((g) => {
        if (g.grn_status === 'created') {
          return { title: g.grn_code, type: 'success', onClick: () => goToGrn(g) };
        }
        if (g.grn_status === 'pending') {
          return { title: g.grn_code, type: 'pending', onClick: () => goToGrn(g) };
        }
        return { title: g.grn_code, type: 'error', onClick: () => goToGrn(g) };
      });
      setChipBoxData(c);
    }
  }, [grnDetailsForInvoice]);

  const addBatchNumber = () => {
    if (batchNumber && !batchNumbers.includes(batchNumber)) {
      setBatchNumbers((prev) => [...prev, batchNumber]);
      setBatchNumber('');
    }
  };

  useEffect(() => {
    if (batch_no_list) {
      setBatchNumbers(batch_no_list.split(',') || []);
    }
  }, [batch_no_list]);

  const deleteBatchNumber = (item) => {
    const filterBatchNumbers = batchNumbers.filter((batchNum) => batchNum !== item);
    setBatchNumbers(filterBatchNumbers);
  };

  const handleSubmit = (values) => {
    const obj = {
      ...values,
      batch_no_list: batchNumbers
    };
    handleInvoiceSubmit(obj);
  };

  return (
    <LkModal
      open={showModal}
      upperHeading="Invoice Details"
      handleClose={() => setShowModal(false)}
    >
      <Form
        initialValues={{
          vendorInvoiceNumber: invoiceViewData?.data?.vendor_invoice_number,
          vendorInvoiceDate: formatDateDashedSeparated(invoiceViewData?.data?.invoice_date)
        }}
        onSubmit={handleSubmit}
        render={({ values, valid }) => (
          <div className="pos-rel" style={{ overflow: 'scroll' }}>
            <FlexBox pt={2.5} justifyContent="space-between" width="750px">
              <InfoTable
                titleKey={INVOICE.VENDOR_INVOICE_NO}
                titleValue={invoiceViewData?.data?.vendor_invoice_number}
                data={infoTableData}
                header={vendor_name}
              />
              <Box width={430}>
                <SubtitleInfoStrip data={subtitleInfoStripData} />
                <FlexBox mt={3.75} justifyContent="space-between">
                  <Field name="vendorInvoiceNumber" validate={required}>
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.modalField}
                        {...input}
                        error={meta.error && meta.touched}
                        label={INVOICE.VENDOR_INVOICE_NO}
                        variant="outlined"
                      />
                    )}
                  </Field>

                  <Field name="vendorInvoiceDate" validate={required}>
                    {({ input, meta }) => (
                      <LkInput
                        className={classes.modalField}
                        {...input}
                        error={meta.error && meta.touched}
                        label={INVOICE.VENDOR_INVOICE_DATE}
                        type="date"
                        variant="outlined"
                        inputProps={{
                          max: formatDateDashedSeparated(Date.now())
                        }}
                      />
                    )}
                  </Field>
                </FlexBox>
                {grnDetailsForInvoice && Boolean(grnDetailsForInvoice.length) && (
                  <ChipBox
                    maxHeight={112}
                    mt={3.75}
                    value={chipBoxData}
                    label={INVOICE.GRNS_ADDED}
                  />
                )}
                {!grnDetailsForInvoice && !grnDetailsForInvoiceLoad && (
                  <div className="mr-20 text-center">{INVOICE.NO_GRN} </div>
                )}
                {grnDetailsForInvoiceLoad && (
                  <div className="mr-20 text-center">{INVOICE.GRN_LOADING} ...</div>
                )}
                {contains_batch_no && (
                  <Box
                    sx={{
                      marginTop: '30px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '10px'
                    }}
                    mt={2.75}
                  >
                    <Box>
                      <Field name="batchNumber">
                        {({ input }) => (
                          <LkInput
                            className={classes.batchNumberField}
                            {...input}
                            number
                            label={INVOICE.BATCH_NUMBER}
                            variant="outlined"
                            value={batchNumber}
                            onChange={(e) => setBatchNumber(e.target.value.trim())}
                            onKeyPress={(e) => e.which === 13 && addBatchNumber()}
                          />
                        )}
                      </Field>
                    </Box>
                    <Box sx={{
                      display: 'flex',
                      gap: '10px'
                    }}
                    >
                      {batchNumbers.map(
                        (item) =>
                          item && (
                            <LkChip
                              key={item}
                              label={item}
                              type="filter"
                              deleteIcon={
                                <CancelIcon
                                  style={{ color: 'rgba(60, 60, 60, 0.23)', fontSize: '16px' }}
                                />
                              }
                              onDelete={() => deleteBatchNumber(item)}
                            />
                          )
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            </FlexBox>
            <Box m="auto" mt={3.25} className={classes.textAlignCenter}>
              <Button
                style={{ marginRight: 10, width: 192, borderRadius: 8 }}
                color="primary"
                onClick={() => setShowModal(false)}
                variant="outlined"
              >
                {INVOICE.CANCEL_LOWERCASE}
              </Button>
              <Button
                color="primary"
                style={{ width: 192, borderRadius: 8 }}
                type="submit"
                disabled={createVendorInvoiceLoading || !valid || invoiceStatus}
                onClick={() => handleSubmit(values)}
                variant="contained"
              >
                {!createVendorInvoiceLoading ? INVOICE.SAVE_AND_CLOSE : <Spinner />}
              </Button>
            </Box>
          </div>
        )}
      />
    </LkModal>
  );
};

export default React.memo(InvoiceDetailModal);
