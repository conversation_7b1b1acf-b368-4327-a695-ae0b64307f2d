// putaway list in grnHome page
export const USER_PUTAWAY_LIST_LOAD = 'USER_PUTAWAY_LIST_LOAD';
export const USER_PUTAWAY_LIST_SUCCESS = 'USER_PUTAWAY_LIST_SUCCESS';
export const USER_PUTAWAY_LIST_FAIL = 'USER_PUTAWAY_LIST_FAIL';
export const USER_PUTAWAY_LIST_RESET = 'USER_PUTAWAY_LIST_RESET';

// mark putaway pending

export const UPDATE_PUTAWAY_TO_PENDING_LOAD = 'UPDATE_PUTAWAY_TO_PENDING_LOAD';
export const UPDATE_PUTAWAY_TO_PENDING_SUCCESS = 'UPDATE_PUTAWAY_TO_PENDING_SUCCESS';
export const UPDATE_PUTAWAY_TO_PENDING_FAIL = 'UPDATE_PUTAWAY_TO_PENDING_FAIL';
export const UPDATE_PUTAWAY_TO_PENDING_RESET = 'UPDATE_PUTAWAY_TO_PENDING_RESET';

// Print putaway
export const PRINT_PUTAWAY_LOAD = 'PRINT_PUTAWAY_LOAD';
export const PRINT_PUTAWAY_SUCCESS = 'PRINT_PUTAWAY_SUCCESS';
export const PRINT_PUTAWAY_FAIL = 'PRINT_PUTAWAY_FAIL';
export const PRINT_PUTAWAY_RESET = 'PRINT_PUTAWAY_RESET';

// Create putaway for release barcode

export const CREATE_PUTAWAY_LOAD = 'CREATE_PUTAWAY_LOAD';
export const CREATE_PUTAWAY_SUCCESS = 'CREATE_PUTAWAY_SUCCESS';
export const CREATE_PUTAWAY_FAIL = 'CREATE_PUTAWAY_FAIL';
export const CREATE_PUTAWAY_RESET = 'CREATE_PUTAWAY_RESET';

// putaway barcode list update
export const UPDATE_PUTAWAY_BARCODE_LIST = 'UPDATE_PUTAWAY_BARCODE_LIST';

export const COMPLETE_PUTAWAY_SG_LOAD = 'COMPLETE_PUTAWAY_SG_LOAD';
export const COMPLETE_PUTAWAY_SG_SUCCESS = 'COMPLETE_PUTAWAY_SG_SUCCESS';
export const COMPLETE_PUTAWAY_SG_FAIL = 'COMPLETE_PUTAWAY_SG_FAIL';
export const COMPLETE_PUTAWAY_SG_RESET = 'COMPLETE_PUTAWAY_SG_RESET';
;
export const RESET_PUTAWAY_BARCODE_LIST = 'RESET_PUTAWAY_BARCODE_LIST';

export const PRINT_MULTIPLE_PUTAWAY_LOAD = 'PRINT_MULTIPLE_PUTAWAY_LOAD';
export const PRINT_MULTIPLE_PUTAWAY_SUCCESS = 'PRINT_MULTIPLE_PUTAWAY_SUCCESS';
export const PRINT_MULTIPLE_PUTAWAY_FAIL = 'PRINT_MULTIPLE_PUTAWAY_FAIL';
export const PRINT_MULTIPLE_PUTAWAY_RESET = 'PRINT_MULTIPLE_PUTAWAY_RESET';
