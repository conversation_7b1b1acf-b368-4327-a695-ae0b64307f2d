import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

const ARRAY_BUFFER_RESPONSE = { responseType: 'arraybuffer' };

export const getEinvoiceListApi = (payload) =>
  tokenInstance().post(config.eInvoice.eInvoiceListAPI, payload);

export const exportEinvoiceListApi = (payload) =>
  tokenInstance().post(config.eInvoice.exportEinvoice, payload, ARRAY_BUFFER_RESPONSE);

export const retryEinvoiceListApi = (payload) =>
  tokenInstance().post(config.eInvoice.retryEinvoice, payload);

export const printEinvoiceListApi = (payload) =>
  tokenInstance().post(config.eInvoice.printEinvoice, payload, ARRAY_BUFFER_RESPONSE);
