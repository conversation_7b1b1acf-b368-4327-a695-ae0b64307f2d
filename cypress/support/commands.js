/* eslint-disable import/no-extraneous-dependencies */
import '@testing-library/cypress/add-commands';

Cypress.Commands.add('getByCy', (selector, ...args) =>
  cy.get(`[data-cy="${String(selector)}"]`, ...args)
);

Cypress.Commands.add('getByTestId', (selector, ...args) =>
  cy.get(`[data-testid="${String(selector)}"]`, ...args)
);

Cypress.Commands.add('getByName', (selector, ...args) =>
  cy.get(`[name="${String(selector)}"]`, ...args)
);

Cypress.Commands.add('firstRowTableCell', (index, value, ...args) =>
  cy.get(`[data-cy="table-cell-0-${String(index)}"]`, ...args).should('contain.text', value ?? '-')
);

Cypress.Commands.add('headerTableCell', (index, value, ...args) =>
  cy
    .get(`[data-cy="table-header-cell-${String(index)}"]`, ...args)
    .should('contain.text', value ?? '-')
);

Cypress.Commands.add('dispatch', (action) => {
  cy.window().its('store').invoke('dispatch', action);
});

Cypress.Commands.add('useSelector', (state) =>
  cy.window().its('store').invoke('getState').its(state)
);

Cypress.Commands.add(
  'login',
  (userName = Cypress.env('USERNAME'), password = Cypress.env('PASSWORD')) => {
    cy.session([userName, password], () => {
      cy.visit('/login');
      cy.get('input[name="email"]').type(userName);
      cy.get('input[name="Password"]').type(password);
      cy.findByRole('button', { name: /login/i }).click();
      cy.url().should('contain', '/dashboard');
    });
  }
);

Cypress.Commands.add('logout', () => {
  cy.findByRole('img', {
    name: /remy sharp/i
  }).click();
  cy.getByCy('logout').click();
});

Cypress.Commands.add('navigate', (navigationList) => {
  navigationList.forEach((data_cy) => {
    cy.getByCy(data_cy).click();
  });
});

Cypress.Commands.add('waitForApi', (method, route, action) => {
  const routeAlias = 'routeAlias';
  cy.intercept(method, route).as(routeAlias);
  action();
  cy.wait(`@${routeAlias}`).its('response.statusCode').should('eq', 200);
  return routeAlias;
});
