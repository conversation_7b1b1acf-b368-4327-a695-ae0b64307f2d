@use "sass:map";
@use "sass:math";
@use './variables' as *;

.form-control {
    background: $color-white;
    color: map.get($colors-grey, grey1);
    border: 1px solid map.get($colors-grey, grey5);
    border-radius: $border-radius-base;
    padding: 10px 16px;
    font-size: $font-size-b3;
    line-height: $line-height-base;

    &.error {
        border-color: map.get($colors-secondary, dark_pink);
        & ~ label {
            color: map.get($colors-secondary, dark_pink);
        }
    }

    &.success {
        border-color: map.get($colors-secondary, paris_green);
        & ~ label {
            color: map.get($colors-secondary, paris_green);
        }
    }

    &:focus,
    &.focus {
        border-color: $color-primary;
        outline: 0;
        & ~ label {
            color: map.get($colors-secondary, turquioise_surf);
        }
    }

    &[disabled] {
        cursor: not-allowed;
        color: map.get($colors-grey, grey2);
        & ~ label {
            color: map.get($colors-secondary, grey2);
        }
    }
}

.material-group {
    .input-error {
        color: map.get($colors-secondary, dark_pink);
        font-size: $font-size-b5;
        margin-top: 4px;
        display: block;
        text-align: left;
    }
}

// radio btns
$md-radio-checked-color: $color-primary;
$md-radio-border-color: map.get($colors-grey, grey3);
$md-radio-size: 20px;
$md-radio-checked-size: 10px;
$md-radio-ripple-size: 15px;

@keyframes ripple {
    0% {
        box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0);
    }
    50% {
        box-shadow: 0px 0px 0px $md-radio-ripple-size rgba(0, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0px 0px 0px $md-radio-ripple-size rgba(0, 0, 0, 0);
    }
}

.md-radio {
    margin: 16px 0;

    &.md-radio-inline {
        display: inline-block;
    }

    input[type="radio"] {
        display: none;
        &:checked + label:before {
            border-color: $md-radio-checked-color;
            animation: ripple 0.2s linear forwards;
        }
        &:checked + label:after {
            transform: scale(1);
        }
    }

    label {
        display: inline-block;
        min-height: $md-radio-size;
        position: relative;
        padding: 0 ($md-radio-size + 10px);
        margin-bottom: 0;
        cursor: pointer;
        vertical-align: bottom;
        &:before,
        &:after {
            position: absolute;
            content: "";
            border-radius: 50%;
            transition: all 0.3s ease;
            transition-property: transform, border-color;
        }
        &:before {
            left: 0;
            top: 0;
            width: $md-radio-size;
            height: $md-radio-size;
            border: 2px solid $md-radio-border-color;
        }
        &:after {
            top: math.div($md-radio-size, 2) - math.div($md-radio-checked-size, 2);
            left: math.div($md-radio-size, 2) - math.div($md-radio-checked-size, 2);
            width: $md-radio-checked-size;
            height: $md-radio-checked-size;
            transform: scale(0);
            background: $md-radio-checked-color;
        }
    }
}

.md-checkbox {
    margin: 16px 0;

    &.md-radio-inline {
        display: inline-block;
    }

    input[type="checkbox"] {
        display: none;
        &:checked {
            + label {
                border-color: $md-radio-checked-color;
                &:before {
                    content: "";
                    // animation: ripple 0.2s linear forwards;
                    border-color: $md-radio-checked-color;
                    background-color: $md-radio-checked-color;
                }
                &:after {
                    transform: rotate(-45deg);
                    width: 12px;
                    height: 6px;
                    border: 2px solid #fff;
                    border-top-style: none;
                    border-right-style: none;
                }
            }
        }
    }

    label {
        display: flex;
        min-height: $md-radio-size;
        position: relative;
        padding: 12px;
        margin-bottom: 0;
        cursor: pointer;
        align-items: center;
        border: 1px solid map.get($colors-grey, grey5);
        border-radius: $border-radius-base;
        &:before {
            position: relative;
            content: "";
            transition: all 0.3s ease-in-out;
            width: 20px;
            height: 20px;
            border: 2px solid map.get($colors-grey, grey5);
            border-radius: 4px;
            color: #fff;
            margin-right: 20px;
            display: inline-block;
        }
        &:after {
            content: "";
            position: absolute;
            left: 16px;
            top: 17px;
        }
    }
}
