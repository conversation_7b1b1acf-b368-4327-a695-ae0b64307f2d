import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import { LOCALISATION } from 'redux/reducers/localisation';
import {
  getProductTitleBy_Brand_Model,
  getProductTitleBy_Value,
  getProductColorFromTitle
} from 'utils/helpers';
import CONSTANTS from 'constant';
import ProductTypeLabel from './ProductTypeLabel';

import { Style_PackingSuccess, Style_Common } from './Style';

const styles = makeStyles()(() => ({ ...Style_PackingSuccess, ...Style_Common }));

const PackingSuccess = () => {
  const { classes } = styles();
  const navigate = useNavigate();
  const packingLocal = useSelector((state) => state[LOCALISATION].localeData.PACKING);

  const {
    accessories = [],
    delayOrderCard,
    roden_stock_values = {},
    tokai_card_values = {},

    products
  } = useSelector((state) => state.order);
  const { qcData = [] } = useSelector((state) => state.qc);

  /** On Reload: User should be on home page (Change path to '/packing') */
  if (!qcData || (qcData && qcData.length === 0)) {
    navigate('/packing');
  }

  /** ------------------------------- END: Auto Printing ------------------------------- */

  const [productListingData, setProductListingData] = useState();
  const [qtyInCartByProductId, setQtyInCartByProductId] = useState({});
  useEffect(() => {
    if (products) {
      const productListData = [];
      const qtyInCartByProdId = {};
      products.forEach((item) => {
        if (
          item?.uwOrder?.product?.classification &&
          CONSTANTS.PRODUCTS_LIST_SUMMARY.includes(`${item?.uwOrder?.product?.classification}`)
        ) {
          if (qtyInCartByProdId[item.order.product.productId]) {
            qtyInCartByProdId[item.order.product.productId] += 1;
          } else {
            qtyInCartByProdId[item.order.product.productId] = 1;
            productListData.push(item);
          }
        }
      });
      if (productListData.length) {
        setProductListingData(productListData);
        setQtyInCartByProductId(qtyInCartByProdId);
      }
    }
  }, [products]);

  const getProductTitle = (product) => {
    if (product.brand?.length && product.size?.length) {
      return getProductTitleBy_Brand_Model({
        product,
        isCapital: true,
        doNotShowColorCode: true,
        isModelNumberInNextLine: true
      });
    }
    if (product?.value) {
      return getProductTitleBy_Value({
        title: product?.value,
        isCapital: true,
        doNotShowColorCode: true
      });
    }
    return '';
  };
  /** Method to show Products Table */
  const productsListing = () => (
    <Box className={`${classes.window1} ${classes.window2}`}>
      <div className={`${classes.rowHead} ${classes.backgroundGrey}`}>
        <div className={classes.flexBasisSmall}>{packingLocal.S_NO}</div>
        <div className={classes.flexBasisLarge}>{packingLocal.BRAND}</div>
        <div className={classes.flexBasisMedium}>{packingLocal.COLOUR}</div>
        <div className={classes.flexBasisMedium}>{packingLocal.CATEGORY}</div>
        <div className={`${classes.textAlignCenter} ${classes.flexBasisSmall}`}>
          {packingLocal.QTY}
        </div>
      </div>
      {productListingData.map((item, index) => {
        const product = item && item.uwOrder && item.order.product;
        const key = `${index} + ${item.uwOrder}`;
        if (product) {
          return (
            <div
              key={key}
              className={`${classes.rowWrapper} ${classes.fw400} ${
                index % 2 === 0 ? classes.backgroundWhite : classes.backgroundGrey
              } ${index + 1 === accessories.length ? classes.lastWrapper : ''}`}
            >
              <div className={classes.flexBasisSmall}>{index + 1}.</div>
              <div className={classes.flexBasisLarge}>{getProductTitle(product)}</div>
              <div className={classes.flexBasisMedium}>
                {getProductColorFromTitle(product?.value)}
              </div>
              <div className={classes.flexBasisMedium}>
                <ProductTypeLabel product_type_detail={item.product_type_detail} />
              </div>
              <div className={`${classes.textAlignCenter} ${classes.flexBasisSmall}`}>
                {qtyInCartByProductId[product.productId]}
              </div>
            </div>
          );
        }
        return '';
      })}
    </Box>
  );

  /** Method to show Accessories Table */
  const accessoriesListing = () => (
    <div>
      <Box className={`${classes.window1} ${classes.window2}`}>
        <div className={`${classes.rowHead} ${classes.backgroundGrey}`}>
          <div className={classes.flexBasisSmall}>{packingLocal.S_NO}</div>
          <div className={classes.flexBasisLarge}>{packingLocal.LOYALTY_SERVICES}</div>
          <div className={classes.flexBasisMedium}>{packingLocal.IMAGE}</div>
        </div>
        {accessories.map((item, index) => {
          const image = item.uwOrder && item.order?.product?.productImages?.[0];
          const key = `${index} + ${item.uwOrder}`;

          return (
            <div
              key={key}
              className={`${classes.rowWrapper} ${classes.fw400} ${
                index % 2 === 0 ? classes.backgroundWhite : classes.backgroundGrey
              } ${index + 1 === accessories.length ? classes.lastWrapper : ''}`}
            >
              <div className={classes.flexBasisSmall}>{index + 1}.</div>
              <div className={classes.flexBasisLarge}>{item.order.product.value}</div>
              <div className={classes.flexBasisMedium}>
                <img
                  className={classes.accessoriesImg}
                  alt="accessory-img"
                  src={`${image || ''}`}
                />
              </div>
            </div>
          );
        })}
      </Box>
    </div>
  );

  /** Preparing Total_qty including Roden stock and delaycard if exist to show Total Quantity */
  const rodenStock = !!(roden_stock_values && Object.keys(roden_stock_values).length);
  const tokaiCard = !!(tokai_card_values && Object.keys(tokai_card_values).length);
  let Total_qty = qcData?.length ? qcData.length - (accessories?.length || 0) : 0;
  if (rodenStock) {
    Total_qty += roden_stock_values ? Object.keys(roden_stock_values).length : 0;
  }
  if (tokaiCard) {
    Total_qty += tokai_card_values ? Object.keys(tokai_card_values).length : 0;
  }
  if (delayOrderCard) {
    Total_qty += 1;
  }

  return (
    <div className={classes.container}>
      <Box className={classes.window1}>
        <img
          className={classes.img}
          alt="order-status"
          src={`${import.meta.env.VITE_STATIC_SERVER}/images/tickmark.svg`}
        />
        <div className={classes.orderStatus}>{packingLocal.PACKING_COMPLETE}</div>
        <div className={classes.para}>{packingLocal.PACKING_COMPLETE_OF_ALL_ITEMS_TEXT}</div>
        <div className={classes.itemsContainer}>
          <div className={classes.itemsWrapper}>
            <div className={classes.itemsNumber}>{Total_qty}</div>
            <div className={classes.itemsDesc}>{packingLocal.TOTAL_ITEMS}</div>
          </div>
          <div className={`${classes.itemsWrapper} ${classes.marginLeft20}`}>
            <div className={`${classes.itemsNumber} ${classes.color_bright_yellow}`}>
              {accessories.length}
            </div>
            <div className={classes.itemsDesc}>{packingLocal.LOYALTY_SERVICES}</div>
          </div>
        </div>
      </Box>
      {/* Show Products Table */}
      {productListingData && (
        <div style={{ marginRight: '24px', width: '35%' }}>{productsListing()}</div>
      )}
      {/* Show Accessories Table */}
      {accessories && accessories.length ? (
        <div style={{ width: '25%' }}>{accessoriesListing()}</div>
      ) : (
        ''
      )}
    </div>
  );
};

export default PackingSuccess;
