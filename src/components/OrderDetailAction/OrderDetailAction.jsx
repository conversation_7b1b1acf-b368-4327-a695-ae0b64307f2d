import React, { useEffect, useRef, useState } from 'react';
import { IconButton, Menu, MenuItem } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useDispatch, useSelector } from 'react-redux';
import { markDamaged, markDamagedReset } from 'redux/actionCreators/qc';
import {
  orderDetailStockOutRequest,
  orderDetailStockOutReset
} from 'redux/actionCreators/orderDetails';
import OrdeDetailsModal from './OrdeDetailsModal';

const useStyles = makeStyles()(() => ({
  menu: { top: 40 },
  menuItem: {
    maxWidth: 200,
    minWidth: 200,
    color: '#00B9C6',
    fontWeight: 500,
    borderBottom: '1px solid #f0f0f0'
  }
}));

const OrderDetailAction = ({
  nexsOrderId,
  markDamageStatus,
  showStockOutStatus,
  status,
  shippingPackageId,
  barcode,
  id,
  product_id,
  incrementId,
  orderFacility,
  isMarkFullFillable,
  markFullFillable,
  orderItemId
}) => {
  const { classes } = useStyles();

  const inputRef = useRef(null);

  const dispatch = useDispatch();

  const [anchorEl, setAnchorEl] = useState(null);
  const [damageModal, setDamageModal] = useState(false);
  const [stockOutModal, setStockOutModal] = useState(false);

  const closeDamageModal = () => setDamageModal(false);
  const closeStockOutModal = () => setStockOutModal(false);

  const stockOut = useSelector((state) => state.orderDetails.orderStockOut);
  const featureToggle = useSelector((state) => state.consul.featureToggle.data);
  const facilityCode = useSelector((state) => state.settings.selectedFacility);
  const barcodedamaged = useSelector((state) => state.qc.isDamaged);

  useEffect(() => {
    if (!barcodedamaged.isLoading) {
      setDamageModal(false);
      setAnchorEl(null);
      dispatch(markDamagedReset());
    }
    if (!stockOut.isLoading) {
      setStockOutModal(false);
      setAnchorEl(null);
      dispatch(orderDetailStockOutReset());
    }
  }, [barcodedamaged.isLoading, stockOut.isLoading]);

  const openMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const markProductDamaged = () => {
    dispatch(markDamaged({ orderItemId: id, barcode, shipmentId: shippingPackageId }));
  };

  const handleInputForStockOut = (e) => {
    if (e.keyCode === 13) {
      let enableFallback = false;
      let fallbackPayload = null;
      const barcodeCapitalize = e.target.value.toUpperCase();
      if (
        featureToggle.stockOutFallbackFacilities?.includes(facilityCode) &&
        orderFacility === facilityCode
      ) {
        enableFallback = true;
        fallbackPayload = {
          nexsOrderId,
          incrementId,
          shippingPackageId,
          updateOrderItems: [
            {
              id,
              barcode: barcodeCapitalize,
              productId: product_id,
              holded: 0,
              status: 'MANUAL_PICKED'
            }
          ]
        };
      }
      dispatch(
        orderDetailStockOutRequest({
          id,
          barcode: barcodeCapitalize,
          oldBracode: barcode,
          shipmentId: shippingPackageId,
          incrementId,
          enableFallback,
          fallbackPayload,
          FALLBACK_ERROR_MESSAGES: featureToggle.stockOutFallbackErrorMessages
        })
      );
    }
  };

  const showMarkDamageOption = () => {
    if (markDamageStatus.includes(status)) {
      return (
        <MenuItem className={classes.menuItem} onClick={() => setDamageModal(true)} disabled>
          Mark Damaged
        </MenuItem>
      );
    }
    return null;
  };

  const showMarkFullFillable = () => {
    if (isMarkFullFillable) {
      return (
        <MenuItem
          className={classes.menuItem}
          onClick={() => markFullFillable(shippingPackageId, orderItemId)}
        >
          Mark Full Fillable
        </MenuItem>
      );
    }
    return null;
  };

  const showStockOutOption = () => {
    if (showStockOutStatus.includes(status) && !isMarkFullFillable) {
      return (
        <MenuItem className={classes.menuItem} onClick={() => setStockOutModal(true)}>
          Stock Out
        </MenuItem>
      );
    }
    return null;
  };

  const openMenu = Boolean(anchorEl);

  return (
    <>
      <IconButton
        id="actions"
        aria-label="actions"
        aria-controls={openMenu ? 'actions' : undefined}
        aria-expanded={openMenu ? 'true' : undefined}
        aria-haspopup="true"
        onClick={openMenuClick}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="actions-menu"
        anchorEl={anchorEl}
        open={openMenu}
        MenuListProps={{
          'aria-labelledby': 'actions'
        }}
        onClose={() => setAnchorEl(null)}
      >
        {showMarkDamageOption()}
        {showMarkFullFillable()}
        {showStockOutOption()}
      </Menu>

      {(stockOutModal || damageModal) && (
        <OrdeDetailsModal
          markOpen={damageModal}
          markclose={closeDamageModal}
          barcode={barcode}
          btnClick={markProductDamaged}
          stockOutOpen={stockOutModal}
          stockOutClose={closeStockOutModal}
          productId={product_id}
          stockOut={stockOut}
          inputRef={inputRef}
          handleKeyDown={handleInputForStockOut}
        />
      )}
    </>
  );
};

export default React.memo(OrderDetailAction);
