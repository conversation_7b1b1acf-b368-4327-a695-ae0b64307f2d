import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import Button from '@mui/material/Button';
import { resetGrnCreate } from 'redux/actionCreators/grn';
import LkChip from 'components/MaterialUi/LkChip';
import LkModal from '../Modal/Modal';
import { toastOpen } from '../../redux/actionCreators/toast';
import './PIDModal.scss';

const statusNameMapping = {
  CREATED: 'created',
  IQC_IN_PROGRESS: 'IQC in progress',
  IQC_HOLD: 'IQC Hold',
  IQC_COMPLETE: 'IQC Complete'
};
const statusMapping = {
  CREATED: 'primary',
  IQC_IN_PROGRESS: 'warning',
  IQC_HOLD: 'error',
  IQC_COMPLETE: 'success'
};

const SamplingSummaryModal = ({
  grnNo,
  open,
  openModal,
  tableData,
  createGrnLocal
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const dispatchToastFunction = useCallback(
    (heading, subHeading, severity) =>
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading,
          subHeading,
          severity
        })
      ),
    [dispatch]
  );

  const confirmFun = () => {
    dispatchToastFunction(createGrnLocal.SUCCESS, createGrnLocal.GRN_SAVED_SUCCESSFULLY, 'success');
    dispatch(resetGrnCreate());
    navigate('/grn/listing');
  };

  const headerConfig = [
    {
      name: createGrnLocal.PID,
      key: 'pid'
    },
    {
      name: createGrnLocal.BOX_CODE,
      key: 'boxCode'
    },
    {
      name: createGrnLocal.TOTAL_QUANTITY,
      key: 'totalQty'
    },
    {
      name: createGrnLocal.SAMPLING_QUANTITY,
      key: 'samplingQty'
    },
    {
      name: createGrnLocal.QC_PASS,
      key: 'qcPassQty'
    },
    {
      name: createGrnLocal.QC_FAIL,
      key: 'qcFailQty'
    },
    {
      name: createGrnLocal.STATUS,
      key: 'status',
      formatBody: useCallback(
        ({ status }) => <LkChip label={statusNameMapping[status]} type={statusMapping[status]} />,
        []
      )
    },
    {
      name: '',
      key: 'button',
      formatHeader: useCallback(
        () => (
          <Button variant="contained" onClick={confirmFun} color="primary">
            {createGrnLocal.OK}
          </Button>
        ),
        []
      ),
      formatBody: useCallback(() => '', [])
    }
  ];
  return (
    <LkModal
      className="grn-summary-modal"
      open={open}
      upperHeading={grnNo}
      handleClose={() => openModal(false)}
    >
      <LkTable
        tableHeight="50vh"
        headerConfig={headerConfig}
        tableData={tableData}
        totalRowsCount={tableData.length}
      />
    </LkModal>
  );
};
export default SamplingSummaryModal;
