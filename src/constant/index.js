export default {
  SLIDES_TO_SHOW: 3,
  SLIDES_TO_SHOW_FOR_BULK: 1,
  CAROUSEL_SETTINGS: {
    dots: false,
    arrows: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000
  },
  BAROCDDE_CLASSIFICATION: {
    SINGLE_BARCODE: [
      11354, 19429, 21567, 19153, 11365, 12345, 13287, 13349, 15667, 16984, 17492, 18585, 19278
    ],
    MUTIPLE_BARCODE: [11355, 11357, 17775, 25002, 41, 26021, 26011],
    EXCEPTIONS_SINGLE_BARCODE: [146268, 147486]
  },
  CONTACT_LENS_CLASSIFICATIONS: [11354, 19429, 19153],
  PRODUCT_CLASSIFICAITON_DETAILS: {
    11354: {
      label: 'Contact Lens',
      color: '#45C476',
      backgroundColor: 'rgb(69,196,118,0.1)'
    },
    11355: {
      label: 'Eyeglasses',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    11357: {
      label: 'Sunglasses',
      color: '#EF4E74',
      backgroundColor: 'rgb(239,78,116,0.1)'
    },
    17775: {
      label: 'Eyeglasses',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    19429: {
      label: 'Non-Powerwise Contact Lens',
      color: '#45C476',
      backgroundColor: 'rgb(69,196,118,0.1)'
    },
    21567: {
      label: 'Non-Power Reading Sunglasses',
      color: '#EF4E74',
      backgroundColor: 'rgb(239,78,116,0.1)'
    },
    19153: {
      label: 'Contact Lens Solution',
      color: '#45C476',
      backgroundColor: 'rgb(69,196,118,0.1)'
    },
    19278: {
      label: 'Coupon Offers',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    18585: {
      label: 'Accessories Revenue',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    17492: {
      label: 'HTO',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    16984: {
      label: 'Expired',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    15667: {
      label: 'Freebie Barcode',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    13349: {
      label: 'Add On',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    13287: {
      label: 'Shipping Accessories',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    12345: {
      label: 'TEST',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    },
    11365: {
      label: 'Free Gift',
      color: '#0088CB',
      backgroundColor: 'rgb(0,136,203,0.1)'
    }
  },
  KEY_SCAN_AGAIN_TO_COMPLETE: 'Scan_again_to_complete',
  KEY_RODEN_STOCK: 'roden_stock',
  KEY_TOKAI_CARD: 'tokai_card',
  KEY_DELAY_CARD: 'delay_card',
  KEY_OWN_DAYS: 'ownDays',
  KEY_PRODUCT_CASE: 'productCase',
  KEY_PRINT_AT_PACKING: 'isPrintAtPacking',
  KEY_PRINT_AT_PACKING_SUCCESS: 'isPrintAtPackingSuccess',
  TITLE_BRAND_MODEL: ['11355', '11357'],
  PRODUCTS_LIST_SUMMARY: ['11355', '11357'],
  WORKSTATION: 'WORKSTATION',
  AUTO_BAG_PRINTED_SHIPMENTS: 'autoBagPrintedShipments',
  SHARK_TANK_IMAGE: 'sharkTankImage',
  KEY_MONO_CARTON: 'MONOCARTON',
  KEY_SCREWDRIVER: 'SCREWDRIVER',
  OWN_DAYS_CARD_BARCODE_SCANNER: 'own_days_card_barcode_scanner'
};
