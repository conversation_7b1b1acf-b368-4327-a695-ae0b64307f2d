import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Typography } from '@mui/material';
import { makeStyles } from 'tss-react/mui';

import LkModal from 'components/Modal/Modal';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import PrintShipment from 'components/common/PrintShipment';
import OrderDetailAction from 'components/OrderDetailAction/OrderDetailAction';
import useAutoToManualJIT from 'components/common/useAutoToManualJIT';

import { concatLocation, splitDate } from 'views/Order/helper';

import usePermissions from 'common/usePermissions';
import useWindowResize from 'common/useWindowResize';
import { printBlobPdf } from 'utils/helpers';

import { toastOpen } from 'redux/actionCreators/toast';
import {
  getPrintShipmentDetailLoad,
  getPrintShipmentDetailReset,
  getPrintShipmentMPDTCLoad
} from 'redux/actionCreators/printShipment';
import { printPackingInvoiceLoad, printPackingInvoiceReset } from 'redux/actionCreators/packing';
import {
  getNotFoundStatusReset,
  orderDetailsShipmentItemsRequest,
  regularToJitLoad
} from 'redux/actionCreators/orderDetails';
import { getConsulKeyValue } from 'redux/reducers/consul';
import { LOGIN } from 'redux/reducers/login';

import { isEmpty } from 'utils/lodash';
import { markFullFillableLoad } from 'redux/actionCreators/qc';
import LkToolTip from 'components/ToolTip/ToolTip';
import ProductToolTip from 'components/common/ProductToolTip';
import MoreItems from './MoreItems';

const useStyles = makeStyles()(() => ({
  container: {
    maxHeight: 700
  },
  updatedAtDate: {
    lineHeight: '159%'
  },
  updatedAtTime: {
    fontSize: 13,
    fontWeight: 400,
    lineHeight: '159%'
  },
  menuItem: {
    maxWidth: '200px',
    minWidth: '200px',
    color: '#00B9C6',
    fontWeight: 500,
    borderBottom: '1px solid #f0f0f0'
  },
  menuItemLastChild: {
    borderBottom: 'none'
  }
}));

const OrderOverviewTable = ({ CONTENT }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const [tableHeight] = useWindowResize(window.innerHeight - 300);
  const [anchorEl, setAnchorEl] = useState(null);
  const [isAllowedToMoveToJIT, setIsAllowedToMoveToJIT] = useState({});

  const shipmentItems = useSelector((state) => state.orderDetails.overview.shipmentItems);
  const { data, isLoading } = useSelector((state) => state.orderDetails.overview);
  const { data: headerData } = useSelector((state) => state.orderDetails.header);
  const { printShipmentData, printShipmentMPDummyData } = useSelector(
    (state) => state.printShipment
  );
  const { printPackingInvoiceLoaded, printPackingInvoiceError } = useSelector(
    (state) => state.packing
  );
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const featureToggle = useSelector((state) => state.consul.featureToggle.data);
  const getNotFoundStatusData = useSelector((state) => state.orderDetails.getNotFoundStatus.data);

  const { isQCSuperVisor, isPackingSuperVisor } = usePermissions(userDetail?.permission);

  const showStockOutStatus = featureToggle?.showStockOut;
  const markDamageStatus = featureToggle?.markDamageStatus;
  const showNotFoundStatus = featureToggle?.notFound?.[selectedFacility]?.NFStatus;
  const showMarkFullFillableForUff = featureToggle?.showMarkFullFillableForUff ?? {};
  const allowedMoveToJITStatuses = featureToggle?.allowedMoveToJITStatuses ?? [];
  const allowedMoveToJITFacilities = featureToggle?.allowedMoveToJITFacilities ?? [];
  const allowedMoveToJITEmpCodes = featureToggle?.allowedMoveToJITEmpCodes ?? [];

  const getFacilityCode = (shipmentId) =>
    data.find((item) => item.shipmentId === shipmentId)?.facility ?? '';

  const {
    formatHeaderAutoToManual,
    formatBodyShowType,
    isAutoToManualModalOpen,
    closeAutoToManualModal,
    triggerAutoToManualFn,
    autoToManualLoading
  } = useAutoToManualJIT(userDetail?.email, featureToggle, getFacilityCode, shipmentItems, classes);

  useEffect(() => {
    dispatch(getConsulKeyValue(['featureToggle']));
    return () => dispatch(getNotFoundStatusReset());
  }, []);

  useEffect(() => {
    if (data.length && !data[0]?.uwShipmentStatus) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.VSM_NOT_REACHABLE,
          severity: 'warning'
        })
      );
    }
  }, [data]);

  useEffect(() => {
    if (printPackingInvoiceLoaded) {
      printBlobPdf(printPackingInvoiceLoaded);
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PRINTING_INVOICE,
          severity: 'success'
        })
      );
      dispatch(printPackingInvoiceReset());
    }
    if (printShipmentData) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PRINTING_SHIPPING_LABEL,
          severity: 'success'
        })
      );
      dispatch(getPrintShipmentDetailReset());
    }
  }, [printPackingInvoiceLoaded, printShipmentData]);

  useEffect(() => {
    if (printPackingInvoiceError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: printPackingInvoiceError?.meta?.displayMessage || CONTENT.SOMETHING_WENT_WRONG,
          severity: 'error'
        })
      );
      dispatch(printPackingInvoiceReset());
    }
  }, [printPackingInvoiceError]);

  const handleShipmentLabel = (shipmentId) => {
    if (headerData?.navChannel === 'MPDTC') {
      dispatch(
        getPrintShipmentMPDTCLoad({
          shippingPackageId: shipmentId,
          channel: headerData.navChannel
        })
      );
    } else {
      dispatch(
        getPrintShipmentDetailLoad({
          shippingPackageId: shipmentId
        })
      );
      setAnchorEl(null);
    }
  };

  useEffect(() => {
    if (!isEmpty(printShipmentMPDummyData)) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PRINTING_SHIPPING_LABEL,
          severity: 'success'
        })
      );
    }
  }, [printShipmentMPDummyData]);

  const handlePrintInvoice = (shipmentId) => {
    dispatch(printPackingInvoiceLoad({ panel: 'orderDetails', shipmentId }));
    setAnchorEl(null);
  };

  const handleMoveToJitFn = (shipmentId) => {
    dispatch(regularToJitLoad({ shipmentId, incrementId: headerData?.incrementId }));
  };

  useEffect(() => {
    const empCode = userDetail?.empCode;
    const isHavingMoveToJITPermission =
      !isEmpty(shipmentItems) &&
      allowedMoveToJITStatuses?.length > 0 &&
      allowedMoveToJITEmpCodes?.length > 0 &&
      !!empCode;

    // Exit early if required dependencies are missing
    if (!isHavingMoveToJITPermission) {
      setIsAllowedToMoveToJIT({});
      return;
    }

    const jitStatusMap = Object.entries(shipmentItems).reduce((acc, [itemKey, itemValue]) => {
      const shipmentData = itemValue?.data || [];

      const hasAllowedStatus = shipmentData.some(
        ({ status, orderItemType, processingType }) =>
          orderItemType === 'REGULAR' &&
          processingType !== 'FR0' &&
          allowedMoveToJITEmpCodes.includes(empCode) &&
          allowedMoveToJITStatuses.includes(status)
      );

      acc[itemKey] = hasAllowedStatus;
      return acc;
    }, {});

    setIsAllowedToMoveToJIT(jitStatusMap);
  }, [shipmentItems, allowedMoveToJITStatuses, allowedMoveToJITEmpCodes, userDetail?.empCode]);

  const disabledActionButtons = useMemo(
    () => !(isQCSuperVisor || isPackingSuperVisor),
    [isQCSuperVisor, isPackingSuperVisor]
  );

  const markFullFillable = (shippingPackageId, orderItemId) => {
    const payload = {
      orderItemId,
      isFullfillable: 0,
      updatedBy: userDetail?.empCode,
      shipmentId: shippingPackageId,
      incrementId: headerData?.incrementId
    };
    dispatch(markFullFillableLoad(payload));
  };

  const headerConfig = [
    {
      key: 'shipmentId',
      name: CONTENT.SHIPMENT_ID,
      enableExpander: true,
      style: { minWidth: 220, maxWidth: 220 }
    },
    { key: 'awbNo', name: CONTENT.AWB_NUMBER, style: { minWidth: 190, maxWidth: 190 } },
    {
      key: 'updatedAt',
      name: CONTENT.UPDATED_AT,
      formatBody: useCallback(
        ({ updatedAt }) => (
          <>
            <Box className={classes.updatedAtDate}>{splitDate(updatedAt).split(' ')[0]}</Box>
            <Box className={classes.updatedAtTime}>{splitDate(updatedAt).split(' ')[1]}</Box>
          </>
        ),
        []
      )
    },
    { key: 'facility', name: CONTENT.FACILITY, style: { minWidth: 100, maxWidth: 100 } },
    { key: 'noOfItems', name: CONTENT.NO_OF_ITEMS, style: { minWidth: 80, maxWidth: 80 } },
    { key: 'status', name: CONTENT.NEXS_STATUS },
    { key: 'uwShipmentStatus', name: CONTENT.VSM_STATUS },
    {
      key: 'actions',
      name: CONTENT.ACTIONS,
      formatBody: useCallback(
        ({ shipmentId, facility }) => (
          <MoreItems
            CONTENT={CONTENT}
            key={shipmentId}
            shipmentId={shipmentId}
            classes={classes}
            handlePrintInvoice={handlePrintInvoice}
            handleShipmentLabel={handleShipmentLabel}
            headerData={headerData}
            disabled={disabledActionButtons}
            isAllowedToMoveToJIT={
              isAllowedToMoveToJIT[shipmentId] &&
              allowedMoveToJITFacilities?.length > 0 &&
              allowedMoveToJITFacilities?.includes(facility)
            }
            handleMoveToJitFn={handleMoveToJitFn}
          />
        ),
        [
          anchorEl,
          headerData,
          disabledActionButtons,
          isAllowedToMoveToJIT,
          allowedMoveToJITFacilities
        ]
      ),
      style: { minWidth: 80, maxWidth: 80 }
    }
  ];

  const extendedHeaderConfig = [
    {
      key: 'pid',
      name: CONTENT.PID,
      formatBody: useCallback(
        ({ product_id }) => (
          <LkToolTip placement="left" title={<ProductToolTip pid={product_id} />}>
            <Link to={`/inventory/${product_id}`} target="_blank">
              {product_id}
            </Link>
          </LkToolTip>
        ),
        []
      )
    },
    { key: 'orderItemId', name: CONTENT.ORDER_ITEM_ID },
    {
      key: '3',
      name: CONTENT.UPDATED_AT,
      formatBody: useCallback(({ updatedAt }) => splitDate(updatedAt), [])
    },
    {
      key: 'locationId',
      name: CONTENT.LOCATION,
      formatBody: useCallback(
        ({ locationId, parentLocation }) => concatLocation(locationId, parentLocation),
        []
      )
    },
    {
      key: 'barcode',
      name: CONTENT.BARCODE,
      formatBody: useCallback(
        ({ barcode }) =>
          barcode ? (
            <Link to={`/historyPage/${barcode}`} target="_blank">
              {barcode}
            </Link>
          ) : (
            '-'
          ),
        []
      )
    },
    {
      key: 'status',
      name: CONTENT.NEXS_STATUS,
      formatBody: useCallback(
        ({ id, status: originalStatus }) => {
          const notFoundEntry = getNotFoundStatusData?.find((item) => item.wmsOrderItemId === id);
          if (notFoundEntry) {
            if (showNotFoundStatus?.[notFoundEntry?.status]) {
              return `${originalStatus} (${showNotFoundStatus[notFoundEntry?.status]})`;
            }
          }
          return originalStatus;
        },
        [getNotFoundStatusData, showNotFoundStatus]
      )
    },
    {
      key: 'uwDetails?.unicomShipmentStatus',
      name: CONTENT.VSM_STATUS,
      formatBody: useCallback(({ uwDetails }) => uwDetails?.unicomShipmentStatus, [])
    },
    {
      key: '7',
      name: CONTENT.ACTIONS,
      formatBody: useCallback(
        ({
          status,
          barcode,
          product_id,
          id,
          shippingPackageId,
          nexsOrderId,
          fulfillableType,
          orderItemId,
          customFields
        }) => {
          const facilityCode = getFacilityCode(shippingPackageId);
          const showStockOutStatusList =
            showStockOutStatus?.[facilityCode] || showStockOutStatus?.defaultStatus || [];

          const isMarkDamaged = markDamageStatus?.includes(status);
          const isStockOut = showStockOutStatusList?.includes(status);
          const storeInventoryType = customFields?.customFields?.STORE_INVENTORY_TYPE;

          const facilityUFFConfig = showMarkFullFillableForUff[facilityCode];
          const isMarkFullFillable =
            fulfillableType === 'NON_FULFILLABLE' &&
            !['LP', 'LO'].includes(storeInventoryType) &&
            facilityUFFConfig?.status?.includes(status) &&
            facilityUFFConfig?.empCodes?.includes(userDetail?.empCode);

          const shouldRenderAction =
            isMarkDamaged ||
            (isStockOut &&
              (fulfillableType !== 'NON_FULFILLABLE' ||
                ['LP', 'LO'].includes(storeInventoryType))) ||
            isMarkFullFillable;

          if (!shouldRenderAction) {
            return '-';
          }

          return (
            <OrderDetailAction
              key={id}
              orderFacility={facilityCode}
              nexsOrderId={nexsOrderId}
              markDamageStatus={markDamageStatus}
              showStockOutStatus={showStockOutStatusList}
              status={status}
              barcode={barcode}
              product_id={product_id}
              id={id}
              shippingPackageId={shippingPackageId}
              incrementId={headerData?.incrementId}
              isMarkFullFillable={isMarkFullFillable}
              markFullFillable={markFullFillable}
              orderItemId={orderItemId}
            />
          );
        },
        [
          headerData,
          data,
          markDamageStatus,
          showStockOutStatus,
          showMarkFullFillableForUff,
          userDetail?.empCode
        ]
      ),
      style: { minWidth: 80, maxWidth: 80 }
    },
    {
      key: '8',
      name: '',
      formatHeader: formatHeaderAutoToManual,
      formatBody: formatBodyShowType,
      style: { minWidth: 80, maxWidth: 80, textAlign: 'center' }
    }
  ];

  const getOrderShipmentItems = (shipmentId) => {
    if (!shipmentItems[shipmentId]?.data) {
      dispatch(orderDetailsShipmentItemsRequest({ id: headerData.incrementId, shipmentId }));
    }
  };

  const renderExtenedComponent = ({ shipmentId }) => (
    <LkTable
      tableHeight={240}
      isDataFetching={shipmentItems[shipmentId]?.isLoading}
      headerConfig={extendedHeaderConfig}
      tableData={shipmentItems[shipmentId]?.data}
      totalRowsCount={shipmentItems[shipmentId]?.data?.length}
      dataRequestFunction={() => getOrderShipmentItems(shipmentId)}
      pageNumber={0}
      showTableCount={false}
      isNonVertualizedTable
    />
  );

  return (
    <>
      <LkTable
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        headerConfig={headerConfig}
        tableData={data}
        totalRowsCount={data?.length}
        pageNumber={0}
        isNonVertualizedTable
        showTableCount={false}
        renderExtenedComponent={renderExtenedComponent}
      />
      {isAutoToManualModalOpen && (
        <LkModal
          disableBackdrop
          modalHeight="201px"
          modalWidth="485px"
          open={isAutoToManualModalOpen}
          handleClose={closeAutoToManualModal}
          upperHeading={
            <Typography variant="h2" textAlign="center">
              Are you sure?
            </Typography>
          }
          subTitle={
            <Typography variant="body1" textAlign="center">
              You want to change the order from <b>auto</b> to <b>manual JIT</b>?
            </Typography>
          }
          primaryBtnText="YES"
          secondaryBtnText="CANCEL"
          showClose={!autoToManualLoading}
          primaryBtn={triggerAutoToManualFn}
          primaryBtnLoading={autoToManualLoading}
          secondaryBtn={!autoToManualLoading ? closeAutoToManualModal : null}
        />
      )}
      {printShipmentData && <PrintShipment printShipmentData={printShipmentData} />}
      {!isEmpty(printShipmentMPDummyData) && (
        <Box sx={{ display: 'none' }}>
          {' '}
          <PrintShipment printShipmentData={printShipmentMPDummyData} type="MPDTC" />
        </Box>
      )}
    </>
  );
};

export default OrderOverviewTable;
