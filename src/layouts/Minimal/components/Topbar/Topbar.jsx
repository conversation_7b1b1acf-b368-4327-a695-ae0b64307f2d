import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { AppBar, Toolbar, Typography } from '@mui/material';

const useStyles = makeStyles()(() => ({
  root: {
    boxShadow: 'none'
  },
  title: {
    color: 'white'
  }
}));

const Topbar = props => {
  const { className, ...rest } = props;

  const { classes, cx } = useStyles();

  return (
    (<AppBar
      {...rest}
      className={cx(classes.root, className)}
      color="primary"
      position="fixed"
    >
      <Toolbar>
        <RouterLink to="/">
          <Typography
            className={classes.title}
            variant="h2"
          >
            NexS
          </Typography>
        </RouterLink>
      </Toolbar>
    </AppBar>)
  );
};

Topbar.propTypes = {
  className: PropTypes.string
};

export default Topbar;
