/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable no-shadow */
import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import CloseIcon from '@mui/icons-material/Close';

import useFilterHook from 'components/common/useFilterHook';
import LkInput from 'components/MaterialUi/LkInput';
import LkChip from 'components/MaterialUi/LkChip';
import Spinner from 'components/Spinner/Spinner';
import LkToolTip from 'components/ToolTip/ToolTip';
import ShortCut from 'components/shortCut/shortCut';
import UploadCsv from 'components/UploadCsv/UploadCsv';
import LkTable from 'components/MaterialUi/LkTable/LkTable';

import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { searchListLoad, searchListReset } from 'redux/actionCreators/filters';
import {
  exportPoLoad,
  exportPoReset,
  getUploadItemErrorReportLoad,
  resetUploadCSV,
  submitPurchaseOrderLoad,
  submitPurchaseOrderReset,
  uploadItemLoad,
  markPoApprovalReset,
  markPoApprovalLoad
} from 'redux/actionCreators/purchaseOrder';
import {
  convertDateFormat,
  fileDownload,
  generatePayloadForSearchAPI,
  genericDateFormatted,
  getAllQueryParam,
  getCurrencySumbol,
  roundUptoFixedDigits
} from 'utils/helpers';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { LOGIN } from 'redux/reducers/login';
import useWindowResize from 'common/useWindowResize';
import usePermissions from 'common/usePermissions';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import { PurchaseOrderStatus } from 'config/PurchaseOrderStatus';
import PurchaseOrderApproveReject from './components/PurchaseOrderApproveReject';
import './purchaseOrder.scss';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  table: {
    minWidth: 700,
    paddingBottom: 4
  },
  container: {
    maxHeight: 700
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },
  outlinedPrimary: {
    marginLeft: '16px',
    height: 35
  }
}));

const PAGE_SIZE = 35;

const defaultObj = {
  isReset: true,
  path: 'purchaseOrder?version=v1',
  payload: {
    type: 'purchaseOrder',
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey: 'created_at',
      sortOrder: 'DESC'
    }
  }
};

const sortingData = {
  id: 'created_at',
  order: 'DESC'
};

let objPayload = { ...defaultObj };

const sampleCSV = [
  [
    'product_id',
    'qty',
    'price',
    'vendor_id',
    'expiry_date',
    'shipping_date',
    'procurement_type',
    'shipment_type',
    'season',
    'reference_number'
  ],
  [
    687789,
    120,
    280.0,
    'WHOLESALE01',
    '2021-03-28',
    '2021-03-27',
    'bulk',
    'LAND',
    'season',
    'abcd;abcd'
  ],
  [
    687789,
    120,
    280.0,
    'WHOLESALE01',
    '2021-03-28',
    '2021-03-27',
    'bulk',
    'AIR',
    'season',
    'abcd;abcd'
  ]
];

let totalApiCall = 0;

const PurchaseOrder = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const PO = useSelector((state) => state[LOCALISATION].localeData.PO);
  const {
    exportPoLoading,
    exportPoData,
    exportPoDataFail,
    uploadItemsData,
    uploadItemsFailData,
    csvErrorSuccess,
    submitPoData,
    poApprovalSuccess,
    poApprovalError,
    poApprovalLoad
  } = useSelector((state) => state.purchaseOrder);
  const {
    searchList,
    seachListFail,
    searchListLoading,
    totalRecordsCount,
    autoSuggestionListPOType,
    autoSuggestionListStatus,
    autoSuggestionListPO,
    autoSuggestionListApproved_By,
    autoSuggestionListFacility,
    autoSuggestionListVendor,
    autoSuggestionListCreated_By
  } = useSelector((state) => state.filters);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const [sortDataState, setSortDataState] = useState(sortingData);
  const [globalPoSearch, setGlobalPoSearch] = useState('');
  const [uploadBulkPoModal, setUploadBulkPoModal] = useState(false);
  const [tableHeight] = useWindowResize(window.innerHeight - 250);
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [resetState, setResetState] = useState(false);
  const [selectedPo, setSelectedPo] = useState([]);
  const [singlePoSelect, setSinglePoSelect] = useState([]);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const [bulkApproveReject, setBulkApproveReject] = useState({
    show: false,
    type: null
  });
  const { isPOSuperVisor, isPOAgent } = usePermissions(userDetail?.permission);

  useEffect(() => {
    sortingData.order = 'DESC';
    sortingData.id = 'created_at';
    return () => {
      totalApiCall = 0;
      dispatch(searchListReset());
      dispatch(submitPurchaseOrderReset());
    };
  }, [dispatch]);

  useEffect(() => {
    if (csvErrorSuccess) {
      fileDownload(csvErrorSuccess, 'POBulkUploadError');
    }
  }, [csvErrorSuccess]);

  const fetchMoreListItems = (sortType, sortKey, nextPagePage) => {
    const st = sortType === 'ASC' ? 'ASC' : 'DESC';
    sortingData.id = sortKey;
    sortingData.order = st;

    objPayload.payload.pageRequest.sortKey = sortKey;
    objPayload.payload.pageRequest.sortOrder = st;
    if (totalApiCall) {
      objPayload.payload.pageRequest.pageNumber = nextPagePage;
      if (!nextPagePage) {
        // if page is 0
        objPayload.isReset = true;
        if (!searchListLoading) {
          // to restrict duplicate api call on reset
          dispatch(searchListLoad(objPayload));
        }
      } else {
        objPayload.isReset = false;
        dispatch(searchListLoad(objPayload));
      }
    }
  };

  useKeyboardShortcut([18, 78], () => {
    // eslint-disable-next-line no-use-before-define
    goToCreatePo();
  });

  const downloadExportonSortKey = useCallback(() => {
    const tempData = JSON.parse(JSON.stringify(objPayload));
    tempData.payload.pageRequest.pageSize = totalRecordsCount;
    dispatch(exportPoLoad(tempData));
  }, [dispatch, totalRecordsCount]);

  useKeyboardShortcut([18, 69], downloadExportonSortKey);

  const closeCsvModal = () => {
    dispatch(resetUploadCSV());
    setResetState(!resetState);
    setUploadBulkPoModal(false);
  };

  useEffect(() => {
    const result = getAllQueryParam(window.location.search);
    objPayload = JSON.parse(JSON.stringify(defaultObj));
    let { payload } = objPayload;
    payload.pageRequest.sortKey = sortingData.id;
    payload.pageRequest.sortOrder = sortingData.order;
    const globalSearchPOID = document.getElementById('globalSearchPO');
    // eslint-disable-next-line max-len
    if (globalSearchPOID && globalSearchPOID.value) {
      payload.global_po_num = globalSearchPOID.value;
    }
    payload = generatePayloadForSearchAPI(result, payload, mapSearchFilterKey);
    totalApiCall = 1;
    dispatch(searchListLoad(objPayload));
  }, [location.search, dispatch]);

  useEffect(() => {
    if (exportPoData?.response?.data) {
      fileDownload(
        exportPoData.response.data,
        `PO-Export-${convertDateFormat(new Date(), 'export')}`
      );
      dispatch(exportPoReset());
    } else if (exportPoDataFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading:
            exportPoDataFail?.response?.data?.message ||
            exportPoDataFail?.error?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
      dispatch(exportPoReset());
    }
  }, [exportPoData, exportPoDataFail, dispatch]);

  useEffect(() => {
    if (poApprovalError) {
      dispatch(toastOpen({ isToastOpen: true, heading: poApprovalError, severity: 'error' }));
      dispatch(markPoApprovalReset());
    }
  }, [poApprovalError, dispatch]);

  const filterSearchOption = (value, key) => {
    if (value) {
      objPayload.payload[key] = value;
    } else {
      delete objPayload.payload[key];
    }
    objPayload.payload.pageRequest.pageNumber = 0;
    objPayload.isReset = true;
    dispatch(searchListLoad(objPayload));
  };

  const goToCreatePo = () => {
    navigate('/po/create');
  };

  useEffect(() => {
    if (seachListFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: seachListFail?.message,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [seachListFail, dispatch]);

  useEffect(() => {
    if (uploadItemsData?.data) {
      setUploadProgress(100);
    }
    if (uploadItemsData?.response?.data) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: uploadItemsData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
      setUploadProgress(0);
    }
  }, [dispatch, uploadItemsData, uploadItemsFailData]);

  // after poapproval success
  useEffect(() => {
    if (poApprovalSuccess) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: poApprovalSuccess.meta.displayMessage,
          severity: 'success'
        })
      );
      dispatch(markPoApprovalReset());
      setBulkApproveReject({ show: false, type: null });
      setSelectedPo([]);
      setSinglePoSelect([]);
      objPayload.isReset = true;
      objPayload.payload.pageRequest.pageNumber = 0;
      setTimeout(() => {
        dispatch(searchListLoad(objPayload));
      }, 2000);
    }
  }, [poApprovalSuccess, dispatch]);

  const selectFiles = (file) => {
    // eslint-disable-next-line no-plusplus
    for (let i = 1; i < 100; i++) {
      setTimeout(() => setUploadProgress(i), 100);
    }
    const formData = new FormData();
    formData.set('file', file);
    dispatch(uploadItemLoad(formData));
  };

  const downloadErrorReport = () => {
    if (csvErrorSuccess) {
      fileDownload(csvErrorSuccess, 'POBulkUploadError');
      return;
    }
    if (uploadItemsFailData?.data?.file) {
      dispatch(getUploadItemErrorReportLoad(uploadItemsFailData?.data?.file));
    }
  };

  const handleSubmit = useCallback((uploadItemsData) => {
    const apiData = {
      current_timestamp: new Date().getTime(),
      version: 1,
      po_type: 'BULK'
    };
    apiData.filename = uploadItemsData.data.file;
    dispatch(submitPurchaseOrderLoad(apiData));
  }, []);

  const handleFileSubmit = (uploadItemsData) => handleSubmit(uploadItemsData);

  useEffect(() => {
    if (submitPoData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: submitPoData.response.data.meta.displayMessage,
          severity: 'error'
        })
      );
    }
    if (submitPoData?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Successfully created POs, please check your email for details',
          severity: 'success'
        })
      );
      closeCsvModal();
    }
  }, [submitPoData, dispatch]);

  const selectPO = (event, po) => {
    const isCheck = event.target.checked;
    if (isCheck) {
      selectedPo.push(po);
    } else {
      selectedPo.forEach((x, i) => {
        if (x.po_num === po.po_num) {
          selectedPo.splice(i, 1);
        }
      });
    }
    setSelectedPo([...selectedPo]);
  };

  const approvalSingleFun = (type, po) => {
    setSinglePoSelect([po]);
    setBulkApproveReject({ show: true, type });
  };

  const headerConfig = [
    {
      name: PO.PO,
      key: 'po_num',
      align: 'left',
      style: { minWidth: '160px', maxWidth: '160px' },
      formatBody: useCallback(
        ({ po_num }) => (
          <a
            href={`${window.location.origin}/po/detail?poNum=${po_num}&fromPo=true`}
            className="text-primary fw-bold "
          >
            {po_num}
          </a>
        ),
        []
      ),
      filterData: {
        type: 'autoSelect',
        columnName: 'PO',
        listData: autoSuggestionListPO,
        selectedFilterList,
        apiName: 'purchaseOrder'
      },

      supportSort: true,
      marginLeft: '20px'
    },
    {
      name: PO.TYPE,
      key: 'po_type',
      align: 'left',
      filterData: {
        type: 'status',
        columnName: 'POType',
        apiName: 'purchaseOrder',
        listData: autoSuggestionListPOType,
        selectedFilterList
      },
      supportSort: true,
      marginLeft: '100px',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: PO.VENDOR,
      key: 'vendor_name',
      filterData: {
        type: 'autoSelect',
        columnName: 'Vendor',
        apiName: 'purchaseOrder',
        listData: autoSuggestionListVendor,
        selectedFilterList
      },

      formatBody: useCallback(
        ({ vendor_name }) => (
          <Box>
            {vendor_name.length > 30 ? (
              <LkToolTip
                placement="bottom-end"
                title={<Box p={1}>{vendor_name} </Box>}
                className="dtoolTip"
              >
                <div className="ellipsis-vertical">{vendor_name}</div>
              </LkToolTip>
            ) : (
              vendor_name || '-'
            )}
          </Box>
        ),
        []
      ),

      supportSort: true,

      marginLeft: '110px',
      align: 'left',
      style: { minWidth: '200px', maxWidth: '200px' }
    },
    {
      name: PO.RECEIVED,
      key: 'order_received_quantity',
      supportSort: true,
      formatBody: useCallback(
        ({ order_received_quantity, order_qty }) => (
          <div>
            {order_received_quantity}/{order_qty}
          </div>
        ),
        []
      ),

      align: 'left',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: PO.SUBMITTED_ON,
      key: 'created_at',
      align: 'left',
      style: { minWidth: '160px', maxWidth: '160px' },
      filterData: {
        type: 'dateRange',
        columnName: 'CREATED',
        selectedFilterList
      },

      formatBody: useCallback(
        ({ created_at }) => (
          <div className="fw-bold">{created_at && genericDateFormatted(created_at)}</div>
        ),
        []
      ),

      supportSort: true
    },
    {
      name: PO.APPROVED_ON,
      key: 'approved_at',
      formatBody: useCallback(
        ({ approved_at }) => (
          <div className="fw-bold">
            {approved_at && genericDateFormatted(approved_at, 'utcTOlocalDate')}
          </div>
        ),
        []
      ),
      filterData: {
        type: 'dateRange',
        columnName: 'APPROVED',
        // columnName="CREATED"
        selectedFilterList
      },

      supportSort: true,

      marginLeft: '10px',
      align: 'left'
    },
    {
      name: PO.CREATED_BY,
      key: 'created_by',
      align: 'left',
      formatBody: useCallback(({ created_by }) => created_by || 'NA', []),

      supportSort: true,
      filterData: {
        type: 'autoSelect',
        columnName: 'Created_By',
        apiName: 'purchaseOrder',
        listData: autoSuggestionListCreated_By,
        selectedFilterList
      },

      marginLeft: '70px'
    },
    {
      name: PO.APPROVED_BY,
      key: 'approved_by',
      align: 'left',
      filterData: {
        type: 'autoSelect',
        columnName: 'Approved_By',
        apiName: 'purchaseOrder',
        listData: autoSuggestionListApproved_By,
        selectedFilterList
      },

      supportSort: true,
      formatBody: useCallback(({ approved_by }) => <div>{approved_by || 'NA'}</div>, [])
    },
    {
      name: PO.FACILITY_INFO,
      key: 'facility_info',
      align: 'left',
      formatBody: useCallback(({ facility_info }) => {
        const t = facility_info && facility_info.split(' - ');
        const facility_name = t[1];
        const facility_code = t[0];
        return (
          <>
            <div className="fw-bold">{facility_code}</div>
            <div className="fs10 text-66 mr-t-10">{facility_name}</div>
          </>
        );
      }, []),

      filterData: {
        type: 'autoSelect',
        columnName: 'Facility',
        apiName: 'purchaseOrder',
        listData: autoSuggestionListFacility,
        selectedFilterList
      },

      supportSort: true,

      style: { minWidth: '150px', maxWidth: '150px' }
    },
    {
      name: PO.PO_VALUE,
      key: 'total_po_cost',
      align: 'right',
      supportSort: true,

      style: { minWidth: '130px', maxWidth: '130px' },
      marginRight: 1,
      formatBody: useCallback(
        ({ total_po_cost, ...po }) => (
          <div className="mr-r8">
            {getCurrencySumbol(po.currency)}
            {roundUptoFixedDigits(total_po_cost)}
          </div>
        ),
        []
      )
    },
    {
      name: PO.PO_STATUS,
      key: 'status',
      align: 'left',

      supportSort: true,
      filterData: {
        type: 'autoSelect',
        columnName: 'Status',
        apiName: 'purchaseOrder',
        listData: autoSuggestionListStatus,
        selectedFilterList
      },

      marginLeft: '-40px',
      style: { minWidth: '160px', maxWidth: '160px' },
      formatBody: useCallback(
        ({ status, ...po }) => (
          <div className="po-list-row">
            <div
              className={`chip-status ${PurchaseOrderStatus[status]?.type} ${
                status === 'PENDING_APPROVAL' && isPOSuperVisor && 'hideStatusChip'
              }`}
            >
              {PurchaseOrderStatus[status]?.text || status}
              {status === 'APPROVED' && (
                <img
                  className="mr-l10"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/InfoFilled.svg`}
                  alt="info"
                />
              )}
              {status === 'REJECTED' && (
                <img
                  className="mr-l10"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/infoRejected.svg`}
                  alt="info"
                />
              )}
            </div>
            {status === 'PENDING_APPROVAL' && isPOSuperVisor && (
              <div className="po-supervisor-actions">
                <img
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/CheckFilled.svg`}
                  alt="check-circle"
                  onClick={() => approvalSingleFun('APPROVE', po)}
                />
                <img
                  className="mr-l10"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/CrossFilledLarge.svg`}
                  alt="check-circle"
                  onClick={() => approvalSingleFun('REJECT', po)}
                />
              </div>
            )}
          </div>
        ),
        []
      )
    }
  ];

  const handleSelectAllClick = () => {
    if (selectedPo.length > 0) {
      setSelectedPo([]);
    }
  };

  const checkboxSelectColumn = {
    name: '',
    key: 'checkBoxSelect',
    formatHeader: useCallback(
      () => (
        <Checkbox
          style={{ padding: 2 }}
          indeterminate={selectedPo.length > 0 && searchList?.length > selectedPo.length}
          checked={searchList?.length === selectedPo.length}
          onClick={handleSelectAllClick}
          color="primary"
          inputProps={{ 'aria-label': 'select all po' }}
        />
      ),
      [selectedPo]
    ),
    formatBody: useCallback(
      (po) => {
        const { po_num } = po;
        const isItemSelected = selectedPo.some((t) => t.po_num === po_num);
        return (
          <Checkbox
            style={{ padding: 2 }}
            onClick={(event) => selectPO(event, po)}
            checked={isItemSelected}
            inputProps={{ 'aria-labelledby': po_num }}
            color="primary"
          />
        );
      },
      [selectedPo]
    ),
    style: { minWidth: '50px', maxWidth: '50px' }
  };

  // @TODO REMOVE this condition(if agent is common role for every user)
  if (isPOSuperVisor || isPOAgent) {
    headerConfig.unshift(checkboxSelectColumn);
  }

  const approvalFun = (status) => {
    const payload = { status, po_num: selectedPo.map((t) => t.po_num), bulkApproval: true };
    // eslint-disable-next-line no-unused-expressions
    !poApprovalLoad && dispatch(markPoApprovalLoad(payload));
  };

  return (
    <div className={`${classes.root} po-dashboard pos-rel`}>
      <div className={classes.mainBar}>
        <Box width="320px">
          <LkInput
            label={`${PO.SEARCH_PO_HERE}...`}
            variant="outlined"
            id="globalSearchPO"
            fullWidth
            value={globalPoSearch}
            onChange={(e) => setGlobalPoSearch(e.target.value)}
            // onBlur={e => handleInputChange(props.location.pathname,queryParams,e)}
            onKeyPress={(e) =>
              e.which === 13 && filterSearchOption(e.target.value, 'global_po_num')
            }
          />
        </Box>
        <div className="display-flex align-items-center">
          {(isPOSuperVisor || isPOAgent) && selectedPo.length > 0 && (
            <>
              <div className="fs16 text-3C selected-no">
                {`${selectedPo.length} ${PO.SELECTED}`}
              </div>
              {isPOSuperVisor && (
                <>
                  <Button
                    onClick={() => {
                      setSinglePoSelect([]);
                      setBulkApproveReject({ show: true, type: 'REJECT' });
                    }}
                    color="primary"
                    style={{ borderRadius: 8, marginRight: '20px' }}
                    type="submit"
                    variant="outlined"
                    disabled={poApprovalLoad}
                  >
                    {PO.REJECT_PO}
                  </Button>
                  <Button
                    onClick={() => {
                      setSinglePoSelect([]);
                      setBulkApproveReject({ show: true, type: 'APPROVE' });
                    }}
                    color="primary"
                    style={{ borderRadius: 8, marginRight: '20px' }}
                    type="submit"
                    variant="contained"
                    disabled={poApprovalLoad}
                  >
                    {PO.APPROVE_PO}
                  </Button>
                </>
              )}
              {isPOAgent && (
                <Button
                  color="primary"
                  style={{ borderRadius: 8 }}
                  type="submit"
                  variant="contained"
                  onClick={() => approvalFun('PENDING_APPROVAL')}
                  disabled={poApprovalLoad}
                >
                  {PO.SEND_FOR_APPROVAL}
                </Button>
              )}
            </>
          )}

          {selectedPo.length === 0 && (
            <>
              <Button
                onClick={() => setUploadBulkPoModal(true)}
                color="primary"
                style={{ borderRadius: 8, marginRight: '20px' }}
                type="submit"
                variant="contained"
              >
                {PO.CREATE_MULTIPLE_PO}
              </Button>
              <LkToolTip
                placement="bottom"
                title={<ShortCut name="Alt+N" />}
                open={isShowShortCuts}
              >
                <Button
                  onClick={() => goToCreatePo()}
                  color="primary"
                  style={{ borderRadius: 8 }}
                  type="submit"
                  variant="contained"
                  data-cy="Create Purchase Order"
                >
                  {PO.CREATE_PURCHASE_ORDER}
                </Button>
              </LkToolTip>
            </>
          )}
        </div>
      </div>

      <div className="invoice-dashboard-main border-grey5-radiusbase overflow-hidden">
        <div className="invoice-dashboard-action pd-16 display-flex justify-content-space-between">
          <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
            {selectedFilterList.map(({ key, value }) => (
              <Box key={`${key}-${value}`} mr={2} mb={1}>
                <LkChip
                  label={`${key}: ${value}`}
                  type="filter"
                  deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                  size="small"
                  onDelete={() => deleteSelectedFilter(key, value)}
                />
              </Box>
            ))}
          </Box>
          <div className="display-flex justify-content-space-between">
            <Button
              style={{ borderRadius: 8 }}
              disabled={!selectedFilterList.length}
              className={classes.outlinedPrimary}
              onClick={() => {
                setGlobalPoSearch('');
                resetFilters();
                sortingData.id = 'created_at';
                sortingData.order = 'DESC';
                setSortDataState({ ...sortingData });
              }}
              variant="outlined"
              color="primary"
            >
              {PO.RESET}
            </Button>
            <LkToolTip placement="bottom" title={<ShortCut name="Alt+E" />} open={isShowShortCuts}>
              {exportPoLoading ? (
                <Button
                  style={{ borderRadius: 8, width: '105px' }}
                  disabled
                  className={classes.outlinedPrimary}
                  variant="contained"
                  color="primary"
                >
                  <Spinner />
                </Button>
              ) : (
                <Button
                  style={{ borderRadius: 8 }}
                  onClick={() => downloadExportonSortKey()}
                  className={classes.outlinedPrimary}
                  variant="outlined"
                  color="primary"
                >
                  {PO.EXPORT}
                </Button>
              )}
            </LkToolTip>
          </div>
        </div>
        <LkTable
          tableHeight={tableHeight}
          headerConfig={headerConfig}
          isDataFetching={searchListLoading}
          tableData={searchList}
          totalRowsCount={totalRecordsCount}
          dataRequestFunction={fetchMoreListItems}
          initialSortBy={sortDataState}
          pageLimit={PAGE_SIZE}
          setFilters={onChangeFilterList}
          rowSize={60}
        />
      </div>
      <UploadCsv
        open={uploadBulkPoModal}
        onClose={closeCsvModal}
        selectFiles={selectFiles}
        sampleCSV={sampleCSV}
        uploadProgress={uploadProgress}
        // checkedItemLabel="This will overwrite existing rows in PO"
        failDataMessage={uploadItemsFailData?.meta?.displayMessage}
        downloadErrorReport={downloadErrorReport}
        uploadCsvPass={uploadItemsData}
        handleSubmit={() => handleFileSubmit(uploadItemsData)}
        resetState={resetState}
        sampleFileName="Sample_Po_Item_Upload"
        subTitle="Please select the CSV file to import PID details, date format is YYYY-MM-DD, 
        procurement type and season are optional"
      />

      {bulkApproveReject.show && (
        <PurchaseOrderApproveReject
          type={bulkApproveReject.type}
          selectedPo={singlePoSelect.length === 1 ? singlePoSelect : selectedPo}
          open={bulkApproveReject.show}
          close={() => {
            setBulkApproveReject({ show: false, type: null });
          }}
        />
      )}
    </div>
  );
};

export default PurchaseOrder;
