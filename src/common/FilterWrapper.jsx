import React, { useState } from 'react';

import FilterListIcon from '@mui/icons-material/FilterList';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { Button, Box, Paper, ClickAwayListener, Fade, Popper } from '@mui/material';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()(() => ({
  root: {
    background: ' #FFFFFF',
    border: ' 1px solid #DDDDDD',
    boxSizing: 'border-box',
    boxShadow: '0px 16px 32px rgba(0, 0, 0, 0.1)',
    borderRadius: '6px'
  },
  submitBtn: {
    borderRadius: 8,
    padding: 4
  }
}));
const FilterWrapper = ({
  children,
  width = '206px',
  marginLeft = '0px',
  onSubmit,
  hideSubmit = false,
  onCloseUpdateFilter,
  monitorPhasefilter,
  open,
  setOpen
}) => {
  const { classes } = useStyles();

  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = () => (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setOpen(!open);
    if (open) {
      onCloseUpdateFilter();
    }
  };

  return (
    <ClickAwayListener
      onClickAway={() => {
        if (open) {
          onCloseUpdateFilter();
        }
        setOpen(false);
      }}
    >
      <Box pt={0.4}>
        <Popper
          open={open}
          anchorEl={anchorEl}
          placement="bottom"
          transition
          style={{ zIndex: monitorPhasefilter ? 12000 : 1302 }}
        >
          {({ TransitionProps }) => (
            <Fade {...TransitionProps} timeout={350}>
              <Paper className={classes.root} style={{ marginLeft }}>
                <Box width={width} pt={2}>
                  {children}

                  {!hideSubmit && (
                    <Box textAlign="center" pt={0.5} pl={2} pr={2} pb={1.5}>
                      <Button
                        fullWidth
                        color="primary"
                        className={classes.submitBtn}
                        type="submit"
                        variant="contained"
                        // disabled={disabled}
                        onClick={() => {
                          setOpen(false);
                          onSubmit();
                        }}
                      >
                        APPLY
                      </Button>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Fade>
          )}
        </Popper>
        {!monitorPhasefilter ? (
          <FilterListIcon
            fontSize="small"
            style={{ color: '#666666', fontSize: '1.1rem' }}
            className="cursor-pointer"
            onClick={handleClick()}
          />
        ) : (
          <DateRangeIcon
            fontSize="small"
            style={{ color: '#666666', fontSize: '1.4rem', marginRight: '10px' }}
            className="cursor-pointer"
            onClick={handleClick()}
          />
        )}
      </Box>
    </ClickAwayListener>
  );
};

export default FilterWrapper;
