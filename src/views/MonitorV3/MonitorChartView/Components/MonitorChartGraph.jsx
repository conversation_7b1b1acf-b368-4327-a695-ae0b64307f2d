import React from 'react';
import Grid from '@mui/material/Grid';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { makeStyles } from 'tss-react/mui';
import { keys } from 'utils/lodash';
import Typography from '@mui/material/Typography';
import { useDispatch, useSelector } from 'react-redux';
import { getMonitorGraphSelector } from 'selectors/monitorSelector';
import Spinner from 'components/Spinner/Spinner';
import { monitorDetailsFiltersUpdate } from 'redux/actionCreators/monitor';
import {
  data,
  options,
  LAST_ONE_HOUR_PROCESSED,
  IN_PROGRESS,
  THRESHOLD_BREACH
} from '../monitorChartData';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const useStyles = makeStyles()({
  container: {
    border: '1px solid #dddddda0',
    display: 'flex',
    width: '100%',
    borderRadius: 8,
    backgroundColor: '#ffffff',
    marginTop: 10,
    padding: 20,
    paddingRight: 0,
    height: '50vh'
  },
  spinner: {
    margin: 'auto'
  }
});

const MAP_SEVERITY = {
  [IN_PROGRESS]: 'standard',
  [LAST_ONE_HOUR_PROCESSED]: 'critical',
  [THRESHOLD_BREACH]: 'severe'
};

const MonitorChartGraph = ({ activeCategory, selectedTab }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const graphData = useSelector(getMonitorGraphSelector);

  const onClickFun = (clickedBar, ev) => {
    ev.persist();
    if (clickedBar.length > 0) {
      const payload = { status: clickedBar[0][['_view']].label[0] };
      const severity = MAP_SEVERITY[clickedBar[0][['_view']].datasetLabel];
      if (severity !== 'standard') {
        payload.severity = severity;
      }
      dispatch(monitorDetailsFiltersUpdate(payload));
    }
  };

  const renderGraph = () => {
    if (graphData.isLoading) {
      return <Spinner className={classes.spinner} />;
    }
    if (keys(graphData[selectedTab]).length && !graphData.isLoading) {
      return (
        <Grid container className={classes.container}>
          <Grid item lg={12} md={12} xl={9} xs={12}>
            <Bar
              getElementAtEvent={onClickFun}
              data={data(graphData, selectedTab, activeCategory)}
              options={options}
            />
          </Grid>
        </Grid>
      );
    }
    return (
      <Typography variant="body2" className="text-center pd-t12">
        No Data ...
      </Typography>
    );
  };

  return renderGraph();
};

export default MonitorChartGraph;
