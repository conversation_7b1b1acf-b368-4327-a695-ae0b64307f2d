import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import { trim } from 'utils/lodash';
import { Box, Card, Button } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import { Field, Form } from 'react-final-form';
import LkInput from 'components/MaterialUi/LkInput';
import Spinner from 'components/Spinner/Spinner';
import CloseIcon from '@mui/icons-material/Close';
import { getTrayDeatilsLoad, resetTrayList } from '../../redux/actionCreators/trayReassign';
import TrayList from './TrayList';
import './TrayReassignment.scss';
import { getBarcodeFromURL } from '../../utils/helpers';

const styles = makeStyles()(() => ({
  root: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    position: 'relative',
    marginTop: '120px',
    marginBottom: '20px'
  },
  textcard: {
    // paddingTop: '35px',
    // paddingBottom: '35px',
    width: '450px'
  },
  label: {
    textAlign: 'center',
    fontWeight: 700,
    fontSize: '22px',
    color: '#333'
  },
  fieldwrapper: {
    position: 'relative',
    display: 'flex',
    justifyContent: 'space-between'
  },
  submitButton: {
    width: '40px',
    border: 'none',
    minWidth: '40px',
    marginLeft: '5px',
    padding: '0'
  }
}));

const TrayReassignment = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { getTrayList, getTrayLoading, searchTrayBarcode } = useSelector(
    (state) => state.trayReassign
  );
  const { trayId = '' } = useParams();
  const [barCodeVal, setBarCodeVal] = useState(trayId);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.TRAYREASSIGNMENT);

  const resetTrayListFn = () => {
    setBarCodeVal('');
    dispatch(resetTrayList());
    navigate('/trayReassignment');
  };

  useEffect(() => {
    if (searchTrayBarcode) {
      setBarCodeVal(searchTrayBarcode);
    }
  }, [searchTrayBarcode]);

  useEffect(() => {
    if (trayId) {
      dispatch(getTrayDeatilsLoad(trayId));
    } else {
      resetTrayListFn();
    }
  }, [trayId]);

  const { classes } = styles();

  const submitBarCode = () => {
    const extractedBarcode = getBarcodeFromURL(trim(barCodeVal))
    setBarCodeVal(extractedBarcode)
    navigate(`/trayReassignment/${extractedBarcode}`);
  };

  const btnBg = barCodeVal.length > 0 ? '#00B9C6' : '#E8E8E8';
  const boxShadow =
    barCodeVal.length > 0
      ? // eslint-disable-next-line max-len
      '0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px rgba(0, 0, 0, 0.14), 0px 1px 5px rgba(0, 0, 0, 0.12)'
      : 'none';
  return (
    <div data-cy="tray-reassignment-container" className="tray-main-wrapper">
      <div className={classes.root}>
        <Box width="auto" boxShadow="0 8px 16px rgba(0,0,0,0.05)" borderRadius="12px">
          <Card
            sx={{
              pt: 5,
              pb: 6
            }}
            className={classes.textcard}
            style={{ boxShadow: '0px 4px 20px rgba(145, 145, 145, 0.2)' }}
          >
            <div data-cy="card-header" className={classes.label}>
              {CONTENT.SCAN_BARCODE_TO_REASSIGN}
            </div>
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
              paddingLeft={4}
              paddingRight={4}
              paddingTop={3}
              paddingBottom={0}
            >
              <Box className={classes.fieldwrapper}>
                <Form
                  onSubmit={submitBarCode}
                  render={({ handleSubmit }) => (
                    <form
                      onSubmit={handleSubmit}
                      name="search-tray-form"
                      className="search-tray-form"
                      noValidate
                    >
                      <Field name="search-tray">
                        {({ input }) => (
                          <div className="material-group">
                            <LkInput
                              id="scan-barcode-input"
                              placeholder={CONTENT.SCAN_BARCODE}
                              className={classes.textfield}
                              {...input}
                              fullWidth
                              value={barCodeVal}
                              onChange={(e) => setBarCodeVal(e.target.value)}
                            />
                            <div className="success-action">
                              {getTrayLoading && <Spinner />}
                              {getTrayList && (
                                <CloseIcon
                                  data-cy="closeIcon"
                                  onClick={resetTrayListFn}
                                  style={{
                                    color: '#3C3C3C8A',
                                    cursor: 'pointer',
                                    position: 'absolute',
                                    top: '1px',
                                    right: '25px'
                                  }}
                                />
                              )}
                              {getTrayList && (
                                <img
                                  data-cy="success-icon"
                                  className="success-icon"
                                  // eslint-disable-next-line max-len
                                  src={`${
                                    import.meta.env.VITE_STATIC_SERVER
                                  }/images/tick-green.svg`}
                                  alt="success icon"
                                />
                              )}
                            </div>
                          </div>
                        )}
                      </Field>
                      <Button
                        className={classes.submitButton}
                        style={{ backgroundColor: btnBg, boxShadow }}
                        onClick={submitBarCode}
                      >
                        <img
                          data-cy="arrow-long-right-img"
                          src={`${import.meta.env.VITE_STATIC_SERVER}/images/${
                            barCodeVal.length > 0
                              ? 'arrow-long-right-white.svg'
                              : 'arrow-long-right.svg'
                          }`}
                          alt="info"
                        />
                      </Button>
                    </form>
                  )}
                />
              </Box>
            </Box>
          </Card>
        </Box>
      </div>

      {getTrayList && <TrayList trayList={getTrayList} barCodeVal={barCodeVal} />}
    </div>
  );
};

export default TrayReassignment;
