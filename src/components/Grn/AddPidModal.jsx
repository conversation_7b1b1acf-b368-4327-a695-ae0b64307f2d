import React, { useEffect, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Form, Field } from 'react-final-form';

import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import Autocomplete from '@mui/material/Autocomplete';
import { LOCALISATION } from 'redux/reducers/localisation';

import { debounce } from 'utils/helpers';
import { required } from 'utils/validation';
import { productDescriptionData } from 'config/PowerConfig';
import { CATEGORY_MAPPING } from 'config/CategoryMapping';
import {
  addPidLoad,
  convertPidLoad,
  resetPidList,
  resetAddPidData,
  searchByDescLoad,
  searchByDescReset,
  addPidReset
} from 'redux/actionCreators/grn';
import { toastOpen } from 'redux/actionCreators/toast';
import LkModal from '../Modal/Modal';
import LkInput from '../MaterialUi/LkInput';
import ProductDescription from './ProductDescription';
import './addPidModal.scss';

const mutatorsObj = {
  setEstimatedQuantity: (args, state, utils) =>
    utils.changeValue(state, 'estimatedQuantity', () => args[0]),
  setMinQuantityScan: (args, state, utils) =>
    utils.changeValue(state, 'minQuantityScan', () => args[0]),
  setScannedId: (args, state, utils) => utils.changeValue(state, 'scanned_id', () => args[0])
};

let obj = {
  purchase_order_code: '',
  isPrescriptionLens: false,
  sph: null,
  cyl: null,
  ap: null,
  product_type: ''
};

let selectedProductName = null;
let tempValues = {};
const AddPidModal = (props) => {
  let mutators;
  const {
    open,
    openModal,
    poNum,
    submitAddOnlyPid,
    classification,
    changePid,
    disabledSearchByDescription = false,
    addOnlyPid = false,
    showSearchBydesc = true
  } = props;
  const [searchDescriptionState, setSearchDescriptionState] = useState(disabledSearchByDescription);
  const createGrnLocal = useSelector((state) => state[LOCALISATION].localeData.CREATE_GRN);

  const dispatch = useDispatch();
  const {
    convertPidData,
    samplingQtyData,
    addPidProccessing,
    addPidData,
    addPidError,
    meta,
    searchByDesc,
    convertPidDataSuccess,
    pidList
  } = useSelector((state) => state.grn);

  const searchByProductID = (value) => {
    obj = { ...obj, search_query: value };
    dispatch(searchByDescLoad(obj));
  };

  const delayedQuery = useCallback(
    debounce((q) => q.length > 2 && searchByProductID(q), 500),
    []
  );

  const apiObj = useCallback(
    (values) => {
      const { estimatedQuantity, scanned_id } = values;
      const { grnNo } = props;
      return {
        grnCode: grnNo,
        pid: {
          pid: convertPidData?.result?.data?.pid || scanned_id,
          estimatedQuantity: estimatedQuantity || 0
        },
        meta,
        type: pidList?.result?.data?.type,
        invoice_level: pidList?.result?.data?.invoice_level
      };
    },
    [convertPidData, meta, props]
  );

  useEffect(() => {
    if (addPidData) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createGrnLocal.PID_SUCCESSFULLY_ADDED,
          severity: 'success'
        })
      );
      openModal(false);
      dispatch(resetPidList());
      dispatch(resetAddPidData());
    }
    if (addPidError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading:
            addPidError?.response?.data?.display_message ||
            addPidError?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
      dispatch(addPidReset());
    }
  }, [addPidData, addPidError, dispatch, openModal]);

  const submitAddPid = useCallback(
    (values) => {
      const newObj = apiObj(values);
      if (values.estimatedQuantity < 0) {
        return dispatch(
          toastOpen({
            isToastOpen: true,
            heading: createGrnLocal.PLEASE_ENTER_AN_POSITIVE_INTEGER,
            severity: 'error'
          })
        );
      }
      if (!addPidProccessing && convertPidDataSuccess) {
        dispatch(addPidReset());
        dispatch(addPidLoad(newObj));
      }
      return null;
    },
    [dispatch, convertPidDataSuccess, addPidProccessing, apiObj]
  );

  useEffect(() => {
    if (
      convertPidData?.response?.data?.display_message ||
      convertPidData?.response?.data?.meta?.displayMessage
    ) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading:
            convertPidData.response.data.display_message ||
            convertPidData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
      dispatch(addPidReset());
    } else if (
      samplingQtyData?.response?.data?.display_message ||
      samplingQtyData?.response?.data?.meta?.displayMessage
    ) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading:
            samplingQtyData.response.data.display_message ||
            samplingQtyData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    } else if (samplingQtyData?.response?.status === 400) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: createGrnLocal.PLEASE_ENTER_AN_INTEGER_VALUE,
          severity: 'error'
        })
      );
    } else if (samplingQtyData?.result?.data) {
      submitAddPid(tempValues);
    }
  }, [convertPidData, samplingQtyData, dispatch, submitAddPid]);

  useEffect(() => {
    obj = {
      ...obj,
      purchase_order_code: poNum,
      isPrescriptionLens: true,
      product_type:
        classification === 11354 || classification === 11356
          ? CATEGORY_MAPPING[classification]
          : 'other'
    };
    if (obj.product_type !== 'prescription-lens') {
      obj.search_query = '1';
      obj.isPrescriptionLens = false;
    }
    return () => {
      dispatch(searchByDescReset());
      obj = {
        purchase_order_code: '',
        sph: null,
        cyl: null,
        ap: null,
        product_type: ''
      };
      selectedProductName = null;
    };
  }, [classification, dispatch, poNum]);

  const isPidExist = (pidInput) => {
    const list = pidList?.result?.data?.grn_pids;
    let pidExist = false;
    list?.forEach((element) => {
      const { pid } = element;
      if (pid === pidInput) {
        pidExist = true;
      }
    });
    return pidExist;
  };

  const getMinQuantity = (values, onChangeValue) => {
    const { grnNo } = props;
    const { scanned_id } = values;
    if (!isPidExist(scanned_id)) {
      const { type, invoice_level } = pidList?.result?.data || {};
      dispatch(
        convertPidLoad({
          scanned_id: onChangeValue || scanned_id,
          meta,
          grn_code: grnNo,
          type,
          invoice_level
        })
      );
      mutators.setEstimatedQuantity(null);
    } else {
      changePid(scanned_id);
      openModal(false);
    }
  };

  const handleKeyPress = (event, values, callback) => {
    if (event.which === 13) {
      callback(values);
    }
  };

  const closeModal = () => {
    dispatch(resetAddPidData());
    openModal(false);
  };

  const selectDesc = (value) => {
    obj = {
      ...obj,
      sph: value['SPH Power'],
      cyl: value['CYL Power'],
      ap: value.AP
    };
    dispatch(searchByDescLoad(obj));
  };

  useEffect(
    () => () => {
      tempValues = {};
    },
    []
  );
  useEffect(() => {
    if (convertPidData) {
      submitAddPid(tempValues);
    }
  }, [convertPidData, submitAddPid]);

  return (
    <LkModal
      open={open}
      title={createGrnLocal.ADD_A_NEW_PID}
      subTitle={createGrnLocal.PLEASE_SCAN_PID_SKU_GTIN_OF_THE_ITEM_BELOW}
      showActionButton={false}
      handleClose={closeModal}
    >
      <Form
        onSubmit={submitAddPid}
        mutators={mutatorsObj}
        render={({ handleSubmit, values, form, invalid }) => {
          tempValues = values;
          mutators = form.mutators;
          // let invalidTemp = invalid || !convertPidDataSuccess;
          return (
            <form onSubmit={handleSubmit} name="otp" className="otp-form" noValidate>
              <div className="add-pid-modal mr-t15 mr-b40">
                {showSearchBydesc && (
                  <Field name="description">
                    {() => (
                      <div className="text-center fs14">
                        <label className="cursor-pointer">
                          <Checkbox
                            checked={searchDescriptionState}
                            disabled={disabledSearchByDescription}
                            color="primary"
                            value={searchDescriptionState}
                            inputProps={{ 'aria-label': 'secondary checkbox' }}
                            onChange={() => {
                              if (values.scanned_id) {
                                mutators.setScannedId(null);
                                if (!addOnlyPid) {
                                  getMinQuantity({ scanned_id: null });
                                }
                              }
                              setSearchDescriptionState(!searchDescriptionState);
                            }}
                          />{' '}
                          {createGrnLocal.SEARCH_BY_DESCRIPTION}
                        </label>
                      </div>
                    )}
                  </Field>
                )}
                {searchDescriptionState &&
                  (classification === 11356 || classification === 11354) && (
                  <ProductDescription
                    descriptionData={productDescriptionData}
                    selectDesc={(value) => selectDesc(value)}
                  />
                )}
                <Field name="scanned_id" validate={required}>
                  {({ input, meta: inputMeta, ...rest }) => (
                    <div className="material-group add-scan-input">
                      {searchDescriptionState ? (
                        <>
                          <Autocomplete
                            {...rest}
                            options={searchByDesc?.product_details || []}
                            onInputChange={() => {
                              mutators.setScannedId(null);
                            }}
                            onChange={(event, newValue) => {
                              selectedProductName = newValue?.name;
                              obj.search_query = newValue?.product_id;
                              mutators.setScannedId(newValue?.product_id);
                              if (!addOnlyPid) {
                                getMinQuantity({ scanned_id: newValue?.product_id });
                              }
                            }}
                            onKeyUp={(e) => delayedQuery(e.target.value)}
                            getOptionLabel={(option) => option.name}
                            filterOptions={(options) => options}
                            renderOption={(renderProps, option) => (
                              <li {...renderProps} key={option.product_id}>
                                <div
                                  style={{ maxWidth: '430px', whiteSpace: 'normal' }}
                                  className="display-flex fs14 option-name"
                                >
                                  <div className="text-primary fw-bold mr-r25">
                                    {option.product_id}
                                  </div>
                                  <div className="text-33">{option.name}</div>
                                </div>
                              </li>
                            )}
                            renderInput={(params) => (
                              <LkInput
                                {...params}
                                {...input}
                                className="input-box"
                                fullWidth
                                label="Enter PID"
                              />
                            )}
                          />
                          <span className="input-error">
                            {inputMeta.error && inputMeta.touched && inputMeta.error}
                          </span>
                        </>
                      ) : (
                        <>
                          <LkInput
                            {...input}
                            onBlur={(e) => {
                              input.onBlur(e);
                              getMinQuantity(values);
                            }}
                            onKeyPress={($event) => handleKeyPress($event, values, getMinQuantity)}
                            fullWidth
                            placeholder={createGrnLocal.SCAN_PID_SKU_GTIN}
                          />
                          <span className="input-error">
                            {inputMeta.error && inputMeta.touched && inputMeta.error}
                          </span>
                        </>
                      )}
                    </div>
                  )}
                </Field>
                {addOnlyPid && (
                  <div className="text-center mr-b40 mr-t40">
                    <Button
                      disabled={invalid}
                      onClick={() => submitAddOnlyPid(values, selectedProductName)}
                      variant="contained"
                      color="primary"
                    >
                      {createGrnLocal.ADD_PID}
                    </Button>
                  </div>
                )}
              </div>
            </form>
          );
        }}
      />
    </LkModal>
  );
};
export default AddPidModal;
