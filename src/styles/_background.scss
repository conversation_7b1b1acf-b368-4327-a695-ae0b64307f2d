@use "sass:map";
@use './variables' as *;

.bg-white {
    background-color: $color-white;
}

.bg-dark_pink {
    background-color: map.get($colors-secondary, dark_pink);
}
.bg-paris_green {
    background-color: map.get($colors-secondary, paris_green);
}
.bg-blue {
    background-color: map.get($colors-secondary, blue);
}
.bg-dark_pink_dot1 {
    background-color: $bg-dark_pink_dot1;
}
.bg-paris_green_dot1 {
    background-color: $bg-paris_green_dot1;
}
.bg-bright_yellow_dot1 {
    background-color: $bg-bright_yellow_dot1;
}
.bg-turquioise_surf_dot1 {
    background-color: $bg-turquioise_surf_dot1;
}
.bg-yellow {
    background-color: $color-yellow;
}
.bg-red_purple_dot9 {
    background-color: $bg-red_purple_dot9;
}
.bg-red,
.bg-error {
    background-color: $color-red;
}

.bg-primary {
    background-color: $color-primary;
}
.bg-33 {
    background-color: map.get($colors-grey, 33);
}
.bg-66 {
    background-color: map.get($colors-grey, 66);
}
.bg-99 {
    background-color: map.get($colors-grey, 99);
}
.bg-cc {
    background-color: map.get($colors-grey, cc);
}
.bg-dd {
    background-color: map.get($colors-grey, dd);
}
.bg-ee {
    background-color: map.get($colors-grey, ee);
}
.bg-f2 {
    background-color: map.get($colors-grey, f2);
}
.bg-f5 {
    background-color: map.get($colors-grey, f5);
}
.bg-fa {
    background-color: map.get($colors-grey, fa);
}
.bg-76 {
    background-color: map.get($colors-secondary, paris_green);
}
