import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getFittingData = ({ scannedCode }) =>
  tokenInstance().post(`${config.fitting.getFittingData}/${scannedCode}`);

export const getFittingQCData = ({ scannedCode }) =>
  tokenInstance().get(config.fitting.getFittingQCData, {
    params: {
      scannedEntity: scannedCode
    }
  });

export const completeFitting = ({ fittingId, shipmentId, trayId = null, payload }) =>
  tokenInstance().post(
    `${config.fitting.completeFitting}/${fittingId}/${shipmentId}${
      trayId ? `?tray_id=${trayId}` : ''
    }`,
    payload
  );
