import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import { Box, Button } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import { debounce } from 'utils/helpers';
import ShortCut from 'components/shortCut/shortCut';
import LkToolTip from 'components/ToolTip/ToolTip';
import { LOCALISATION } from 'redux/reducers/localisation';
import { resetInvoiceReferenceCreation } from '../../redux/actionCreators/invoiceReferenceCreation'
import { resetGrnCreate } from '../../redux/actionCreators/grn'
import { resetGrnListing } from '../../redux/actionCreators/grnListing';
import TabsContainer from '../../components/GRNListing/TabsContainer';
import LkInput from '../../components/MaterialUi/LkInput';

import { toastOpen } from '../../redux/actionCreators/toast';

// import { debounce } from './../../utils/helpers';
const styles = makeStyles()((theme) => ({
  root: {
    flexGrow: 1,
    borderBottom: '1px solid #DDDDDD'
  },
  container: {
    height: '90vh',
    backgroundColor: 'white',
    padding: theme.spacing(3)
  },
  tabLabel: {
    textTransform: 'none',
    color: theme.palette.primary,
    fontSize: '14px',
    fontWeight: 'normal'
  },
  tab: {
    minWidth: '100px'
  },
  tabContent: {
    borderRadius: '8px',
    border: '1px solid #DDDDDD'
  },
  tabBody: {
    maxHeight: '340px',
    overflowY: 'scroll'

  },
  btn: {
    borderRadius: 8,
  }

}))
const GrnListing = () => {
  const navigate = useNavigate();
  const { classes } = styles();
  const dispatch = useDispatch();
  const [value, setValue] = useState('')
  const { isShowShortCuts } = useSelector(state => state.shortCuts);
  const { exportGrnDetailFail } = useSelector(state => state.grnListing);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.GRN_LIST);

  // const [tab, setTabValue] = useState('GRN');
  const searchGrn = (event) => {
    event.persist();
  }
  const delayedQuery = useCallback(debounce(q => setValue(q), 500), []);

  const onChangeFun = ({ target }) => {
    delayedQuery(target.value)
  }

  useEffect(() => {
    if (exportGrnDetailFail) {
      const message = exportGrnDetailFail?.response?.data?.meta?.displayMessage
        || exportGrnDetailFail?.response?.data?.message;
      dispatch(toastOpen({
        isToastOpen: true,
        heading: 'Error',
        subHeading: message,
        severity: 'error'
      }));
    }

  }, [exportGrnDetailFail, dispatch])

  useEffect(() => () => {
    dispatch(resetGrnListing())
  }, [dispatch])

  const redirectFun = () => {
    dispatch(resetGrnCreate())
    dispatch(resetInvoiceReferenceCreation())
    navigate('/grn/create')
  }

  useKeyboardShortcut([18, 78], redirectFun);

  return (
    <div className={classes.container}>
      <Box display="flex" justifyContent="space-between">
        <Box width={320} pb={3}>
          <LkInput size="small" onKeyUp={searchGrn}
            onChange={onChangeFun} fullWidth placeholder={CONTENT.SEARCH_GRN_HERE}
          />
        </Box>
        <Box>
          <LkToolTip placement="bottom" title={<ShortCut name="Alt+N" />} open={isShowShortCuts}>
            <Button color="primary" variant="contained" className={classes.btn}
              onClick={redirectFun}
            >{CONTENT.CREATE_GRN}</Button>
          </LkToolTip>
        </Box>
      </Box>
      <TabsContainer mainGrnSearch={value} />
    </div>
  );
}
export default GrnListing;
