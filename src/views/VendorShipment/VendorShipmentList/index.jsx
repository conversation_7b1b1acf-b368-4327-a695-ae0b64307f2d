import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { omit, keys } from 'utils/lodash';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import useFilterHook from 'components/common/useFilterHook';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import {
  VENDOR_SHIPMENT,
  getVendorShipmentListExportLoad,
  getVendorShipmentListExportReset,
  getVendorShipmentListLoad
} from 'redux/reducers/vendorShipment';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import {
  convertDateFormat,
  fileDownload,
  generatePayloadForSearchAPI,
  genericDateFormatted,
  getAllQueryParam,
  listToCSVString
} from 'utils/helpers';
import VendorShipment<PERSON>istHeader from './VendorShipmentListHeader';
import VendorShipmentListBody from './VendorShipmentListBody';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    height: 'calc(100vh - 65px)',
    background: '#FFFFFF'
  }
}));

const PAGE_SIZE = 35;

const requestPayload = {
  pageRequest: {
    pageNumber: 0,
    pageSize: PAGE_SIZE,
    sortKey: 'created_at',
    sortOrder: 'DESC'
  }
};

const VendorShipmentList = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const location = useLocation();
  const initialReq = useRef(false);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.VENDOR_SHIPMENT_LIST);
  const { data, isLoading, totalCount } = useSelector(
    (state) => state[VENDOR_SHIPMENT].vendorShipmentList
  );
  const { data: exportData, isLoading: isExportLoading } = useSelector(
    (state) => state[VENDOR_SHIPMENT].exportVendorShipmentList
  );
  const [requestBody, setRequestBody] = useState(requestPayload);
  const [searchVal, setSearchVal] = useState('');

  const fetchMoreData = (sortOrder, sortKey, pageNumber) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNumber,
        sortKey,
        sortOrder
      }
    }));
  };

  const exportToCSV = (exportDataDetails) => {
    const columns = [
      CONTENT.VENDOR_SHIPMENT,
      CONTENT.QUANTITY,
      CONTENT.PO,
      CONTENT.VENDOR_INV_NO,
      CONTENT.FACILITY_CODE,
      CONTENT.ETD,
      CONTENT.BOE_DATE,
      CONTENT.WH_LANDED_DATE,
      CONTENT.EAD,
      CONTENT.AD_DATE,
      CONTENT.CC_DATE,
      CONTENT.CREATED_BY
    ];

    const exportKeys = [
      'shipmentId',
      'shipmentQuantity',
      'poNum',
      'vendorInvoiceNumber',
      'facilityCode',
      'expectedDispatchDate',
      'billOfEntryDate',
      'whLandingDate',
      'expectedArrivalDate',
      'actualDispatchDate',
      'customClearanceDate',
      'createdBy'
    ];

    const mappedExportData = exportDataDetails.map((item) => {
      const newItem = { ...item };
      keys(newItem).forEach((key) => {
        if (key.includes('Date')) {
          newItem[key] = genericDateFormatted(newItem[key], 'utcTOlocalDate');
        }
      });
      return newItem;
    });

    const csvString = listToCSVString(mappedExportData, columns, exportKeys);
    fileDownload(csvString, `shipmentList-${convertDateFormat(new Date(), 'export')}`);
  };

  const onGlobalSearch = (value) => {
    if (value) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        pageRequest: {
          ...prevReqBody.pageRequest,
          pageNumber: 0
        },
        shipment_id: [value]
      }));
    } else {
      setRequestBody((prevReqBody) => ({
        ...omit(prevReqBody, ['shipment_id'])
      }));
    }
  };

  useEffect(() => {
    if (initialReq.current >= 1) {
      dispatch(getVendorShipmentListLoad(requestBody));
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current += 1;
    const result = getAllQueryParam(window.location.search);

    const payload = generatePayloadForSearchAPI(result, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => {
      const newState = {
        pageRequest: {
          pageNumber: 0,
          pageSize: PAGE_SIZE,
          sortKey: prevReqBody.pageRequest.sortKey,
          sortOrder: prevReqBody.pageRequest.sortOrder
        },
        ...payload
      };
      if (prevReqBody.shipment_id) {
        newState.shipment_id = prevReqBody.shipment_id;
      }
      return newState;
    });
  }, [location.search]);
  useEffect(() => {
    if (exportData?.length) {
      exportToCSV(exportData);
      dispatch(getVendorShipmentListExportReset());
    }
  }, [exportData]);

  const onDownloadFun = () => {
    if (data.length > 100) {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PLEASE_APPLY_FILTER,
          severity: 'error'
        })
      );
    }
    if (data.length === totalCount) {
      return exportToCSV(data);
    }
    return dispatch(
      getVendorShipmentListExportLoad({
        ...requestBody,
        pageRequest: { ...requestBody.pageRequest, pageNumber: 0, pageSize: 100 }
      })
    );
  };

  const resetAppliedFilters = useCallback(() => {
    const tempData = selectedFilterList.filter(
      ({ key }) => !(key.startsWith('po_num') || key.startsWith('facility_code'))
    );
    resetFilters(tempData);
    if (searchVal) {
      setSearchVal('');
      onGlobalSearch('');
    }
  }, [searchVal, requestBody]);

  return (
    <Box className={classes.root} data-cy="vendor-shipment-container">
      <VendorShipmentListHeader
        onSearch={onGlobalSearch}
        CONTENT={CONTENT}
        selectedFilterList={selectedFilterList}
        setSearchVal={setSearchVal}
        searchVal={searchVal}
      />
      <VendorShipmentListBody
        pageLimit={PAGE_SIZE}
        pageNumber={requestBody.offset}
        fetchMoreListItems={fetchMoreData}
        disableDownloadFun={!data.length || isExportLoading}
        CONTENT={CONTENT}
        onDownloadFun={onDownloadFun}
        deleteSelectedFilter={deleteSelectedFilter}
        data={data}
        isLoading={isLoading}
        totalCount={totalCount}
        onChangeFilterList={onChangeFilterList}
        selectedFilterList={selectedFilterList}
        resetAppliedFilters={resetAppliedFilters}
        searchVal={searchVal}
      />
    </Box>
  );
};

export default VendorShipmentList;
