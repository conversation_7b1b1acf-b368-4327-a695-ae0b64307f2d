import { createSlice } from '@reduxjs/toolkit';
import { returnPickingScanBarcodeFailPayload } from '../../selectors/pickingScan';

const initialState = {
  barcodeScan: {
    savedScannedBarcodes: [],
    inProgressBoxes: [],
    data: {},
    isBoxWiseTransfer: false,
    payload: {},
    isLoading: false
  },

  pickingScanSummary: {
    isLoading: false,
    data: [],
    assignedTo: ''
  }
};

const registerSlice = createSlice({
  name: 'pickingScan',
  initialState,
  reducers: {
    getPickingScanSummaryLoad: (state) => {
      state.pickingScanSummary.isLoading = true;
      state.pickingScanSummary.data = [];
    },
    getPickingScanSummarySuccess: (state, { payload }) => {
      state.pickingScanSummary.isLoading = false;
      state.pickingScanSummary.data = payload.productQuantityDetailsList;
      state.pickingScanSummary.assignedTo = payload.assignedTo;
    },
    getPickingScanSummaryFail: (state) => {
      state.pickingScanSummary = initialState.pickingScanSummary;
    },

    pickingScanBarcodeLoad: (state, { payload }) => {
      const { barcode, box_barcode } = payload;
      const isAllowDuplicateScan = payload?.isAllowDuplicateScan;
      const { savedScannedBarcodes, isBoxWiseTransfer, inProgressBoxes } = state.barcodeScan;
      const barcodeIndex = savedScannedBarcodes.findIndex(
        (eachItem) => eachItem.barcode === barcode
      );
      let updatedScannedItems = savedScannedBarcodes;
      if (barcodeIndex >= 0) {
        updatedScannedItems = savedScannedBarcodes.map((eachItem, index) => {
          if (index === barcodeIndex && (eachItem.status !== 'success' || isAllowDuplicateScan)) {
            return { ...eachItem, status: 'pending' };
          }
          return eachItem;
        });
      } else if (barcode) {
        updatedScannedItems = [
          { barcode, status: 'pending', boxBarcode: box_barcode },
          ...savedScannedBarcodes
        ];
      }
      const inProgressBoxesTemp =
        isBoxWiseTransfer && box_barcode ? [inProgressBoxes, box_barcode] : inProgressBoxes;

      state.barcodeScan.savedScannedBarcodes = updatedScannedItems;
      state.barcodeScan.inProgressBoxes = inProgressBoxesTemp;
      state.barcodeScan.isLoading = true;
    },
    pickingScanBarcodeSuccess: (state, { payload }) => {
      const { barcode, productQuantityDetails } = payload;
      let successItems = state.barcodeScan.savedScannedBarcodes;
      if (state.barcodeScan.isBoxWiseTransfer) {
        const boxItems = payload.boxBarcodeItemResponse.map((eachItem) => ({
          ...eachItem,
          status: 'pending',
          serverStatus: eachItem.status,
          boxBarcode: eachItem.boxBarcode
        }));
        successItems = [...boxItems, ...successItems];
      } else {
        successItems = state.barcodeScan.savedScannedBarcodes.map((eachItem) => {
          if (eachItem.barcode === barcode) {
            return { ...eachItem, status: 'success' };
          }
          return eachItem;
        });
      }

      const productIndex = state.pickingScanSummary.data.findIndex(
        ({ productId }) => productId === productQuantityDetails.productId
      );

      // updating Product count
      if (productIndex > -1) {
        state.pickingScanSummary.data[productIndex] = productQuantityDetails;
      }

      state.barcodeScan.savedScannedBarcodes = successItems;
      state.barcodeScan.data = payload;
      state.barcodeScan.isLoading = false;
    },
    pickingScanBarcodeFail: (state, { payload }) => {
      const { updatedItems, updatedInprogressBoxes } = returnPickingScanBarcodeFailPayload(
        state,
        payload
      );
      state.barcodeScan.savedScannedBarcodes = updatedItems;
      state.barcodeScan.inProgressBoxes = updatedInprogressBoxes;
      state.barcodeScan.isLoading = false;
    },
    pickingScanToogleBoxwiseScanner: (state) => {
      state.barcodeScan.isBoxWiseTransfer = !state.barcodeScan.isBoxWiseTransfer;
    }
  }
});

export const {
  getPickingScanSummaryLoad,
  getPickingScanSummarySuccess,
  getPickingScanSummaryFail,
  pickingScanBarcodeLoad,
  pickingScanBarcodeSuccess,
  pickingScanBarcodeFail,
  pickingScanToogleBoxwiseScanner
} = registerSlice.actions;

export const PICKING_SCAN = registerSlice.name;

export default registerSlice.reducer;
