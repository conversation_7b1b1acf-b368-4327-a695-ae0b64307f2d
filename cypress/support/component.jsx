/* eslint-disable import/no-extraneous-dependencies */
import '@cypress/code-coverage/support';

import React from 'react';
import { Provider } from 'react-redux';
import { mount } from 'cypress/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { StyledEngineProvider, ThemeProvider } from '@mui/material';

import theme from 'theme';
import configureStore from 'redux/redux-store';

import Toast from 'components/Toast/Toast';
import Loader from 'components/common/Loader';

import { initialState } from './initialState';
import './commands';
import '../../src/styles/index.scss';

Cypress.Commands.add('mount', (component, options = {}) => {
  const { routerProps = { initialEntries: ['/'] }, state = {}, ...mountOptions } = options;
  const store = configureStore({ ...initialState, ...state });

  if (window.Cypress) {
    window.store = store;
  }

  const wrapped = (
    <StyledEngineProvider injectFirst>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <Loader />
          <Toast />
          <MemoryRouter {...routerProps}>{component}</MemoryRouter>
        </ThemeProvider>
      </Provider>
    </StyledEngineProvider>
  );

  return mount(wrapped, mountOptions);
});

Cypress.Commands.add('mountWithRouter', (component, route, initialPath, state = {}) => {
  cy.mount(
    <Routes>
      <Route path={route} element={component} />
    </Routes>,
    {
      routerProps: {
        initialEntries: [initialPath]
      },
      state
    }
  );
});
