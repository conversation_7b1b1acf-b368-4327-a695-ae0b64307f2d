import { useMemo } from 'react';
import { isObject } from 'utils/lodash';

const agentTemp = '/nexs/api/po/v1/bulkSubmitforapproval';
const supervisorTemp = '/nexs/api/po/v1/bulkApproveorreject';
const QCsupervisorTemp = 'can_fail_any_order_qc';
const pickingSupervisorTemp = 'nexs_picking_super_user';

const usePermissions = (permission = {}) => {
  const isPermissionExists = (apiPermission, path) =>
    isObject(apiPermission) &&
    Object.keys(apiPermission).some((eachKey) =>
      apiPermission[eachKey].some((t) => t === path || t === '*')
    );

  const isPOSuperVisor = useMemo(() => {
    const { nexs_po_api } = permission;
    return isPermissionExists(nexs_po_api, supervisorTemp);
  }, [permission]);

  const isQCSuperVisor = useMemo(() => {
    const { nexs_order_qc } = permission;
    if (nexs_order_qc?.ALL) {
      return nexs_order_qc.ALL.some((t) => t === QCsupervisorTemp || t === '*');
    }
    return false;
  }, [permission]);

  const isPOAgent = useMemo(() => {
    const { nexs_po_api } = permission;
    return isPermissionExists(nexs_po_api, agentTemp);
  }, [permission]);

  const isPickingSuperVisor = useMemo(() => {
    const { nexs_picking } = permission;
    return isPermissionExists(nexs_picking, pickingSupervisorTemp);
  }, [permission]);

  const isPackingSuperVisor = useMemo(() => {
    const { nexs_packing } = permission;
    return isPermissionExists(nexs_packing, '');
  }, [permission]);

  const canMarkInventoryBad = useMemo(() => {
    const { nexs_ems } = permission;
    return isPermissionExists(nexs_ems, '/mark/barcode/bad');
  }, [permission]);

  const isGrnSuperVisor = useMemo(() => {
    const { nexs_grn } = permission;
    return isPermissionExists(nexs_grn, '');
  }, [permission]);

  return {
    isPOSuperVisor,
    isPOAgent,
    isQCSuperVisor,
    isPickingSuperVisor,
    isPackingSuperVisor,
    canMarkInventoryBad,
    isGrnSuperVisor
  };
};

export default usePermissions;
