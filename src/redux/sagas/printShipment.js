import { call, put, all, takeLatest } from 'redux-saga/effects';
import { DONE } from 'views/Packing/SupervisorPackingConstants';
import {
  getPrintShipmentDetailApi,
  printLensDetailsSticker,
  printJobCardApi,
  getPrintShipmentUAEapi,
  printPack<PERSON>lipApi,
  getPrintShipmentMPDTCApi,
  printMpShipmentDummyApi
} from 'apis/printShipment';
import { updateOrderDetailsStatus } from 'redux/actionCreators/order';
import {
  getPrintShipmentDetailSuccess,
  printLensLabStickerSuccess,
  printLensLabStickerFail,
  getPrintPackslipSuccess,
  getPrintPackslipFail,
  getPrintShipmentDetailReset,
  getPrintMPShipmentDummySuccess,
  getPrintMPShipmentDummyFail,
  isDoShipmentFailed
} from 'redux/actionCreators/printShipment';
import {
  GET_PRINT_SHIPMENT_DETAIL_LOAD,
  PRINT_LENS_LAB_STICKER_LOAD,
  PRINT_JOB_CARD,
  GET_PRINT_PACKSLIP_LOAD,
  GET_PRINT_SHIPMENT_MPDTC_LOAD
} from 'redux/actionTypes/printShipment';
import { showLoader, hideLoader } from 'redux/actionCreators/loader';
import { printBlobPdf, sleep } from 'utils/helpers';
import { showToaster } from './toast';

const delay_retry = import.meta.env.VITE_QC_PRINTSHIPMENT_DELAY;
const max_retry = import.meta.env.VITE_QC_PRINTSHIPMENT_RETRY;

function* printLabLensStickerSaga(action) {
  try {
    yield put(showLoader());
    const response = yield call(printLensDetailsSticker, action.payload.fittingId);
    printBlobPdf(response.data);
    yield put(printLensLabStickerSuccess(response.data));
    yield put(hideLoader());
  } catch (er) {
    yield showToaster(er);
    yield put(hideLoader());
    yield put(printLensLabStickerFail());
  }
}

function* printJobCardSaga({ payload }) {
  try {
    yield put(showLoader());
    yield call(printJobCardApi, payload);
    yield showToaster(null, 'success', 'Print triggered from RXU');
    yield put(hideLoader());
  } catch (er) {
    yield showToaster(er);
    yield put(hideLoader());
  }
}

export function* getPrintShipmentUAESaga({ payload }) {
  try {
    const { data } = yield call(getPrintShipmentUAEapi, payload);
    printBlobPdf(data);
  } catch (er) {
    const errorArrayBuffer = new TextDecoder().decode(er?.response?.data);
    const error = JSON.parse(errorArrayBuffer);
    yield showToaster(error);
  }
}

function* getPrintShipmentDetailSaga(action = { payload: {} }, count = 0) {
  try {
    yield put(showLoader());
    if (!action.payload.manualRetry && count === 0) {
      yield sleep(delay_retry);
    }
    const { data } = yield call(getPrintShipmentDetailApi, action.payload);
    if (data.shipmentPdfAvailable) {
      yield getPrintShipmentUAESaga(action);
    } else {
      yield put(getPrintShipmentDetailSuccess(data));
    }
    yield put(updateOrderDetailsStatus(DONE));

    yield put(hideLoader());
  } catch (error) {
    if (action.payload?.disableOnError) {
      yield showToaster(error, 'error', null, 'Shipment not generated. Retry after 5 minutes');
      yield put(getPrintShipmentDetailReset());
      yield put(isDoShipmentFailed(true));
      yield put(hideLoader());
      return;
    }
    if (!action.payload.manualRetry && count < max_retry) {
      yield sleep(delay_retry);
      yield getPrintShipmentDetailSaga(action, count + 1);
    } else {
      yield showToaster(error, 'error', null, 'Invoice not generated. Retry after 1 minute');
      yield put(getPrintShipmentDetailReset());
      yield put(hideLoader());
    }
  }
}

function* getPrintMpShipmentDummySaga({ payload }) {
  try {
    yield put(showLoader());
    const { data } = yield call(printMpShipmentDummyApi, payload);
    yield put(getPrintMPShipmentDummySuccess(data?.data));
    yield put(hideLoader());
  } catch (er) {
    yield put(getPrintMPShipmentDummyFail());
    yield showToaster(er);
    yield put(hideLoader());
  }
}

function* getPrintShipmentMPDTCSaga({ payload }, count = 0) {
  const delayMPDTCShipment = 3000;
  try {
    yield put(showLoader());
    if (count === 0) {
      yield sleep(delayMPDTCShipment);
    }
    const { data } = yield call(getPrintShipmentMPDTCApi, payload);
    printBlobPdf(data);
    yield put(updateOrderDetailsStatus(DONE));
    yield put(hideLoader());
  } catch (er) {
    if (count < Number(max_retry) + 3) {
      yield sleep(delayMPDTCShipment);
      yield getPrintShipmentMPDTCSaga({ payload }, count + 1);
    } else {
      const errorArrayBuffer = new TextDecoder().decode(er?.response?.data);
      const error = errorArrayBuffer ? JSON.parse(errorArrayBuffer) : er;
      yield showToaster('', 'error', error?.meta?.displayMessage);
      yield put(hideLoader());
      const isPackingSummary = window.location.pathname.includes('/packing-summary');
      if (!isPackingSummary) {
        yield getPrintMpShipmentDummySaga({ payload: payload.shippingPackageId });
      }
    }
  }
}

function* getPrintPackslipSaga({ payload }) {
  try {
    yield put(showLoader());
    const response = yield call(printPackSlipApi, payload);
    yield put(getPrintPackslipSuccess(response.data.data));
    yield put(hideLoader());
  } catch (error) {
    yield put(getPrintPackslipFail(error));
    yield put(hideLoader());
    yield showToaster(error);
  }
}

export function* runPrintShipment() {
  yield all([
    takeLatest(GET_PRINT_SHIPMENT_DETAIL_LOAD, getPrintShipmentDetailSaga),
    takeLatest(GET_PRINT_SHIPMENT_MPDTC_LOAD, getPrintShipmentMPDTCSaga),
    takeLatest(GET_PRINT_PACKSLIP_LOAD, getPrintPackslipSaga),
    takeLatest(PRINT_LENS_LAB_STICKER_LOAD, printLabLensStickerSaga),
    takeLatest(PRINT_JOB_CARD, printJobCardSaga)
  ]);
}
