import Box from '@mui/material/Box';

import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { Tabs } from '@mui/material';

import { omit } from 'utils/lodash';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';

import {
  exportReactivetransfersListLoad,
  getreactiveTransferListLoad
} from 'redux/reducers/reactiveTransfer';
import { LOCALISATION } from 'redux/reducers/localisation';

import useFilterHook from 'components/common/useFilterHook';
import { StyledTab, TabPanel } from 'components/common/StyledTabs';

import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import ReactiveTransferTabsContainer from './ReactiveTransferTabsContainer';

const PAGE_SIZE = 25;

const initialRequest = {
  pageRequest: {
    pageNo: 0,
    pageSize: PAGE_SIZE,
    sortOrder: 'DESC',
    sortKey: 'updated_at'
  },
  status: 'pending'
};

const ReactiveTransferBody = ({ selectedTransfers, setSelectedTransfers }) => {
  const [requestBody, setRequestBody] = useState(initialRequest);
  const [tabValue, setTabValue] = useState('pending');
  const [globalSearch, setGlobalSearch] = useState('');
  const initialReq = useRef(false);
  const location = useLocation();
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const dispatch = useDispatch();
  const isCreateTransferSuccess = useSelector(
    (state) => state.reactiveTransfer.createReactiveTransfer.isSuccess
  );
  const { data, isLoading, totalPendingCount, totalOpenCount, totalClosedCount } = useSelector(
    (state) => state.reactiveTransfer.list
  );

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.REACTIVE_TRANSFER);

  const handleChange = (event, newValue) => {
    setGlobalSearch('');
    setTabValue(newValue);
    resetFilters();
    setSelectedTransfers([]);
  };

  const dispatchListLoadFun = () => dispatch(getreactiveTransferListLoad(requestBody));

  useEffect(() => {
    if (isCreateTransferSuccess) {
      setSelectedTransfers([]);
      dispatchListLoadFun();
    }
  }, [isCreateTransferSuccess]);

  useEffect(() => {
    if (initialReq.current) {
      dispatchListLoadFun();
    }
  }, [requestBody]);

  useEffect(() => {
    initialReq.current = true;
    const queryFilter = getAllQueryParam(window.location.search);
    const payload = generatePayloadForSearchAPI(queryFilter, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => ({
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNo: 0
      },
      status: tabValue,
      ...payload
    }));
  }, [location.search, dispatch, tabValue]);

  const fetchMoreData = (sortOrder, sortKey, pageNo) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNo,
        sortKey,
        sortOrder
      }
    }));
  };

  const onGlobalSearch = (incrementId) => {
    if (incrementId) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        pageRequest: {
          ...prevReqBody.pageRequest,
          pageNumber: 0
        },
        incrementId
      }));
    } else {
      setRequestBody((prevReqBody) => ({ ...omit(prevReqBody, 'incrementId') }));
    }
  };

  const exportFun = () => {
    dispatch(
      exportReactivetransfersListLoad({
        ...requestBody,
        pageRequest: {
          ...requestBody.pageRequest,
          pageNo: 0,
          pageSize: 2000
        }
      })
    );
  };

  return (
    <Box>
      <Box>
        <Tabs value={tabValue} onChange={handleChange}>
          <StyledTab
            data-cy="pendingTransfersTab"
            label={`${CONTENT.PENDING_TRANSFERS} ${totalPendingCount}`}
            value="pending"
          />
          <StyledTab
            data-cy="openTransferTab"
            label={`${CONTENT.OPEN_TRANSFER} ${totalOpenCount}`}
            value="created"
          />
          <StyledTab data-cy="closeTransferTab" label={CONTENT.CLOSE_TRANSFER} value="closed" />
        </Tabs>
        <Box className="border-grey5-radiusbase">
          <TabPanel value={tabValue} index="pending">
            <ReactiveTransferTabsContainer
              onGlobalSearch={onGlobalSearch}
              value={globalSearch}
              setValue={setGlobalSearch}
              fetchMoreData={fetchMoreData}
              onChangeFilterList={onChangeFilterList}
              selectedFilterList={selectedFilterList}
              deleteSelectedFilter={deleteSelectedFilter}
              resetFilters={resetFilters}
              selectedTransfers={selectedTransfers}
              setSelectedTransfers={setSelectedTransfers}
              exportFun={exportFun}
              data={data}
              isLoading={isLoading}
              totalCount={totalPendingCount}
              pageLimit={PAGE_SIZE}
            />
          </TabPanel>
          <TabPanel value={tabValue} index="created">
            <ReactiveTransferTabsContainer
              onGlobalSearch={onGlobalSearch}
              value={globalSearch}
              setValue={setGlobalSearch}
              fetchMoreData={fetchMoreData}
              onChangeFilterList={onChangeFilterList}
              selectedFilterList={selectedFilterList}
              deleteSelectedFilter={deleteSelectedFilter}
              resetFilters={resetFilters}
              selectedTransfers={selectedTransfers}
              setSelectedTransfers={setSelectedTransfers}
              exportFun={exportFun}
              disableCheckBox
              data={data}
              isLoading={isLoading}
              totalCount={totalOpenCount}
              pageLimit={PAGE_SIZE}
            />
          </TabPanel>
          <TabPanel value={tabValue} index="closed">
            <ReactiveTransferTabsContainer
              onGlobalSearch={onGlobalSearch}
              value={globalSearch}
              setValue={setGlobalSearch}
              fetchMoreData={fetchMoreData}
              onChangeFilterList={onChangeFilterList}
              selectedFilterList={selectedFilterList}
              deleteSelectedFilter={deleteSelectedFilter}
              resetFilters={resetFilters}
              selectedTransfers={selectedTransfers}
              setSelectedTransfers={setSelectedTransfers}
              exportFun={exportFun}
              disableCheckBox
              data={data}
              isLoading={isLoading}
              totalCount={totalClosedCount}
              pageLimit={PAGE_SIZE}
            />
          </TabPanel>
        </Box>
      </Box>
    </Box>
  );
};

export default ReactiveTransferBody;
