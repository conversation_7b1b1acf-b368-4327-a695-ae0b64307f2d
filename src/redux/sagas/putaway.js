import { call, put, all, takeLatest } from 'redux-saga/effects';
import {
  USER_PUTAWAY_LIST_LOAD,
  UPDATE_PUTAWAY_TO_PENDING_LOAD,
  PRINT_PUTAWAY_LOAD,
  CREATE_PUTAWAY_LOAD,
  COMPLETE_PUTAWAY_SG_LOAD,
  PRINT_MULTIPLE_PUTAWAY_LOAD
} from 'redux/actionTypes/putaway';
import {
  getUserPutawaySuccess,
  getUserPutawayFail,
  updatePutawayToPendingSuccess,
  updatePutawayToPendingFail,
  printPutawaySuccess,
  printPutawayFail,
  putawayCreationSuccess,
  getUserPutawayLoad,
  updatePutawayBarcodeStatusList,
  putawayCreationFail,
  completePutAwaySGSuccess,
  completePutAwaySGFail,
  printMultiplePutawaySuccess,
  printMultiplePutawayFail
} from 'redux/actionCreators/putaway';
import {
  userPutaway<PERSON>ist<PERSON><PERSON>,
  markPutaway<PERSON>ending<PERSON>pi,
  printPending<PERSON>pi,
  putaway<PERSON>reation<PERSON><PERSON>,
  completePutAwaySGA<PERSON>
} from 'apis/putaway';
import { toastOpen } from 'redux/actionCreators/toast';
import { fileDownload, sleep } from 'utils/helpers';
import { showInfoToaster, showToaster } from './toast';

function* userPutawayListSaga(action = { payload: {} }) {
  try {
    const data = yield call(userPutawayListApi, action.payload);
    yield put(getUserPutawaySuccess(data.response.data));
  } catch (er) {
    yield put(getUserPutawayFail(er));
  }
}

function* markPutawayPendingSaga(action = { payload: {} }) {
  try {
    const data = yield call(markPutawayPendingApi, action.payload);
    yield put(updatePutawayToPendingSuccess(data.response));
  } catch (er) {
    yield put(updatePutawayToPendingFail(er));
  }
}

function* printPutawaySaga(action = { payload: {} }) {
  try {
    const data = yield call(printPendingApi, action.payload);
    data.response.putawayId = action.payload;
    yield put(printPutawaySuccess(data.response));
  } catch (er) {
    yield put(printPutawayFail(er));
  }
}

function* updateputawaybarcodeListFun(type, barcode, list, reason) {
  const tempList = { ...list };
  tempList[type].push({ barcode, reason });
  yield put(updatePutawayBarcodeStatusList(tempList));
}

const returnToast = (heading, type = 'error') =>
  toastOpen({
    isToastOpen: true,
    heading,
    severity: type
  });

function* createPutawaySaga(action = { payload: {} }) {
  const { empCode, barcode, barcodeList } = action.payload;
  try {
    yield updateputawaybarcodeListFun('Scanned', barcode, barcodeList);
    const response = yield call(putawayCreationApi, barcode);
    const { message } = response.data.data;
    yield put(returnToast(`${barcode} ${message}`, 'success'));
    yield put(getUserPutawayLoad({ type: 'PUTAWAY_SHELF_TRANSFER', userid: empCode }));
    yield updateputawaybarcodeListFun('Passed', barcode, barcodeList);
    yield put(putawayCreationSuccess(response.data));
  } catch (er) {
    const reason = er?.response?.data?.meta?.displayMessage || er?.response?.data?.meta?.message;
    yield put(returnToast(`${reason} , ${barcode}`));
    yield updateputawaybarcodeListFun('Failed', barcode, barcodeList, reason);
    yield put(putawayCreationFail(reason));
  }
}

function* completePutAwaySGSaga(action) {
  try {
    const { data } = yield call(completePutAwaySGApi, action.payload);
    if (data.success) {
      yield put(completePutAwaySGSuccess(data.success));
      yield showToaster('', 'success', 'Putaway Completed Successfully!');
    } else {
      yield put(completePutAwaySGFail());
      yield showToaster('', 'error', data?.error);
    }
  } catch (error) {
    yield showToaster(error);
    yield put(completePutAwaySGFail(error));
  }
}

// In putaway.js saga
function* printMultiplePutawaySaga(action) {
  const { putawayCodes } = action.payload;

  try {
    // Show initial toast
    yield showInfoToaster('Generating PDFs', `Processing ${putawayCodes.length} putaway codes...`);

    const results = [];

    // Process each code sequentially
    for (let i = 0; i < putawayCodes.length; i++) {
      const code = putawayCodes[i];

      try {
        const { response } = yield call(printPendingApi, code);
        if (response?.data?.byteLength > 0) {
          // Download PDF immediately
          fileDownload(response.data, `PUTAWAY-${code}`, 'pdf');
          results.push({ code, success: true });
        }

        // Delay between requests
        if (i < putawayCodes.length - 1) {
          yield sleep(300);
        }
      } catch (error) {
        results.push({ code, success: false, error });
      }
    }

    const successful = results.filter((r) => r.success).length;
    const failed = results.filter((r) => !r.success).length;

    yield put(
      toastOpen({
        isToastOpen: true,
        heading: 'Print Complete',
        subHeading: `${successful} PDFs downloaded, ${failed} failed`,
        severity: failed === 0 ? 'success' : 'warning'
      })
    );
    yield put(printMultiplePutawaySuccess(results));
  } catch (error) {
    yield showToaster(error);
    yield put(printMultiplePutawayFail(error));
  }
}

export function* runPutawayCalls() {
  yield all([
    takeLatest(USER_PUTAWAY_LIST_LOAD, userPutawayListSaga),
    takeLatest(UPDATE_PUTAWAY_TO_PENDING_LOAD, markPutawayPendingSaga),
    takeLatest(PRINT_PUTAWAY_LOAD, printPutawaySaga),
    takeLatest(CREATE_PUTAWAY_LOAD, createPutawaySaga),
    takeLatest(COMPLETE_PUTAWAY_SG_LOAD, completePutAwaySGSaga),
    takeLatest(PRINT_MULTIPLE_PUTAWAY_LOAD, printMultiplePutawaySaga)
  ]);
}
