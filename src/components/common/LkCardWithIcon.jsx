import CardContent from '@mui/material/CardContent';
import Box from '@mui/material/Box';
import LkCard from 'components/MaterialUi/LkCard';
import React from 'react';

const LkCardWithIcon = ({ message, children }) => (
  <Box data-cy="card-container">
    <LkCard
      width={450}
      elevation={5}
      marginX="auto"
      marginTop={15}
      marginBottom={7}
      paddingX={4.25}
      paddingY={5}
    >
      <CardContent data-cy="card-content" className="display-flex justify-content-center">
        <Box>
          <Box data-cy="card-content" className="display-flex justify-content-center">
            {children}
          </Box>
          <Box data-cy="success-message">{message}</Box>
        </Box>
      </CardContent>
    </LkCard>
  </Box>
);

export default LkCardWithIcon;
