import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';

import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import CloseIcon from '@mui/icons-material/Close';

import Spinner from 'components/Spinner/Spinner';
import LkToolTip from 'components/ToolTip/ToolTip';
import useFilterHook from 'components/common/useFilterHook';
import LkInput from 'components/MaterialUi/LkInput';
import LkChip from 'components/MaterialUi/LkChip';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import ShortCut from 'components/shortCut/shortCut';
import { InvoiceOrderStatus } from 'config/InvoiceOrderStatus';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import PendingToolTip from 'views/PurchaseOrder/PurchaseOrderDetail/PendingToolTip';
import {
  fileDownload,
  getQueryParam,
  getAllQueryParam,
  generatePayloadForSearchAPI,
  genericDateFormatted
} from 'utils/helpers';
import useWindowResize from 'common/useWindowResize';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import {
  exportInvoiceReset,
  exportInvoiceLoad
} from 'redux/actionCreators/invoiceReferenceCreation';
import { searchListReset } from 'redux/actionCreators/filters';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { invoiceListLoad, invoiceListReset } from 'redux/actionCreators/invoiceDashboard';
import './invoiceDashboard.scss';

const useStyles = makeStyles()(() => ({
  table: {
    minWidth: 700
  },
  container: {
    maxHeight: 700
  },
  outlinedPrimary: {
    marginLeft: '16px',
    height: 35
  }
}));

const PAGE_SIZE = 25;

const defaultObj = {
  isReset: true,
  path: 'purchaseInvoice?version=v1',
  payload: {
    type: 'purchaseInvoice',
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey: 'created_at',
      sortOrder: 'DESC'
    }
  }
};

let objPayload = { ...defaultObj };

const initialSortData = { id: 'created_at', order: 'DESC' };

const sortingData = {
  key: 'created_at',
  order: 'DESC'
};

const KEY_MAP = {
  vendorInvoiceNumber: 'Invoice_Vendor_Invoice_Number',
  invoiceStatus: 'Invoice_Status',
  invoicePo: 'Invoice_PO',
  invoiceDate: 'Invoice_Invoice Date',
  invoiceCreated: 'Invoice_CREATED',
  invoiceCompleted: 'Invoice_COMPLETED',
  invoiceCreatedBy: 'Invoice_Created_By',
  invoiceASN: 'Invoice_ASN',
  invoiceGRN: 'Invoice_GRN'
};

let fromPo = null;

const InvoiceDashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { classes } = useStyles();
  const INVOICE = useSelector((state) => state[LOCALISATION].localeData.INVOICE);
  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters } =
    useFilterHook();
  const {
    searchList = [],
    seachListFail,
    searchListLoading = true,
    totalRecordsCount
  } = useSelector((state) => state.invoiceDashboard);
  const {
    autoSuggestionListInvoice_Status,
    autoSuggestionListInvoice_PO,
    // autoSuggestionListInvoice_GRN,
    autoSuggestionListInvoice_Created_By,
    autoSuggestionListInvoice_Vendor_Invoice_Number
  } = useSelector((state) => state.filters);

  const { exportInvoiceData, exportInvoiceDataFail, exportInvoiceLoading } = useSelector(
    (state) => state.invoiceReferenceCreation
  );
  const dispatch = useDispatch();
  const [isInitialReq, setIsInitialReq] = useState(true);
  const [tableHeight] = useWindowResize(window.innerHeight - 250);
  const [tableHeightValue, setTableHeightValue] = useState(0);
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const [globalInvoiceSearch, setGlobalInvoiceSearch] = useState('');

  useEffect(() => {
    fromPo = null;
    fromPo = getQueryParam(location.search.replace('?', ''), 'fromPo');
    if (fromPo) {
      setTableHeightValue(tableHeight - 110);
    } else {
      setTableHeightValue(tableHeight);
    }
  }, []);

  const exportInvoice = useCallback(() => {
    const tempData = JSON.parse(JSON.stringify(objPayload));
    tempData.payload.pageRequest.pageSize = totalRecordsCount;
    dispatch(exportInvoiceLoad(tempData));
  }, [dispatch, totalRecordsCount]);

  const goToCreateInvoice = () => {
    navigate('/invoice/create');
  };

  useKeyboardShortcut([18, 78], () => {
    goToCreateInvoice();
  });

  useKeyboardShortcut([18, 69], exportInvoice);

  useEffect(() => {
    if (exportInvoiceData?.response?.data) {
      fileDownload(
        exportInvoiceData.response.data,
        `Invoice-Export-${genericDateFormatted(new Date())}`
      );
      dispatch(exportInvoiceReset());
    } else if (exportInvoiceDataFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: exportInvoiceDataFail.response.data.message,
          severity: 'error'
        })
      );
      dispatch(exportInvoiceReset());
    }
  }, [exportInvoiceData, exportInvoiceDataFail, dispatch]);

  const filterSearchOption = (value, key) => {
    if (value) {
      objPayload.payload[key] = value;
    } else {
      delete objPayload.payload[key];
    }
    objPayload.payload.pageRequest.pageNumber = 0;
    objPayload.isReset = true;
    dispatch(invoiceListLoad(objPayload));
  };

  const getSearchList = () => {
    const result = getAllQueryParam(window.location.search);
    objPayload = JSON.parse(JSON.stringify(defaultObj));
    let { payload } = objPayload;
    payload.pageRequest.sortKey = sortingData.key;
    payload.pageRequest.sortOrder = sortingData.order;
    const globalSearchPOID = document.getElementById('searchInvoiceValue');
    if (globalSearchPOID && globalSearchPOID.value) {
      payload.global_po_num = globalSearchPOID.value;
    }
    payload = generatePayloadForSearchAPI(
      result,
      payload,
      { ...mapSearchFilterKey, poNum: 'po_num' },
      'Invoice_'
    );
    dispatch(invoiceListLoad(objPayload));
  };

  useEffect(() => {
    setIsInitialReq(false);

    getSearchList();
  }, [location.search]);

  useEffect(
    () => () => {
      dispatch(exportInvoiceReset());
      dispatch(searchListReset());
      dispatch(invoiceListReset());
      sortingData.order = 'DESC';
      sortingData.key = 'created_at';
    },
    [dispatch]
  );

  const updateRequestBody = (sortType, sortKey, page) => {
    if (!isInitialReq) {
      const st = sortType === 'ASC' ? 'ASC' : 'DESC';
      sortingData.id = sortKey;
      sortingData.order = st;

      objPayload.payload.pageRequest.sortKey = sortKey;
      objPayload.payload.pageRequest.sortOrder = st;

      objPayload.payload.pageRequest.pageNumber = page;
      if (!page) {
        // if page is 0
        objPayload.isReset = true;
        if (!searchListLoading) {
          // to restrict duplicate api call on reset
          dispatch(invoiceListLoad(objPayload));
        }
      } else {
        objPayload.isReset = false;
        dispatch(invoiceListLoad(objPayload));
      }
    }
  };

  const headerConfig = [
    {
      name: INVOICE.VENDOR_INV_NO,
      key: 'vendor_invoice_number',
      formatBody: useCallback(
        ({ invoice_ref_number, vendor_invoice_number }) => (
          <Link
            className="text-primary fw-bold fs12"
            to={`/invoice/view?invoiceRefNumber=${invoice_ref_number}`}
          >
            {vendor_invoice_number}
          </Link>
        ),
        []
      ),
      columnName: KEY_MAP.vendorInvoiceNumber,
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListInvoice_Vendor_Invoice_Number,
        selectedFilterList,
        columnName: KEY_MAP.vendorInvoiceNumber,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.STATUS,
      key: 'status',
      columnName: KEY_MAP.invoiceStatus,
      formatBody: useCallback(
        ({ status }) => (
          <LkChip
            label={InvoiceOrderStatus[status]?.text || status}
            type={InvoiceOrderStatus[status]?.type}
          />
        ),
        []
      ),
      style: { minWidth: '120px', maxWidth: '120px' },
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListInvoice_Status,
        selectedFilterList,
        columnName: KEY_MAP.invoiceStatus,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.RECIEVED,
      key: 'order_accepted_quantity',
      formatBody: useCallback(
        ({
          order_accepted_quantity,
          order_rejected_quantity,
          order_pending_quantity,
          total_invoice_qty
        }) => (
          <LkToolTip
            placement="bottom-end"
            title={
              <PendingToolTip
                good={order_accepted_quantity}
                bad={order_rejected_quantity}
                pending={order_pending_quantity}
                accedptedLabel="Accepted"
                rejectedLabel="Rejected"
                pendeingLabel="Pending"
              />
            }
          >
            <div>
              {order_accepted_quantity + order_rejected_quantity}/{total_invoice_qty}
            </div>
          </LkToolTip>
        ),
        []
      ),
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: INVOICE.PO,
      key: 'po_num',
      formatBody: useCallback(
        ({ po_num }) => (
          <Link
            target="_blank"
            to={`/po/detail?poNum=${po_num}&fromPo=true`}
            className="text-primary fw-bold fs12"
          >
            {po_num}{' '}
          </Link>
        ),
        []
      ),
      columnName: KEY_MAP.invoicePo,
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListInvoice_PO,
        selectedFilterList,
        columnName: KEY_MAP.invoicePo,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.VENDOR_INV_DATE,
      key: 'invoice_date',
      columnName: KEY_MAP.invoiceDate,
      formatBody: useCallback(
        ({ invoice_date }) => (invoice_date ? genericDateFormatted(invoice_date) : '-'),
        []
      ),
      style: { minWidth: '180px', maxWidth: '180px' },
      filterData: {
        type: 'dateRange',
        columnName: KEY_MAP.invoiceDate,
        selectedFilterList,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.CREATED_ON,
      key: 'created_at',
      formatBody: useCallback(
        ({ created_at }) => (
          <div className="fw-bold">
            {created_at && genericDateFormatted(created_at, 'utcTOlocalDate')}
          </div>
        ),
        []
      ),
      supportSort: true,
      columnName: 'created_at',
      filterData: {
        type: 'dateRange',
        onSubmit: { onChangeFilterList },
        columnName: KEY_MAP.invoiceCreated,
        selectedFilterList,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.COMPLETED_ON,
      key: 'closed_at',
      formatBody: useCallback(
        ({ closed_at }) => (
          <div className="fw-bold">{closed_at && genericDateFormatted(closed_at)}</div>
        ),
        []
      ),

      columnName: KEY_MAP.invoiceCompleted,
      filterData: {
        type: 'dateRange',
        columnName: KEY_MAP.invoiceCompleted,
        selectedFilterList,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.CREATED_BY,
      key: 'created_by',
      columnName: KEY_MAP.invoiceCreatedBy,
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListInvoice_Created_By,
        selectedFilterList,
        columnName: KEY_MAP.invoiceCreatedBy,
        apiName: 'purchaseInvoice'
      }
    },
    {
      name: INVOICE.QC_DONE_TOTAL_SAMPLE_SIZE,
      key: 'qc_done',
      formatBody: useCallback(
        ({ qc_done, total_sampling_qty }) =>
          (qc_done && total_sampling_qty) || qc_done === 0 || total_sampling_qty === 0
            ? `${qc_done}/${total_sampling_qty}`
            : '-',
        []
      ),
      align: 'right',
      style: { minWidth: '150px', maxWidth: '150px' }
    },
    {
      name: INVOICE.QC_PASS_QC_DONE,
      key: 'qc_pass',
      formatBody: useCallback(
        ({ qc_done, qc_pass }) =>
          (qc_done && qc_pass) || qc_done === 0 || qc_pass === 0 ? `${qc_pass}/${qc_done}` : '-',
        []
      ),
      align: 'right',
      style: { minWidth: '140px', maxWidth: '140px' }
    },
    {
      name: INVOICE.INVOICE_REF_NUM,
      key: 'invoice_ref_number',
      align: 'right'
    }
  ];

  const filterList = React.useMemo(
    () => selectedFilterList.filter(({ key }) => key.startsWith('Invoice') || key === 'poNum'),
    [selectedFilterList]
  );
  const isDisabledFun = React.useMemo(() => {
    const temp = filterList.filter(({ key }) => key !== 'fromPo' && key !== 'poNum');
    return !temp.length;
  }, [filterList]);

  useEffect(() => {
    if (seachListFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: seachListFail?.message,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [seachListFail, dispatch]);

  return (
    <div>
      <div className={`${!fromPo && 'pd-25 invoice-dashboard'} bg-white `}>
        {!fromPo && (
          <div className="searc-create-container display-flex justify-content-space-between mr-b25">
            <LkInput
              id="searchInvoiceValue"
              onKeyPress={(e) =>
                e.which === 13 && filterSearchOption(e.target.value, 'global_vendor_invoice_number')
              }
              value={globalInvoiceSearch}
              onChange={(e) => setGlobalInvoiceSearch(e.target.value)}
              className="search-input"
              label={`${INVOICE.SEARCH_INVOICE_HERE}...`}
            />
            <LkToolTip placement="bottom" title={<ShortCut name="Alt+N" />} open={isShowShortCuts}>
              <Button
                style={{ borderRadius: 8 }}
                onClick={() => goToCreateInvoice()}
                variant="contained"
                color="primary"
              >
                {INVOICE.CREATE_INVOICE}
              </Button>
            </LkToolTip>
          </div>
        )}
        <div
          className={`invoice-dashboard-main  ${
            !fromPo && 'border-grey5-radiusbase'
          }  overflow-hidden`}
        >
          <div
            className="invoice-dashboard-action 
                        pd-16 display-flex justify-content-space-between"
          >
            <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
              {filterList.map(({ key, value }) => {
                if (key === 'poNum' || key === 'fromPo') {
                  return null;
                }
                const temp = key.startsWith('Invoice_') ? key.slice(8) : key;
                return (
                  <Box key={key} mr={2} mb={1}>
                    <LkChip
                      label={`${temp}: ${value}`}
                      type="filter"
                      deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                      size="small"
                      onDelete={() => deleteSelectedFilter(key, value)}
                    />
                  </Box>
                );
              })}
            </Box>
            <div className="display-flex justify-content-space-between">
              <Button
                style={{ borderRadius: 8 }}
                disabled={isDisabledFun}
                className={classes.outlinedPrimary}
                onClick={() => {
                  setGlobalInvoiceSearch('');
                  const tempData = selectedFilterList.filter(
                    ({ key }) => !key.startsWith('Invoice_')
                  );
                  resetFilters(tempData);
                  sortingData.key = 'created_at';
                  sortingData.order = 'DESC';
                }}
                variant="outlined"
                color="primary"
              >
                {INVOICE.RESET}
              </Button>
              <LkToolTip
                placement="bottom"
                title={<ShortCut name="Alt+E" />}
                open={isShowShortCuts}
              >
                {exportInvoiceLoading ? (
                  <Button
                    style={{ borderRadius: 8, width: '105px' }}
                    disabled
                    className={classes.outlinedPrimary}
                    variant="contained"
                    color="primary"
                  >
                    <Spinner />
                  </Button>
                ) : (
                  <Button
                    style={{ borderRadius: 8 }}
                    onClick={() => exportInvoice()}
                    className={classes.outlinedPrimary}
                    variant="outlined"
                    color="primary"
                  >
                    {INVOICE.EXPORT}
                  </Button>
                )}
              </LkToolTip>
            </div>
          </div>
          <LkTable
            tableHeight={tableHeightValue}
            headerConfig={headerConfig}
            isDataFetching={searchListLoading}
            tableData={searchList}
            dataRequestFunction={updateRequestBody}
            setFilters={onChangeFilterList}
            pageLimit={PAGE_SIZE}
            initialSortBy={initialSortData}
            totalRowsCount={totalRecordsCount}
          />
        </div>
      </div>
    </div>
  );
};

export default InvoiceDashboard;
