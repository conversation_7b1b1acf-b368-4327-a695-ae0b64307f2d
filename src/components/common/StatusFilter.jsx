import React, { useState, useEffect, useCallback } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Checkbox from '@mui/material/Checkbox';
import { makeStyles } from 'tss-react/mui';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import { useDispatch, useSelector } from 'react-redux';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { searchSuggestionLoad, searchSuggestionReset } from 'redux/actionCreators/filters';
import Spinner from 'components/Spinner/Spinner';
import { Box } from '@mui/material';
import FilterWrapper from './FilterWrapper';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  listHeight: {
    height: '200px',
    overflowY: 'scroll'
  },
  listItem: {
    padding: 0,
    margin: 0,
    paddingLeft: '15px'
  },
  listItemIcon: {
    minWidth: '30px'
  },
  submitBtn: {
    borderRadius: 8,
    padding: 4
  },
  textCapitalize: {
    textTransform: 'uppercase'
  }
}));

const icon = <CheckBoxOutlineBlankIcon fontSize="small" style={{ fill: '#999999' }} />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;
const StatusFilter = ({
  onSubmit,
  marginLeft,
  selectedFilterList,
  listData,
  columnName,
  apiName
}) => {
  const [open, setOpen] = useState(false);
  const { classes } = useStyles();
  const [checked, setChecked] = useState([]);
  const { autoSuggestionListLoading, autoSuggestionListFail } = useSelector(
    (state) => state.filters
  );

  // const [placement, setPlacement] = useState();
  const dispatch = useDispatch();

  useEffect(() => {
    if (open && !listData) {
      dispatch(
        searchSuggestionLoad({
          data: { key: mapSearchFilterKey[columnName], value: '', name: columnName, apiName }
        })
      );
    }
  }, [open, listData, columnName, apiName, dispatch]);

  // const getListData = useMemo(() => {
  //     if (columnName === 'Status') return autoSuggestionListStatus || []
  //     if (columnName === 'Type') return autoSuggestionListType || []

  // }, [autoSuggestionListStatus, autoSuggestionListType, columnName])

  const handleToggle = (value) => () => {
    // event.preventDefault()

    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };
  const submitData = () => {
    const temp = checked.map((t) => ({ key: columnName, value: t }));
    onSubmit(temp, columnName);
  };

  const updateFilterChanges = useCallback(() => {
    let temp = selectedFilterList.filter((t) => t.key === columnName);
    temp = temp.map((t) => t.value);
    setChecked([...temp]);
  }, [setChecked, selectedFilterList, columnName]);

  useEffect(() => {
    updateFilterChanges();
  }, [selectedFilterList, updateFilterChanges]);

  useEffect(() => {
    if (autoSuggestionListFail) {
      setOpen(false);
      dispatch(searchSuggestionReset());
    }
  }, [autoSuggestionListFail]);

  const onSubmitWithEnter = (event) => {
    event.stopPropagation();
    if (event.which === 13) {
      submitData();
      setOpen(!open);
    }
  };

  return (
    <FilterWrapper
      onSubmit={submitData}
      marginLeft={marginLeft}
      open={open}
      setOpen={setOpen}
      onCloseUpdateFilter={updateFilterChanges}
      dataCy={columnName}
    >
      <List className={classes.listHeight}>
        {(listData || []).map((value) => {
          const labelId = `checkbox-list-label-${value}`;
          const dataCy = `${columnName}-list-item-${value}`;
          return (
            <ListItem
              key={value}
              role={undefined}
              className={classes.listItem}
              button
              onClick={handleToggle(value)}
              data-cy={dataCy}
            >
              <ListItemIcon className={classes.listItemIcon}>
                <Checkbox
                  edge="start"
                  checked={checked.indexOf(value) !== -1}
                  // tabIndex={-1}
                  disableRipple
                  color="primary"
                  inputProps={{ 'aria-labelledby': labelId }}
                  icon={icon}
                  checkedIcon={checkedIcon}
                  onKeyPress={onSubmitWithEnter}
                />
              </ListItemIcon>
              <ListItemText
                classes={{ root: classes.textCapitalize }}
                id={labelId}
                primary={<div className="fs12"> {value}</div>}
              />
            </ListItem>
          );
        })}
        {autoSuggestionListLoading && (
          <Box textAlign="center" pt={1}>
            <Spinner />
          </Box>
        )}
      </List>
    </FilterWrapper>
  );
};

export default StatusFilter;
