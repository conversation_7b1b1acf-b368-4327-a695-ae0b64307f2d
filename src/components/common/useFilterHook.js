import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { getAllQueryParam } from 'utils/helpers';

const mockPowers = ['SPH', 'CYL', 'AXIS', 'AP', 'BC'];

let filtersSelected = [];

const useFilterHook = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedFilterList, setSelectedFilterList] = useState([]);

  const pushSearchToUrl = (data) => {
    let temp = data.map(({ key, value }) => `${key}=${value}`);
    temp = temp.join('&');
    navigate(`${window.location.pathname}?${temp}`);
  };
  const onChangeFilterList = (data, type, fromVendorItemMaster = false, fromPowerData = false) => {
    let temp = [...filtersSelected];
    if (!data.length) {
      temp = temp.filter((t) => t.key !== type);
    } else {
      temp = temp.filter((t) => t.key !== data[0]?.key);
    }
    if (fromPowerData) {
      temp = temp.filter((t) => !mockPowers.includes(t.key));
    }
    let tempResult = [];
    if (fromVendorItemMaster) {
      tempResult = [...data];
    } else {
      tempResult = [...temp, ...data];
    }
    pushSearchToUrl(tempResult);
    setSelectedFilterList([...tempResult]);
  };

  const addFilterList = (data) => {
    const temp = [...selectedFilterList];
    const tempResult = [...temp, ...data];
    pushSearchToUrl(tempResult);
    setSelectedFilterList([...tempResult]);
  };

  const deleteSelectedFilter = (key, value) => {
    const temp = [...selectedFilterList];
    const index = temp.findIndex((t) => t.key === key && t.value === value);
    temp.splice(index, 1);
    if (!temp.length) {
      navigate(window.location.pathname);
    }
    setSelectedFilterList(temp);
    pushSearchToUrl(temp);
  };

  const updateSelectedFilter = ({ key, value }) => {
    let temp = [...selectedFilterList];
    temp = temp.filter((t) => t.key !== key);
    temp.push({ key, value });
    pushSearchToUrl(temp);
  };
  const resetFilters = (temp = []) => {
    setSelectedFilterList(temp);
    pushSearchToUrl(temp);
  };
  useEffect(() => {
    filtersSelected = [];
    const result = getAllQueryParam(window.location.search);
    if (result.length === 0) {
      setSelectedFilterList([]);
    } else {
      setSelectedFilterList(result);
      filtersSelected = result;
    }
  }, [location.search]);

  return {
    selectedFilterList,
    onChangeFilterList,
    deleteSelectedFilter,
    resetFilters,
    updateSelectedFilter,
    addFilterList
  };
};

export default useFilterHook;
