import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getTrayDeatilsApi = (payload) =>
  tokenInstance().get(`${config.trayReassign.getTrayDeatils}/${payload}`);

export const getTrayHistoryApi = (trayBarcode) =>
  tokenInstance().get(`${config.trayReassign.getTrayHistory}/${trayBarcode}`);

export const validateTrayApi = (payload) =>
  tokenInstance()
    .get(`${config.trayReassign.validateTray}/${payload}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const uploadVendorMasterApi = (payload) =>
  tokenInstance()
    .post(`${config.vendorMaster.upload}`, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });

export const reassignTrayApi = (payload) =>
  tokenInstance()
    .post(`${config.trayReassign.reassign}`, payload)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
