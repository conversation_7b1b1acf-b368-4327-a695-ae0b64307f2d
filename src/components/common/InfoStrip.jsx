import React from 'react';
import { useSelector } from 'react-redux';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import LkToolTip from 'components/ToolTip/ToolTip';
import LkChip from '../MaterialUi/LkChip';
import ShortCut from '../shortCut/shortCut';

const InfoStrip = ({ data, children }) => {
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);

  const downStrip = (onClickFun, className, item) => (
    <Box data-cy={item.value} onClick={onClickFun} className={`fs16 fw-bold mr-t8 ${className}`}>
      {item.value}
    </Box>
  );

  const renderScreen = () => (
    <div className="display-flex flex1">
      {data.map((item, index) => {
        const {
          className = '',
          onClick,
          parent_po_num,
          parent_po_num_url,
          status,
          type,
          divClassName = ''
        } = item;
        return (
          <div
            key={item.key}
            className={`display-flex flex-direction-column mr-r25 ${divClassName}`}
          >
            <div className="display-flex">
              <Typography data-cy={`headerInfo-${item.key}`} variant="body1">
                {item.key}
              </Typography>
              {item.status && (
                <div className="mr-l10">
                  <LkChip label={status} type={type} />
                </div>
              )}
              {parent_po_num && (
                <a
                  href={parent_po_num_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mr-l25 fs12 text-primary"
                >
                  <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/Info.svg`} alt="info" />
                  <span>Amended</span>
                </a>
              )}
            </div>
            {index === 0 ? (
              <LkToolTip
                placement="bottom"
                title={<ShortCut name="Alt+D" />}
                open={isShowShortCuts}
              >
                {downStrip(onClick, className, item)}
              </LkToolTip>
            ) : (
              downStrip(onClick, className, item)
            )}
          </div>
        );
      })}
    </div>
  );

  return (
    <div
      data-cy="infoStrip"
      className="display-flex justify-content-space-between 
      align-items-center pd-20 bg-white border-bottom-dd border-top-dd"
    >
      {renderScreen()}
      {children}
    </div>
  );
};
export default InfoStrip;
