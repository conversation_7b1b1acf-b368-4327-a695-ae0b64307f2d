import React from 'react';
import { makeStyles } from 'tss-react/mui';
import Drawer from '@mui/material/Drawer';
import { Box, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const useStyles = makeStyles()({
  fullList: {
    width: 'auto'
  },
  heading: {
    borderBottom: '1px solid #D4D4D4'
  }
});

const NavigationDrawer = ({
  modalwidth = '767px',
  anchor = 'right',
  openDrawer,
  handleClose,
  children,
  heading = ' ',
  htmlHeading,
  disableEnforceFocus = false
}) => {
  const { classes, cx } = useStyles();

  // eslint-disable-next-line no-shadow
  const list = (anchor) => (
    <div
      className={cx({
        [classes.fullList]: anchor === 'top' || anchor === 'bottom'
      })}
      style={{ width: modalwidth }}
      role="presentation"
    >
      <div>
        <Box
          className={classes.heading}
          pr={1}
          pt={0.5}
          pl={2}
          pb={0.5}
          display="flex"
          justifyContent="space-between"
          alignItems="center"
        >
          {heading && <Box className="fs20 fw-bold">{heading}</Box>}
          {htmlHeading}
          <Box>
            <IconButton onClick={handleClose} size="large">
              <CloseIcon style={{ cursor: 'pointer' }} />
            </IconButton>
          </Box>
        </Box>
        {children}
      </div>
    </div>
  );

  return (
    <div>
      <React.Fragment key={anchor}>
        <Drawer
          anchor={anchor}
          open={openDrawer}
          onClose={handleClose}
          data-cy="navigationDrawer"
          disableEnforceFocus={disableEnforceFocus}
        >
          {list(anchor)}
        </Drawer>
      </React.Fragment>
    </div>
  );
};

export default NavigationDrawer;
