import React, { useCallback, useState } from 'react';
import { Box, Button, IconButton } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import PrintIcon from '@mui/icons-material/Print';
import LkInput from 'components/MaterialUi/LkInput';
import { debounce } from 'utils/helpers';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';

const useStyles = makeStyles()({
  field: {
    width: 200,
    marginLeft: 10
  },
  inputField: {
    width: 370
  }
});

const EInvoiceFilters = ({
  selectedFilterList,
  deleteSelectedFilter,
  onChangeFun,
  selectedShipmentId,
  generateFun,
  printFun
}) => {
  const { classes } = useStyles();
  const [value, setValue] = useState('');
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.E_INVOICE);
  const delayedQuery = useCallback(
    debounce((q) => onChangeFun((data) => ({ ...data, globalSearchField: q, pageNo: 0 })), 500),
    []
  );

  const valueChangeFun = (newValue) => {
    setValue(newValue);
    delayedQuery(newValue);
  };

  return (
    <Box
      paddingTop={2}
      borderTop="1px solid lightgrey"
      className="display-flex 
    justify-content-space-between
    align-items-center
    bg-white pd-10"
    >
      <Box className="display-flex align-items-center">
        <LkInput
          fullWidth
          value={value}
          id="searchInput"
          onChange={({ target }) => valueChangeFun(target.value)}
          className={classes.inputField}
          size="small"
          placeholder={CONTENT.SEARCH_INV_OR_INC_OR_SHIP_ID}
          InputProps={{
            startAdornment: (
              <img
                className="image-container"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/search.svg`}
                alt=""
              />
            )
          }}
        />
        <Box ml={2}>
          <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />
        </Box>
      </Box>
      <Box
        className="display-flex 
    justify-content-space-between
    align-items-center"
      >
        <Button
          size="small"
          variant="contained"
          disabled={!selectedShipmentId.length}
          color="primary"
          component="span"
          data-cy="generateEInvoice"
          onClick={() => generateFun()}
        >
          {CONTENT.GENERATE_E_INVOICE}
        </Button>
        <IconButton
          data-cy="printBtn"
          disabled={!selectedShipmentId.length}
          onClick={() => printFun()}
        >
          <PrintIcon color={selectedShipmentId.length ? 'primary' : 'disabled'} fontSize="small" />
        </IconButton>
      </Box>
    </Box>
  );
};

export default EInvoiceFilters;
