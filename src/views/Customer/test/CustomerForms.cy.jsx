import React from 'react';
import COUNTRY_MOCK_DATA from './data/countryMockData.json';
import Customer from '../CustomerFormsMain';
import config from '../../../config';
import { CUSTOMER as CONTENT } from '../../../../public/localisation/lang_en.json';

describe('Customer Forms', () => {
  beforeEach(() => {
    cy.intercept('GET', config.facility.countryState, {
      statusCode: 200,
      body: COUNTRY_MOCK_DATA
    }).as('countryState');
    cy.mount(<Customer />);
  });
  it('renders correctly', () => {
    cy.findByText(CONTENT.GENERAL_DETAILS).should('exist');
    cy.getByName('name').should('have.attr', 'placeholder', 'Enter Customer Name');
    cy.getByName('website').should('have.attr', 'placeholder', 'Enter Website URL (optional)');

    cy.findByText(CONTENT.BILLING_ADDRESS).should('exist');
    cy.getByName('billing_addressLine1').should('have.attr', 'placeholder', 'Enter Address Line 1');
    cy.getByName('billing_addressLine2').should(
      'have.attr',
      'placeholder',
      'Enter Address Line 2 (optional)'
    );
    cy.getByName('billing_city').should('have.attr', 'placeholder', 'Enter City');
    cy.getByName('billing_pincode').should('have.attr', 'placeholder', 'Enter PIN Code');
    cy.getByName('billing_country').should('have.attr', 'placeholder', 'Select Country');
    cy.getByName('billing_state').should('have.attr', 'placeholder', 'Enter State');
    cy.getByName('billing_phone').should('have.attr', 'placeholder', 'Enter Contact Number');

    cy.findByText(CONTENT.SHIPPING_ADDRESS).should('exist');
    cy.getByName('shipping_addressLine1').should(
      'have.attr',
      'placeholder',
      'Enter Address Line 1'
    );
    cy.getByName('shipping_addressLine2').should(
      'have.attr',
      'placeholder',
      'Enter Address Line 2 (optional)'
    );
    cy.getByName('shipping_city').should('have.attr', 'placeholder', 'Enter City');
    cy.getByName('shipping_pincode').should('have.attr', 'placeholder', 'Enter PIN Code');
    cy.getByName('shipping_country').should('have.attr', 'placeholder', 'Select Country');
    cy.getByName('shipping_state').should('have.attr', 'placeholder', 'Enter State');
    cy.getByName('shipping_phone').should('have.attr', 'placeholder', 'Enter Contact Number');

    cy.findByText(CONTENT.CUSTOMER_CONTACT).should('exist');
    cy.getByName('email').should('have.attr', 'placeholder', 'Enter Email');
    cy.getByName('mobile').should('have.attr', 'placeholder', 'Enter Contact Number');

    cy.findByText(CONTENT.ACCOUNTING_DETAILS).should('exist');
    cy.getByName('gstin').should('have.attr', 'placeholder', 'Enter GSTIN (optional)');
    cy.getByName('pan').should('have.attr', 'placeholder', 'Enter PAN (optional)');
    cy.getByName('tin').should('have.attr', 'placeholder', 'Enter TIN (optional)');
    cy.getByName('centralSaleTax').should(
      'have.attr',
      'placeholder',
      'Enter Centeral Sales Tax (optional)'
    );
    cy.getByName('serviceTax').should('have.attr', 'placeholder', 'Enter Service Tax (optional)');

    cy.getByCy('taxExempted').should('have.text', 'Enter TAX Exempted (optional)');
    cy.getByCy('taxExempted')
      .getByName('taxExempted')
      .should('have.attr', 'type', 'checkbox')
      .should('be.checked');

    cy.getByCy('registeredDealer').should('have.text', 'Enter Registered Dealer (optional)');
    cy.getByCy('registeredDealer')
      .getByName('registeredDealer')
      .should('have.attr', 'type', 'checkbox')
      .should('be.checked');

    cy.getByCy('dualCompanyRetail').should('have.text', 'Dual Company Retail (optional)');
    cy.getByCy('dualCompanyRetail')
      .getByName('dualCompanyRetail')
      .should('have.attr', 'type', 'checkbox')
      .should('be.checked');

    cy.getByCy('providesCForm').should('have.text', 'Provides C Form (optional)');
    cy.getByCy('providesCForm')
      .getByName('providesCForm')
      .should('have.attr', 'type', 'checkbox')
      .should('be.checked');
    cy.findByRole('button', { name: CONTENT.CANCEL }).should('be.enabled');
    cy.findByRole('button', { name: CONTENT.CONFIRM }).should('be.enabled');
  });

  it('should show error message on submit', () => {
    cy.findByRole('button', { name: CONTENT.CONFIRM }).click();

    cy.getByCy('name').find('fieldset').should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('name')
      .find('p')
      .should('have.text', 'Please provide valid customer name.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');

    cy.getByCy('billing_addressLine1')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_addressLine1')
      .find('p')
      .should('have.text', 'Please provide address 1.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_city')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_city')
      .find('p')
      .should('have.text', 'Please provide city.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_pincode')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_pincode')
      .find('p')
      .should('have.text', 'Please provide pincode.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_country')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_country')
      .find('p')
      .should('have.text', 'Please select country.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_state')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_state')
      .find('p')
      .should('have.text', 'Please provide state.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_phone')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('billing_phone')
      .find('p')
      .should('have.text', 'Please provide contact number.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');

    cy.getByCy('shipping_addressLine1')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_addressLine1')
      .find('p')
      .should('have.text', 'Please provide address 1.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_city')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_city')
      .find('p')
      .should('have.text', 'Please provide city.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_pincode')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_pincode')
      .find('p')
      .should('have.text', 'Please provide pincode.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_country')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_country')
      .find('p')
      .should('have.text', 'Please select country.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_state')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_state')
      .find('p')
      .should('have.text', 'Please provide state.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_phone')
      .find('fieldset')
      .should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('shipping_phone')
      .find('p')
      .should('have.text', 'Please provide contact number.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');

    cy.getByCy('email').find('fieldset').should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('email')
      .find('p')
      .should('have.text', 'Please provide email.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
    cy.getByCy('mobile').find('fieldset').should('have.css', 'border-color', 'rgb(229, 57, 53)');
    cy.getByCy('mobile')
      .find('p')
      .should('have.text', 'Please provide Contact number.')
      .should('have.css', 'color', 'rgb(229, 57, 53)');
  });
});
