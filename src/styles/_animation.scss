@use "sass:map";
@use './variables' as *;

.spinner {
    display: block;
    width: 20px;
    height: 20px;
    margin: 0 auto;
    border-radius: 50%;
    border: 2px solid $color-white;
    border-right-color: transparent;
    animation: spinner 0.8s linear infinite;
    &-paris_green {
        border: 2px solid map.get($colors-secondary, paris_green);
        border-right-color: transparent;
    }
}

@keyframes spinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Ripple effect */
@mixin ripple($hoverBg, $clickBg) {
    background-position: center;
    transition: background 0.8s;
    &:hover {
        background: $hoverBg radial-gradient(circle, transparent 1%, $hoverBg 1%) center/15000%;
    }
    &:active {
        background-color: $clickBg;
        background-size: 100%;
        transition: background 0s;
    }
}

.width-ease-in-out {
    -webkit-transition: width 0.25s ease-in-out;
    -moz-transition: width 0.25s ease-in-out;
    -o-transition: width 0.25s ease-in-out;
    transition: width 0.25s ease-in-out;
}

.animate-all {
    -webkit-transition: all 0.25s ease-in-out;
    -moz-transition: all 0.25s ease-in-out;
    -o-transition: all 0.25s ease-in-out;
    transition: all 0.25s ease-in-out;
}
