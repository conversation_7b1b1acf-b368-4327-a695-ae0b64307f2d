import React from 'react';
import './ItemInfo.scss';
import { Button } from '@mui/material'; // Typography
import AddCircleIcon from '@mui/icons-material/AddCircle';
import { makeStyles } from 'tss-react/mui';
import { useDispatch } from 'react-redux';
import { updateQtyReset } from '../../redux/actionCreators/grn'; // resetPidList

const useStyles = makeStyles()(() => ({
  root: {
    backgroundColor: 'white'
  }
}));

const ItemsInfo = ({ text = '0', subText = '', toggleModal }) => {
  const dispatch = useDispatch();

  // removed addmore button as we removed sampling percentage now
  const showAddMore = false;

  const { classes } = useStyles();

  const openModalFun = () => {
    dispatch(updateQtyReset());
    toggleModal(true);
  };
  return (
    <div
      className={`pd-t8 pd-b8 pd-l16 pd-r16 item-info border-dd display-flex border-radius-base 
      justify-content-center flex-direction-column ${showAddMore ? ' bg-76' : ''}`}
      elevation={0}
    >
      <div className="display-flex justify-content-space-between align-items-center">
        <div className={`fs24 fw700 pd-t4 pd-b4 ${showAddMore ? ' text-white' : ''}`}>{text}</div>
        {showAddMore && (
          <Button
            className={classes.root}
            onClick={openModalFun}
            startIcon={<AddCircleIcon fontSize="small" />}
          >
            ADD
          </Button>
        )}
      </div>
      {subText && (
        <div className={`fs14 pd-b4 ${showAddMore ? ' text-white' : ' text-99'}`}>{subText}</div>
      )}
    </div>
  );
};
export default ItemsInfo;
