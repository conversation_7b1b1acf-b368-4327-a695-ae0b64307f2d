import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useLocation } from 'react-router-dom';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import { getPIDDetailsLoad } from 'redux/actionCreators/inventory';
import { parseQuery } from 'utils/helpers';
import InventoryV2 from 'views/InventoryV2';
import ProductBarCodesTable from './ProductBarCodesTable/ProductBarCodesTable';
import InventoryTopbar from './InventoryTopbar/InventoryTopbar';
import InventoryLeftContainer from './InventoryLeftContainer/InventoryLeftContainer';

const useStyles = makeStyles()(() => ({
  rootContainer: {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#E5E5E5',
    height: '100%',
    overflow: 'hidden',
    gap: 12
  },
  content: {
    display: 'flex',
    alignItems: 'start',
    justifyContent: 'start',
    padding: '0px 12px',
    gap: 12
  }
}));

const Inventory = () => {
  const location = useLocation();
  const { classes } = useStyles();
  const { productId } = useParams();
  const dispatch = useDispatch();
  const selectedFacility = useSelector((state) => state.settings.selectedFacility);
  const isVersionV2 = parseQuery(location.search)?.version === 'v2';

  const { data: pidDetails, pidDetailsLoading } = useSelector(
    (state) => state.inventory.pidDetails
  );

  useEffect(() => {
    if (productId) {
      dispatch(getPIDDetailsLoad(productId));
    }
  }, [productId]);

  const renderScreen = () => (
    <Box className={classes.rootContainer}>
      <Box>
        <InventoryTopbar productId={productId} />
      </Box>
      <Box className={classes.content}>
        <InventoryLeftContainer data={pidDetails} loading={pidDetailsLoading} />
        <ProductBarCodesTable productId={productId} selectedFacility={selectedFacility} />
      </Box>
    </Box>
  );

  return isVersionV2 ? <InventoryV2 /> : renderScreen();
};

export default Inventory;
