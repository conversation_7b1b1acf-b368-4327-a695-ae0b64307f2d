import * as action from '../actionTypes/grnListing'

//  Search GRN
export function grnSearchLoad(data) {
    return {
        type: action.GRN_SEARCH_LOAD,
        payload: data
    };
}

export function grnSearchSuccess(data) {
    return {
        type: action.GRN_SEARCH_SUCCESS,
        payload: data
    };
}

export function grnSearchFail(data) {
    return {
        type: action.GRN_SEARCH_FAIL,
        payload: data
    };
}


//  Search blocked pid

export function searchBlockedPidsLoad(data) {
    return {
        type: action.SEARCH_BLOCKED_PIDS_LOAD,
        payload: data
    };
}

export function searchBlockedPidsSuccess(data) {
    return {
        type: action.SEARCH_BLOCKED_PIDS_SUCCESS,
        payload: data
    };
}

export function searchBlockedPidsFail(data) {
    return {
        type: action.SEARCH_BLOCKED_PIDS_FAIL,
        payload: data
    };
}


//  Reassign GRN
export function reassignGRNReset(data) {
    return {
        type: action.REASSIGN_GRN_RESET,
        payload: data
    };
}

export function reassignGRNLoad(data) {
    return {
        type: action.REASSIGN_GRN_LOAD,
        payload: data
    };
}

export function reassignGRNSuccess(data) {
    return {
        type: action.REASSIGN_GRN_SUCCESS,
        payload: data
    };
}

export function reassignGRNFail(data) {
    return {
        type: action.REASSIGN_GRN_FAIL,
        payload: data
    };
}

// export grn details

export function exportGrnDetailsLoad(data) {
    return {
        type: action.EXPORT_GRN_DETAILS_LOAD,
        payload: data
    };
}
export function exportGrnDetailsReset(data) {
    return {
        type: action.EXPORT_GRN_DETAILS_RESET,
        payload: data
    };
}

export function exportGrnDetailsSuccess(data) {
    return {
        type: action.EXPORT_GRN_DETAILS_SUCCESS,
        payload: data
    };
}

export function exportGrnDetailsFail(data) {
    return {
        type: action.EXPORT_GRN_DETAILS_FAIL,
        payload: data
    };
}




// Manual override


export function manualOverrideLoad(data) {
    return {
        type: action.MANUAL_OVERRIDE_LOAD,
        payload: data
    };
}

export function manualOverrideSuccess(data) {
    return {
        type: action.MANUAL_OVERRIDE_SUCCESS,
        payload: data
    };
}

export function manualOverrideFail(data) {
    return {
        type: action.MANUAL_OVERRIDE_FAIL,
        payload: data
    };
}


//reset grn listing

export function resetGrnListing(data) {
    return {
        type: action.RESET_GRN_LISTING,
        payload: data
    };
}


