import React, { useState } from 'react';
import { makeStyles } from 'tss-react/mui';

import LkChip from 'components/MaterialUi/LkChip';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import CancelIcon from '@mui/icons-material/Cancel';

const useStyles = makeStyles()(() => ({
  container: {
    marginLeft: '20px',
    display: 'flex',
    alignItems: 'center',
    flexWrap: 'wrap'
  },
  chip: {
    fontWeight: 400,
    fontSize: 13,
    lineHeight: '18px',
    letterSpacing: '0.16px',
    color: '#3C3C3C',
    marginRight: 10,
    marginTop: 5,
    minHeight: 32
  },
  showMoreButton: {
    fontWeight: 400,
    fontSize: 11,
    lineHeight: '120%',
    letterSpacing: '0.4px',
    color: 'rgba(60, 60, 60, 0.54)'
  }
}));

const FilterChips = ({ filters, deleteData }) => {
  const { classes } = useStyles();
  const [showAll, setShowAll] = useState(false);

  const renderCard = ({ key, values, value, label, disableDelete = false }) => (
    <LkChip
      key={key}
      label={`${label}: ${values || value}`}
      type="filter"
      deleteIcon={disableDelete ? null : <CancelIcon style={{ color: 'rgba(60, 60, 60, 0.23)' }} />}
      onDelete={disableDelete ? null : () => deleteData(key)}
      className={classes.chip}
      size="medium"
    />
  );

  const initialChildren = filters?.slice(0, 4);
  const moreChildren = filters?.slice(4, filters.length);
  const renderShowAll = () => (
    <Button onClick={() => setShowAll(true)} className={classes.showMoreButton}>
      +{moreChildren.length} More
    </Button>
  );

  return (
    <Box className={classes.container}>
      {initialChildren.map(renderCard)}
      {moreChildren.length > 0 && (showAll ? moreChildren.map(renderCard) : renderShowAll())}
    </Box>
  );
};

export default FilterChips;
