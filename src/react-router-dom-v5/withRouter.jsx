// src/hooks/withRouter.js
import React from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';

const withRouter = (Component) => function(props) {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();

  const history = {
    push: (path) => navigate(path),
    replace: (path) => navigate(path, { replace: true }),
    goBack: () => navigate(-1),
    goForward: () => navigate(1),
    location,
  };

  return <Component {...props} history={history} location={location} match={{ params }} />;
};

export default withRouter;