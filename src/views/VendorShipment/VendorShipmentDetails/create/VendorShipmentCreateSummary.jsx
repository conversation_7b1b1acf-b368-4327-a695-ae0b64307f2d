import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { makeStyles } from 'tss-react/mui';
import { Box, Button, IconButton } from '@mui/material';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import InfoStrip from 'components/common/InfoStrip';
import LkToolTip from 'components/ToolTip/ToolTip';
import { useDispatch, useSelector } from 'react-redux';
import ShortCut from 'components/shortCut/shortCut';
import AddCircle from '@mui/icons-material/AddCircle';
import UploadCsv from 'components/UploadCsv/UploadCsv';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import LkInput from 'components/MaterialUi/LkInput';
import { toastOpen } from 'redux/actionCreators/toast';
import { keys } from 'utils/lodash';
import { csvFileReader, formatDateDashedSeparated, getQueryParam, numberOnly } from 'utils/helpers';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import {
  createShipmentItemLoad,
  createShipmentItemReset,
  createVendorShipmentLoad,
  getDetailsByShipmentLoad,
  getPidDetailsLoad,
  uploadVendorPoShipmentItemsLoad,
  uploadVendorPoShipmentItemsReset
} from 'redux/reducers/vendorShipment';
import { LOGIN } from 'redux/reducers/login';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()(() => ({}));

const initialPidData = {
  productId: '',
  productDesc: 'PID Description',
  quantity: 0,
  canEditPid: true,
  newPid: true
};

const sampleCSV = [
  ['product_id', 'quantity'],
  ['131932', '2'],
  ['131933', '4'],
  ['131935', '3']
];

const VendorShipmentCreateSummary = () => {
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();
  const quantityRef = useRef(null);
  const { shipmentId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.VENDOR_SHIPMENT_SUMMARY);

  const {
    shipmentDetails,
    uploadVendorPoShipmentItems,
    createShipmentItems,
    pidDetails,
    newQtyPayload
  } = useSelector((state) => state.vendorShipment);
  const { isLoading: uploadFileLoading, data: uploadedData } = uploadVendorPoShipmentItems;
  const { isSuccess } = createShipmentItems;

  const empCode = useSelector((state) => state[LOGIN].user.userDetail?.empCode);

  const [activeRow, setActiveRow] = useState(-1);
  const [activePid, setActivePid] = useState(-1);
  const [finalData, setFinalData] = useState([initialPidData]);
  const [openImportCsvModal, setOpenImportCsvModal] = useState(false);
  const [file, setFile] = useState('');
  const [resetState, setResetState] = useState(false);
  const [isDisableCancel, setIsDisableCancel] = useState(false);

  const infoStripData = [
    {
      key: CONTENT.PO_SHIPMENT,
      value: shipmentDetails?.shipmentId ?? '-',
      status: shipmentDetails?.status,
      type: 'parisGreen',
      className: 'fs16 fw-bold text-turquioise_surf cursor-pointer'
    },
    {
      key: CONTENT.CREATION_DATE,
      value: shipmentDetails?.createdAt
        ? formatDateDashedSeparated(shipmentDetails?.createdAt)
        : '-'
    },
    { key: CONTENT.VENDOR, value: shipmentDetails?.vendorId ?? '-' },
    {
      key: CONTENT.PO,
      value: shipmentDetails?.poNum ?? '-',
      type: 'parisGreen',
      className: 'fs16 fw-bold text-turquioise_surf cursor-pointer',
      onClick: () =>
        shipmentDetails?.poNum && navigate(`/po/detail?poNum=${shipmentDetails?.poNum}`)
    }
  ];

  useEffect(() => {
    if (shipmentId) {
      dispatch(getDetailsByShipmentLoad({ shipment_id: shipmentId }));
    }
  }, [shipmentId]);

  function unloadListener(event) {
    const confirmationMessage = CONTENT.RELOAD_WARN_MSG;
    event.preventDefault();
    // eslint-disable-next-line no-param-reassign
    event.returnValue = confirmationMessage; // Required for some browsers
    return confirmationMessage;
  }

  useEffect(() => {
    const newQty = getQueryParam(location.search.replace('?', ''), 'newQty');
    if (newQty && keys(newQtyPayload)?.length > 0) {
      /** On Reload event */
      window.addEventListener('beforeunload', unloadListener);
    }
    if (newQty && !keys(newQtyPayload)?.length > 0) {
      navigate(`/vendorShipment/edit/${shipmentId}`);
    }
    return () => {
      if (newQty) {
        window.removeEventListener('beforeunload', unloadListener);
      }
    };
  }, [location.search, newQtyPayload]);

  useEffect(() => {
    const fetchedData = pidDetails[activePid];

    if (fetchedData && keys(fetchedData).length > 0) {
      const productId = activePid;
      const { name: productDesc } = fetchedData;
      const withData = finalData.filter((item) => item?.productId !== '');

      setFinalData(() => {
        const newEntry = {
          productDesc,
          productId,
          quantity: 0,
          canEditPid: true,
          newPid: productId
        };
        return [...withData, newEntry];
      });

      setTimeout(() => {
        quantityRef.current.focus();
      }, 100);
    }
  }, [pidDetails, activePid]);

  useEffect(() => {
    if (shipmentDetails) {
      const uploadedDataNew = shipmentDetails?.items?.map((item) => ({
        productId: item?.product_id,
        productDesc: item?.desc,
        canEditPid: false,
        newPid: false,
        ...item
      }));
      setFinalData(uploadedDataNew);
    }
  }, [shipmentDetails]);

  useEffect(() => {
    if (isSuccess) {
      navigate('/vendorShipment/list');
    }
    return () => {
      dispatch(createShipmentItemReset());
    };
  }, [isSuccess]);

  useEffect(() => {
    if (uploadedData?.length > 0) {
      const uploadedDataNew = uploadedData.map((item) => ({
        productId: item?.product_id,
        productDesc: item?.desc,
        canEditPid: false,
        newPid: false,
        ...item
      }));
      setFinalData(uploadedDataNew);
      setOpenImportCsvModal(false);
      setResetState(true);
      setIsDisableCancel(true);
    }
  }, [uploadedData]);

  const checkPid = (value) => {
    const pidAlreadyExist = finalData.some((i) => Number(i.productId) === Number(value));
    if (pidAlreadyExist) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PID_ALREADY_ADDED,
          subHeading: CONTENT.PLEASE_CHOOSE_DIFF_PID,
          severity: 'error',
          autoHideDuration: 2000
        })
      );
    } else if ((value !== '' && !pidDetails[value]) || pidDetails[value]?.error) {
      dispatch(
        getPidDetailsLoad({
          product_ids: [value],
          state_code: shipmentDetails?.stateCode,
          supplier_id: shipmentDetails?.vendorId
        }),
        []
      );
    }
  };

  const pidInput = (value) => (
    <LkInput
      fullWidth
      variant="outlined"
      placeholder={value?.toString() || CONTENT.ENTER_PID}
      onBlur={(e) => {
        checkPid(e.currentTarget.value);
        setActivePid(e.currentTarget.value);
      }}
    />
  );

  const deleteItem = (pid) => {
    const filteredItem = finalData.filter((i) => i.productId !== pid);
    setFinalData(filteredItem);
  };

  const showPidError = () => {
    dispatch(
      toastOpen({
        isToastOpen: true,
        heading: CONTENT.PLEASE_ENTER_QTY,
        severity: 'error',
        autoHideDuration: 2000
      })
    );
  };

  const updateQty = (qty, item, index, newItem) => {
    if (qty && Number(qty) > 0) {
      setFinalData((prevData) => {
        const updatedData = [...prevData];
        updatedData[index] = {
          ...updatedData[index],
          quantity: Number(qty),
          ...(newItem && { canEditPid: false })
        };
        return updatedData;
      });
    } else {
      showPidError();
    }
  };

  const qtyInput = (value, item, index, newItem, pid) => (
    <LkInput
      fullWidth
      variant="outlined"
      disabled={!pid}
      onBlur={(e) => updateQty(e.currentTarget.value, item, index, newItem)}
      onKeyPress={(e) => numberOnly(e)}
      placeholder={value?.toString()}
      inputRef={newItem ? quantityRef : null}
    />
  );

  const headerConfig = [
    {
      name: CONTENT.SNO,
      key: 'index',
      style: { minWidth: '50px', maxWidth: '50px' },
      formatBody: useCallback((_, index) => <span>{index + 1}</span>, [finalData])
    },
    {
      name: CONTENT.PID,
      key: 'pid',
      style: { minWidth: '50px', maxWidth: '50px' },
      formatBody: useCallback(
        ({ productId, canEditPid }) =>
          canEditPid ? (
            pidInput(productId)
          ) : (
            <span className={cx(`${classes.readOnlyInput} fs14`)}>{productId}</span>
          ),
        [finalData]
      )
    },
    {
      name: CONTENT.PID_DESCRIPTION,
      key: 'productDesc',
      style: { minWidth: '150px', maxWidth: '150px' },
      formatBody: useCallback(
        ({ productDesc }) => (
          <Box>
            <LkToolTip
              placement="bottom-end"
              title={<Box p={1}>{productDesc} </Box>}
              className="dtoolTip"
            >
              <Box className="ellipsis-vertical text-99">{productDesc}</Box>
            </LkToolTip>
          </Box>
        ),
        [finalData]
      )
    },
    {
      name: CONTENT.QUANTITY,
      key: 'quantity',
      style: { minWidth: '40px', maxWidth: '40px' },
      formatBody: useCallback(
        (item, index) => qtyInput(item.quantity, item, index, item.canEditPid, item.productId),
        [finalData]
      )
    },
    {
      name: '',
      key: 'action',
      style: { minWidth: '10px', maxWidth: '10px' },
      formatBody: useCallback(
        ({ productId }, index) => (
          <IconButton
            data-cy={`delete-icon-${index}`}
            aria-label="delete"
            onClick={() => deleteItem(productId)}
          >
            <DeleteOutlineIcon />
          </IconButton>
        ),
        [finalData]
      )
    }
  ];

  const tableheight = useMemo(
    () => (!finalData?.length || finalData?.length === 1 ? 106 : finalData.length * 59 + 49),
    [finalData]
  );

  const addPidFunc = () => {
    setActiveRow(-1);
    setFinalData(finalData ? [...finalData, { ...initialPidData }] : [{ ...initialPidData }]);
  };

  const upLoadSelectedFileFun = (uploadedFile) => {
    if (uploadedFile) {
      setFile(uploadedFile);
    }
  };

  const handleFileSubmit = () => {
    const isNewQty = getQueryParam(location.search.replace('?', ''), 'newQty');
    const formData = new FormData();
    formData.set('file', file);
    const uploadPayload = {
      shipment_id: shipmentDetails?.shipmentId,
      vendor_po_shipment_id: shipmentDetails?.id
    };
    if (shipmentDetails?.poNum) {
      uploadPayload.po_num = shipmentDetails?.poNum;
    } else {
      uploadPayload.vendor_id = shipmentDetails?.vendorId;
    }
    if (isNewQty && keys(newQtyPayload).length > 0) {
      csvFileReader(file, (rows) => {
        const totalQtySum = rows?.slice(1).reduce((sum, row) => sum + parseInt(row[1], 10), 0);
        if (Number(newQtyPayload?.shipment_quantity) === Number(totalQtySum)) {
          dispatch(
            createVendorShipmentLoad({
              createPayload: newQtyPayload,
              uploadPayload: { formData, payload: { ...uploadPayload } }
            })
          );
        } else {
          dispatch(
            toastOpen({
              isToastOpen: true,
              heading: `${CONTENT.MATCH_QTY_WARNING}!`,
              severity: 'error',
              autoHideDuration: 2000
            })
          );
        }
      });
    } else {
      dispatch(uploadVendorPoShipmentItemsLoad({ formData, payload: { ...uploadPayload } }));
    }
  };

  const disableAddPid = useMemo(
    () => finalData?.some(({ productId, quantity }) => !productId || !quantity),
    [finalData]
  );

  const handleClose = () => {
    setOpenImportCsvModal(false);
    dispatch(uploadVendorPoShipmentItemsReset());
  };

  const handleSubmit = () => {
    const isNewQty = getQueryParam(location.search.replace('?', ''), 'newQty');
    const filteredPidItems = finalData?.map(({ productId, quantity }) => ({
      product_id: productId,
      quantity
    }));
    const finalPayload = {
      shipment_id: shipmentDetails?.shipmentId,
      items: filteredPidItems,
      created_by: empCode,
      updated_by: empCode,
      version: 0,
      vendor_po_shipment_id: shipmentDetails?.id
    };
    if (isNewQty && newQtyPayload?.shipment_quantity) {
      const totalQtySum = finalData?.reduce((sum, item) => sum + item.quantity, 0);
      if (Number(newQtyPayload?.shipment_quantity) === Number(totalQtySum)) {
        dispatch(
          createVendorShipmentLoad({ createPayload: newQtyPayload, itemsPayload: finalPayload })
        );
      } else {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: `${CONTENT.MATCH_QTY_WARNING}!`,
            severity: 'error',
            autoHideDuration: 2000
          })
        );
      }
    } else {
      dispatch(createShipmentItemLoad(finalPayload));
    }
  };

  return (
    <Box data-cy="shipment-summary-container">
      <Box>
        <InfoStrip data={infoStripData}>
          <Box className="display-flex align-items-center">
            <LkToolTip placement="bottom" title={<ShortCut name="Ctrl+I" />} open={isShowShortCuts}>
              <Button
                color="primary"
                style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                variant="outlined"
                data-cy="import-csv-btn"
                onClick={() => setOpenImportCsvModal(true)}
                startIcon={
                  <img
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/import.svg`}
                    alt="Import"
                  />
                }
              >
                {CONTENT.IMPORT_CSV}
              </Button>
            </LkToolTip>
            <Button
              color="primary"
              style={{ marginRight: 10, borderRadius: 8, height: 36 }}
              variant="outlined"
              data-cy="cancel-btn"
              disabled={isDisableCancel}
              onClick={() => navigate(`/vendorShipment/details/${shipmentDetails?.shipmentId}`)}
            >
              {CONTENT.CANCEL}
            </Button>
            <Button
              color="primary"
              style={{ borderRadius: 8, height: 36 }}
              type="submit"
              data-cy="done-btn"
              variant="contained"
              onClick={handleSubmit}
              disabled={disableAddPid}
            >
              {CONTENT.DONE}
            </Button>
          </Box>
        </InfoStrip>
      </Box>
      <Box sx={{ padding: '40px' }}>
        <LkTable
          isNonVertualizedTable
          tableHeight={tableheight}
          headerConfig={headerConfig}
          tableData={finalData ?? []}
          totalRowsCount={1}
          showTableCount={false}
          rowSize={60}
          selectedRows={[activeRow]}
          rowKey="index"
        />
        <Box mt={2} className="text-center">
          <Button
            size="small"
            variant="outlined"
            color="primary"
            data-cy="add-pid-btn"
            startIcon={<AddCircle />}
            onClick={() => addPidFunc()}
            disabled={disableAddPid}
          >
            {CONTENT.ADD_PID}
          </Button>
        </Box>
      </Box>
      {openImportCsvModal && (
        <UploadCsv
          open={openImportCsvModal}
          onClose={handleClose}
          selectFiles={upLoadSelectedFileFun}
          handleSubmit={handleFileSubmit}
          sampleCSV={sampleCSV}
          checkedItemLabel={CONTENT.FILE_UPLOAD_WARN_MESSAGE}
          localUpload
          resetState={resetState}
          importLoading={uploadFileLoading}
          sampleFileName="Sample_Vendor_Shipment_Upload"
        />
      )}
    </Box>
  );
};

export default VendorShipmentCreateSummary;
