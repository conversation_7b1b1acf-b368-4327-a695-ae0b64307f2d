// GET DROP DOWN MENU OPTIONS FROM API

// HEADER
export const ORDER_DETAILS_HEADER_LOAD = 'ORDER_DETAILS_HEADER_LOAD';
export const ORDER_DETAILS_HEADER_SUCCESS = 'ORDER_DETAILS_HEADER_SUCCESS';
export const ORDER_DETAILS_HEADER_FAIL = 'ORDER_DETAILS_HEADER_FAIL';

// OVERVIEW
export const ORDER_DETAILS_OVERVIEW_LOAD = 'ORDER_DETAILS_OVERVIEW_LOAD';
export const ORDER_DETAILS_OVERVIEW_SUCCESS = 'ORDER_DETAILS_OVERVIEW_SUCCESS';
export const ORDER_DETAILS_OVERVIEW_FAIL = 'ORDER_DETAILS_OVERVIEW_FAIL';

// Courier List
export const ORDER_DETAILS_COURIER_LIST_LOAD = 'ORDER_DETAILS_COURIER_LIST_LOAD';
export const ORDER_DETAILS_COURIER_LIST_SUCCESS = 'ORDER_DETAILS_COURIER_LIST_SUCCESS';
export const ORDER_DETAILS_COURIER_LIST_FAIL = 'ORDER_DETAILS_COURIER_LIST_FAIL';

// ORDER STOCK OUT
export const ORDER_STOCK_OUT_LOAD = 'ORDER_STOCK_OUT_LOAD';
export const ORDER_STOCK_OUT_SUCCESS = 'ORDER_STOCK_OUT_SUCCESS';
export const ORDER_STOCK_OUT_FAIL = 'ORDER_STOCK_OUT_FAIL';
export const ORDER_STOCK_OUT_RESET = 'ORDER_STOCK_OUT_RESET';

// ITEMS
export const ORDER_DETAILS_ITEMS_LOAD = 'ORDER_DETAILS_ITEMS_LOAD';
export const ORDER_DETAILS_ITEMS_SUCCESS = 'ORDER_DETAILS_ITEMS_SUCCESS';
export const ORDER_DETAILS_ITEMS_FAIL = 'ORDER_DETAILS_ITEMS_FAIL';

// ORDER DETAILS SHIPMENT LIST
export const ORDER_DETAILS_SHIPMENT_ITEMS_LOAD = 'ORDER_DETAILS_SHIPMENT_ITEMS_LOAD';
export const ORDER_DETAILS_SHIPMENTS_ITEMS_SUCCESS = 'ORDER_DETAILS_SHIPMENTS_ITEMS_SUCCESS';
export const ORDER_DETAILS_SHIPMENTS_ITEMS_FAIL = 'ORDER_DETAILS_SHIPMENTS_ITEMS_FAIL';

// SHIPMENT REASSIGN
export const ORDER_DETAILS_SHIPMENT_REASSIGN_LOAD = 'ORDER_DETAILS_SHIPMENT_REASSIGN_LOAD';
export const ORDER_DETAILS_SHIPMENT_REASSIGN_SUCCESS = 'ORDER_DETAILS_SHIPMENT_REASSIGN_SUCCESS';
export const ORDER_DETAILS_SHIPMENT_REASSIGN_FAIL = 'ORDER_DETAILS_SHIPMENT_REASSIGN_FAIL';

// SHIPMENT
export const ORDER_DETAILS_SHIPMENTS_LOAD = 'ORDER_DETAILS_SHIPMENTS_LOAD';
export const ORDER_DETAILS_SHIPMENTS_SUCCESS = 'ORDER_DETAILS_SHIPMENTS_SUCCESS';
export const ORDER_DETAILS_SHIPMENTS_FAIL = 'ORDER_DETAILS_SHIPMENTS_FAIL';

// PRINT INVOICE
export const ORDER_DETAILS_PRINT_INVOICE_LOAD = 'ORDER_DETAILS_PRINT_INVOICE_LOAD';
export const ORDER_DETAILS_PRINT_INVOICE_SUCCESS = 'ORDER_DETAILS_PRINT_INVOICE_SUCCESS';
export const ORDER_DETAILS_PRINT_INVOICE_FAIL = 'ORDER_DETAILS_PRINT_INVOICE_FAIL';
export const ORDER_DETAILS_PRINT_INVOICE_RESET = 'ORDER_DETAILS_PRINT_INVOICE_RESET';

// INVOICE TAB API
export const ORDER_DETAILS_INVOICE_DATA_LOAD = 'ORDER_DETAILS_INVOICE_DATA_LOAD';
export const ORDER_DETAILS_INVOICE_DATA_SUCCESS = 'ORDER_DETAILS_INVOICE_DATA_SUCCESS';
export const ORDER_DETAILS_INVOICE_DATA_FAIL = 'ORDER_DETAILS_INVOICE_DATA_FAIL';

// RESET

export const ORDER_DETAILS_RESET_DATA = 'ORDER_DETAILS_RESET_DATA';

// Oder details History
export const ORDER_DETAILS_HISTORY_LOAD = 'ORDER_DETAILS_HISTORY_LOAD';
export const ORDER_DETAILS_HISTORY_SUCCESS = 'ORDER_DETAILS_HISTORY_SUCCESS';
export const ORDER_DETAILS_HISTORY_FAIL = 'ORDER_DETAILS_HISTORY_FAIL';
export const ORDER_DETAILS_HISTORY_RESET = 'ORDER_DETAILS_HISTORY_RESET';

export const ORDER_DETAILS_AUTO_TO_MANUAL_LOAD = 'ORDER_DETAILS_AUTO_TO_MANUAL_LOAD';
export const ORDER_DETAILS_AUTO_TO_MANUAL_SUCCESS = 'ORDER_DETAILS_AUTO_TO_MANUAL_SUCCESS';
export const ORDER_DETAILS_AUTO_TO_MANUAL_FAIL = 'ORDER_DETAILS_AUTO_TO_MANUAL_FAIL';
export const ORDER_DETAILS_AUTO_TO_MANUAL_RESET = 'ORDER_DETAILS_AUTO_TO_MANUAL_RESET';

export const ORDER_DETAILS_AUTO_REASSIGN_LOAD = 'ORDER_DETAILS_AUTO_REASSIGN_LOAD';
