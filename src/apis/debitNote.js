
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { convertToQueryString } from '../utils/helpers';

export const getDebitNoteListApi = payload => {
  const f = convertToQueryString(payload.apiObj);
  return tokenInstance().get(`${config.debitNote.list}?${f}`)
    .then(response => response)
    .catch(error => { throw error });
};

export const getDebitNoteListCsvApi = payload => {
  const f = convertToQueryString(payload.apiObj);
  return tokenInstance().get(`${config.debitNote.listCsv}?${f}`)
    .then(response => {
      return new Blob([response.data], { type: 'application/csv' });
    })
    .then(blob => ({blob}))
    .catch(error => { throw error });
};

export const getDebitNoteDetailApi = dbNum => {
  return tokenInstance().get(`${config.debitNote.detail}?id=${dbNum}`)
    .then(response => response)
    .catch(error => { throw error });
};
