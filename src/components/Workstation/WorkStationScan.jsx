import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';

import LkModal from 'components/Modal/Modal';
import constant from 'constant';
import localStorageHelper from 'utils/localStorageHelper';
import { toastOpen } from 'redux/actionCreators/toast';
import LkInput from 'components/MaterialUi/LkInput';

const useStyles = makeStyles()(() => ({
  heading: {
    marginTop: 32
  },
  label: {
    marginBottom: 16,
    textAlign: 'center',
    fontWeight: 700,
    fontSize: '24px',
    color: '#333'
  },
  barcode: {
    width: 300,
    margin: '0 auto',
    display: 'flex'
  }
}));

const WorkStationScan = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [openModal, setOpenModal] = useState(!localStorageHelper.getItem(constant.WORKSTATION));

  useEffect(() => {
    if (openModal) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Please scan workstation barcode first',
          severity: 'error'
        })
      );
      return setOpenModal(true);
    }
    return setOpenModal(false);
  }, []);

  const handleClose = (e) => {
    const barcodeValue = e.target.value?.trim();
    if (e.key === 'Enter') {
      localStorageHelper.setItem(constant.WORKSTATION, barcodeValue);
      setOpenModal(false);
    }
  };

  return (
    <LkModal
      modalWidth={500}
      modalHeight={200}
      upperHeading={
        <Typography data-cy="workstation-title" variant="h3" className={classes.heading}>
          Please scan work station barcode
        </Typography>
      }
      open={openModal}
      handleClose={() => setOpenModal(false)}
      showClose={false}
      showActionButton
    >
      <LkInput
        id="barcodeId"
        autoFocus
        className={classes.barcode}
        variant="outlined"
        placeholder="Scan work station barcode"
        size="small"
        onKeyUp={handleClose}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <img
                style={{ width: '25px' }}
                alt="barcode-img"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/barcodeScan.svg`}
              />
            </InputAdornment>
          )
        }}
      />
    </LkModal>
  );
};

export default WorkStationScan;
