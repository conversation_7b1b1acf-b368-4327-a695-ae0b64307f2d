import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import LKChip from 'components/common/LKChip';
import useWindowResize from 'common/useWindowResize';
import LkTable from 'components/MaterialUi/LkTable/LkTable';

import { genericDateFormatted } from 'utils/helpers';

import {
  FINANCE_DO,
  approveDOLoading,
  getDODetailsLoading,
  rejectDOLoading
} from 'redux/reducers/finance';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';

import FinanceDOApproveRejectModal from './FinaceModal/FinanceDOApproveRejectModal';
import InfoModal from './FinaceModal/InfoModal';

const STATUS_COLOR = {
  created: 'warning',
  approved: 'success',
  rejected: 'error'
};
const initialSortBy = { id: 'createdAt', order: 'DESC' };

const FinanceTableBody = ({
  fetchMoreListItems,
  pageLimit,
  submitData,
  pageNumber,
  selectedFilters,
  setInfoModal,
  infoModal
}) => {
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 200);

  const [approveRejectDOModal, setApproveRejectDOModal] = useState({
    modalOpen: false,
    doValue: '',
    entity: '',
    poNo: '',
    doNumber: '',
    id: '',
    status: ''
  });

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.FINANCE_DO);
  const { isLoading, data, totalCount } = useSelector((state) => state[FINANCE_DO].DOList);
  const DODetails = useSelector((state) => state[FINANCE_DO].DODetails);

  const STATUS_NAME = {
    created: CONTENT.CREATED,
    approved: CONTENT.APPROVED_UPPERCASE,
    rejected: CONTENT.REJECTED
  };

  const handleModal = (_, status, modalParams) => {
    setApproveRejectDOModal((prevReqBody) => ({
      ...prevReqBody,
      modalOpen: true,
      status,
      ...modalParams
    }));
  };

  const handleSubmit = (rejectionReason) => {
    if (approveRejectDOModal?.status === 'rejected' && rejectionReason === '') {
      return dispatch(
        toastOpen({
          isToastOpen: true,
          heading: CONTENT.PLEASE_ENTER_REASON_FOR_REJECTION,
          severity: 'error'
        })
      );
    }

    if (approveRejectDOModal?.status === 'approved') {
      dispatch(approveDOLoading({ id: approveRejectDOModal?.id }));
    }
    if (approveRejectDOModal?.status === 'rejected') {
      dispatch(
        rejectDOLoading({
          id: approveRejectDOModal?.id,
          rejectReason: rejectionReason
        })
      );
    }
    return null;
  };

  const headerConfig = [
    {
      name: CONTENT.DO_NUMBER,
      key: 'incrementId',
      enableExpander: true,
      filterData: {
        type: 'input',
        columnName: 'doNumber'
      }
    },
    {
      name: CONTENT.PO_NUMBER,
      key: 'poNumber',
      filterData: {
        type: 'input',
        columnName: 'poNumber'
      }
    },
    {
      name: CONTENT.DO_TYPE,
      key: 'doType'
    },
    {
      name: CONTENT.ENTITY,
      key: 'customer',
      formatBody: useCallback(({ customer }) => customer?.name ?? '-', [])
    },
    {
      name: CONTENT.SO_VALUE,
      key: 'soValue',
      formatBody: useCallback(
        ({ orderItems }) =>
          orderItems?.reduce((acc, { price, quantity }) => acc + price * quantity, 0),
        []
      )
    },
    {
      name: CONTENT.EMPLOYEE_ID,
      key: 'createdBy'
    },
    {
      name: CONTENT.CREATED_AT,
      key: 'createdAt',
      supportSort: true,
      formatBody: useCallback(({ createdAt }) => genericDateFormatted(createdAt), []),
      filterData: {
        type: 'dateRange',
        columnName: 'createdAt',
        selectedFilterList: selectedFilters
      }
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      formatBody: useCallback(({ status }) => {
        const statusValue = status.toLowerCase();
        return <LKChip color={STATUS_COLOR[statusValue]} value={STATUS_NAME[statusValue]} />;
      }, []),
      filterData: {
        type: 'status',
        listData: ['CREATED', 'APPROVED', 'REJECTED'],
        columnName: 'status',
        selectedFilterList: selectedFilters,
        submitFilter: submitData
      }
    },
    {
      name: CONTENT.ACTION,
      key: 'action',
      formatBody: useCallback(({ status, customer, orderItems, poNumber, incrementId, id }) => {
        const doValue = orderItems?.reduce((acc, { price, quantity }) => acc + price * quantity, 0);
        const entity = customer?.name;
        const modalParams = { doValue, entity, poNo: poNumber, doNumber: incrementId, id };
        if (status === 'CREATED') {
          return (
            <Box display="flex" gap={1.5}>
              <Button
                variant="contained"
                color="success"
                size="small"
                onClick={(e) => handleModal(e, 'approved', modalParams)}
              >
                {CONTENT.APPROVE_UPPERCASE}
              </Button>
              <Button
                variant="contained"
                color="error"
                size="small"
                onClick={(e) => handleModal(e, 'rejected', modalParams)}
              >
                {CONTENT.REJECT_UPPERCASE}
              </Button>
            </Box>
          );
        }
        return null;
      }, []),
      style: { minWidth: 200, maxWidth: 200 }
    }
  ];

  const extendedHeaderConfig = [
    {
      name: 'Invoice Value',
      key: 'invoiceValueDO',
      formatBody: useCallback(({ totalPrice }) => <Box>{totalPrice}</Box>)
    },
    {
      name: 'Invoice Number',
      key: 'invoiceNumber'
    },
    {
      name: 'Carrier',
      key: 'courierCode'
    },
    {
      name: 'Total Price',
      key: 'totalPrice'
    },
    {
      name: 'AWB',
      key: 'awbNumber'
    }
  ];
  const getDOOrderDetails = (id, incrementId) => {
    if (!DODetails?.[id]) {
      dispatch(getDODetailsLoading({ id, incrementId }));
    }
  };

  const renderExtenedComponent = ({ id, incrementId }) => (
    <LkTable
      tableHeight={190}
      rowExtendedSize={300}
      tableData={DODetails?.[id]?.data}
      headerConfig={extendedHeaderConfig}
      pageNumber={0}
      showTableCount={false}
      isNonVertualizedTable
      dataRequestFunction={() => getDOOrderDetails(id, incrementId)}
    />
  );

  return (
    <>
      <LkTable
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        headerConfig={headerConfig}
        tableData={data}
        totalRowsCount={totalCount}
        pageLimit={pageLimit}
        pageNumber={pageNumber}
        dataRequestFunction={fetchMoreListItems}
        isNonVertualizedTable
        setFilters={submitData}
        renderExtenedComponent={renderExtenedComponent}
        initialSortBy={initialSortBy}
      />
      {approveRejectDOModal?.modalOpen && (
        <FinanceDOApproveRejectModal
          open={approveRejectDOModal?.modalOpen}
          options={approveRejectDOModal}
          isDOApprove={approveRejectDOModal?.status === 'approved'}
          handleClose={() =>
            setApproveRejectDOModal((prevReqBody) => ({ ...prevReqBody, modalOpen: false }))
          }
          handleSubmit={handleSubmit}
        />
      )}
      {infoModal && (
        <InfoModal
          open={infoModal}
          handleClose={() => setInfoModal(false)}
          isApprove={approveRejectDOModal?.status === 'approved'}
          doNumber={approveRejectDOModal?.doNumber}
        />
      )}
    </>
  );
};

export default FinanceTableBody;

FinanceTableBody.propTypes = {
  fetchMoreListItems: PropTypes.func.isRequired,
  pageLimit: PropTypes.number.isRequired,
  submitData: PropTypes.func.isRequired,
  pageNumber: PropTypes.number.isRequired,
  selectedFilters: PropTypes.arrayOf(
    PropTypes.exact({
      key: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
      keyValue: PropTypes.string.isRequired,
      values: PropTypes.string.isRequired,
      disableDelete: PropTypes.bool.isRequired
    })
  ).isRequired,
  setInfoModal: PropTypes.func.isRequired,
  infoModal: PropTypes.bool.isRequired
};
