@use "sass:map";
@use './variables' as *;

.border-ee {
    border: 1px solid map.get($colors-grey, ee);
}

.border-radius-base {
    border-radius: $border-radius-base;
}

.border-radius-6 {
    border-radius: 6px;
}

.border-radius-8 {
    border-radius: 8px;
}


.border-grey5-radiusbase {
    @extend .border-dd;
    @extend .border-radius-base;
}
.border-top-dd {
    border-top: 1px solid map.get($colors-grey, dd);
}
.border-bottom-dd {
    border-bottom: 1px solid map.get($colors-grey, dd);
}
.border-bottom-ee {
    border-bottom: 1px solid map.get($colors-grey, ee);
}
.border-left-ee {
    border-left: 1px solid map.get($colors-grey, ee);
}
.border-left-dd {
    border-left: 1px solid map.get($colors-grey, dd);
}
.border-right-dd {
    border-right: 1px solid map.get($colors-grey, dd);
}
.border-left-no-radius {
    border-top-left-radius: 0;
}
.border-right-ee {
    border-right: 1px solid map.get($colors-grey, ee);
}
.border-top-ee {
    border-top: 1px solid map.get($colors-grey, ee);
}
.border-dd {
    border: 1px solid map.get($colors-grey, dd);
}
.border-66 {
    border: 1px solid map.get($colors-grey, 66);
}
.border-brigth-yellow {
    border: 1px solid $color-yellow;
}
.border-bottom-dd-dashed {
    border-bottom: 1px dashed map.get($colors-grey, dd);
}

.border-radius-50cent {
    border-radius: 50%;
}
.border-primary,
.border-success {
    border: 1px solid $color-primary;
}
.border-error,
.border-red {
    border: 1px solid $color-red;
}
