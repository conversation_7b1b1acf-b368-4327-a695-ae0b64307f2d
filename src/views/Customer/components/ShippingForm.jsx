import React from 'react';
import { useSelector } from 'react-redux';

import { Box, MenuItem } from '@mui/material';

import { LOCALISATION } from 'redux/reducers/localisation';

const ShippingForm = ({
  CustomerFormTitle,
  CustomerFieldContainer,
  CustomerTextField,
  formState,
  doCountryList,
  handleChange,
  errors
}) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);
  return (
    <Box>
      <CustomerFormTitle variant="h4">{CONTENT.SHIPPING_ADDRESS}</CustomerFormTitle>
      <CustomerFieldContainer>
        <CustomerTextField
          name="shipping_addressLine1"
          data-cy="shipping_addressLine1"
          label="Enter Address Line 1"
          value={formState.shipping_addressLine1}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.shipping_addressLine1}
          helperText={errors.shipping_addressLine1}
          placeholder="Enter Address Line 1"
        />
        <CustomerTextField
          name="shipping_addressLine2"
          value={formState.shipping_addressLine2}
          onChange={handleChange}
          placeholder="Enter Address Line 2 (optional)"
        />
        <CustomerTextField
          name="shipping_city"
          data-cy="shipping_city"
          label="Enter City"
          value={formState.shipping_city}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.shipping_city}
          helperText={errors.shipping_city}
          placeholder="Enter City"
        />
        <CustomerTextField
          name="shipping_pincode"
          data-cy="shipping_pincode"
          label="Enter PIN Code"
          value={formState.shipping_pincode}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.shipping_pincode}
          helperText={errors.shipping_pincode}
          placeholder="Enter PIN Code"
        />

        <CustomerTextField
          name="shipping_country"
          data-cy="shipping_country"
          select
          label="Country"
          onChange={handleChange}
          onBlur={handleChange}
          value={formState.shipping_country}
          error={!!errors.shipping_country}
          helperText={errors.shipping_country}
          placeholder="Select Country"
        >
          <MenuItem selected disabled value="">
            Select Country
          </MenuItem>
          {doCountryList.map(({ country_code, country_name }) => (
            <MenuItem key={country_code} value={country_code}>
              {country_name}
            </MenuItem>
          ))}
        </CustomerTextField>

        <CustomerTextField
          name="shipping_state"
          data-cy="shipping_state"
          label="Enter State"
          value={formState.shipping_state}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.shipping_state}
          helperText={errors.shipping_state}
          placeholder="Enter State"
        />
        <CustomerTextField
          name="shipping_phone"
          data-cy="shipping_phone"
          label="Enter Contact Number"
          value={formState.shipping_phone}
          onChange={handleChange}
          onBlur={handleChange}
          error={!!errors.shipping_phone}
          helperText={errors.shipping_phone}
          placeholder="Enter Contact Number"
        />
      </CustomerFieldContainer>
    </Box>
  );
};

export default ShippingForm;
