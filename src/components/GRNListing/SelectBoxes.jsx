import React from 'react'
import LkInput from './../../components/MaterialUi/LkInput';
import { Box } from '@mui/material';
import LkSelect from 'components/MaterialUi/LkSelectBox';
import MenuItem from "@mui/material/MenuItem";

const statusMock = [{ key: "created", value: "Created" }, { key: "closed", value: "Closed" }, { key: "in_progress", value: "In Progress" }]

const selectBoxMapping = {
    pid: "PID",
    grnCode: "GRN Number",
    po: "PO Number",
    invoiceRefNumber: "Invoice Ref Number",
    "vendorInvoice": "Vendor Invoice Num",
    vendor: "Vendor Name",
    createdBy: "Created By"
}
const SelectBoxes = ({ selectBox, inputBox, handleSelectBox, handleInputBox, selectBoxItems }) => {
    
    return (
        <Box display="flex">

            <Box width={200}>
                <LkSelect style={{ height: 40 }} label={false} fullWidth={true} value={selectBox} onChange={handleSelectBox}>
                    <MenuItem value={0}>{"Please Select"}</MenuItem>
                    {selectBoxItems.map((t) => <MenuItem key={t.key} value={t.key}>{t.value}</MenuItem>)}
                </LkSelect>
            </Box>
            <Box width={200} ml={2} >
                {selectBox !== 0 &&
                    <>
                        {selectBox !== "grnStatus" && selectBox !== 0 ?
                            <LkInput fullWidth={true}
                                label={`Search ${selectBoxMapping[selectBox]}...`}
                                onChange={(e) => handleInputBox(false, e)} disabled={selectBox === 0 ? true : false} value={inputBox} /> :
                            <LkSelect disabled={selectBox === 0} style={{ height: 40 }} label={false} fullWidth={true} value={inputBox} onChange={(e) => handleInputBox(true, e)}>
                                <MenuItem value={0}>{"Please Select"}</MenuItem>

                                {statusMock.map((t) => <MenuItem key={t.key} value={t.key}>{t.value}</MenuItem>)}

                            </LkSelect>}
                    </>

                }
            </Box>
        </Box>

    )
}

export default SelectBoxes