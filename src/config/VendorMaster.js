export const STATUSMAPPING = {
  rejected: 'error',
  approved: 'success',
  pending_approval: 'warning'
};

export const CLASSFICATION_TYPE_CONST = {
  EYEFRAME: 'eye-frames',
  CONTACT_LENS: 'contact-lens',
  PRESCRITION_LENS: 'prescription-lens',
  SUNGLASSES: 'sunglasses',
  CONTACT_LENS_SOLUTIONS: 'lens-cleaning-solution',
  SMART_GLASSES: 'smart_glasses',
  ZERO_POWER: 'zeropower',
  NON_POWER_READING: 'Non-Power Reading',
  SPECTACLES: 'spectacles',
  METAL_CHIP: 'Metal Clip',
  ACCESSORIES: 'shipping_accessories, Accessories',
  CASES: 'cases'
};

export const CLASSIFICATION_CONFIG = [
  { key: 'Select Type', value: 0 },
  { key: 'Eyeglass', value: CLASSFICATION_TYPE_CONST.EYEFRAME },
  { key: 'Contact Lens', value: CLASSFICATION_TYPE_CONST.CONTACT_LENS },
  { key: 'Prescription Lens', value: CLASSFICATION_TYPE_CONST.PRESCRITION_LENS },
  { key: 'Sunglasses', value: CLASSFICATION_TYPE_CONST.SUNGLASSES },
  { key: 'Contact Lens Solution', value: CLASSFICATION_TYPE_CONST.CONTACT_LENS_SOLUTIONS },
  { key: 'Smart Glasses', value: CLASSFICATION_TYPE_CONST.SMART_GLASSES },
  { key: 'Zeropower', value: CLASSFICATION_TYPE_CONST.ZERO_POWER },
  { key: 'Non Power Reading', value: CLASSFICATION_TYPE_CONST.NON_POWER_READING },
  { key: 'Spectacles', value: CLASSFICATION_TYPE_CONST.SPECTACLES },
  { key: 'Clip On', value: CLASSFICATION_TYPE_CONST.METAL_CHIP },
  { key: 'Accessories', value: CLASSFICATION_TYPE_CONST.ACCESSORIES },
  { key: 'Cases', value: CLASSFICATION_TYPE_CONST.CASES }
];

export const statusImgMapping = {
  pending_approval: '/images/pendingApproval.svg',
  approved: '/images/checkFilledSmall.svg',
  rejected: '/images/cancel-filled.svg'
};

export const statusCSSMapping = {
  pending_approval: 'brightYellow',
  approved: 'parisGreen',
  rejected: 'red'
};

export const statusValueMapping = {
  pending_approval: 'Pending Approval',
  rejected: 'Rejected',
  approved: 'Approved'
};

export const STATUS_LIST_DATA = ['APPROVED', 'PENDING_APPROVAL', 'CONFIRMED', 'REJECTED'];

export const APPROVE_REFECT_CONFIG = {
  CONFIRMED: 'APPROVED',
  PENDING_APPROVAL: 'CONFIRMED'
};
