import React, { useEffect, useState, useMemo } from 'react';
import Tab from '@mui/material/Tab';
import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import { StyledTabs } from 'components/common/StyledTabs';
import './facilityListTab.scss';
import LkInput from 'components/MaterialUi/LkInput';
import CreateFacility from 'views/Facility/FacilityCreation/CreateFacility';
import { useDispatch, useSelector } from 'react-redux';
import { saveFacilityReset, getFacilityListReset } from 'redux/actionCreators/facility';
import { toastOpen } from 'redux/actionCreators/toast';
import useFilterHook from 'components/common/useFilterHook';
import { LOCALISATION } from 'redux/reducers/localisation';
import FacilityListing from './FacilityList';
import FiltersContainer from './FiltersContainer';

const styles = makeStyles()((theme) => ({
  positionRelative: { position: 'relative' },
  tab1Absolute: { position: 'absolute', background: 'white', top: 48, left: 1 },
  tab2Absolute: { position: 'absolute', background: 'white', top: 48, left: 161 },
  tab3Absolute: { position: 'absolute', background: 'white', top: 48, left: 321 },
  container: { height: '90vh', backgroundColor: '#f5f5f5', padding: theme.spacing(3) },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  },
  root: { backgroundColor: '#FFFFFF' }
}));

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const FacilityListTab = () => {
  const { classes, cx } = styles();
  const dispatch = useDispatch();
  const [value, setValue] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [globalSearch, setGlobalSearch] = useState(null);
  const [openCreateModal, setOpenCreateModal] = useState(false);

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.FACILITY);

  const {
    facilitySave,
    facilityDraftSuccess,
    facilityList: { facilityListLoading, data, totalRecordsCount }
  } = useSelector((state) => state.facility);

  const { selectedFilterList, deleteSelectedFilter, resetFilters, onChangeFilterList } =
    useFilterHook();

  useEffect(() => {
    dispatch(getFacilityListReset());
    dispatch(saveFacilityReset());
  }, [dispatch]);

  useEffect(() => {
    if (facilitySave) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: facilitySave?.meta?.displayMessage,
          severity: 'success'
        })
      );
      setTimeout(() => {
        dispatch(
          toastOpen({ isToastOpen: true, heading: CONTENT.FETCHING_UPDATED_DATA, severity: 'info' })
        );
      }, 1000);
      dispatch(saveFacilityReset());
    }
  }, [facilitySave, dispatch, value]);

  const handleChange = (_event, newValue) => {
    setValue(newValue);
  };

  const activeFilterList = useMemo(
    () => selectedFilterList.filter(({ key }) => key.startsWith('Active_')),
    [selectedFilterList]
  );

  const draftFilterList = useMemo(
    () => selectedFilterList.filter(({ key }) => key.startsWith('Draft_')),
    [selectedFilterList]
  );

  const activeLabel = useMemo(
    () =>
      value === 0 && !facilityListLoading
        ? `${CONTENT.ACTIVE} ${totalRecordsCount || 0}`
        : CONTENT.ACTIVE,
    [totalRecordsCount]
  );

  const draftLabel = useMemo(
    () =>
      value === 1 && !facilityListLoading
        ? `${CONTENT.ALL} (${totalRecordsCount || 0})`
        : CONTENT.ALL,
    [totalRecordsCount]
  );

  const onChangeFun = (e) => setSearchValue(e.target.value);

  return (
    <div className={classes.container}>
      <div className={classes.mainBar}>
        <Box width="320px">
          <LkInput
            label={CONTENT.SEARCH_FACILITY}
            onKeyPress={(e) => e.key === 'Enter' && setGlobalSearch(e.target.value)}
            variant="outlined"
            value={searchValue}
            fullWidth
            className={classes.root}
            onChange={onChangeFun}
          />
        </Box>
        <div>
          <CreateFacility
            openModal={openCreateModal}
            setOpenModal={setOpenCreateModal}
            CONTENT={CONTENT}
          />
        </div>
      </div>
      <Box width="100%" overflow="hidden" margin="0 auto" className={classes.positionRelative}>
        <StyledTabs value={value} onChange={handleChange}>
          <Tab label={activeLabel} className={cx('tabs', { tabselected: value === 0 })} />
          <Tab label={draftLabel} className={cx('tabs', { tabselected: value === 1 })} />
        </StyledTabs>
        <Box
          className={cx('border-grey5-radiusbase overflow-hidden', {
            'border-left-no-radius': value === 0
          })}
        >
          <TabPanel value={value} index={value}>
            <Box
              width={158}
              component="div"
              height={2}
              className={cx(value === 0 ? classes.tab1Absolute : classes.tab2Absolute)}
            />
            <FiltersContainer
              data={value === 0 ? activeFilterList : draftFilterList}
              deleteSelectedFilter={deleteSelectedFilter}
              resetFilters={resetFilters}
              tabValue={value === 0 ? 'Active_' : 'Draft_'}
              selectedFilterList={selectedFilterList}
              CONTENT={CONTENT}
            />
            <FacilityListing
              selectedFilterList={value === 0 ? activeFilterList : draftFilterList}
              isDraftMode={value === 1}
              globalSearch={globalSearch}
              totalRecordsCount={totalRecordsCount}
              data={data}
              facilityDraftSuccess={facilityDraftSuccess}
              facilityListLoading={facilityListLoading}
              openCreateModal={openCreateModal}
              onChangeFilterList={onChangeFilterList}
              CONTENT={CONTENT}
            />
          </TabPanel>
        </Box>
      </Box>
    </div>
  );
};

export default FacilityListTab;
