import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import LkModal from 'components/Modal/Modal';
import LkInput from 'components/MaterialUi/LkInput';
import Spinner from 'components/Spinner/Spinner';

import { getIqcBarcodeDetailsLoad } from 'redux/reducers/inwardQC';
import { isEmpty } from 'utils/lodash';

const useStyle = makeStyles()(() => ({
  modalHeading: {
    fontWeight: 700,
    fontSize: 24,
    marginTop: 20,
    marginBottom: 24
  },
  modalContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: 270,
    margin: 'auto'
  },
  inputText: {
    fontSize: 14,
    fontWeight: 400,
    color: '#333333'
  },
  button: {
    marginTop: 24
  }
}));

const BeginIqcModal = ({ open, handleClose, grnCode }) => {
  const { classes } = useStyle();
  const [input, setInput] = useState('');
  const [error, setError] = useState(false);

  const { data, isLoading } = useSelector((state) => state.inwardQC.iqcBarcodeDetails);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleChange = (e) => {
    if (!e.target.value) {
      setError(true);
      setInput(e.target.value);
    } else {
      setError(false);
      setInput(e.target.value);
    }
  };

  const handleClick = (newValue) => {
    if (!newValue.trim()) {
      setError(true);
    } else {
      setError(false);
      dispatch(
        getIqcBarcodeDetailsLoad({
          barcode: newValue.trim(),
          grnCode
        })
      );
    }
  };

  useEffect(() => {
    if (!isEmpty(data)) {
      navigate(`/IQC?barcode=${input.trim()}&grnCode=${grnCode}`);
    }
  }, [data]);

  const handleKeyUp = (e) => {
    handleChange(e);
    if (e.key === 'Enter') {
      setInput(e.target.value);
      handleClick(e.target.value);
    }
  };

  return (
    <LkModal
      upperHeading={<h3 className={classes.modalHeading}>Scan Box/Barcode</h3>}
      open={open}
      showClose={false}
      handleClose={handleClose}
      modalWidth={580}
      modalHeight={230}
    >
      <Box className={classes.modalContainer}>
        <LkInput error={error} onKeyUp={handleKeyUp} label="scan box/barcode" autoFocus fullWidth />
        <Button
          onClick={handleClick}
          className={classes.button}
          color="primary"
          variant="contained"
          disabled={isLoading}
        >
          {isLoading ? <Spinner /> : 'BEGIN IQC'}
        </Button>
      </Box>
    </LkModal>
  );
};

export default BeginIqcModal;
