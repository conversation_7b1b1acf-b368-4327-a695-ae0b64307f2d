export const EDGING_QC_STATUS_MAPPING = {
  IN_TRAY: 'EDGING_QC_PASS',
  QCHold: 'EDGING_QC_HOLD',
  QCUnhold: 'EDGING_QC_UNHOLD',
  QCFailed: 'QCFailed'
};

export const TOAST_MESSAGE_MAPPING = {
  fail: {
    heading: 'Edging QC Failed',
    severity: 'error'
  },
  hold: {
    heading: 'Edging QC on Hold',
    severity: 'warning'
  },
  unhold: {
    heading: 'Edging QC on UnHold',
    severity: 'warning'
  },
  success: {
    heading: 'Edging QC Success',
    severity: 'success'
  }
};
