import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import ListIcon from '@mui/icons-material/List';
import LkTable from 'components/MaterialUi/LkTable/LkTable';

import useWindowResize from 'common/useWindowResize';
import {
  getActiveApproverRoleLoad,
  getChildApproverRoleLoad,
  getChildApproverRoleReset
} from 'redux/reducers/userManagement';
import { LKTreeItem, LKTreeView } from 'components/MaterialUi/LKTree';
import { LOGIN } from 'redux/reducers/login';
import Spinner from 'components/Spinner/Spinner';
import AddUserModal from '../UserWidgets/AddUserModal';

const useStyles = makeStyles()(() => ({
  button: {
    width: 140,
    padding: '4px 22px',
    marginLeft: 12
  }
}));

const UserRoleTable = () => {
  const [tableHeight] = useWindowResize(window.innerHeight - 250);
  const { classes } = useStyles();
  const [open, setOpen] = useState(false);
  const [roleName, setRoleName] = useState('');

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const approverRole = useSelector((state) => state.userManagement.approverRoles);
  const childRole = useSelector((state) => state.userManagement.childApproverRole);
  const [extendedTableData, setExtendedTableData] = useState({});

  useEffect(() => {
    dispatch(getActiveApproverRoleLoad());
    return () => {
      dispatch(getChildApproverRoleReset());
    };
  }, []);

  useEffect(() => {
    setExtendedTableData(childRole.data);
  }, [childRole]);

  const getChildRole = (approverRoleGroupId) => {
    if (!extendedTableData[approverRoleGroupId]) {
      dispatch(getChildApproverRoleLoad({ empCode: userDetail.empCode, approverRoleGroupId }));
    }
  };

  const showLoader = (loaderData, loaderId) => {
    if (loaderData[loaderId]?.isLoading) {
      return <Spinner />;
    }
    return null;
  };

  const showLabelIcon = (labelIconData, labelIconId) => {
    if (labelIconData[labelIconId] && !labelIconData[labelIconId]?.isLoading) {
      return !labelIconData[labelIconId]?.data?.length;
    }
    return false;
  };

  const getLinkTreeChild = (roleId) =>
    extendedTableData[roleId]?.data?.map(({ name, id }) => (
      <LKTreeView key={id}>
        <LKTreeItem
          nodeId={id.toString()}
          loader={showLoader(extendedTableData, id)}
          labelIcon={showLabelIcon(extendedTableData, id)}
          labelText={name}
          child={
            <div style={{ zIndex: 999 }}>
              <Button
                startIcon={<ListIcon />}
                className={classes.button}
                color="primary"
                variant="outlined"
                onClick={() => navigate(`/usermanagement/lists?role=${name}&id=${id}`)}
              >
                USER LIST
              </Button>
              <Button
                startIcon={<PersonAddIcon />}
                className={classes.button}
                color="primary"
                variant="outlined"
                onClick={() => {
                  setOpen(true);
                  setRoleName(name);
                }}
              >
                Add User
              </Button>
            </div>
          }
          onClick={() => {
            getChildRole(id);
          }}
        >
          <div />
          {extendedTableData[id] ? getLinkTreeChild(id) : null}
        </LKTreeItem>
      </LKTreeView>
    ));

  const headerConfig = [
    {
      name: 'Roles',
      key: 'name',
      formatBody: useCallback(
        ({ id, name }) => (
          <LKTreeView defaultExpanded={[id.toString()]}>
            <LKTreeItem
              loader={showLoader(extendedTableData, id)}
              labelIcon={showLabelIcon(extendedTableData, id)}
              nodeId={id.toString()}
              labelText={name}
              child={
                <div style={{ zIndex: 999 }}>
                  <Button
                    startIcon={<ListIcon />}
                    className={classes.button}
                    color="primary"
                    variant="outlined"
                    onClick={() => navigate(`/usermanagement/lists?role=${name}&id=${id}`)}
                  >
                    USER LIST
                  </Button>
                  <Button
                    startIcon={<PersonAddIcon />}
                    className={classes.button}
                    color="primary"
                    variant="outlined"
                    onClick={() => {
                      setOpen(true);
                      setRoleName(name);
                    }}
                  >
                    Add User
                  </Button>
                </div>
              }
              onClick={() => getChildRole(id)}
            >
              {getLinkTreeChild(id)}
            </LKTreeItem>
          </LKTreeView>
        ),
        [extendedTableData]
      ),

      style: { minWidth: 200, maxWidth: 200, textIndent: 8, paddingLeft: 20 }
    }
  ];

  return (
    <>
      <LkTable
        plainTheme
        tableHeight={tableHeight}
        headerConfig={headerConfig}
        isDataFetching={approverRole?.isLoading}
        tableData={approverRole?.data}
        totalRowsCount={approverRole?.data?.length}
        isNonVertualizedTable
        noDataText="No child roles"
      />
      {open && <AddUserModal open={open} roleName={roleName} handleClose={() => setOpen(false)} />}
    </>
  );
};

export default UserRoleTable;
