import * as action from '../actionTypes/layout';

// layout define
export function layoutDefineLoad(data) {
    return { type: action.LAYOUT_DEFINE_LOAD, payload: data };
}

export function layoutDefineSuccess(data) {
    return { type: action.LAYOUT_DEFINE_SUCCESS, payload: data };
}

export function layoutDefineFail(data) {
    return { type: action.LAYOUT_DEFINE_FAIL, payload: data };
}

export function layoutDefineReset(data) {
    return { type: action.LAYOUT_DEFINE_RESET, payload: data };
}

// layout save
export function layoutSaveLoad(data) {
    return { type: action.LAYOUT_SAVE_LOAD, payload: data };
}

export function layoutSaveSuccess(data) {
    return { type: action.LAYOUT_SAVE_SUCCESS, payload: data };
}

export function layoutSaveFail(data) {
    return { type: action.LAYOUT_SAVE_FAIL, payload: data };
}

export function layoutSaveReset(data) {
    return { type: action.LAYOUT_SAVE_RESET, payload: data };
}

// set layout create data
export function setLayoutCreateData(data) {
    return { type: action.SET_LAYOUT_CREATE_DATA, payload: data };
}

// get layout detail data
export function getLayoutDetailLoad(data) {
    return { type: action.GET_LAYOUT_DEATIL_LOAD, payload: data };
}

export function getLayoutDetailSuccess(data) {
    return { type: action.GET_LAYOUT_DEATIL_SUCCESS, payload: data };
}

export function getLayoutDetailFail(data) {
    return { type: action.GET_LAYOUT_DEATIL_FAIL, payload: data };
}

export function getLayoutDetailReset(data) {
    return { type: action.GET_LAYOUT_DEATIL_RESET, payload: data };
}

// get layout list data
export function getLayoutListLoad(data) {
    return { type: action.GET_LAYOUT_LIST_LOAD, payload: data };
}

export function getLayoutListSuccess(data) {
    return { type: action.GET_LAYOUT_LIST_SUCCESS, payload: data };
}

export function getLayoutListFail(data) {
    return { type: action.GET_LAYOUT_LIST_FAIL, payload: data };
}

export function getLayoutListReset(data) {
    return { type: action.GET_LAYOUT_LIST_RESET, payload: data };
}