import React from 'react';
import { Route, Routes } from 'react-router-dom';

import apiConfig from '../../../config';
import header from './data/header.json';
import response from './data/mockData.json';
import COUNTRY_MOCK_DATA from './data/countryMockData.json';
import Customer from '../CustomerFormsMain';
import CustomerDashboard from '../CustomerDashboard';
import { CUSTOMER as CONTENT } from '../../../../public/localisation/lang_en.json';

const addressTemplate = (address) => {
  if (address?.length === 0) {
    return <>-</>;
  }

  const addressDetails = address[0];
  return `${addressDetails?.addressLine1},${addressDetails?.addressLine2} 
     ${addressDetails?.city} ${addressDetails?.state} ${addressDetails?.country} 
     ${addressDetails?.pincode}`;
};

const fillCustomerForm = () => {
  cy.getByName('name').type('Test Name');
  cy.getByName('billing_addressLine1').type('Test Address Line 1');
  cy.getByName('billing_addressLine2').type('Test Address Line 2');
  cy.getByName('billing_city').type('Test City');
  cy.getByName('billing_pincode').type('123456');
  cy.getByCy('billing_country').click();
  cy.get('[data-value="IN"]').click();
  cy.getByName('billing_state').type('Karnataka');
  cy.getByName('billing_phone').type('1234567890');

  cy.getByName('shipping_addressLine1').type('Test Address Line 1');
  cy.getByName('shipping_addressLine2').type('Test Address Line 2');
  cy.getByName('shipping_city').type('Test City');
  cy.getByName('shipping_pincode').type('123456');
  cy.getByCy('shipping_country').click();
  cy.get('[data-value="IN"]').click();
  cy.getByName('shipping_state').type('Karnataka');
  cy.getByName('shipping_phone').type('1234567891');

  cy.getByName('email').type('<EMAIL>');
  cy.getByName('mobile').type('1234567892');

  cy.getByName('gstin').type('TEST0000GST');
};
describe('Customer Dashboard', () => {
  beforeEach(() => {
    cy.intercept('GET', `${apiConfig.customer.customerListing}**`, {
      statusCode: 200,
      body: {
        data: response.data
      }
    });
    cy.mount(<CustomerDashboard />);
  });

  it('should render correctly', () => {
    cy.get('#globalSearchId').should('have.attr', 'placeholder', 'Search by Customer Name');
    cy.findByRole('button', { name: /CREATE NEW CUSTOMER/i }).should('be.enabled');
    header.map((headerValue, index) => cy.headerTableCell(index, headerValue));

    const { name, code, customerAddressDetails, createdBy, updatedAt } = response.data.content[0];
    const billingAddressFilter = customerAddressDetails?.filter(
      (address) => address.addressType === 'BILLING'
    );
    const ShippingAddressFilter = customerAddressDetails?.filter(
      (address) => address.addressType === 'SHIPPING'
    );
    const billingAddress = addressTemplate(billingAddressFilter);
    const shippingAddress = addressTemplate(ShippingAddressFilter);

    const firstRowData = [name, code, billingAddress, shippingAddress, createdBy, updatedAt];

    firstRowData.forEach((data, index) => {
      if (index === 6 || index === 2 || index === 3) {
        return;
      }
      cy.firstRowTableCell(index, data);
    });
  });

  it('should search by customer name', () => {
    cy.get('#globalSearchId').type('{enter}');
    cy.getByCy('toaster-message').should(
      'have.text',
      CONTENT.SEARCH_BY_CUSTOMER_NAME_CANNOT_BE_EMPTY
    );
    cy.get('#globalSearchId').type('Customer 1{enter}');
    cy.getByCy('Customer Name-Customer 1').should('exist');
    cy.getByCy('Customer Name-Customer 1').findByTestId('CancelIcon').click();
    cy.getByCy('Customer Name-Customer 1').should('not.exist');
  });

  it('should create customer', () => {
    cy.findByRole('button', { name: /CREATE NEW CUSTOMER/i }).click();
    cy.intercept('GET', apiConfig.facility.countryState, {
      statusCode: 200,
      body: COUNTRY_MOCK_DATA
    }).as('countryState');
    cy.intercept('POST', apiConfig.customer.create, {
      statusCode: 200,
      body: {
        data: {
          isSuccess: true
        }
      }
    });
    cy.mount(
      <Routes>
        <Route path="/customer/:action" element={<Customer />} />
      </Routes>,
      {
        routerProps: {
          initialEntries: ['/customer/create']
        },
        state: { login: { user: { userDetail: { empCode: 'testemp123' } } } }
      }
    );

    cy.findByRole('button', { name: CONTENT.CONFIRM }).click();
    cy.getByCy('modal').should('not.exist');
    fillCustomerForm();
    cy.findByRole('button', { name: CONTENT.CONFIRM }).click();

    cy.getByCy('modal').should('exist');
    cy.findByRole('button', { name: CONTENT.CREATE_NEW_CUSTOMER_UPPERCASE }).click();
  });

  it('should disbale customer', () => {
    cy.intercept('PUT', `${apiConfig.customer.update}/**`, {
      statusCode: 200,
      body: {
        data: {
          isSuccess: true
        }
      }
    });
    cy.getByCy('table-cell-0-6').click();
    cy.findByText(/disable/i).click();
  });

  it('should enable customer', () => {
    cy.intercept('PUT', `${apiConfig.customer.update}/**`, {
      statusCode: 200,
      body: {
        data: {
          isSuccess: true
        }
      }
    });
    cy.getByCy('table-cell-1-6').click();
    cy.findByText(/enable/i).click();
  });

  // it('should edit customer', () => {
  //   cy.intercept('GET', apiConfig.facility.countryState, {
  //     statusCode: 200,
  //     body: COUNTRY_MOCK_DATA
  //   }).as('countryState');
  //   cy.getByCy('table-cell-0-6').click();
  //   cy.findByText(/edit customer/i).click();
  //   cy.intercept('GET', `${apiConfig.customer.getCustomer}/**`, {
  //     statusCode: 200,
  //     body: {
  //       data: response.data.content[0]
  //     }
  //   }).as('getCustomer');
  //   cy.intercept('PUT', `${apiConfig.customer.update}/**`, {
  //     statusCode: 200,
  //     body: {
  //       data: {
  //         isSuccess: true
  //       }
  //     }
  //   });
  //   cy.mount(
  //     <Routes>
  //       <Route path="/customer/:action/:id" element={<Customer />} />
  //     </Routes>,
  //     {
  //       routerProps: {
  //         initialEntries: ['/customer/edit/1234']
  //       },
  //       state: { login: { user: { userDetail: { empCode: 'testemp123' } } } }
  //     }
  //   );
  //   cy.wait('@getCustomer');
  //   cy.findByRole('button', { name: CONTENT.CONFIRM }).click();

  //   cy.findByRole('button', { name: CONTENT.SAVE_CHANGES }).click();
  //   cy.mount(<CustomerDashboard />);
  // });

  it('should land on listing page if action is canceled', () => {
    cy.findByRole('button', { name: /CREATE NEW CUSTOMER/i }).click();

    cy.mount(
      <Routes>
        <Route path="/customer/:action" element={<Customer />} />
      </Routes>,
      {
        routerProps: {
          initialEntries: ['/customer/create']
        },
        state: { login: { user: { userDetail: { empCode: 'testemp123' } } } }
      }
    );

    cy.findByRole('button', { name: CONTENT.CANCEL }).click();
    cy.mount(<CustomerDashboard />);
  });
});

describe('Customer onboard show error message', () => {
  it(' when get customer list fails', () => {
    cy.intercept('GET', `${apiConfig.customer.customerListing}**`, {
      statusCode: 500,
      body: {
        message: 'Internal Server Error'
      }
    });
    cy.mount(<CustomerDashboard />);

    cy.getByCy('toaster-message').should('have.text', 'Internal Server Error');
  });

  it('customer craete api fails', () => {
    cy.intercept('POST', `${apiConfig.customer.create}`, {
      statusCode: 500,
      body: {
        message: 'Internal Server Error'
      }
    });
    cy.intercept('GET', apiConfig.facility.countryState, {
      statusCode: 200,
      body: COUNTRY_MOCK_DATA
    }).as('countryState');

    cy.mount(
      <Routes>
        <Route path="/customer/:action" element={<Customer />} />
      </Routes>,
      {
        routerProps: {
          initialEntries: ['/customer/create']
        },
        state: { login: { user: { userDetail: { empCode: 'testemp123' } } } }
      }
    );
    fillCustomerForm();
    cy.findByRole('button', { name: CONTENT.CONFIRM }).click();
    cy.findByRole('button', { name: CONTENT.CREATE_NEW_CUSTOMER_UPPERCASE }).click();

    cy.getByCy('toaster-message').should('have.text', 'Internal Server Error');
  });

  it('customer details api fails', () => {
    cy.intercept('GET', `${apiConfig.customer.getCustomer}/**`, {
      statusCode: 500,
      body: {
        message: 'Internal Server Error'
      }
    }).as('getCustomer');
    cy.mount(
      <Routes>
        <Route path="/customer/:action/:id" element={<Customer />} />
      </Routes>,
      {
        routerProps: {
          initialEntries: ['/customer/edit/1234']
        },
        state: { login: { user: { userDetail: { empCode: 'testemp123' } } } }
      }
    );
    cy.wait('@getCustomer');

    cy.getByCy('toaster-message').should('have.text', 'Internal Server Error');
  });
});
