import React, { useCallback } from 'react'
import GrnTabActions from './GRNTabActions';
import GrnTable from './GrnTable'
import { useDispatch, useSelector } from 'react-redux';
import { exportGrnDetailsLoad } from '../../redux/actionCreators/grnListing';
import useKeyboardShortcut from 'common/useKeyboardShortcut';





const GRNTab = ({ objPayload, handleChange, mainGrnSearch, sortingData, selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters, sortKey, setSortkey }) => {
    const dispatch = useDispatch();
    const { totalGrnRecordsCount } = useSelector(state => state.grnListing);
    const resetFun = () => {
        setSortkey('created_at')
        sortingData.key = 'created_at';
        sortingData.order = 'DESC';
        let tempData = selectedFilterList.filter(({ key }) => !key.startsWith("GRN_"))
        resetFilters(tempData)
    }

    const exportFun = useCallback(() => {
        let tempData = JSON.parse(JSON.stringify(objPayload))
        tempData.payload.pageRequest.pageSize = totalGrnRecordsCount;
        dispatch(exportGrnDetailsLoad(tempData))
    }, [dispatch, totalGrnRecordsCount, objPayload])


    useKeyboardShortcut([18, 69], exportFun);


    return <div >
        <GrnTabActions
            exportFun={exportFun}
            selectedFilterList={selectedFilterList}
            deleteSelectedFilter={deleteSelectedFilter}
            resetFilters={resetFun}
        />
        <GrnTable
            sortingData={sortingData}
            mainGrnSearch={mainGrnSearch}
            handleChange={handleChange}
            selectedFilterList={selectedFilterList}
            onChangeFilterList={onChangeFilterList}
            objPayload={objPayload}
            sortKey={sortKey}
            setSortkey={setSortkey}
        />
    </div>
}

export default React.memo(GRNTab)