import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { makeStyles } from 'tss-react/mui';
import { Box } from '@mui/material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';

import Spinner from 'components/Spinner/Spinner';
import LkToolTip from 'components/ToolTip/ToolTip';
import ProductToolTip from 'components/common/ProductToolTip';

import { getBlockedsPidsLoad } from 'redux/actionCreators/grnDetails';
import useWindowResize from 'common/useWindowResize';
import { StyledTableCell, StyledTableRow } from 'theme/table.theme';

import TableCount from './TableCount';

const styles = makeStyles()(() => ({
  container: {
    borderTop: '1px solid #DDDDDD',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
    maxHeight: 600,
    position: 'relative'
  },
  vendorName: {
    width: 160
  },
  tableCellMaxWidth: {
    width: 180
  }
}));

const obj = {
  page: 0
};
const BloackedProductsTable = ({ keyValue, value, mainGrnSearch, overrideFun, actionValues }) => {
  const { classes } = styles();
  const dispatch = useDispatch();
  const [tableHeight] = useWindowResize(window.innerHeight - 332);

  const { getGrnBlockedPidsLoading, getGrnBlockedPids } = useSelector((state) => state.grnDetails);
  useEffect(() => {
    if (getGrnBlockedPids) {
      const { page_size, total_count } = getGrnBlockedPids?.result || {};
      obj.page_size = page_size;
      obj.total_count = total_count;
      obj.pageCount = Math.floor(total_count / page_size);
    }
  }, [getGrnBlockedPids]);

  const scrollTable = (event) => {
    const tBody = event.target;

    if (tBody.scrollTop >= tBody.scrollHeight - 700) {
      const no_of_pages = 5;
      if (!getGrnBlockedPidsLoading && obj.page < no_of_pages) {
        obj.page += 1;
        const payload = {
          pageNum: obj.page,
          noReset: true
        };
        if (value !== '' && value !== 0) {
          payload.key = mainGrnSearch !== '' ? 'grnCode' : keyValue;
          payload.value = mainGrnSearch !== '' ? mainGrnSearch : value;
        }
        if (mainGrnSearch !== '') {
          payload.key = 'grnCode';
          payload.value = mainGrnSearch;
        }
        if (
          getGrnBlockedPids?.result?.total_count > getGrnBlockedPids?.result?.data?.products.length
        ) {
          dispatch(getBlockedsPidsLoad(payload));
        }
      }
    }
  };
  return (
    <div>
      <Box>
        <TableContainer
          id="tableBody"
          style={{ height: tableHeight, overflowX: 'scroll' }}
          onScroll={(event) => scrollTable(event)}
          className={classes.container}
        >
          <Table stickyHeader aria-label="sticky table" className={classes.table}>
            <TableHead>
              <StyledTableRow>
                <StyledTableCell align="left">Product ID</StyledTableCell>
                <StyledTableCell align="left">GRN</StyledTableCell>
                <StyledTableCell align="left">Created By</StyledTableCell>
                <StyledTableCell align="left">Invoice Qty</StyledTableCell>
                <StyledTableCell align="left">Estim. Qty</StyledTableCell>
                <StyledTableCell align="left">Sampling</StyledTableCell>
                <StyledTableCell align="left">QC Pass</StyledTableCell>
                <StyledTableCell align="left">QC Fail</StyledTableCell>
                <StyledTableCell align="left">Failed Since</StyledTableCell>
                <StyledTableCell className={classes.tableCellMaxWidth} align="right">
                  <Box pr={1}>Take Action</Box>
                </StyledTableCell>
              </StyledTableRow>
            </TableHead>
            <TableBody>
              {
                getGrnBlockedPids?.result?.data?.products.map((data) => {
                  const {
                    grn_code,
                    pid,
                    created_by,
                    invoice_qty,
                    estimated_qty,
                    sampling_pct,
                    qc_pass,
                    qc_fail,
                    failed_since,
                    category_id
                  } = data;
                  const { manual_override_allowed, manual_override_rejected } = actionValues;
                  const testOverRideAllowed = manual_override_allowed.some(
                    (t) => t.grn_code === grn_code && t.pid === pid
                  );
                  const testOverRideRejected = manual_override_rejected.some(
                    (t) => t.grn_code === grn_code && t.pid === pid
                  );
                  return (
                    <React.Fragment key={grn_code}>
                      <StyledTableRow>
                        <StyledTableCell align="left">
                          <LkToolTip
                            title={
                              <ProductToolTip
                                poId={getGrnBlockedPids?.result?.data?.po_id}
                                pid={pid}
                                classification={category_id}
                              />
                            }
                            width={category_id === 11356 ? '300px' : '756px'}
                          >
                            <div className="">{pid}</div>
                          </LkToolTip>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{grn_code}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{created_by}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{invoice_qty}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div>{estimated_qty}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{sampling_pct}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{qc_pass}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{qc_fail}</div>
                        </StyledTableCell>
                        <StyledTableCell align="left">
                          <div className="">{failed_since}</div>
                        </StyledTableCell>
                        <StyledTableCell align="right">
                          <Box pr={1}>
                            <CheckCircleIcon
                              className="mr-r12"
                              color={testOverRideAllowed ? 'primary' : 'disabled'}
                              onClick={() => overrideFun(true, grn_code, pid)}
                            />
                            <CancelIcon
                              color={testOverRideRejected ? 'error' : 'disabled'}
                              onClick={() => overrideFun(false, grn_code, pid)}
                            />
                          </Box>
                        </StyledTableCell>
                      </StyledTableRow>
                    </React.Fragment>
                  );
                })
                //     })
              }
              {!getGrnBlockedPidsLoading && !getGrnBlockedPids?.result?.data.products && (
                <StyledTableRow>
                  <StyledTableCell colSpan={11} align="center">
                    No Data...
                  </StyledTableCell>
                </StyledTableRow>
              )}
            </TableBody>
          </Table>
          {getGrnBlockedPids?.result?.data.products && (
            <TableCount
              loading={getGrnBlockedPidsLoading}
              count={getGrnBlockedPids?.result?.data?.products.length}
              totalCount={getGrnBlockedPids?.result?.total_count}
            />
          )}
        </TableContainer>
        {getGrnBlockedPidsLoading && <Spinner className="display-grid-center mr-t10 mr-b10" />}
      </Box>
    </div>
  );
};

export default React.memo(BloackedProductsTable);
