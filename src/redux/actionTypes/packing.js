export const PACKING_HOLD_MODAL = 'PACKING_HOLD_MODAL';
export const PACKING_UNHOLD_MODAL = 'PACKING_UNHOLD_MODAL';
export const SET_SELECTED_INDEX = 'SET_SELECTED_INDEX';

// Mark Packing Done
export const MARK_PACKING_DONE_LOAD = 'MARK_PACKING_DONE_LOAD';
export const MARK_PACKING_DONE_SUCCESS = 'MARK_PACKING_DONE_SUCCESS';
export const MARK_PACKING_DONE_FAIL = 'MARK_PACKING_DONE_FAIL';
export const MARK_PACKING_DONE_RESET = 'MARK_PACKING_DONE_RESET';

// LSM COURIER AWB
export const LSM_COURIER_AWB_LOAD = 'LSM_COURIER_AWB_LOAD';
export const LSM_COURIER_AWB_SUCCESS = 'LSM_COURIER_AWB_SUCCESS';
export const LSM_COURIER_AWB_FAIL = 'LSM_COURIER_AWB_FAIL';





// Print Packing INVOICE
export const PRINT_PACKING_INVOICE_LOAD = 'PRINT_PACKING_INVOICE_LOAD';
export const PRINT_PACKING_INVOICE_SUCCESS = 'PRINT_PACKING_INVOICE_SUCCESS';
export const PRINT_PACKING_INVOICE_RESET = 'PRINT_PACKING_INVOICE_RESET';

// Print Packing INVOICE
export const IS_BARCODE_VALID_LOAD = 'IS_BARCODE_VALID_LOAD';
export const IS_BARCODE_VALID_SUCCESS = 'IS_BARCODE_VALID_SUCCESS';
export const IS_BARCODE_VALID_FAIL = 'IS_BARCODE_VALID_FAIL';
export const IS_BARCODE_VALID_RESET = 'IS_BARCODE_VALID_RESET';
