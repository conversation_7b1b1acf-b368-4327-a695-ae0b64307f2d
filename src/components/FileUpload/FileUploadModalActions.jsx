import React from 'react';
import classNames from 'clsx';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

const csvMimeTypes = [
  '.csv',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel'
].join(', ');

const FileUploadModalActions = ({
  closeModal,
  uploadCsvPass,
  selectedFile,
  handleSubmit,
  importLoading,
  selectFile,
  checkedItems,
  checkedItemLabel,
  errorInImport,
  disableImport,
  CONTENT
}) => {
  if (uploadCsvPass) {
    return null;
  }
  if (errorInImport) {
    return (
      <Button
        data-cy="ok_button"
        style={{ borderRadius: 8 }}
        variant="contained"
        color="primary"
        onClick={closeModal}
      >
        {CONTENT.OK}
      </Button>
    );
  }
  if (selectedFile) {
    return (
      <Box className="display-flex justify-content-space-between pd-12">
        <Box className="mr-r5">
          <Button
            style={{ borderRadius: 8 }}
            variant="outlined"
            color="primary"
            onClick={closeModal}
            data-cy="cancel-fileupload-modal"
          >
            {CONTENT.CANCEL}
          </Button>
        </Box>
        <Box className="mr-l5">
          <Button
            style={{ borderRadius: 8 }}
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={importLoading || disableImport}
          >
            {CONTENT.IMPORT}
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="display-flex justify-content-space-between align-items-center pd-12">
      <Box className="mr-r5">
        <Button style={{ borderRadius: 8 }} variant="outlined" color="primary" onClick={closeModal}>
          {CONTENT.CANCEL}
        </Button>
      </Box>
      <Box className="mr-l5">
        <Box className="file-upload-container">
          <input
            id="upload-items-file"
            type="file"
            onChange={selectFile}
            className="display-none"
            accept={csvMimeTypes}
          />
          <label
            className={classNames(
              'bg-primary',
              'border-radius-base',
              'pd-t8',
              'pd-b8',
              'pd-l20',
              'pd-r20',
              'cursor-pointer',
              'fs18',
              'text-white',
              {
                'op-5': !checkedItems && checkedItemLabel,
                'pointer-events': !checkedItems && checkedItemLabel
              }
            )}
            htmlFor="upload-items-file"
            data-cy="upload-file"
          >
            {CONTENT.UPLOAD_FILE_UPPERCASE}
          </label>
        </Box>
      </Box>
    </Box>
  );
};

export default FileUploadModalActions;
