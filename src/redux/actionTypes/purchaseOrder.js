export const PURCHAE_ORDER_LIST_LOAD = 'PURCHAE_ORDER_LIST_LOAD';
export const PURCHAE_ORDER_LIST_SUCCESS = 'PURCHAE_ORDER_LIST_SUCCESS';
export const PURCHAE_ORDER_LIST_FAIL = 'PURCHAE_ORDER_LIST_FAIL';
export const PURCHAE_ORDER_LIST_RESET = 'PURCHAE_ORDER_LIST_RESET';

// purcahse order detail
export const PURCHASE_ORDER_DETAIL_LOAD = 'PURCHASE_ORDER_DETAIL_LOAD';
export const PURCHASE_ORDER_DETAIL_SUCCESS = 'PURCHASE_ORDER_DETAIL_SUCCESS';
export const PURCHASE_ORDER_DETAIL_FAIL = 'PURCHASE_ORDER_DETAIL_FAIL';
export const PURCHASE_ORDER_DETAIL_RESET = 'PURCHASE_ORDER_DETAIL_RESET'

// upload item
export const UPLOAD_ITEM_LOAD = 'UPLOAD_ITEM_LOAD';
export const UPLOAD_ITEM_SUCCESS = 'UPLOAD_ITEM_SUCCESS';
export const UPLOAD_ITEM_FAIL = 'UPLOAD_ITEM_FAIL';
export const UPLOAD_ITEM_PERCENT = 'UPLOAD_ITEM_PERCENT';

// get upload item error report
export const GET_UPLOAD_ITEM_ERROR_LOAD = 'GET_UPLOAD_ITEM_ERROR_LOAD';
export const GET_UPLOAD_ITEM_ERROR_SUCCESS = 'GET_UPLOAD_ITEM_ERROR_SUCCESS';
export const GET_UPLOAD_ITEM_ERROR_FAIL = 'GET_UPLOAD_ITEM_ERROR_FAIL';

// Reset upload csv
export const RESET_UPLOAD_CSV = 'RESET_UPLOAD_CSV';

// submit purchase order
export const SUBMIT_PURCHASE_ORDER_LOAD = 'SUBMIT_PURCHASE_ORDER_LOAD';
export const SUBMIT_PURCHASE_ORDER_SUCCESS = 'SUBMIT_PURCHASE_ORDER_SUCCESS';
export const SUBMIT_PURCHASE_ORDER_FAIL = 'SUBMIT_PURCHASE_ORDER_FAIL';
export const SUBMIT_PURCHASE_ORDER_RESET = 'SUBMIT_PURCHASE_ORDER_RESET';

// generate Purchase order Number
export const GENERATE_PURCHASE_ORDER_NUMBER_LOAD = 'GENERATE_PURCHASE_ORDER_NUMBER_LOAD';
export const GENERATE_PURCHASE_ORDER_NUMBER_SUCCESS = 'GENERATE_PURCHASE_ORDER_NUMBER_SUCCESS';
export const GENERATE_PURCHASE_ORDER_NUMBER_FAIL = 'GENERATE_PURCHASE_ORDER_NUMBER_FAIL';

// submit approval
export const SUBMIT_APPROVAL_LOAD = 'SUBMIT_APPROVAL_LOAD';
export const SUBMIT_APPROVAL_SUCCESS = 'SUBMIT_APPROVAL_SUCCESS';
export const SUBMIT_APPROVAL_FAIL = 'SUBMIT_APPROVAL_FAIL';
export const SUBMIT_APPROVAL_RESET = 'SUBMIT_APPROVAL_RESET';

// amend po
export const AMEND_PO_LOAD = 'AMEND_PO_LOAD';
export const AMEND_PO_SUCCESS = 'AMEND_PO_SUCCESS';
export const AMEND_PO_FAIL = 'AMEND_PO_FAIL';
export const AMEND_PO_RESET = 'AMEND_PO_RESET';
export const AMEND_PO_DRAFT = 'AMEND_PO_DRAFT';
export const AMEND_DRAFT_RESET = 'AMEND_DRAFT_RESET';

// hold po
export const HOLD_PO_LOAD = 'HOLD_PO_LOAD';
export const HOLD_PO_SUCCESS = 'HOLD_PO_SUCCESS';
export const HOLD_PO_FAIL = 'HOLD_PO_FAIL';
export const HOLD_PO_RESET = 'HOLD_PO_RESET';

// EDIT PO
export const EDIT_PO_SET = 'EDIT_PO_SET';
export const RESET_CREATE_PO = 'RESET_CREATE_PO';

// unhold po
export const UNHOLD_PO_LOAD = 'UNHOLD_PO_LOAD';
export const UNHOLD_PO_SUCCESS = 'UNHOLD_PO_SUCCESS';
export const UNHOLD_PO_FAIL = 'UNHOLD_PO_FAIL';
export const UNHOLD_PO_RESET = 'UNHOLD_PO_RESET';

// close po
export const CLOSE_PO_LOAD = 'CLOSE_PO_LOAD';
export const CLOSE_PO_SUCCESS = 'CLOSE_PO_SUCCESS';
export const CLOSE_PO_FAIL = 'CLOSE_PO_FAIL';
export const CLOSE_PO_RESET = 'CLOSE_PO_RESET';

// create po
export const CREATE_PO_LOAD = 'CREATE_PO_LOAD';
export const CREATE_PO_SUCCESS = 'CREATE_PO_SUCCESS';
export const CREATE_PO_FAIL = 'CREATE_PO_FAIL';

// get pid details
export const GET_PID_DETAILS_LOAD = 'GET_PID_DETAILS_LOAD';
export const GET_PID_DETAILS_SUCCESS = 'GET_PID_DETAILS_SUCCESS';
export const GET_PID_DETAILS_FAIL = 'GET_PID_DETAILS_FAIL';

// export po
export const EXPORT_PO_LOAD = 'EXPORT_PO_LOAD';
export const EXPORT_PO_SUCCESS = 'EXPORT_PO_SUCCESS';
export const EXPORT_PO_FAIL = 'EXPORT_PO_FAIL';
export const EXPORT_PO_RESET = 'EXPORT_PO_RESET';


// processing status po
export const PROCESSING_STATUS_LOAD = 'PROCESSING_STATUS_LOAD';
export const PROCESSING_STATUS_SUCCESS = 'PROCESSING_STATUS_SUCCESS';
export const PROCESSING_STATUS_FAIL = 'PROCESSING_STATUS_FAIL';
export const PROCESSING_STATUS_RESET = 'PROCESSING_STATUS_RESET';


// MAKING PO APPROVAL
export const MARK_PO_APPROVE_LOAD = 'MARK_PO_APPROVE_LOAD';
export const MARK_PO_APPROVE_SUCCESS = 'MARK_PO_APPROVE_SUCCESS';
export const MARK_PO_APPROVE_FAIL = 'MARK_PO_APPROVE_FAIL';
export const MARK_PO_APPROVE_RESET = 'MARK_PO_APPROVE_RESET';
