import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import LkModal from 'components/Modal/Modal';

import { LOCALISATION } from 'redux/reducers/localisation';
import { createCustomerReset, CUSTOMER, updateCustomerReset } from 'redux/reducers/customer';
import { mergeContentValues } from 'utils/helpers';

const ConfirmationModal = ({ isEdit, open, name, handleClose, handleSubmit, loading }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const isSuccess = useSelector((state) => state[CUSTOMER].isCreatedOrUpdated.isSuccess);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.CUSTOMER);

  useEffect(() => {
    if (isSuccess) {
      handleClose();
      dispatch(createCustomerReset());
      dispatch(updateCustomerReset());
      navigate('/customer/list');
    }
  }, [isSuccess]);

  return (
    <LkModal
      open={open}
      handleClose={handleClose}
      modalWidth={520}
      title={isEdit ? CONTENT.EDIT_CUSTOMER_DETAILS : `${CONTENT.CREATE_NEW_CUSTOMER}?`}
      subTitle={
        isEdit
          ? mergeContentValues(CONTENT.PLEASE_CONFIRM_IF_YOU_WANT_TO_UPDATE_CUSTOMER, name)
          : CONTENT.PLEASE_CONFIRM_IF_YOU_WANT_TO_CREATE_CUSTOMER
      }
      primaryBtnText={isEdit ? CONTENT.SAVE_CHANGES : CONTENT.CREATE_NEW_CUSTOMER_UPPERCASE}
      primaryBtnLoading={loading}
      secondaryBtnText={CONTENT.GO_BACK}
      primaryBtn={handleSubmit}
      secondaryBtn={handleClose}
    />
  );
};

export default ConfirmationModal;
