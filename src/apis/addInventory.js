// import { tokenInstance } from '../utils/axiosInstance';
// import config from '../config';

import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getAddInventoryApi = (payload) =>
  tokenInstance().post(config.addInventory.list, payload);

export const uploadAddInventoryApi = (payload) =>
  tokenInstance().post(config.addInventory.bulkUpload, payload);

export const addBarcodeToInventoryApi = (payload) =>
  tokenInstance().post(config.addInventory.singleUpload, payload);
