import React from 'react';
import Snackbar from '@mui/material/Snackbar';
import MuiAlert from '@mui/material/Alert';
import { makeStyles } from 'tss-react/mui';

import { useSelector, useDispatch } from 'react-redux';
import { toastOpen } from '../../redux/actionCreators/toast';

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  paper: {
    maxWidth: '700px',
    wordBreak: 'break-word'
  },
  filledSuccess: {
    backgroundColor: '#45C476',
    fontWeight: 'normal'
  },
  filledError: {
    backgroundColor: '#DF3747',
    fontWeight: 'normal'
  }
}));

const Toast = () => {
  const { isToastOpen, heading, subHeading, severity, autoHideDuration } = useSelector(
    (state) => state.toast
  );
  const dispatch = useDispatch();
  // severity: error,warning,info,success
  const { classes, cx } = useStyles();

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    dispatch(
      toastOpen({ isToastOpen: false, heading: '', subHeading: '', autoHideDuration: 3000 })
    );
  };

  return (
    <div className={classes.root}>
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        onClose={handleClose}
        open={isToastOpen}
        autoHideDuration={autoHideDuration}
      >
        <MuiAlert
          data-cy="toast"
          elevation={6}
          variant="filled"
          className={classes.paper}
          severity={severity || 'success'}
        >
          {heading && (
            <div data-cy="toaster-message" className="text-b1 text-white">
              {heading}
            </div>
          )}
          {subHeading && (
            <div
              data-cy="toaster-subheadeing"
              className={cx('text-b3 text-white', { 'mr-t10': heading })}
            >
              {subHeading}
            </div>
          )}
        </MuiAlert>
      </Snackbar>
    </div>
  );
};

export default Toast;
