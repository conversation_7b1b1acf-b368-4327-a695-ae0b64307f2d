import { Box, MenuItem, Button } from '@mui/material';
import React from 'react';
import LkModal from 'components/Modal/Modal';
import LkSelect from 'components/MaterialUi/LkSelectBox';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';

const GRNPrintConfirmModal = ({
  open,
  handleClose,
  printFun,
  printOptions,
  handleSelect,
  printValue
}) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.PRINT_CONFIRM_MODAL);

  return (
    <LkModal
      open={open}
      handleClose={handleClose}
      title={CONTENT.PRINT_OPTIONS}
      subTitle={CONTENT.SELECT_FROM_OPTIONS_BELOW}
      modalWidth="520px"
      modalHeight="334px"
      showActionButton={false}
    >
      <Box width="270px" margin="0 auto" mt={6}>
        <LkSelect
          style={{ height: 40 }}
          label={false}
          value={printValue}
          data-cy="select-print-options"
          fullWidth
          onChange={handleSelect}
        >
          <MenuItem value={0}>{CONTENT.SELECT_PRINT_OPTIONS}</MenuItem>;
          {printOptions.map((print) => {
            const { key, value } = print;
            return (
              <MenuItem value={key} key={key} data-cy={`${value}`}>
                {value}
              </MenuItem>
            );
          })}
        </LkSelect>
      </Box>
      <div className="display-flex justify-content-center mr-b40 mr-t40">
        <Button
          onClick={() => printFun()}
          disabled={!printValue}
          style={{ borderRadius: 8 }}
          data-cy="confirm-print-btn"
          variant="contained"
          color="primary"
        >
          {CONTENT.PRINT}
        </Button>
      </div>
    </LkModal>
  );
};

export default GRNPrintConfirmModal;
