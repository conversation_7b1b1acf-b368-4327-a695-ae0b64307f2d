import { required } from 'components/Validations/validator';

export const ValidationRules = [
  {
    field: 'name',
    method: required,
    message: 'Please provide valid customer name.',
    pattern: '^[a-zA-Z0-9 _-]+$'
  },
  {
    field: 'billing_addressLine1',
    method: required,
    message: 'Please provide address 1.'
  },
  {
    field: 'billing_city',
    method: required,
    message: 'Please provide city.'
  },
  {
    field: 'billing_pincode',
    method: required,
    message: 'Please provide pincode.'
  },
  {
    field: 'billing_country',
    method: required,
    message: 'Please select country.'
  },
  {
    field: 'billing_state',
    method: required,
    message: 'Please provide state.'
  },
  {
    field: 'billing_phone',
    method: required,
    message: 'Please provide contact number.'
  },
  {
    field: 'shipping_addressLine1',
    method: required,
    message: 'Please provide address 1.'
  },
  {
    field: 'shipping_city',
    method: required,
    message: 'Please provide city.'
  },
  {
    field: 'shipping_pincode',
    method: required,
    message: 'Please provide pincode.'
  },
  {
    field: 'shipping_country',
    method: required,
    message: 'Please select country.'
  },
  {
    field: 'shipping_state',
    method: required,
    message: 'Please provide state.'
  },
  {
    field: 'shipping_phone',
    method: required,
    message: 'Please provide contact number.'
  },
  {
    field: 'email',
    method: required,
    message: 'Please provide email.'
  },
  {
    field: 'mobile',
    method: required,
    message: 'Please provide Contact number.'
  }
];
