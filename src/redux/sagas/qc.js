import { call, put, all, takeLatest } from 'redux-saga/effects';

import {
  getQCHoldReasons,
  markDamagedApi,
  markQCDone,
  fittingByStationCodeApi,
  markFullFillableApi
} from 'apis/qc';
import {
  getQCHoldReasonsSuccess,
  getQCHoldReasonsFail,
  markQcDoneSuccess,
  markQcDoneFail,
  markDamagedSuccess,
  markDamagedFail,
  markDamagedReset,
  fittingByStationCodeSuccess,
  fittingByStationCodeFail
} from 'redux/actionCreators/qc';
import { showLoader, hideLoader } from 'redux/actionCreators/loader';
import { newRelicNoticeError } from 'utils/newRelicHelper';
import { NEWRELIC_TITLE } from 'utils/newRelicTitles';
import {
  QC_HOLD_REASONS_LOAD,
  MARK_QC_DONE_LOAD,
  MARK_PRODUCT_DAMAGED_REQUEST,
  FITTING_BY_STATION_CODE_LOAD,
  MARK_FULL_FILLABLE_LOAD
} from 'redux/actionTypes/qc';
import { showToaster } from './toast';
import { getItemsOrderListSaga } from './orderDetails';

function getQCStatus(data = []) {
  // Check for QCUnhold first
  if (data.some((item) => item.status === 'QCUnhold')) {
    return 'QCUnhold';
  }
  // Then check for QCFailed
  if (data.some((item) => item.status === 'QCFailed')) {
    return 'QCFailed';
  }
  // Finally check for QCHold
  if (data.some((item) => item.status === 'QCHold')) {
    return 'QCHold';
  }
  // If none of the above statuses are found
  return 'QCPass';
}

function* fetchQCHoldReasons({ id }) {
  try {
    yield put(showLoader());
    const data = yield call(getQCHoldReasons, id);
    yield put(getQCHoldReasonsSuccess({ data, id }));
    yield put(hideLoader());
  } catch (er) {
    yield put(hideLoader());
    yield put(getQCHoldReasonsFail(er));
  }
}

function* markQcDone(action = { payload: {} }) {
  try {
    const qc = action.payload?.qcData;
    if (qc) {
      qc.forEach((q, index) => {
        qc[index] = { ...q };
        qc[index].orderItemId = qc[index].uwItemId;
      });
    }
    yield put(showLoader());
    const data = yield call(markQCDone, { ...action.payload, qcData: qc });
    yield put(hideLoader());
    yield put(markQcDoneSuccess({ data, qcData: qc }));
  } catch (er) {
    const qcData = action.payload?.qcData;
    const qcUpdateStatus = getQCStatus(qcData);
    const customAttributes = {
      apiPayload: JSON.stringify(er?.config?.data),
      shippingPackageId: qcData?.[0]?.shippingPackageId || '',
      qcUpdateStatus
    };
    newRelicNoticeError(NEWRELIC_TITLE.MARK_QC_DONE, er, customAttributes);
    yield put(hideLoader());
    yield put(markQcDoneFail(er));
    yield showToaster(er);
  }
}

function* markProductDamaged(action = { payload: {} }) {
  const { orderItemId, barcode, shipmentId } = action.payload;
  try {
    yield call(markDamagedApi, { orderItemId });
    yield put(markDamagedSuccess(true));
    yield showToaster(null, 'success', `Barcode (${barcode}) has been stocked out`);
    yield put(markDamagedReset());
    yield call(getItemsOrderListSaga, { payload: { id: orderItemId, shipmentId } });
  } catch (error) {
    yield put(markDamagedFail());
    yield showToaster(error);
  }
}

function* fittingByStationCode(action = { payload: {} }) {
  try {
    const { data } = yield call(fittingByStationCodeApi, action.payload);
    yield put(fittingByStationCodeSuccess(data));
  } catch (error) {
    yield showToaster(error);
    yield put(fittingByStationCodeFail());
  }
}
function* markFullFillableSaga(action = { payload: {} }) {
  const { orderItemId, shipmentId, updatedBy, isFullfillable, incrementId } = action.payload;
  yield put(showLoader());
  try {
    yield call(markFullFillableApi, { orderItemId, updatedBy, isFullfillable });
    yield call(getItemsOrderListSaga, { payload: { id: incrementId, shipmentId } });
    yield put(hideLoader());
  } catch (error) {
    yield put(hideLoader());
    yield showToaster(error);
  }
}

export function* runQCCalls() {
  yield all([
    takeLatest(QC_HOLD_REASONS_LOAD, fetchQCHoldReasons),
    takeLatest(MARK_QC_DONE_LOAD, markQcDone),
    takeLatest(MARK_PRODUCT_DAMAGED_REQUEST, markProductDamaged),
    takeLatest(FITTING_BY_STATION_CODE_LOAD, fittingByStationCode),
    takeLatest(MARK_FULL_FILLABLE_LOAD, markFullFillableSaga)
  ]);
}
