import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const d365ListApi = (payload) => tokenInstance().post(config.d365.d365List, payload);

export const d365DetailsApi = (payload) => tokenInstance().post(config.d365.d365Details, payload);

export const retryd365DetailsApi = ({ payload, entityType, ismarkSuccess }) => {
  let url = `${config.d365.retry}${entityType}`;
  if (ismarkSuccess) {
    url = `${config.d365.retry}${entityType}?markSuccess=true`;
  }
  return tokenInstance().post(url, payload);
};
