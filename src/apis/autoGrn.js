import { tokenInstance } from 'utils/axiosInstance';
import config from '../config';

export const getInvoiceDetailsApi = (payload) => {
  const { userName, ...restPayload } = payload;
  return tokenInstance({ userName }).get(config.autoGrn.invoiceDetails, {
    params: restPayload
  });
};

export const addBarcodeApi = (payload) => {
  const { userName, ...restPayload } = payload;
  return tokenInstance({ userName }).post(config.autoGrn.addBarcode, restPayload);
};

export const scannedBarcodesApi = (payload) => {
  const { userName, invoiceNumber } = payload;
  return tokenInstance({ userName }).get(config.autoGrn.scannedBarcodes, {
    params: { invoiceNumber }
  });
};

export const grnSummaryApi = (payload) => {
  const { userName, invoiceNumber } = payload;
  return tokenInstance({ userName }).get(config.autoGrn.grnSummary, { params: { invoiceNumber } });
};

export const getGrnDetailApi = (payload) => {
  const { userName, grnNumber } = payload;
  return tokenInstance({ userName }).get(config.autoGrn.getGrnDetails, { params: grnNumber });
};

export const wareHouseRecieveApi = (payload) => {
  const { userName, ...restPayload } = payload;
  return tokenInstance({ userName }).put(config.autoGrn.wareHouseRecieve, restPayload);
};
