import * as action from '../actionTypes/constraint'


//constraint list 

export function getConstraintListLoad(data) {
    return {
        type: action.CONSTRAINT_LIST_LOAD,
        payload: data
    };
}

export function getConstraintListSuccess(data) {
    return {
        type: action.CONSTRAINT_LIST_SUCCESS,
        payload: data
    };
}

export function getConstraintListFail(data) {
    return {
        type: action.CONSTRAINT_LIST_FAIL,
        payload: data
    };
}


export function getConstraintListReset(data) {
    return {
        type: action.CONSTRAINT_LIST_RESET,
        payload: data
    };
}


//Constriant Save

export function saveConstraintLoad(data) {
    return {
        type: action.SAVE_CONSTRAINT_LOAD,
        payload: data
    };
}

export function saveConstraintSuccess(data) {
    return {
        type: action.SAVE_CONSTRAINT_SUCCESS,
        payload: data
    };
}

export function saveConstraintFail(data) {
    return {
        type: action.SAVE_CONSTRAINT_FAIL,
        payload: data
    };
}


export function saveConstraintReset(data) {
    return {
        type: action.SAVE_CONSTRAINT_RESET,
        payload: data
    };
}

//validate condition

export function validateConditionLoad(data) {
    return { type: action.VALIDATE_CONDITION_LOAD, payload: data };
}

export function validateConditionSuccess(data) {
    return { type: action.VALIDATE_CONDITION_SUCCESS, payload: data };
}

export function validateConditionFail(data) {
    return { type: action.VALIDATE_CONDITION_FAIL, payload: data };
}

export function validateConditionReset(data) {
    return { type: action.VALIDATE_CONDITION_RESET, payload: data };
}

//validate consequnce

export function validateConsequenceLoad(data) {
    return { type: action.VALIDATE_CONSEQUENCE_LOAD, payload: data };
}

export function validateConsequenceSuccess(data) {
    return { type: action.VALIDATE_CONSEQUENCE_SUCCESS, payload: data };
}

export function validateConsequenceFail(data) {
    return { type: action.VALIDATE_CONSEQUENCE_FAIL, payload: data };
}

export function validateConsequenceReset(data) {
    return { type: action.VALIDATE_CONSEQUENCE_FAIL, payload: data };
}

//meta Data

export function cmsMetaDataLoad(data) {
    return { type: action.META_DATA_LOAD, payload: data };
}

export function cmsMetaDataSuccess(data) {
    return { type: action.META_DATA_SUCCESS, payload: data };
}

export function cmsMetaDataFail(data) {
    return { type: action.META_DATA_FAIL, payload: data };
}

export function cmsMetaDataReset(data) {
    return { type: action.META_DATA_RESET, payload: data };
}

//facilityAutoComplete

export function facilityAutoCompleteLoad(data) {
    return { type: action.FACILITY_AUTOCOMPLETE_LOAD, payload: data };
}

export function facilityAutoCompleteSuccess(data) {
    return { type: action.FACILITY_AUTOCOMPLETE_SUCCESS, payload: data };
}

export function facilityAutoCompleteFail(data) {
    return { type: action.FACILITY_AUTOCOMPLETE_FAIL, payload: data };
}

export function facilityAutoCompleteReset(data) {
    return { type: action.FACILITY_AUTOCOMPLETE_RESET, payload: data };
}
