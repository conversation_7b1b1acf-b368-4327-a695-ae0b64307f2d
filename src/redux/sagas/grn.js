import { call, put, all, takeLatest } from 'redux-saga/effects';
import { validateB<PERSON><PERSON><PERSON> } from 'apis/bulkInventory/bulkInventory';
import { getPIDdetailsApi } from 'apis/inventory';
import {
  getGrnList,
  createGrn,
  grnBoxCodeApi,
  qcDetail<PERSON>pi,
  getInvoicesByVendorId,
  getInvoiceDetail,
  getPidList,
  getPidDetails,
  addPid,
  getBoxItems,
  convertPid,
  getSamplingQty,
  updateGrnItemApi,
  closeGrnApi,
  getSearchByDescApi,
  getPidQcSampling,
  getEditItem,
  deleteItem,
  updateQty,
  getGrnSummary,
  getExpiryDetails,
  productMismatchApi,
  getAllGrnForAnInvoiceApi,
  isGrnOpenApi,
  getProductDetailsApi,
  closeAllOpenGrnApi
} from '../../apis/grn';
import {
  grnListFail,
  grnListSuccess,
  grnCreateSuccess,
  grnCreateFail,
  createBoxCodeStatusFail,
  createBoxCodeStatusSuccess,
  createQcDetailStatusFail,
  createQcDetailStatusSuccess,
  getInvoicesSuccess,
  getInvoicesFail,
  getInvoiceDetailSuccess,
  getInvoiceDetailFail,
  getPidListSuccess,
  getPidListFail,
  getPidDetailSuccess,
  getPidDetailFail,
  addPidSuccess,
  addPidFail,
  convertPidSuccess,
  convertPidFail,
  getSamplingQtySuccess,
  getSamplingQtyFail,
  getPidQcSamplingSuccess,
  getPidQcSamplingFail,
  getEditItemSuccess,
  getEditItemFail,
  deleteItemSuccess,
  deleteItemFail,
  updateQtySuccess,
  updateQtyFail,
  setGrnMeta,
  getGrnSummarySuccess,
  grnCloseSuccess,
  grnCloseFail,
  getGrnSummaryFail,
  getExpiryDetailSuccess,
  getExpiryDetailFail,
  getQcDataSuccess,
  getQcDataFail,
  productMismatchFail,
  productMismatchSuccess,
  updateGrnItemSuccess,
  updateGrnItemFail,
  getAllGrnForAnInvoiceSuccess,
  getAllGrnForAnInvoiceFail,
  searchByDescSuccess,
  searchByDescFail,
  searchByDescPrductsSuccess,
  searchByDescPrductsFail,
  isGrnOpenSuccess,
  isGrnOpenFail,
  closeAllOpenGRNReset
} from '../actionCreators/grn';

import {
  GRN_LIST_LOAD,
  GRN_CREATE_LOAD,
  BOX_CODE_LOAD,
  QC_DETAIL_LOAD,
  GET_INVOICES_BY_VENDOR_ID_LOAD,
  GET_INVOICE_DETAIL_LOAD,
  GET_PID_LIST_LOAD,
  GET_PID_DETAIL_LOAD,
  ADD_PID_LOAD,
  CONVERT_PID_LOAD,
  GET_SAMPLING_QTY_LOAD,
  UPDATE_GRN_ITEM_LOAD,
  GET_PID_QC_SAMPLING_LOAD,
  GET_ITEM_EDIT_LOAD,
  GET_ITEM_DELETE_LOAD,
  GRN_CLOSE_LOAD,
  UPDATE_QTY_LOAD,
  GET_GRN_SUMMARY_LOAD,
  GET_QC_DATA_LOAD,
  GET_EXPIRY_DETAILS_LOAD,
  PRODUCT_MISMATCH_LOAD,
  GET_GRN_DETAILS_FOR_AN_INVOICE_LOAD,
  SEARCH_BY_DESC_LOAD,
  SEARCH_BY_DESC_PRODUCTS_LOAD,
  IS_GRN_OPEN_LOAD,
  CLOSE_ALL_OPEN_GRN_LOAD
} from '../actionTypes/grn';
import { extractApiErrorMessage } from '../../utils/helpers';
import { showToaster } from './toast';

export function* createBoxCodeSaga(action = { payload: {} }) {
  try {
    yield call(validateBoxApi, action.payload.barcode);
    const { response, error } = yield call(grnBoxCodeApi, action.payload);
    if (response) {
      yield put(createBoxCodeStatusSuccess(action.payload));
    } else {
      yield put(createBoxCodeStatusFail(error.response.data));
    }
  } catch (error) {
    yield showToaster(error);
  }
}

export function* createQcDetailSaga(action = { payload: {} }) {
  const { response, error } = yield call(qcDetailApi, action.payload);
  if (response) {
    yield put(createQcDetailStatusSuccess(response.data));
    if (response.data.result.meta) {
      yield put(setGrnMeta(response.data.result.meta));
    }
  } else {
    yield put(createQcDetailStatusFail(error.response));
  }
}

function* getGrnListSaga(action = { payload: {} }) {
  try {
    const data = yield call(getGrnList, action.payload);
    yield put(grnListSuccess(data));
  } catch (er) {
    yield put(grnListFail(er));
  }
}

// eslint-disable-next-line consistent-return
function* createGrnSaga(action = { payload: {} }) {
  try {
    const { req, selectedGrn } = action.payload;
    if (selectedGrn) {
      return yield put(grnCreateSuccess(selectedGrn));
    }
    const data = yield call(createGrn, req);
    if (selectedGrn) {
      yield put(grnCreateSuccess(selectedGrn));
    } else {
      yield put(grnCreateSuccess(data.result.data));
    }
  } catch (er) {
    yield put(grnCreateFail(er));
  }
}

function* getInvoicesByVendorIdSaga(action = { payload: {} }) {
  try {
    const data = yield call(getInvoicesByVendorId, action.payload);
    yield put(getInvoicesSuccess(data.data));
  } catch (er) {
    yield put(getInvoicesFail(er));
  }
}

function* getInvoiceDetailSaga(action = { payload: {} }) {
  try {
    const data = yield call(getInvoiceDetail, action.payload);
    yield put(getInvoiceDetailSuccess(data));
  } catch (er) {
    yield put(getInvoiceDetailFail(er));
  }
}

function* getPidListSaga(action = { payload: {} }) {
  try {
    const data = yield call(getPidList, action.payload);
    yield put(getPidListSuccess(data));
    yield put(setGrnMeta(data.result.meta));
  } catch (er) {
    yield put(getPidListFail(er));
  }
}

function* getBoxItemsDataSaga(action = { payload: {} }) {
  const { response, error } = yield call(getBoxItems, action.payload);
  if (response) {
    yield put(getQcDataSuccess(response.data.result));
  } else {
    yield put(getQcDataFail(error));
  }
}

function* getPidDetailSaga(action = { payload: {} }) {
  try {
    const data = yield call(getPidDetails, action.payload);
    yield put(getPidDetailSuccess(data));
  } catch (er) {
    yield put(getPidDetailFail(er));
  }
}
// QC Sampling
function* getPidQcSamplingSaga(action = { payload: {} }) {
  try {
    const data = yield call(getPidQcSampling, action.payload);
    yield put(getPidQcSamplingSuccess(data));
  } catch (er) {
    yield put(getPidQcSamplingFail(er));
  }
}
// Item Edit
function* getEditItemSaga(action = { payload: {} }) {
  const { response, error } = yield call(getEditItem, action.payload);
  if (response) {
    yield put(getEditItemSuccess(response.data.result.data));
  } else {
    yield showToaster(error);
    yield put(getEditItemFail(error.response.data));
  }
}
// Item Delete
function* deleteItemSaga(action = { payload: {} }) {
  try {
    const data = yield call(deleteItem, action.payload);
    yield put(deleteItemSuccess(data));
  } catch (error) {
    yield put(deleteItemFail(error.response.data));
  }
}

function* addPidSaga(action = { payload: {} }) {
  try {
    const data = yield call(addPid, action.payload);
    yield put(addPidSuccess(data));
    if (data.result.meta) {
      yield put(setGrnMeta(data.result.meta));
    }
  } catch (er) {
    yield put(addPidFail(er));
  }
}

function* convertPidSaga(action = { payload: {} }) {
  try {
    const data = yield call(convertPid, action.payload);
    yield put(convertPidSuccess(data));
  } catch (er) {
    yield put(convertPidFail(er));
  }
}

function* getSamplingQtySaga(action = { payload: {} }) {
  try {
    const data = yield call(getSamplingQty, action.payload);
    yield put(getSamplingQtySuccess(data));
  } catch (er) {
    yield put(getSamplingQtyFail(er));
  }
}

function* updateQtySaga(action = { payload: {} }) {
  try {
    yield call(updateQty, action.payload);
    const { estimated_qty } = action.payload;
    yield put(updateQtySuccess(estimated_qty));
  } catch (er) {
    yield put(updateQtyFail(er));
  }
}

function* getGrnSummarySaga(action = { payload: {} }) {
  try {
    const data = yield call(getGrnSummary, action.payload);
    yield put(getGrnSummarySuccess(data?.result?.data));
  } catch (er) {
    yield put(getGrnSummaryFail(er));
  }
}

function* getExpiryDetailsSaga(action = { payload: {} }) {
  const { response, error } = yield call(getExpiryDetails, action.payload);
  if (response) {
    yield put(getExpiryDetailSuccess(response.data.result));
  } else {
    yield put(getExpiryDetailFail(error));
  }
}

function* getProductMismatchSaga(action = { payload: {} }) {
  const { response, error } = yield call(productMismatchApi, action.payload);
  if (response) {
    yield put(productMismatchSuccess(response));
  } else {
    yield put(productMismatchFail(error.response.data));
  }
}

function* updateGrnItemSaga(action = { payload: {} }) {
  const { response, error } = yield call(updateGrnItemApi, action.payload);
  if (response) {
    yield put(updateGrnItemSuccess(response));
  } else {
    yield put(updateGrnItemFail(error.response.data));
  }
}

function* closeGrnSaga(action = { payload: {} }) {
  const { response, error } = yield call(closeGrnApi, action.payload);
  if (response) {
    yield put(grnCloseSuccess(response.data));
  } else {
    yield put(grnCloseFail(error.response.data));
  }
}

function* getAllGrnForAnInvoiceSaga(action = { payload: {} }) {
  const { response, error } = yield call(getAllGrnForAnInvoiceApi, action.payload);
  if (response) {
    yield put(getAllGrnForAnInvoiceSuccess(response.data.result.data));
  } else {
    yield put(getAllGrnForAnInvoiceFail(extractApiErrorMessage(error)));
  }
}

function* searchByDescSaga(action = { payload: {} }) {
  try {
    const response = yield call(getSearchByDescApi, action.payload);
    yield put(searchByDescSuccess(response.data.data));
  } catch (er) {
    yield put(searchByDescFail(er));
  }
}

function* searchByDescProductsSaga(action = { payload: {} }) {
  try {
    let data = {};
    if (action.payload.purchase_order_code) {
      data = yield call(getProductDetailsApi, action.payload);
    } else {
      const { data: response } = yield call(getPIDdetailsApi, action.payload.pid);
      const {
        axisType: axis_type,
        color: colour,
        edgeDistance: ed,
        imageUrl: image_url,
        value: name,
        classification,
        ...rest
      } = response.data.productDetailsResponse || {};
      data.response = {
        data: {
          product_details: [
            {
              ...rest,
              classification,
              axis_type,
              colour,
              ed,
              image_url,
              image_urls: [],
              name,
              product_url: image_url,
              product_color: colour
            }
          ]
        }
      };
    }
    yield put(searchByDescPrductsSuccess(data));
  } catch (er) {
    yield put(searchByDescPrductsFail(er));
  }
}

function* isGrnOpenSaga(action = { payload: {} }) {
  try {
    const data = yield call(isGrnOpenApi, action.payload);
    yield put(isGrnOpenSuccess(data?.response?.data?.result));
  } catch (er) {
    yield put(isGrnOpenFail(er));
  }
}

function* closeAllOpenGrnSaga(action = { payload: {} }) {
  try {
    const data = yield call(closeAllOpenGrnApi, action.payload);
    yield showToaster(data, 'success');
  } catch (er) {
    yield showToaster(er);
  }
  yield put(closeAllOpenGRNReset());
}

export function* runGrnCalls() {
  yield all([
    takeLatest(GRN_LIST_LOAD, getGrnListSaga),
    takeLatest(GRN_CREATE_LOAD, createGrnSaga),
    takeLatest(BOX_CODE_LOAD, createBoxCodeSaga),
    takeLatest(QC_DETAIL_LOAD, createQcDetailSaga),
    takeLatest(GET_INVOICES_BY_VENDOR_ID_LOAD, getInvoicesByVendorIdSaga),
    takeLatest(GET_INVOICE_DETAIL_LOAD, getInvoiceDetailSaga),
    takeLatest(GET_PID_LIST_LOAD, getPidListSaga),
    takeLatest(GET_PID_DETAIL_LOAD, getPidDetailSaga),
    takeLatest(ADD_PID_LOAD, addPidSaga),
    takeLatest(CONVERT_PID_LOAD, convertPidSaga),
    takeLatest(GET_SAMPLING_QTY_LOAD, getSamplingQtySaga),
    takeLatest(GET_PID_QC_SAMPLING_LOAD, getPidQcSamplingSaga),
    takeLatest(GET_ITEM_EDIT_LOAD, getEditItemSaga),
    takeLatest(GET_ITEM_DELETE_LOAD, deleteItemSaga),
    takeLatest(UPDATE_QTY_LOAD, updateQtySaga),
    takeLatest(GET_GRN_SUMMARY_LOAD, getGrnSummarySaga),
    takeLatest(GET_QC_DATA_LOAD, getBoxItemsDataSaga),
    takeLatest(GET_EXPIRY_DETAILS_LOAD, getExpiryDetailsSaga),
    takeLatest(PRODUCT_MISMATCH_LOAD, getProductMismatchSaga),
    takeLatest(UPDATE_GRN_ITEM_LOAD, updateGrnItemSaga),
    takeLatest(GRN_CLOSE_LOAD, closeGrnSaga),
    takeLatest(GET_GRN_DETAILS_FOR_AN_INVOICE_LOAD, getAllGrnForAnInvoiceSaga),
    takeLatest(SEARCH_BY_DESC_LOAD, searchByDescSaga),
    takeLatest(SEARCH_BY_DESC_PRODUCTS_LOAD, searchByDescProductsSaga),
    takeLatest(IS_GRN_OPEN_LOAD, isGrnOpenSaga),
    takeLatest(CLOSE_ALL_OPEN_GRN_LOAD, closeAllOpenGrnSaga)
  ]);
}
