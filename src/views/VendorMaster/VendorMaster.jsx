import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';

import useF<PERSON><PERSON><PERSON>ook from 'components/common/useFilterHook';
import UploadVendorMaster from 'components/UploadVendorMaster/UploadVendorMaster';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import {
  getVendorMasterListLoad,
  getVendorMasterListReset,
  exportVendorMasterLoad,
  exportVendorMasterReset,
  vendorMasterStatusApproveReset
} from 'redux/actionCreators/vendorMaster';
import {
  convertDateFormat,
  fileDownload,
  generatePayloadForSearchAPI,
  getAllQueryParam
} from 'utils/helpers';
import { searchListReset } from 'redux/actionCreators/filters';
import { LOCALISATION } from 'redux/reducers/localisation';
import { toastOpen } from 'redux/actionCreators/toast';
import { mapSearchFilter<PERSON>ey } from 'config/filtersKeyMapping';

import VendorMasterHeader from './VendorMasterHeader';
import VendorMasterBody from './VendorMasterBody';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  mainBar: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '27px',
    marginTop: '4px'
  }
}));

const sortingData = {
  id: 'updated_at',
  order: 'DESC'
};

const PAGE_SIZE = 15;

const defaultObj = {
  isReset: true,
  path: 'vendorItemMaster?version=v1',
  payload: {
    type: 'vendorItemMaster',
    pageRequest: {
      pageNumber: 0,
      pageSize: PAGE_SIZE,
      sortKey: 'updated_at',
      sortOrder: 'DESC'
    }
  }
};

let objPayload = { ...defaultObj };

const VendorMaster = () => {
  const location = useLocation();
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const isInitialReq = useRef(true);
  const [selectedPIDs, setSelectedPIDs] = useState([]);

  const [requestBody, setRequestBody] = useState(defaultObj);
  const [filterInput, setFilterInput] = useState(0);
  const [showUploadVednorMaster, setShowUploadVednorMaster] = useState(false);

  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.VENDOR_ITEM_MASTER);

  const {
    vendoreMasterListFail,
    totalRecordsCount,
    exportVendorData,
    exportVendorDataFail,
    approveStatus: { isSuccess: isApproveSuccess }
  } = useSelector((state) => state.vendorMaster);

  const { onChangeFilterList } = useFilterHook();

  // calling load after approve api success
  useEffect(() => {
    if (isApproveSuccess) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        isReset: true,
        payload: {
          ...prevReqBody.payload,
          pageRequest: {
            ...prevReqBody.payload.pageRequest,
            pageNumber: 0
          }
        }
      }));
      dispatch(vendorMasterStatusApproveReset());
      setSelectedPIDs([]);
    }
  }, [isApproveSuccess]);

  useEffect(() => {
    if (!isInitialReq.current) {
      dispatch(getVendorMasterListLoad(requestBody));
    }
  }, [requestBody]);

  useEffect(() => {
    isInitialReq.current = false;
    const result = getAllQueryParam(window.location.search);
    objPayload = JSON.parse(JSON.stringify(defaultObj));
    let { payload } = objPayload;
    payload.pageRequest.sortKey = sortingData.id;
    payload.pageRequest.sortOrder = sortingData.order;
    payload = generatePayloadForSearchAPI(result, payload, mapSearchFilterKey);
    if (payload.globalSelector) {
      payload.classification_type = payload.globalSelector;
      setFilterInput(payload.globalSelector);
      delete payload.globalSelector;
      setRequestBody(objPayload);
    }
  }, [location.search]);

  useEffect(() => {
    if (exportVendorDataFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: exportVendorDataFail.response.data.message,
          severity: 'error'
        })
      );
      dispatch(exportVendorMasterReset());
    } else if (exportVendorData) {
      fileDownload(
        exportVendorData?.response?.data,
        `VIM-Export-${convertDateFormat(new Date(), 'export')}`
      );
      dispatch(exportVendorMasterReset());
    }
  }, [exportVendorData, exportVendorDataFail, dispatch]);

  useEffect(() => {
    if (vendoreMasterListFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          subHeading: vendoreMasterListFail?.response?.data?.message,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [vendoreMasterListFail, dispatch]);

  useEffect(
    () => () => {
      sortingData.id = 'updated_at';
      sortingData.order = 'DESC';
      dispatch(getVendorMasterListReset());
      dispatch(exportVendorMasterReset());
      dispatch(searchListReset());
    },
    [dispatch]
  );

  useKeyboardShortcut([17, 73], () => {
    setShowUploadVednorMaster(true);
  });

  const exportVendorMaster = useCallback(() => {
    const tempData = objPayload;
    tempData.payload.pageRequest.pageSize = totalRecordsCount;
    dispatch(exportVendorMasterLoad(tempData));
  }, [dispatch, totalRecordsCount]);

  useKeyboardShortcut([18, 69], () => {
    if (filterInput) {
      exportVendorMaster();
    }
  });

  const changeSearchOption = (event) => {
    const { value } = event.target;
    if (value) {
      sortingData.id = 'updated_at';
      sortingData.order = 'DESC';
      setFilterInput(value);
      onChangeFilterList([{ key: 'globalSelector', value }], 'globalSelector', true);
    }
  };

  const showUploadModal = () => {
    setShowUploadVednorMaster(true);
  };

  const handleSelectAllClick = () => {
    if (selectedPIDs.length > 0) {
      setSelectedPIDs([]);
    }
  };

  const selectPidFun = (event, pidInfo) => {
    const isCheck = event.target.checked;
    if (isCheck) {
      selectedPIDs.push(pidInfo);
    } else {
      selectedPIDs.forEach((x, i) => {
        if (x.id === pidInfo.id) {
          selectedPIDs.splice(i, 1);
        }
      });
    }
    setSelectedPIDs([...selectedPIDs]);
  };

  return (
    <div className={`${classes.root} po-dashboard pos-rel`}>
      <VendorMasterHeader
        classes={classes}
        filterInput={filterInput}
        showUploadModal={showUploadModal}
        isShowShortCuts={isShowShortCuts}
        changeSearchOption={changeSearchOption}
        selectedPIDs={selectedPIDs}
        isApproveSuccess={isApproveSuccess}
      />
      {!filterInput && (
        <div
          style={{ height: '50vh' }}
          className="text-33 fs16 fw-bold text-center display-grid-center"
        >
          {CONTENT.SELECT_CATEGORY_TO_CONTINUE}
        </div>
      )}
      <VendorMasterBody
        filterInput={filterInput}
        selectedPIDs={selectedPIDs}
        handleSelectAllClick={handleSelectAllClick}
        selectPidFun={selectPidFun}
        setRequestBody={setRequestBody}
        isInitialReq={isInitialReq}
        sortingData={sortingData}
        requestBody={requestBody}
        PAGE_SIZE={PAGE_SIZE}
        exportVendorMaster={exportVendorMaster}
        isApproveSuccess={isApproveSuccess}
      />
      {showUploadVednorMaster && (
        <UploadVendorMaster
          open={showUploadVednorMaster}
          close={() => {
            setShowUploadVednorMaster(false);
          }}
        />
      )}
    </div>
  );
};

export default VendorMaster;
