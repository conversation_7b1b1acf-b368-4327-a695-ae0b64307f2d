import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getUserDataAPI = () => tokenInstance().get(config.supervisor.getUserList);

export const getSupervisorCategories = () => tokenInstance().get(config.supervisor.getCategories);

export const assignOrderItemsAPI = (requestBody) =>
  tokenInstance().post(config.supervisor.createSkippedItemSummary, null, { params: requestBody });

export const notFoundOrderItemsAPI = async (requestBody) =>
  tokenInstance().post(config.supervisor.markItemPermanentlyNotFound, null, {
    params: requestBody
  });

export const scanOrderPickingAPI = ({ id, scannedBarCode }) =>
  tokenInstance().post(`${config.supervisor.scan}/${id}/${scannedBarCode}`);

// ORDER STOCK OUT
export const orderStockOutApi = ({ id, barcode }) =>
  tokenInstance().post(`${config.orderDetails.barcodeStockOut}/${id}/${barcode.toUpperCase()}`);

export const getSkippedDataAPI = (requestBody) =>
  tokenInstance().post(config.supervisor.getSkippedList, requestBody);

export const getUploadPidDataApi = (requestBody) =>
  tokenInstance().put(config.supervisor.getUploadFastZonePid, requestBody);

// TODO: APIs are not ready for hold data
export const getHoldDataAPI = async () => ({
  response: {
    totalRecords: 1,
    data: [
      {
        pickingDetail: {
          id: 23,
          userId: 222,
          pickingSummaryId: 2,
          incrementId: 1700004315,
          wmsOrderItemId: 542,
          productId: 131932,
          productName: `Black Full Rim Rectangle Medium (Size-52) 
              Vincent Chase VAGABOND VC E11850 -C1 Eyeglasses`,
          channel: 'CUSTOM',
          productType: 'EYEFRAME',
          orderItemCount: 4,
          itemBarcode: null,
          locationBarcode: null,
          locationHierarchy: null,
          shipmentId: 'SQNXS122000000000073',
          status: 'TEMP_NOT_FOUND',
          detail: null,
          boxCode: null,
          mergedBoxCode: null,
          mergedBy: null,
          picklistOrderItemId: 21,
          markPickedWms: 0,
          markPickedIms: 0,
          productQuantity: 1,
          orderType: 'standard',
          priority: 0,
          facility: 'QNXS1',
          holdReason: null,
          holdReasonSubmittedAt: null,
          skippedReason: null,
          duplicate: false,
          qcRequired: false,
          newProductId: null,
          imageUrl: null,
          createdAt: '2021-10-18T00:36:19.000+00:00',
          updatedAt: '2022-01-12 13:44:42',
          productImage:
            '/v/i/vincent-chase-vc-e11850-full-rim-rectangle-c1-eyeglasses_g_9512_1.jpg',
          createdBy: 'LSP05068',
          updatedBy: null,
          version: 8,
          skippedCount: 0,
          skippedBy: 'LSP05068',
          skippedDate: '2021-10-18T00:36:19.000+00:00'
        },
        locationQty: 2,
        facilityQty: 2
      }
    ],
    meta: null
  }
});

export const holdOrderItemsAPI = async (requestBody) => {
  tokenInstance().post(config.supervisor.getSkippedList, requestBody);
  return {
    response: {
      status: true
    }
  };
};

export const unHoldOrderItemsAPI = async () => ({
  response: {
    status: true
  }
});

export const moveFromASRSToManual = (payload) =>
  tokenInstance().post(config.supervisor.moveFromASRSToManual, payload);

export const bulkUploadDiscardedShipmentAPI = (requestBody) =>
  tokenInstance().put(config.supervisor.bulkUploadDiscardedShipment, requestBody);
