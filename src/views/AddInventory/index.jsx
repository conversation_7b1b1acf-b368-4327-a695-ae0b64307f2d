import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { omit } from 'utils/lodash';

import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';

import useFilterHook from 'components/common/useFilterHook';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import { generatePayloadForSearchAPI, getAllQueryParam } from 'utils/helpers';
import { getAddInventoryListLoad } from 'redux/reducers/addInventory';
import { LOCALISATION } from 'redux/reducers/localisation';
import AddInventoryHeader from './AddInventoryHeader';
import AddInventoryBody from './AddInventoryBody';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    height: 'calc(100vh - 65px)',
    background: '#FFFFFF'
  }
}));

const PAGE_SIZE = 35;

const requestPayload = {
  pageRequest: {
    pageNumber: 0,
    pageSize: PAGE_SIZE,
    sortKey: 'createdAt',
    sortOrder: 'DESC'
  }
};

const AddInventory = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const location = useLocation();
  const initialReq = useRef(false);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.ADD_INVENTORY);

  const [requestBody, setRequestBody] = useState(requestPayload);

  const dispatchListLoadFn = () => dispatch(getAddInventoryListLoad(requestBody));

  useEffect(() => {
    if (initialReq.current) {
      dispatchListLoadFn();
    }
  }, [requestBody]);

  const { selectedFilterList, onChangeFilterList, deleteSelectedFilter } = useFilterHook();

  useEffect(() => {
    initialReq.current = true;
    const queryFilter = getAllQueryParam(window.location.search);
    const payload = generatePayloadForSearchAPI(queryFilter, {}, mapSearchFilterKey);
    setRequestBody((prevReqBody) => {
      let upldatedRequest = { pageRequest: { ...prevReqBody.pageRequest }, ...payload };
      if (prevReqBody.pid) {
        upldatedRequest = {
          ...upldatedRequest,
          pageRequest: {
            ...upldatedRequest.pageRequest,
            pageNumber: 0
          },
          pid: prevReqBody.pid
        };
      }
      if (payload) {
        upldatedRequest = {
          ...upldatedRequest,
          pageRequest: {
            ...upldatedRequest.pageRequest,
            pageNumber: 0
          }
        };
      }
      return {
        ...upldatedRequest
      };
    });
  }, [location.search]);

  const fetchMoreData = (sortOrder, sortKey, pageNumber) => {
    setRequestBody((prevReqBody) => ({
      ...prevReqBody,
      pageRequest: {
        ...prevReqBody.pageRequest,
        pageNumber,
        sortKey,
        sortOrder
      }
    }));
  };

  const onGlobalSearch = (pid) => {
    initialReq.current = true;
    if (pid) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        pageRequest: {
          ...prevReqBody.pageRequest,
          pageNumber: 0
        },
        pid
      }));
    } else {
      setRequestBody((prevReqBody) => ({
        ...omit(prevReqBody, ['pid'])
      }));
    }
  };

  return (
    <Box data-cy="add-inventory-container" className={classes.root}>
      <AddInventoryHeader
        selectedFilterList={selectedFilterList}
        deleteSelectedFilter={deleteSelectedFilter}
        fetchMoreListItems={dispatchListLoadFn}
        onSearch={onGlobalSearch}
        CONTENT={CONTENT}
      />
      <AddInventoryBody
        selectedFilterList={selectedFilterList}
        onChangeFilterList={onChangeFilterList}
        pageLimit={PAGE_SIZE}
        pageNumber={requestBody.pageRequest.pageNumber}
        fetchMoreListItems={fetchMoreData}
        CONTENT={CONTENT}
      />
    </Box>
  );
};

export default AddInventory;
