import React from 'react';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { useDispatch, useSelector } from 'react-redux';
import { DISTRIBUTOR_ORDERS, getCreateOrderDataLoad } from 'redux/reducers/distributorOrders';
import { LOGIN } from 'redux/reducers/login';
import { Link } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()(() => ({
  steppers: {
    width: '33.33%',
    height: '81vh',
    paddingTop: '30px',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column'
  },
  orderDetails: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    padding: '25px 15px',
    paddingRight: '40px',
    border: '1px solid #939393',
    borderRadius: '12px',
    marginBottom: '20px',
    width: '43%'
  },
  successMessage: {
    color: 'var(--success-dark, #3B873E)',
    fontWeight: '500'
  },
  colorBlack: {
    color: 'black'
  }
}));

export const PlaceOrder = ({ resetOrderDetails, CONTENT, orderDetails }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const { isSuccess, doNumber } = useSelector((state) => state[DISTRIBUTOR_ORDERS].createOrder);
  const empCode = useSelector((state) => state[LOGIN].user.userDetail?.empCode);
  const currency = useSelector((state) => state.consul.currency.data);
  const { pidListing, referenceOrderId } = useSelector(
    (state) => state[DISTRIBUTOR_ORDERS].csvData
  );

  const selectedCountry = orderDetails?.customer?.customerAddressDetails?.[0].country;

  const createOrder = () => {
    const payload = {
      referenceOrderId,
      empCode
    };
    dispatch(getCreateOrderDataLoad(payload));
  };

  return (
    <Box data-cy="orderDetails-container" className={classes.steppers}>
      {isSuccess && (
        <Box
          data-cy="success-message-container"
          className="pd-b15 display-flex flex-direction-column align-items-center"
        >
          <img
            data-cy="success-image"
            width="100px"
            height="100px"
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/success-icon.png`}
            alt="success-icon.png"
          />
          <Box data-cy="success-message" className={classes.successMessage}>
            {CONTENT.ORDER_CREATED_SUCCESSFULLY}
          </Box>
          <Box data-cy="do-number" className="fs16 pd-t10 pd-b5">
            {CONTENT.DO_NUMBER}:
          </Box>
          <Link
            data-cy="doNumber"
            onClick={resetOrderDetails}
            className="fs16 fw-bold mr-t8"
            to="/marketPlace/distributorOrders/list"
          >
            {doNumber}
          </Link>
        </Box>
      )}
      <Box className={classes.orderDetails}>
        <Box data-cy="total-so-value-content" className="fs12 pd-b5">
          {CONTENT.TOTAL_SO_VALUE}
        </Box>
        <Box data-cy="total-so-value" className={classes.colorBlack}>
          {currency?.[selectedCountry] ?? '₹'}{' '}
          {pidListing?.reduce((acc, curr) => acc + curr.quantity * curr.price, 0) || 0}
        </Box>
        <Box width="100%" className="fs12 pd-t10 pd-b10 display-flex justify-content-space-between">
          <span data-cy="total-pid-content">{CONTENT.TOTAL_PID}</span>
          <span data-cy="total-pid" className={classes.totalNumbers}>
            {pidListing?.length}
          </span>
        </Box>
        <Box width="100%" className="fs12 display-flex justify-content-space-between">
          <span data-cy="total-qty-content">{CONTENT.TOTAL_QTY}</span>
          <span data-cy="total-qty" className={classes.totalNumbers}>
            {pidListing?.reduce((acc, curr) => acc + curr.quantity, 0) || 0}
          </span>
        </Box>
      </Box>
      <Box width="43%">
        {!isSuccess ? (
          <Button
            data-cy="create-order-btn"
            fullWidth
            color="primary"
            style={{ borderRadius: 6, marginRight: '20px' }}
            type="submit"
            variant="contained"
            onClick={createOrder}
          >
            {CONTENT.CREATE_ORDER}
          </Button>
        ) : (
          <Button
            data-cy="create-new-order"
            fullWidth
            color="primary"
            style={{ borderRadius: 6, marginRight: '20px' }}
            type="submit"
            variant="contained"
            onClick={resetOrderDetails}
          >
            {CONTENT.CREATE_NEW_ORDER}
          </Button>
        )}
      </Box>
    </Box>
  );
};
