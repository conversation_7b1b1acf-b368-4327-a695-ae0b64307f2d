/* eslint-disable no-param-reassign */
import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './layoutCreation.scss';
import { Button } from '@mui/material';
import LayoutCreateHeader from 'components/LayoutCreate/LayoutCreateHeader';
import {
  layoutDefineReset,
  layoutSaveLoad,
  layoutSaveReset,
  setLayoutCreateData,
  getLayoutDetailLoad
} from 'redux/actionCreators/layout';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import PhyicalEntityCreate from 'components/LayoutCreate/PhsyicalEntityCreate';
import { toastOpen } from 'redux/actionCreators/toast';
import { getQueryParam, getRandomValues } from 'utils/helpers';
import Spinner from 'components/Spinner/Spinner';
import { cloneDeep } from 'utils/lodash';
import { LOCALISATION } from 'redux/reducers/localisation';
import { useNavigate, useLocation } from 'react-router-dom';
// import RegexModal from 'components/LayoutCreate/RegexModal';

let maxChildren = 0;
let entityId = null;
let UNDO = [];
let REDO = [];
let isClone = false;

const UndoRedo = (undoAction, redoAction) => (
  <div className="undo-redo pos-abs bg-white display-flex justify-content-space-between">
    <div
      style={{ opacity: UNDO.length === 0 ? '0.5' : 1 }}
      onClick={() => undoAction()}
      onKeyDown={() => undoAction()}
      className="border-right-de pd-10 cursor-pointer"
    >
      <UndoIcon />
    </div>
    <div
      style={{ opacity: REDO.length === 0 ? '0.5' : 1 }}
      onClick={() => redoAction()}
      onKeyDown={() => redoAction()}
      className="pd-10 cursor-pointer"
    >
      <RedoIcon />
    </div>
  </div>
);

const LayoutCreation = (props) => {
  const { showHeader = true, layout_id } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const [showEntityID, setShowEntityID] = useState(null);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editEntityData, setEditEntityData] = useState(null);
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const dispatch = useDispatch();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.LAYOUT_PAGE);

  const {
    layoutSaveData,
    layoutSaveProccessing,
    layoutCreateData,
    layoutDraftProccessing,
    layoutDetailData,
    layoutDetailProccessing
  } = useSelector((state) => state.layout);
  const { entityList, entityListLoading } = useSelector((state) => state.facility);

  const getLayoutDetail = () => {
    const layoutId = getQueryParam(location.search.replace('?', ''), 'layoutId');
    const viewModeTemp = getQueryParam(location.search.replace('?', ''), 'viewMode');

    setViewMode(!!viewModeTemp);

    if (layoutId) {
      dispatch(getLayoutDetailLoad({ id: layoutId }));
    } else if (!layoutCreateData?.name) {
      setOpenCreateModal(true);
    }
  };

  useEffect(() => {
    if (layout_id) {
      dispatch(getLayoutDetailLoad({ id: layout_id }));
    }
  }, [layout_id, dispatch]);

  const addIndexing = (data, m, index) => {
    data.id = index;
    m += 1;
    if (m >= maxChildren) {
      maxChildren = m;
    }
    if (data.children) {
      data.children.forEach((element, ind) => {
        addIndexing(element, m, `${data.id},${ind}`);
      });
    }
  };

  const layoutIndexing = () => {
    maxChildren = 0;
    layoutCreateData.layout_schema.forEach((value, index) => {
      addIndexing(value, 0, index.toString());
    });
  };

  useEffect(() => {
    getLayoutDetail();
    layoutIndexing();
    entityId = null;
    maxChildren = 0;
    UNDO = [];
    REDO = [];
    return () => {
      UNDO = [];
      REDO = [];
      isClone = false;
      entityId = null;
      maxChildren = 0;
      dispatch(layoutDefineReset());
      dispatch(layoutSaveReset());
      dispatch(setLayoutCreateData({ layout_schema: [] }));
    };
  }, [dispatch]);

  useEffect(() => {
    if (layoutDetailData && layoutCreateData?.layout_schema) {
      const clone = getQueryParam(location.search.replace('?', ''), 'clone');
      isClone = clone;
      if (clone) {
        dispatch(
          setLayoutCreateData({
            ...layoutCreateData,
            nameCopy: layoutCreateData?.name,
            layout_id: null,
            facility_id: null,
            status: 'DRAFT'
          })
        );
        setOpenCreateModal(true);
      }
      layoutIndexing();
    }
  }, [layoutDetailData, entityList]);

  const findId = (dat, d, s, action) => {
    if (entityId) {
      dat.forEach((value, index) => {
        if (value.id === entityId) {
          if (action === 'edit' && d.id) {
            s[index] = d;
          } else if (action === 'edit' && !d.id) {
            if (!s[index].children) {
              s[index].children = [];
            }
            s[index].children.push(d);
          } else if (action === 'delete') {
            s.splice(index, 1);
          }
        } else if (value.children) {
          findId(value.children, d, value.children, action);
        }
      });
    } else {
      layoutCreateData.layout_schema.push(d);
    }
  };

  const undoAction = () => {
    if (UNDO.length > 0) {
      REDO.push(cloneDeep([...layoutCreateData.layout_schema]));
      layoutCreateData.layout_schema = UNDO.pop();
      layoutIndexing();
      setShowEntityID(`${getRandomValues()}'layout'`);
    }
  };

  const redoAction = () => {
    if (REDO.length > 0) {
      UNDO.push(cloneDeep([...layoutCreateData.layout_schema]));
      layoutCreateData.layout_schema = REDO.pop();
      layoutIndexing();
      setShowEntityID(`${getRandomValues()}'layout'`);
    }
  };

  const checkNameExist = (data, vals) => {
    let isExsist = false;
    const an = (dat, values) => {
      dat.forEach((value) => {
        if (values.type.toUpperCase() === value.type.toUpperCase() && values.id !== value.id) {
          isExsist = true;
        } else if (value.children) {
          an(value.children, values);
        }
      });
    };
    an(data, vals);
    return isExsist;
  };

  const closeDrawer = useCallback(() => {
    setOpenDrawer(false);
    setEditEntityData(null);
    entityId = null;
  }, []);

  const addEntity = useCallback(
    (values) => {
      UNDO.push(cloneDeep([...layoutCreateData.layout_schema]));
      if (checkNameExist(layoutCreateData.layout_schema, values)) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: `${values.type} entity already exist`,
            severity: 'error'
          })
        );
      } else {
        findId(layoutCreateData.layout_schema, values, layoutCreateData.layout_schema, 'edit');
        layoutIndexing();
        closeDrawer();
      }
    },
    [layoutCreateData]
  );

  const setSelectedId = (event, id) => {
    if (!openDrawer) {
      event.stopPropagation();
      setShowEntityID(id);
      entityId = id;
    }
  };

  const editPhsicalEntity = (id) => {
    if (id) {
      const p = id.split(',');
      let targetObject = [...layoutCreateData.layout_schema];
      if (p.length > 1) {
        targetObject = { ...targetObject[parseInt(p[0], 10)] };
        p.forEach((element, index) => {
          if (index > 0) {
            targetObject = { ...targetObject.children[parseInt(element, 10)] };
          }
        });
      } else {
        targetObject = { ...targetObject[parseInt(p[0], 10)] };
      }
      setEditEntityData(targetObject);
    }
    setOpenDrawer(true);
  };

  const disableStorageEntity = (values) => {
    const p = values?.id && entityList?.physical_entity[values?.type];
    return !!(p && (p.count > 0 || p.invertory > 0));
  };

  const deletePhyicalEntity = (value) => {
    UNDO.push(cloneDeep([...layoutCreateData.layout_schema]));
    if (disableStorageEntity(value)) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Cann\'t delete this entity',
          severity: 'error'
        })
      );
    } else {
      findId(layoutCreateData.layout_schema, null, layoutCreateData.layout_schema, 'delete');
      layoutIndexing();
      setShowEntityID(null);
      entityId = null;
    }
  };

  const checkStorageLayout = (data) => {
    data.forEach((value) => {
      if (value.children) {
        value.storage = false;
        checkStorageLayout(value.children);
      } else {
        value.storage = true;
      }
    });
  };

  const saveLayout = useCallback(
    (status) => {
      layoutCreateData.status = status;
      checkStorageLayout(layoutCreateData.layout_schema);
      layoutCreateData.layout_schema = { storage: false, children: layoutCreateData.layout_schema };
      dispatch(layoutSaveLoad({ ...layoutCreateData }));
      layoutCreateData.layout_schema = layoutCreateData.layout_schema.children;
    },
    [layoutCreateData]
  );

  const pysicalEntityTab = (value, width, backgroundColor) => {
    const { type, children, id } = value;
    let w = width;
    const bg = backgroundColor === '#ffffff' ? '#f5f5f5' : '#ffffff';
    w = width - 40;
    let storageTemp = false;
    let countTemp = 0;
    let inventoryTemp = 0;
    if (entityList?.physical_entity[type]) {
      const { storage, count, inventory } = entityList.physical_entity[type];
      storageTemp = storage;
      countTemp = count;
      inventoryTemp = inventory;
    }
    return (
      <div
        key={id}
        onMouseOver={(event) => setSelectedId(event, id)}
        onFocus={(event) => setSelectedId(event, id)}
        onMouseLeave={() => !openDrawer && setShowEntityID(null)}
        style={{ width: `${w}px`, backgroundColor: bg }}
        className="display-flex justify-content-center pd-t20 mr-t25 layoutTab cursor-pointer"
      >
        <div className="display-flex justify-content-space-between">
          <div className="display-flex">
            <div className="name-circle mr-r15">{type?.[0]}</div>
            <div className="display-flex justify-content-space-between flex-direction-column">
              <div className="fs10">{CONTENT.PHYSICAL_ENTITY}</div>
              <div className="fs16 fw-bold">{type}</div>
            </div>
          </div>
          {!viewMode && (
            <div
              style={{ display: id === showEntityID && !openDrawer ? 'block' : 'none' }}
              className="display-flex align-items-center"
            >
              <div
                style={{ display: 'inline-block' }}
                onKeyDown={() => editPhsicalEntity(id)}
                onClick={() => editPhsicalEntity(id)}
              >
                <img
                  className="img-action mr-r10"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/edit.svg`}
                  alt="edit"
                />
              </div>
              {!(children && children.length > 0) && (
                <div
                  style={{ display: 'inline-block' }}
                  onKeyDown={() => deletePhyicalEntity(value)}
                  onClick={() => deletePhyicalEntity(value)}
                >
                  <img
                    className="img-action mr-r10"
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/delete-grey.svg`}
                    alt="delete"
                  />
                </div>
              )}
              {!(storageTemp && (countTemp > 0 || inventoryTemp > 0)) && (
                <div
                  style={{ display: 'inline-block' }}
                  onKeyDown={() => editPhsicalEntity()}
                  onClick={() => editPhsicalEntity()}
                >
                  <img
                    className="img-action mr-r10"
                    src={`${import.meta.env.VITE_STATIC_SERVER}/images/Add.svg`}
                    alt="add"
                  />
                </div>
              )}
            </div>
          )}
        </div>
        {children?.map((val) => pysicalEntityTab(val, w, bg))}
      </div>
    );
  };

  useEffect(() => {
    if (layoutSaveData?.data?.layout_id) {
      isClone = false;
      layoutCreateData.layout_id = layoutSaveData.data.layout_id;
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Layout is successfully created',
          severity: 'success'
        })
      );
      /* as we are sending layout_id only from facility details, 
      up on save navigation is not required on facility details */
      if (!layout_id) {
        navigate('/layoutpage/list');
      }
    } else if (layoutSaveData?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: layoutSaveData.meta.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [layoutSaveData]);

  const errorView = () => {
    if (layoutCreateData) {
      return (
        <>
          {openDrawer && (
            <PhyicalEntityCreate
              entityList={entityList}
              editEntityData={editEntityData}
              openDrawer={openDrawer}
              addEntity={(values) => addEntity(values)}
              closeDrawer={() => closeDrawer()}
              CONTENT={CONTENT}
            />
          )}
          {showHeader && (
            <LayoutCreateHeader
              CONTENT={CONTENT}
              viewMode={viewMode}
              isClone={isClone}
              layoutData={layoutCreateData}
              layoutSaveProccessing={layoutSaveProccessing}
              layoutDraftProccessing={layoutDraftProccessing}
              saveLayout={(status) => saveLayout(status)}
              navigate={navigate}
              openCreateModal={openCreateModal}
            />
          )}
          <>
            <div className="layout-creation mr-t40 pos-rel">
              {!viewMode && UndoRedo(undoAction, redoAction)}
              <div
                className="display-flex justify-content-center pd-t20 layoutTab mr-auto"
                style={{ width: '813px', backgroundColor: '#FFFFF' }}
              >
                <div
                  className="display-flex fs16 fw-bold
                 justify-content-space-between align-items-center"
                >
                  <div className="display-flex align-items-center">
                    <img
                      className="img-action mr-r10"
                      src={`${import.meta.env.VITE_STATIC_SERVER}/images/Facility.svg`}
                      alt="Facility"
                    />
                    <div>{CONTENT.FACILITY}</div>
                  </div>
                  {!viewMode && (
                    <Button
                      onMouseOver={() => {
                        entityId = null;
                      }}
                      onClick={() => editPhsicalEntity()}
                      color="primary"
                      style={{ borderRadius: 8 }}
                      type="submit"
                      variant="contained"
                    >
                      +{CONTENT.ADD_PHYSICAL_ENTITY}
                    </Button>
                  )}
                </div>
                {layoutCreateData?.layout_schema?.map((value) =>
                  pysicalEntityTab(value, 813, '#ffffff')
                )}
              </div>
            </div>
            {/* <RegexModal  /> */}
          </>
        </>
      );
    }
    return (
      <div className="display-grid-center pd-15 fw-bold">
        {CONTENT.NO_DATA_EXIST_FOR_LAYOUT_SCMA}
      </div>
    );
  };

  if (layoutDetailProccessing || entityListLoading) {
    return <Spinner className="display-grid-center pd-15" />;
  }
  return errorView();
};

export default LayoutCreation;
