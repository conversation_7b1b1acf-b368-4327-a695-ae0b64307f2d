import React from 'react';
import LKButton from '../components/Button/LKButton';

export default {
  title: 'Components/Button',
  component: LKButton,
  argTypes: {
    children: {
      control: 'text',
      description: 'Overwritten description'
    },
    color: {
      control: 'select',
      options: ['primary', 'secondary', 'error']
    },
    variant: {
      control: 'select',
      options: ['contained', 'outlined']
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large']
    }
  },
  args: {
    // default values
    children: 'Default Button',
    size: 'small'
  }
};

const Template = (args) => <LKButton {...args} />;

export const PrimaryColor = Template.bind({});

PrimaryColor.args = {
  color: 'primary',
  variant: 'contained',
  children: 'button'
};

export const SecondaryColor = Template.bind({});

SecondaryColor.args = {
  color: 'secondary',
  variant: 'outlined'
};
