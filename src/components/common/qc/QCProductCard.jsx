import React from 'react';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Carousel from 'components/Carousel/Carousel';
import { v4 as uuidv4 } from 'uuid';

const useStyles = makeStyles()(() => ({
  carouselHeader: {
    marginTop: '64px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 15,
  },
  carouselDetails: {
    padding: '2px',
    borderRight: '1px solid #DDDDDD',
    '&:last-child': {
      borderRight: 'none'
    }
  }
}));

const CAROUSEL_SETTINGS = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 3000
};

const QCProductCard = ({
  slideImages,
  desc,
  footerData,
  children,
  carouselCustomSettings = CAROUSEL_SETTINGS
}) => {
  const { classes } = useStyles();

  function getSlides(images) {
    return images?.map((image, index) => (
      <div key={uuidv4()} className={classes.imageContainer}>
        <img className="product-images" src={image} alt={`img-${index}`} />
      </div>
    ));
  }

  return (
    <Box
      data-cy="qc-product-carousel"
      sx={{
        width: {
          lg: '45%',
          xs: '30%',
        },
        padding: '20px',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Carousel
        customSettings={carouselCustomSettings}
        Box
        slides={getSlides(slideImages)}
        isLeftRightPadding
        currentSlide={0}
      />
      <Box data-cy="product-details" className={classes.carouselHeader}>
        <Box data-cy="qc-product-desc" className="fs20 fw-bold">
          {desc}
        </Box>
        <Box data-cy="footer-details" display="flex" flexDirection="column" width="100%">
          {Object.entries(footerData).map(([key, value]) => (
            <Box
              component="span"
              key={key}
              data-cy={`qc-product-key-${key}`}
              className={`${classes.carouselDetails} product-values`}
              sx={{
                fontSize: {
                  lg: '16px',
                  xs: '22px',
                },
              }}
            >
              {key}: <b className="fw-bold">{value}</b>
            </Box>
          ))}
        </Box>
      </Box>
      {children}
    </Box>
  );
};

export default QCProductCard;
