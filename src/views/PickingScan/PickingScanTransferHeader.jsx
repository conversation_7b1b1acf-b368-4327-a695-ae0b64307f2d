import React, { useMemo } from 'react';
import { makeStyles } from 'tss-react/mui';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import IconButton from '@mui/material/IconButton';

import { LOCALISATION } from 'redux/reducers/localisation';
import OutOfComponent from 'components/common/OutOfComponent';
import Phases from '../../common/Phases';

const useStyles = makeStyles()(() => ({
  container: {
    padding: '10px 20px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    height: 90,
    background: '#FFFFFF',
    boxSizing: 'border-box'
  },
  left: {
    display: 'flex',
    flexDirection: 'row',
    gap: 10
  },
  right: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    textAlign: 'center'
  },
  scanCompleteBtn: {
    borderRadius: 4,
    width: '234px',
    height: '42px'
  }
}));

const PickingScanTransferHeader = ({ transferCode, productList, CONFIG }) => {
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.TRANSFER);
  const { classes } = useStyles();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(`/distributorOrder/Shipment/details/${transferCode}`);
  };

  const totalPidScanned = useMemo(
    () =>
      productList.filter(({ totalQuantity, pickedQuantity }) => totalQuantity === pickedQuantity)
        .length,
    [productList]
  );

  const reducerFun = ({ totalScanQty, totalQty }, { pickedQuantity, totalQuantity }) => ({
    totalScanQty: totalScanQty + pickedQuantity,
    totalQty: totalQty + totalQuantity
  });

  const totalItemScanned = useMemo(
    () => productList.reduce(reducerFun, { totalScanQty: 0, totalQty: 0 }),
    [productList]
  );

  return (
    <Box className={classes.container}>
      <Box className={classes.left}>
        <IconButton onClick={handleBack} size="large">
          <ArrowBackIcon />
        </IconButton>
        <Box className={classes.meta}>
          <Phases labels={CONFIG.labels} />
          <TextField
            value={transferCode}
            onChange={null}
            fullWidth
            id="picking-scan-textfield"
            variant="outlined"
            size="small"
            disabled
          />
        </Box>
      </Box>
      <Box className={classes.right}>
        <OutOfComponent
          title={CONTENT.PID_SCANNED}
          dividend={totalPidScanned || 0}
          divisor={productList?.length || 0}
        />
        <OutOfComponent
          title={CONTENT.TOTAL_ITEMS_ADDED}
          dividend={totalItemScanned.totalScanQty || 0}
          divisor={totalItemScanned.totalQty || 0}
        />

        <Button
          startIcon={<CheckCircleIcon />}
          className={classes.scanCompleteBtn}
          color="primary"
          variant="contained"
          data-cy="handle-back-btn"
          onClick={handleBack}
        >
          {CONTENT.SCANNING_COMPLETE}
        </Button>
      </Box>
    </Box>
  );
};

export default PickingScanTransferHeader;
