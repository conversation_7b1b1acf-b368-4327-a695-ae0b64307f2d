import * as action from '../actionTypes/storePacking';

// GET STORE PACKING LIST
export const getStorePackingListLoad = (data) => ({
  type: action.STORE_PACKING_LIST_LOAD,
  payload: data
});

export const getStorePackingListSuccess = (data) => ({
  type: action.STORE_PACKING_LIST_SUCCESS,
  payload: data
});

export const getStorePackingListOnFlush = (data) => ({
  type: action.STORE_PACKING_LIST_ON_FLUSH,
  payload: data
});

export const getStorePackingListOnChangeCourier = (data) => ({
  type: action.STORE_PACKING_LIST_ON_CHANGE_COURIER,
  payload: data
});

export const getStorePackingListFail = () => ({
  type: action.STORE_PACKING_LIST_FAIL
});

export const getStorePackingListReset = (data) => ({
  type: action.STORE_PACKING_LIST_RESET,
  payload: data
});

// FLUSH STORE PACKING
export const flushStorePackingLoad = (data) => ({
  type: action.STORE_PACKING_FLUSH_LOAD,
  payload: data
});

export const flushStorePackingSuccess = (data) => ({
  type: action.STORE_PACKING_FLUSH_SUCCESS,
  payload: data
});

export const flushStorePackingFail = () => ({
  type: action.STORE_PACKING_FLUSH_FAIL
});

export const flushStorePackingReset = (data) => ({
  type: action.STORE_PACKING_FLUSH_RESET,
  payload: data
});

// PRINT SHIPMENT LABEL
export const printShipmentLabelLoad = (data) => ({
  type: action.STORE_SHIPMENT_PRINT_LABEL_LOAD,
  payload: data
});

export const printShipmentLabelSuccess = (data) => ({
  type: action.STORE_SHIPMENT_PRINT_LABEL_SUCCESS,
  payload: data
});

export const printShipmentLabelFail = () => ({
  type: action.STORE_SHIPMENT_PRINT_LABEL_FAIL
});

export const printShipmentLabelReset = (data) => ({
  type: action.STORE_SHIPMENT_PRINT_LABEL_RESET,
  payload: data
});

// GET STORE COURIER LIST
export const getStoreCourierListLoad = (data) => ({
  type: action.STORE_COURIER_LIST_LOAD,
  payload: data
});

export const getStoreCourierListSuccess = (data) => ({
  type: action.STORE_COURIER_LIST_SUCCESS,
  payload: data
});

export const getStoreCourierListFail = () => ({
  type: action.STORE_COURIER_LIST_FAIL
});

// REMOVE SHIPMENT
export const removeShipmentLoad = (data) => ({
  type: action.STORE_SHIPMENT_REMOVE_LOAD,
  payload: data
});

export const removeShipmentSuccess = (data) => ({
  type: action.STORE_SHIPMENT_REMOVE_SUCCESS,
  payload: data
});

export const removeShipmentFail = (data) => ({
  type: action.STORE_SHIPMENT_REMOVE_FAIL,
  payload: data
});

// CHANGE STORE COURIER
export const changeStoreCourierLoad = (data) => ({
  type: action.STORE_COURIER_CHANGE_LOAD,
  payload: data
});

export const changeStoreCourierSuccess = (data) => ({
  type: action.STORE_COURIER_CHANGE_SUCCESS,
  payload: data
});

export const changeStoreCourierFail = (data) => ({
  type: action.STORE_COURIER_CHANGE_FAIL,
  payload: data
});

export const resetNewScannedIdData = () => ({
  type: action.STORE_COURIER_NEW_SHIPMENTID_DATA_RESET
});
