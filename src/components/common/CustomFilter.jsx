import React, { useState, useEffect, useCallback } from 'react';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Checkbox from '@mui/material/Checkbox';
import { makeStyles } from 'tss-react/mui';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import { Box } from '@mui/material';

import FilterWrapper from './FilterWrapper';

const useStyles = makeStyles()(() => ({
  root: {
    padding: '24px',
    background: '#fff'
  },
  container: {
    borderColor: '#F2F2F2'
  },
  listHeight: {
    height: '200px',
    overflowY: 'scroll'
  },
  listItem: {
    padding: 0,
    margin: 0,
    paddingLeft: '15px'
  },
  listItemIcon: {
    minWidth: '30px'
  },
  submitBtn: {
    borderRadius: 8,
    padding: 4
  },
  listMenus: {
    width: '180px',
    background: '#f5f5f5'
  }
}));

const mockKeys = ['SPH', 'CYL', 'AXIS', 'AP', 'BC'];

const icon = <CheckBoxOutlineBlankIcon fontSize="small" style={{ fill: '#999999' }} />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const CustomFilter = ({ onSubmit, marginLeft, selectedFilterList, listData, columnName }) => {
  const listDataValues = listData?.Power?.values;
  const [open, setOpen] = useState(false);
  const { classes } = useStyles();
  const [checked, setChecked] = useState({
    SPH: [],
    CYL: [],
    AXIS: [],
    AP: [],
    BC: []
  });
  const [activeItem, setActiveItem] = useState(1);

  const handleToggle = (key, value) => () => {
    // event.preventDefault()
    const currentIndex = checked[key].indexOf(value);
    const newChecked = { ...checked };

    if (currentIndex === -1) {
      newChecked[key].push(value);
    } else {
      newChecked[key].splice(currentIndex, 1);
    }

    setChecked(newChecked);
  };
  const submitData = () => {
    const temp = [];
    Object.keys(checked).forEach((key) => {
      checked[key].forEach((value) => temp.push({ key, value }));
    });
    onSubmit(temp, columnName, false, true);
  };
  const setOpenfun = useCallback((v) => setOpen(v), [setOpen]);

  const updateFilterChanges = useCallback(() => {
    const temp = {
      SPH: [],
      CYL: [],
      AXIS: [],
      AP: [],
      BC: []
    };
    selectedFilterList.forEach(({ key, value }) => {
      if (mockKeys.includes(key)) {
        temp[key].push(value);
      }
    });

    setChecked({ ...temp });
  }, [setChecked, selectedFilterList]);

  useEffect(() => {
    updateFilterChanges();
  }, [selectedFilterList, updateFilterChanges]);
  return (
    <FilterWrapper
      onSubmit={submitData}
      width="250px"
      marginLeft={marginLeft}
      open={open}
      setOpen={setOpenfun}
      onCloseUpdateFilter={updateFilterChanges}
      dataCy={columnName}
    >
      <Box display="flex" width="100%" borderBottom={1} className={classes.container} mb={1}>
        <Box className={classes.listMenus}>
          <List className={classes.listHeight}>
            {listDataValues.map(({ key }, index) => {
              const isActive = activeItem === index;
              return (
                <ListItem
                  key={key}
                  role={undefined}
                  className={classes.listItem}
                  style={{ background: isActive ? 'white' : '' }}
                  button
                  onClick={() => setActiveItem(index)}
                >
                  <ListItemText
                    className="pd-4"
                    id={key}
                    primary={<div className="fs12"> {key}</div>}
                  />
                </ListItem>
              );
            })}
          </List>
        </Box>
        <Box width="100%">
          <List className={classes.listHeight}>
            {open &&
              listDataValues[activeItem].values.map((key) => {
                const t = listDataValues[activeItem].key;
                key += '';
                return (
                  <ListItem
                    key={key + ''}
                    role={undefined}
                    className={classes.listItem}
                    button
                    onClick={handleToggle(t, key)}
                  >
                    <ListItemIcon className={classes.listItemIcon}>
                      <Checkbox
                        edge="start"
                        // checked={checked[t].indexOf(key) !== -1}
                        checked={checked[t]?.includes(key)}
                        // tabIndex={-1}
                        disableRipple
                        color="primary"
                        inputProps={{ 'aria-labelledby': key }}
                        icon={icon}
                        checkedIcon={checkedIcon}
                      />
                    </ListItemIcon>
                    <ListItemText id={key} primary={<div className="fs12"> {key}</div>} />
                  </ListItem>
                );
              })}
          </List>
        </Box>
      </Box>
    </FilterWrapper>
  );
};

export default React.memo(CustomFilter);
