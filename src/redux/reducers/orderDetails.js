import {
  getNotFoundStatusFail,
  getNotFoundStatusLoad,
  getNotFoundStatusReset,
  getNotFoundStatusSuccess
} from 'redux/actionCreators/orderDetails';
import * as action from '../actionTypes/orderDetails';

const initialState = {
  header: {
    data: {},
    isLoading: true
  },
  items: {
    data: [],
    isLoading: true
  },
  overview: {
    data: [],
    isLoading: true,
    shipmentItems: {}
  },
  shipments: {
    data: [],
    isLoading: true
  },
  invoices: {
    data: [],
    isLoading: true
  },
  courierList: {
    isLoading: true,
    data: []
  },
  shipmentReassign: {
    isLoading: true,
    data: []
  },
  orderStockOut: {
    isLoading: false,
    status: null
  },
  printInvoiceData: {},
  orderDetailsHistory: {},
  autoToManual: {
    isLoading: false,
    isSuccess: false
  },
  getNotFoundStatus: {
    isLoading: false,
    data: []
  }
};

const orderDetails = (state = initialState, { type, payload }) => {
  switch (type) {
    case action.ORDER_DETAILS_HEADER_LOAD:
      return initialState;
    case action.ORDER_DETAILS_HEADER_SUCCESS:
      return {
        ...state,
        header: {
          ...state.header,
          data: payload,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_HEADER_FAIL:
      return {
        ...state,
        header: {
          ...initialState.header,
          isLoading: false
        },
        items: {
          ...initialState.items,
          isLoading: false
        },
        overview: {
          ...initialState.overview,
          isLoading: false
        },
        shipments: {
          ...initialState.shipments,
          isLoading: false
        },
        invoices: {
          ...initialState.invoices,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_COURIER_LIST_LOAD:
      return {
        ...state,
        courierList: {
          ...initialState.courierList,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_COURIER_LIST_SUCCESS:
      return {
        ...state,
        courierList: {
          ...initialState.courierList,
          isLoading: false,
          data: payload
        }
      };
    case action.ORDER_DETAILS_COURIER_LIST_FAIL:
      return {
        ...state,
        courierList: {
          ...initialState.courierList,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_SHIPMENT_REASSIGN_LOAD:
      return {
        ...state,
        shipmentReassign: {
          ...state.shipmentReassign,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_SHIPMENT_REASSIGN_SUCCESS:
      return {
        ...state,
        shipmentReassign: {
          ...state.shipmentReassign,
          isLoading: false,
          data: payload
        }
      };
    case action.ORDER_DETAILS_SHIPMENT_REASSIGN_FAIL:
      return {
        ...state,
        shipmentReassign: {
          ...state.shipmentReassign,
          isLoading: false
        }
      };
    case action.ORDER_STOCK_OUT_LOAD:
      return {
        ...state,
        orderStockOut: {
          ...state.orderStockOut,
          isLoading: true
        }
      };
    case action.ORDER_STOCK_OUT_SUCCESS:
      return {
        ...state,
        orderStockOut: {
          ...state.orderStockOut,
          isLoading: false,
          status: true
        }
      };
    case action.ORDER_STOCK_OUT_FAIL:
      return {
        ...state,
        orderStockOut: {
          ...state.orderStockOut,
          isLoading: false
        }
      };
    case action.ORDER_STOCK_OUT_RESET:
      return {
        ...state,
        orderStockOut: {
          isLoading: false,
          status: null
        }
      };
    case action.ORDER_DETAILS_OVERVIEW_LOAD:
      return {
        ...state,
        overview: {
          ...initialState.overview,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_OVERVIEW_SUCCESS:
      return {
        ...state,
        overview: {
          ...state.overview,
          data: payload.shipmentLists,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_OVERVIEW_FAIL:
      return {
        ...state,
        overview: {
          ...initialState.overview,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_ITEMS_LOAD:
      return {
        ...state,
        items: {
          ...initialState.items,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_ITEMS_SUCCESS:
      return {
        ...state,
        items: {
          ...state.items,
          data: payload.itemDetails,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_ITEMS_FAIL:
      return {
        ...state,
        items: initialState.items
      };
    case action.ORDER_DETAILS_SHIPMENTS_LOAD:
      return {
        ...state,
        shipments: {
          ...initialState.shipments,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_SHIPMENTS_SUCCESS:
      return {
        ...state,
        shipments: {
          ...state.shipments,
          data: payload.shipmentList,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_SHIPMENTS_FAIL:
      return {
        ...state,
        shipments: {
          ...initialState.shipments,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_SHIPMENT_ITEMS_LOAD:
      return {
        ...state,
        overview: {
          ...state.overview,
          shipmentItems: {
            ...state.overview.shipmentItems,
            [payload.shipmentId]: {
              isLoading: true,
              data: []
            }
          }
        }
      };
    case action.ORDER_DETAILS_SHIPMENTS_ITEMS_SUCCESS: {
      const { orderItemResponses } = payload.orderItemHeaderResponse;
      const { vsmData } = payload;

      let dataWithUwDetails = orderItemResponses.map((eachItem) => ({
        ...eachItem,
        nexsOrderId: payload.nexsOrderId
      }));
      if (vsmData) {
        dataWithUwDetails = dataWithUwDetails.map((eachOrderItem) => {
          if (vsmData.data[eachOrderItem.orderItemId]?.uwItemId === eachOrderItem.orderItemId) {
            return {
              ...eachOrderItem,
              orderItemType: payload?.orderItemHeaderResponse?.orderItemType,
              orderItemSubType: payload?.orderItemHeaderResponse?.orderItemSubType,
              uwDetails: vsmData.data[eachOrderItem.orderItemId]
            };
          }
          return eachOrderItem;
        });
      }

      return {
        ...state,
        overview: {
          ...state.overview,
          shipmentItems: {
            ...state.overview.shipmentItems,
            [payload.shipmentId]: {
              ...state.overview.shipmentItems[payload.shipmentId],
              data: dataWithUwDetails,
              isLoading: false
            }
          }
        }
      };
    }
    case action.ORDER_DETAILS_SHIPMENTS_ITEMS_FAIL:
      return {
        ...state,
        overview: {
          ...state.overview,
          shipmentItems: {
            ...state.overview.shipmentItems,
            [payload]: {
              ...state.overview.shipmentItems[payload],
              isLoading: false
            }
          }
        }
      };
    case action.ORDER_DETAILS_PRINT_INVOICE_LOAD:
      return {
        ...state,
        printInvoiceData: {
          ...state.printInvoiceData,
          [payload]: {
            data: null,
            isLoading: true
          }
        }
      };
    case action.ORDER_DETAILS_PRINT_INVOICE_SUCCESS:
      return {
        ...state,
        printInvoiceData: {
          ...state.printInvoiceData,
          [payload.shipmentId]: {
            ...state.printInvoiceData[payload.shipmentId],
            data: payload.response,
            isLoading: false
          }
        }
      };
    case action.ORDER_DETAILS_PRINT_INVOICE_FAIL:
      return {
        ...state,
        printInvoiceData: {
          ...state.printInvoiceData,
          [payload]: {
            ...state.printInvoiceData[payload],
            data: null,
            isLoading: false
          }
        }
      };
    case action.ORDER_DETAILS_PRINT_INVOICE_RESET:
      return {
        ...state,
        printInvoiceData: initialState.printInvoiceData
      };
    case action.ORDER_DETAILS_INVOICE_DATA_LOAD:
      return {
        ...state,
        invoices: {
          ...initialState.invoices,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_INVOICE_DATA_SUCCESS:
      return {
        ...state,
        invoices: {
          ...state.invoices,
          data: payload.invoiceLists,
          isLoading: false
        }
      };
    case action.ORDER_DETAILS_INVOICE_DATA_FAIL:
      return {
        ...state,
        invoices: {
          ...initialState.invoices,
          isLoading: false
        }
      };

    case action.ORDER_DETAILS_HISTORY_LOAD:
      return {
        ...state,
        orderDetailsHistory: {
          ...state.orderDetailsHistory,
          [payload]: {
            data: [],
            isLoading: true
          }
        }
      };
    case action.ORDER_DETAILS_HISTORY_SUCCESS:
      return {
        ...state,
        ...state.orderDetailsHistory,
        orderDetailsHistory: {
          [payload.id]: {
            isLoading: false,
            data: payload.data
          }
        }
      };
    case action.ORDER_DETAILS_HISTORY_RESET:
    case action.ORDER_DETAILS_HISTORY_FAIL:
      return {
        ...state,
        orderDetailsHistory: {
          ...state.orderDetailsHistory,
          [payload]: {
            isLoading: false,
            data: []
          }
        }
      };

    case action.ORDER_DETAILS_RESET_DATA:
      return initialState;
    case action.ORDER_DETAILS_AUTO_TO_MANUAL_LOAD:
      return {
        ...state,
        autoToManual: {
          ...state.autoToManual,
          isLoading: true
        }
      };
    case action.ORDER_DETAILS_AUTO_TO_MANUAL_SUCCESS:
      return {
        ...state,
        autoToManual: {
          ...state.autoToManual,
          isLoading: false,
          isSuccess: true
        }
      };
    case action.ORDER_DETAILS_AUTO_TO_MANUAL_FAIL:
    case action.ORDER_DETAILS_AUTO_TO_MANUAL_RESET:
      return {
        ...state,
        autoToManual: {
          ...state.autoToManual,
          isLoading: false,
          isSuccess: false
        }
      };
    case getNotFoundStatusLoad.type:
      return {
        ...state,
        getNotFoundStatus: {
          ...state.getNotFoundStatus,
          isLoading: true
        }
      };
    case getNotFoundStatusSuccess.type:
      return {
        ...state,
        getNotFoundStatus: {
          ...state.getNotFoundStatus,
          isLoading: false,
          data: [...state.getNotFoundStatus.data, ...payload.data]
        }
      };
    case getNotFoundStatusFail.type:
      return {
        ...state,
        getNotFoundStatus: {
          ...state.getNotFoundStatus,
          isLoading: false
        }
      };
    case getNotFoundStatusReset.type:
      return {
        ...state,
        getNotFoundStatus: initialState.getNotFoundStatus
      };
    default:
      return state;
  }
};

export default orderDetails;
