import React, { useEffect, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';

import LkModal from 'components/Modal/Modal';
import FlexBox from 'components/core/FlexBox';
import Spinner from 'components/Spinner/Spinner';
import MoreItem from 'components/common/MoreItem';
import { InfoStrip, InfoTable } from 'components/common';
import SubtitleInfoStrip from 'components/common/SubtitleInfoStrip';
import PrintConfirmModal from 'components/PrintConfirmModal/PrintConfirmModal';

import { LOCALISATION } from 'redux/reducers/localisation';
import { pdfDownloadLoad, pdfDownloadReset } from 'redux/actionCreators/pdfDownload';
import { debitNoteDetailLoad } from 'redux/actionCreators/debitNote';
import { toastOpen } from 'redux/actionCreators/toast';

import {
  convertDateFormat,
  getQueryParam,
  roundUptoDigits,
  roundUptoFixedDigits,
  fileDownload,
  getCurrencySumbol,
  getTaxType
} from 'utils/helpers';
import useKeyboardShortcut from 'common/useKeyboardShortcut';
import { DebitNoteStatus, pdfNameMapping } from 'config/DebitNoteStatus';

import DebitNoteTable from './components/DebitNoteTable';

const DebitNoteDetail = () => {
  const location = useLocation();
  const dnNumTemp = getQueryParam(location.search.replace('?', ''), 'dnNum');
  const { debitNoteDetailLoading, debitNoteDetailSuccess, debitNoteDetailError } = useSelector(
    (state) => state.debitNote
  );
  const dispatch = useDispatch();
  const [selectedPrintOption, setSelectedPrintOption] = useState(0);
  const { pdfDownloadSuccess, pdfDownloadError } = useSelector((state) => state.pdfDownload);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.DEBIT_NOTE);

  const [showPrintModal, setShowPrintModal] = useState(false);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const getDN = () => {
    dispatch(debitNoteDetailLoad(dnNumTemp));
  };
  // need to move outside of the component
  const printOptions = [
    { value: CONTENT.DEBIT_NOTE, key: 'debitnote' },
    { value: CONTENT.DELIVERY_CHALLAN, key: 'deliverychallan' }
  ];

  const errorContainer = () => {
    const isError = debitNoteDetailError?.meta?.displayMessage;
    return isError ? (
      <Box className="display-grid-center pd-t15">{isError}</Box>
    ) : (
      <Spinner className="display-grid-center pd-15" />
    );
  };

  const subtitleInfoStripData = [
    {
      title: 0,
      subtitle: CONTENT.TOTAL_ORDER,
      titleClassName: 'fs20',
      subTitleClassName: 'fs14 text-99 mr-t5'
    },
    {
      title: 0,
      subtitle: CONTENT.ACCEPTED,
      titleClassName: 'fs20',
      subTitleClassName: 'fs14 text-99 mr-t5'
    },
    {
      title: 0,
      subtitle: CONTENT.REJECTED,
      titleClassName: 'fs20 text-red',
      subTitleClassName: 'fs14 text-99 mr-t5'
    },
    {
      title: 0,
      subtitle: CONTENT.PENDING,
      titleClassName: 'fs20 text-bright-yellow',
      subTitleClassName: 'fs14 text-99 mr-t5'
    }
  ];
  const calAmount = () => {
    const amount = { misMatch: 0, return: 0, total: 0 };
    // eslint-disable-next-line no-unused-expressions
    debitNoteDetailSuccess &&
      debitNoteDetailSuccess.items &&
      debitNoteDetailSuccess.items.forEach((element) => {
        const { price_mismatch_qty, unit_price, sub_total_amount, total_amount } = element;
        if (price_mismatch_qty) {
          amount.misMatch += price_mismatch_qty * unit_price;
        }
        if (!price_mismatch_qty) {
          amount.return += sub_total_amount;
          amount.total += total_amount;
        }
      });
    return amount;
  };

  const convertedCurrency = getCurrencySumbol(debitNoteDetailSuccess?.currency);

  const priceStripData = [
    {
      title: CONTENT.PRICE_MISMATCH,
      subtitle: `${convertedCurrency}${roundUptoFixedDigits(calAmount().misMatch, 2)}`,
      titleClassName: 'fs12 mr-r0',
      subTitleClassName: 'fs16 mr-t8 mr-r40'
    },
    {
      title: CONTENT.OTHER_RETURN,
      subtitle: `${convertedCurrency}${roundUptoFixedDigits(calAmount().return, 2)}`,
      titleClassName: 'fs12 mr-r40',
      subTitleClassName: 'fs16 mr-t8 mr-r40'
    },
    {
      title: CONTENT.TOTAL_WITH_TAX,
      subtitle: `${convertedCurrency}${roundUptoFixedDigits(calAmount().total, 2)}`,
      titleClassName: 'fs12',
      subTitleClassName: 'fs16 mr-t8 fw-bold'
    }
  ];

  const infoTableData = [
    { key: CONTENT.DEBIT_NOTE, value: debitNoteDetailSuccess?.debit_note_num },
    { key: CONTENT.STATUS, value: DebitNoteStatus[debitNoteDetailSuccess?.status]?.text },
    { key: CONTENT.TYPE, value: debitNoteDetailSuccess?.type },
    { key: CONTENT.CREATED_BY, value: 'Auto Generated' },
    {
      key: CONTENT.CREATED_ON,
      value:
        debitNoteDetailSuccess?.created_at &&
        convertDateFormat(debitNoteDetailSuccess?.created_at, 'shortDateTime')
    },
    { key: CONTENT.VENDOR_INVOICE_NO, value: debitNoteDetailSuccess?.vendor_inv_num },
    { key: CONTENT.NXS_INVOICE, value: debitNoteDetailSuccess?.invoice_num },
    {
      key: CONTENT.VENDOR_INV_DATE,
      value: debitNoteDetailSuccess?.invoice_date
        ? convertDateFormat(debitNoteDetailSuccess?.invoice_date, 'abbDate')
        : null
    },
    {
      key: CONTENT.PURCHASE_ORDER,
      value: debitNoteDetailSuccess?.po_num,
      // eslint-disable-next-line max-len
      link: `${window.location.origin}/po/detail?poNum=${debitNoteDetailSuccess?.po_num}&fromPo=true`
    },
    { key: CONTENT.VENDOR_CODE, value: debitNoteDetailSuccess?.vendor_code },
    { key: CONTENT.TOTAL_QTY, value: debitNoteDetailSuccess?.total_qty },
    {
      key: CONTENT.TOTAL_PRICE,
      value: `${convertedCurrency} ${roundUptoFixedDigits(
        debitNoteDetailSuccess?.total_debit_amount
      )}`
    }
  ];
  // Alt + D -> Open DN Detail Modal
  useKeyboardShortcut([18, 68], () => {
    setDetailModalOpen(true);
  });

  const toolTipElement = () => (
    <Box
      className="display-flex justify-content-space-between mr-10 cursor-pointer"
      width="100px"
      onClick={() => setShowPrintModal(true)}
    >
      <Box className="fs14">
        {' '}
        <img
          className="mr-r8"
          src={`${import.meta.env.VITE_STATIC_SERVER}/images/print.svg`}
          alt="img"
        />
        {CONTENT.PRINT}
      </Box>
      <Box className="fs14 text-99">{CONTENT.CTRL_P}</Box>
    </Box>
  );

  const printFunCall = () => {
    setShowPrintModal(false);
    dispatch(pdfDownloadLoad({ type: selectedPrintOption, id: dnNumTemp }));
  };

  useEffect(() => {
    if (pdfDownloadSuccess) {
      fileDownload(
        pdfDownloadSuccess,
        `${pdfNameMapping[selectedPrintOption]}-NEXS-${dnNumTemp}`,
        'pdf'
      );
      dispatch(pdfDownloadReset());
    }
    if (pdfDownloadError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: pdfDownloadError?.meta?.displayMessage,
          severity: 'error'
        })
      );

      dispatch(pdfDownloadReset());
    }
  }, [pdfDownloadError, pdfDownloadSuccess]);

  useEffect(() => {
    if (debitNoteDetailError) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: debitNoteDetailError?.meta?.displayMessage,
          severity: 'error',
          autoHideDuration: 3000
        })
      );
    }
  }, [debitNoteDetailError]);
  useEffect(() => {
    getDN();
  }, []);
  const changePrintOptions = useCallback(
    (e) => setSelectedPrintOption(e.target.value),
    [setSelectedPrintOption]
  );

  const goToPoNum = (po_id) => {
    window.open(`${window.location.origin}/po/detail?poNum=${po_id}&fromPo=true`, '_blank');
  };

  const infoStripData = [
    {
      key: CONTENT.DEBIT_NOTE,
      value: debitNoteDetailSuccess?.debit_note_num,
      status: DebitNoteStatus[debitNoteDetailSuccess?.status]?.text,
      type: DebitNoteStatus[debitNoteDetailSuccess?.status]?.type,
      className: 'fs16 fw-bold text-turquioise_surf cursor-pointer',
      onClick: () => setDetailModalOpen(true)
    },
    {
      key: CONTENT.DEBIT_NOTE_DATE,
      value: convertDateFormat(debitNoteDetailSuccess?.created_at, 'shortDate')
    },
    { key: CONTENT.VENDOR, value: debitNoteDetailSuccess?.vendor_name },
    {
      key: CONTENT.PO,
      value: debitNoteDetailSuccess?.po_num,
      className: 'fs16 fw-bold text-turquioise_surf cursor-pointer',
      onClick: () => goToPoNum(debitNoteDetailSuccess?.po_num)
    },
    {
      key: CONTENT.TOTAL,
      value: `${convertedCurrency}${roundUptoDigits(
        debitNoteDetailSuccess?.total_debit_amount || 0,
        2
      )}`
    }
  ];
  const subtitleInfoStrip = [
    {
      title: debitNoteDetailSuccess?.total_qty || 0,
      subtitle: CONTENT.QTY,
      titleClassName: 'fs20',
      subTitleClassName: 'fs14 text-99 mr-t5'
    },
    {
      title: debitNoteDetailSuccess?.out_of_po_qty || 0,
      subtitle: CONTENT.OUT_OF_PO,
      titleClassName: 'fs20',
      subTitleClassName: 'fs14 text-99 mr-t5'
    },
    {
      title: debitNoteDetailSuccess?.price_mismatch_qty || 0,
      subtitle: CONTENT.PRICE_MISMATCH,
      titleClassName: 'fs20',
      subTitleClassName: 'fs14 text-99 mr-t5'
    },
    {
      title: debitNoteDetailSuccess?.qc_fail_qty || 0,
      subtitle: CONTENT.QC_FAIL,
      titleClassName: 'fs20',
      subTitleClassName: 'fs14 text-99 mr-t5'
    }
  ];

  return !debitNoteDetailLoading && debitNoteDetailSuccess ? (
    <Box>
      {showPrintModal && (
        <PrintConfirmModal
          changePrintfun={changePrintOptions}
          printValueTemp={selectedPrintOption}
          open={showPrintModal}
          printFun={printFunCall}
          handleClose={() => setShowPrintModal(false)}
          printOptions={printOptions}
        />
      )}

      <InfoStrip data={infoStripData}>
        <MoreItem itemsToShow={toolTipElement()} />
      </InfoStrip>

      <Box className="pd-15 bg-white">
        <Box className="display-flex justify-content-space-between">
          <SubtitleInfoStrip data={subtitleInfoStrip} />
          <SubtitleInfoStrip data={priceStripData} />
        </Box>
      </Box>
      <Box pl={3} pr={3}>
        <DebitNoteTable
          CONTENT={CONTENT}
          items={debitNoteDetailSuccess.items ? debitNoteDetailSuccess.items : []}
          poNum={debitNoteDetailSuccess?.po_num}
          currency={convertedCurrency}
          taxes={getTaxType(debitNoteDetailSuccess?.applicable_tax_rates)}
        />
      </Box>
      <LkModal
        open={detailModalOpen}
        upperHeading={CONTENT.DETAILS}
        handleClose={() => setDetailModalOpen(false)}
      >
        <Box className="pos-rel">
          <FlexBox pt={2.5} justifyContent="space-between" width="750px">
            <InfoTable data={infoTableData} />
            <Box>
              <SubtitleInfoStrip data={subtitleInfoStripData} />
            </Box>
          </FlexBox>
        </Box>
      </LkModal>
    </Box>
  ) : (
    errorContainer()
  );
};

export default DebitNoteDetail;
