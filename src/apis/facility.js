import { convertToQueryString } from 'utils/helpers';
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const facilitySaveApi = (payload) => {
  const url = `${config.facility.saveFacility}`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const getFacilityTypeApi = () => tokenInstance().get(config.facility.facilityType);

export const facilityListApi = ({ payload }) =>
  tokenInstance().post(config.facility.facilityList, payload);

export const getTaxDetailsApi = (country, state = 'DEFAULT') => {
  const url = `${config.facility.taxDetails}?country=${country}&state=${state}`;
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const getPincodeDetailsApi = ({ pincode }) => {
  const url = `${config.facility.pinCode}${pincode}`;
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const uploadLogoApi = (payload) => {
  const url = `${config.facility.uploadLogo}${1}/uploadLogo`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const facilityDetailsApi = (id) => {
  const url = `${config.facility.facilityDetails}${id}`;
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const layoutSchemaListApi = (keyword = '') => {
  const url = `${config.facility.layoutSchema}?page_number=0&page_size=20&keyword=${keyword}`;
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const getEntitiesListApi = (payload, pagination) => {
  let url = `${config.facility.getEntitiesList}`;
  payload.forEach((t) => {
    // eslint-disable-next-line prefer-template
    url = url + '/' + t;
  });
  if (pagination) {
    const f = convertToQueryString(pagination);
    url = `${url}?${f}`;
  }
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const getCountryStateListApi = () => {
  const url = `${config.facility.countryState}`;
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const getDownloadEntityApi = ({
  facility_code,
  entity_type,
  parent_name,
  parent_type,
  parent_barcode
}) => {
  // ˝ /facility/{facility_code}/{entity_type}/download
  let url = `${config.facility.facilityDetails}${facility_code}/${entity_type}/download`;
  if (parent_name) {
    // eslint-disable-next-line max-len
    url = `${url}?parent_name=${parent_name}&parent_barcode=${parent_barcode}&parent_type=${parent_type}`;
  }
  return tokenInstance()
    .get(url)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const uploadFacilityEntityCsvApi = ({ facility_code, payload, entity_type }) => {
  const url = `${config.facility.facilityDetails}${facility_code}/${entity_type}/upload`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const exportEntityAsCsvApi = (payload) => {
  const { facility_code, entity_type } = payload;
  const url = `${config.facility.facilityDetails}${facility_code}/${entity_type}/export`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const bulkEditEntityApi = ({ facility_code, payload, type }) => {
  const url = `${config.facility.facilityDetails}${facility_code}/${type}/upload`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const editEntityApi = ({ facility_code, payload, entity_type }) => {
  const url = `${config.facility.facilityDetails}${facility_code}/${entity_type}/edit`;
  return tokenInstance()
    .post(url, payload)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

// /facility/LKH284/floor/delete?entity_id=1,2,3,4,6,15&is_storage=false
export const deleteEntityApi = ({ facility_code, entity_id, entity_type, storage }) => {
  // eslint-disable-next-line max-len
  const url = `${config.facility.facilityDetails}${facility_code}/${entity_type}/delete?entity_id=${entity_id}&is_storage=${storage}`;
  return tokenInstance()
    .post(url, null)
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });
};

export const legalOwnerListApi = (payload) =>
  tokenInstance().get(config.facility.legalOwnerList, { params: payload });
