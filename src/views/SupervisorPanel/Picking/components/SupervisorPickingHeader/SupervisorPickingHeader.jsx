import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import { LOGIN } from 'redux/reducers/login';
import { useSelector } from 'react-redux';
import usePermissions from 'common/usePermissions';
import UploadFastZonePid from './UploadFastZonePid';

const useStyles = makeStyles()(() => ({
  container: {
    padding: 25,
    boxSizing: 'border-box',
    borderBottom: '1px solid #E5E5E5',
    maxHeight: 89,
    background: '#FFFFFF'
  },
  btn: {
    width: 160,
    height: 38,
    fontSize: 15,
    lineHeight: '26px'
  },
  assignmentBtn: {
    borderRadius: '4px 0px 0px 4px'
  },
  skippedListBtn: {
    borderRadius: '0px 4px 4px 0px'
  },
  disabledBtn: {
    background: '#BFEEEF !important',
    color: '#3C3C3C !important'
  }
}));

const SupervisorPickingHeader = ({ CONTENT }) => {
  const { classes, cx } = useStyles();
  const navigate = useNavigate();
  const { tab } = useParams();
  const [isOpenCsvModal, setIsOpenCsvModal] = useState(false);
  const { permission } = useSelector((state) => state[LOGIN].user.userDetail) || {};
  const { isPickingSuperVisor } = usePermissions(permission);

  return (
    <Box display="flex" alignItems="center" justifyContent="space-between">
      <Box display="flex" flexDirection="row" className={classes.container}>
        <Button
          className={cx(classes.btn, classes.assignmentBtn, {
            [classes.disabledBtn]: tab !== 'assignment'
          })}
          variant="contained"
          color="primary"
          onClick={() => navigate('/supervisor/picking/assignment/pickerAllocation')}
        >
          {CONTENT.ASSIGNMENT}
        </Button>
        <Button
          className={cx(classes.btn, classes.skippedListBtn, {
            [classes.disabledBtn]: tab !== 'skipped'
          })}
          variant="contained"
          color="primary"
          onClick={() => navigate('/supervisor/picking/skipped/skipped')}
        >
          {CONTENT.SKIPPED_LIST}
        </Button>
      </Box>
      <Box marginRight="25px">
        <Button
          disabled={!isPickingSuperVisor}
          variant="contained"
          onClick={() => setIsOpenCsvModal(true)}
        >
          {CONTENT.UPLOAD_FAST_ZONE_PID}
        </Button>
        {isOpenCsvModal && <UploadFastZonePid setIsOpenCsvModal={setIsOpenCsvModal} />}
      </Box>
    </Box>
  );
};

export default React.memo(SupervisorPickingHeader);
