import React from 'react';
import { useNavigate } from 'react-router-dom';
import { convertDateFormat } from 'utils/helpers';
import { InfoStrip } from 'components/common';
import { PurchaseOrderStatus } from 'config/PurchaseOrderStatus';

import Button from '@mui/material/Button';
import AddCircle from '@mui/icons-material/AddCircle';

const VendorShipmentDetailsListHeader = ({ CONTENT, data, shipmentId }) => {
  const navigate = useNavigate();
  const infoStripData = [
    {
      key: CONTENT.PO_SHIPMENT,
      value: data.shipmentId ?? '-',
      status: data.status,
      type: PurchaseOrderStatus.APPROVED?.type,
      className: 'text-turquioise_surf'
    },
    {
      key: CONTENT.SHIPMENT_EXPECTED_DISPATCH_DATE,
      value: data?.expectedDispatchDate
        ? convertDateFormat(data.expectedDispatchDate, 'shortDate')
        : '-'
    },
    { key: CONTENT.VENDOR, value: data.vendorId ?? '-' },
    {
      key: CONTENT.PO,
      value: data.poNum ?? '-',
      type: 'parisGreen',
      className: 'fs16 fw-bold text-turquioise_surf cursor-pointer',
      onClick: () => data?.poNum && navigate(`/po/detail?poNum=${data?.poNum}`)
    },
    { key: CONTENT.TOTAL, value: data.shipmentQuantity ?? '-' }
  ];
  return (
    <InfoStrip data={infoStripData}>
      <Button
        variant="outlined"
        className="mr-r16 border-radius-8"
        data-cy="edit-bn"
        onClick={() => navigate(`/vendorShipment/edit/${shipmentId}`)}
      >
        {CONTENT.EDIT}
      </Button>
      <Button
        variant="outlined"
        className="border-radius-8"
        data-cy="add-pid-bn"
        onClick={() => navigate(`/vendorShipmentDetails/${shipmentId}`)}
        startIcon={<AddCircle />}
      >
        {CONTENT.ADD_PID}
      </Button>
    </InfoStrip>
  );
};

export default VendorShipmentDetailsListHeader;
