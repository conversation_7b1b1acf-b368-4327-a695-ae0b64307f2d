import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import FlagOutlinedIcon from '@mui/icons-material/FlagOutlined';
import { getConsulKeyValue } from 'redux/reducers/consul';
import { LOGIN } from 'redux/reducers/login';
import { LOCALISATION } from 'redux/reducers/localisation';
import { isEmpty } from 'utils/lodash';
import { Style_LensCard } from './Style';
import QCEMSActions from './QCEMSActions';

const styles = makeStyles()(() => ({ ...Style_LensCard }));

const handEdgingUsedMapping = {
  left: 'leftLensHandEdgingRequired',
  right: 'rightLensHandEdgingRequired'
};

const CL_OPTIONS_TO_RENDER = {
  sph: 'customerSPH',
  cyl: 'customerCYL',
  axis: 'customerAxis',
  ap: 'ap'
};
const FIRST_BOX_OPITONS = [
  { key: 'sph', value: 'SPH', class: 'a2' },
  { key: 'cyl', value: 'CYL', class: '94' },
  { key: 'axis', value: 'AXIS', class: '22' },
  { key: 'ap', value: 'AP', class: '22' },
  { key: 'pd', value: 'PD', class: '28' }
];
const SECOND_BOX_OPTIONS = [
  { key: 'bottomDistance', value: 'FH', class: 'a2' },
  { key: 'edgeDistance', value: 'A', class: '94' },
  { key: 'topDistance', value: 'B', class: '22' },
  { key: 'nearPD', value: 'DBL', class: '22' },
  { key: 'effectiveDia', value: 'ED', class: '28' }
];

const LensCard = (props) => {
  const { classes } = styles();
  const { eye, lens, qcHoldHandler, qcUnholdHandler, qcLensoMeterData } = props;
  const dispatch = useDispatch();
  const { BAROCDDE_CLASSIFICATION, CONTACT_LENS_CLASSIFICATIONS } = useSelector(
    (state) => state.consul.classification.data
  );
  const { qcData } = useSelector((state) => state.qc);
  const { lensFocus } = useSelector((state) => state.order);
  const {
    order: { uwItemId },
    order
  } = lens;
  const classification = lens?.order?.product?.classification || '';
  const QC = useSelector((state) => state[LOCALISATION].localeData.QC);
  const { orderBarcodeInfo } = useSelector((state) => state.order);
  const qc = qcData.find((q) => q.uwItemId === uwItemId);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  let coating = '';
  let showHoldCTA = true;
  if (
    !isEmpty(BAROCDDE_CLASSIFICATION) &&
    classification &&
    BAROCDDE_CLASSIFICATION.SINGLE_BARCODE.includes(classification)
  ) {
    showHoldCTA = false;
  }

  const packageName = lens?.order?.customOptions?.lensPackage || '';
  const coatingOption = lens?.order?.customOptions?.coating?.toLowerCase() || '';
  if (packageName.toLowerCase().includes('blu')) {
    if (coatingOption.includes('arc')) {
      coating = 'BLUECUT ARC';
    } else {
      coating = 'BLUECUT';
    }
  } else {
    coating = packageName;
  }

  /** Used to findout Is classification belongs to Contact Lens. */
  let isContactLens = false;
  if (
    !isEmpty(CONTACT_LENS_CLASSIFICATIONS) &&
    classification &&
    CONTACT_LENS_CLASSIFICATIONS.includes(classification)
  ) {
    isContactLens = true;
  }
  const barcodeVal = useMemo(() => {
    if (!qcData) {
      return null;
    }

    const eyeIndex = eye === 'right' ? 1 : 2;
    const eyeData = qcData[eyeIndex];

    return eyeData?.barcodeStatus !== 'IN_QC' ? eyeData?.barcode : null;
  }, [eye, qcData]);

  useEffect(() => {
    dispatch(getConsulKeyValue(['classification']));
  }, []);

  return (
    <div id="lens_container" className={classes.container}>
      {/* to show fail count */}
      {order?.barcode &&
      ((orderBarcodeInfo.qcFailCount &&
        Object.keys(orderBarcodeInfo.qcFailCount).length &&
        orderBarcodeInfo.qcFailCount[order.barcode] > 0) ||
        (orderBarcodeInfo.isReturnOrder && Object.keys(orderBarcodeInfo.isReturnOrder).length)) ? (
          <div className={classes.flagContainer}>
            <div className={classes.flagWrapper}>
              {orderBarcodeInfo.qcFailCount && orderBarcodeInfo.qcFailCount[order.barcode] > 0 ? (
                <>
                  <FlagOutlinedIcon className={classes.flagIcon} />
                  <div className={classes.flagStatus}>
                    {orderBarcodeInfo.qcFailCount[order.barcode]}x QC Failed
                  </div>
                </>
              ) : (
                ''
              )}
            </div>
            <div className={classes.flagWrapper}>
              {orderBarcodeInfo?.isReturnOrder?.[order.barcode] ? (
                <>
                  <FlagOutlinedIcon className={classes.flagIcon} />
                  <div className={classes.flagStatus}>Returned</div>
                </>
              ) : (
                ''
              )}
            </div>
          </div>
        ) : (
          ''
        )}
      <div className={classes.header}>{eye} lens</div>
      {/* {lens?.order?.customOptions?.lensName ?
                <div className={classes.description}>{lens.order.customOptions.lensName}</div>
                : ""
            } */}
      {lens?.order?.customOptions?.lensPackage ? (
        <div className={classes.description}>{lens.order.customOptions.lensPackage}</div>
      ) : (
        ''
      )}
      <div
        className={`mr-b10 mr-t8 ${classes.powerInfoBorder} powerInfo ${
          lensFocus === eye ? ' border-primary-2px' : null
        }`}
      >
        {lens?.order?.customOptions ? (
          <Box textAlign="center" className="display-flex justify-content-space-between">
            {FIRST_BOX_OPITONS.map((item, index) => {
              if (Object.keys(lens.order.customOptions).includes(item.key)) {
                const optionValue =
                  isContactLens && Object.keys(CL_OPTIONS_TO_RENDER).includes(item.key)
                    ? lens.order.customOptions[CL_OPTIONS_TO_RENDER[item.key]] || '-'
                    : lens.order.customOptions[item.key] || '-';
                const dynamicClassName = `fw-bold text-${
                  optionValue.includes('-') && optionValue.length > 1 ? 'red' : 'black'
                } 
                                    ${classes.marginTop5} ${
            optionValue.length > 5 ? classes.powerFontSizeSmall : classes.powerFontSize
          }`;
                return (
                  <div key={item.key}>
                    <div className={classes.widthPower}>
                      <div className={`fs16 text-66 ${classes.marginTop5}`}>{item.value}</div>
                      <div className={dynamicClassName}>{optionValue}</div>
                    </div>
                    {FIRST_BOX_OPITONS.length !== index + 1 && (
                      <div className={classes.seperator} />
                    )}
                  </div>
                );
              }
              return null;
            })}
          </Box>
        ) : (
          ''
        )}
        {lens?.order?.customOptions ? (
          <Box textAlign="center" className={classes.borderTopSpace}>
            {SECOND_BOX_OPTIONS.map((item, index) => {
              if (Object.keys(lens.order.customOptions).includes(item.key)) {
                const optionValue = lens.order.customOptions[item.key] || '-';
                const dynamicClassName = `fw-bold text-${
                  optionValue.includes('-') && optionValue.length > 1 ? 'red' : 'black'
                } ${classes.marginTop5} ${classes.fontSizePower}`;
                return (
                  <div key={item.key}>
                    <div className={classes.widthPower}>
                      <div className={`fs16 text-66 ${classes.marginTop5}`}>{item.value}</div>
                      <div className={dynamicClassName}>{optionValue}</div>
                    </div>
                    {SECOND_BOX_OPTIONS.length !== index + 1 && (
                      <div className={classes.seperator} />
                    )}
                  </div>
                );
              }
              return null;
            })}
          </Box>
        ) : (
          ''
        )}
      </div>
      {qcLensoMeterData ? (
        <>
          <div className={classes.qcLensoMeterHeading}>Lensometer Data</div>
          <div
            className={`mr-b20 mr-t8 ${classes.powerInfoBorder} powerInfo ${
              lensFocus === eye ? ' border-primary-2px' : null
            }`}
          >
            <Box textAlign="center" className="display-flex justify-content-space-between">
              {FIRST_BOX_OPITONS.map((item, index) => {
                if (Object.keys(qcLensoMeterData).includes(item.key)) {
                  const optionValue =
                    isContactLens && Object.keys(CL_OPTIONS_TO_RENDER).includes(item.key)
                      ? qcLensoMeterData[CL_OPTIONS_TO_RENDER[item.key]] || '-'
                      : qcLensoMeterData[item.key] || '-';
                  const dynamicClassName = `fw-bold ${classes.marginTop5} ${
                    optionValue.length > 5 ? classes.powerFontSizeSmall : classes.powerFontSize
                  } ${classes.qcLensoMeterPower}`;
                  return (
                    <div key={item.key}>
                      <div className={classes.widthPower}>
                        <div className={`fs16 text-66 ${classes.marginTop5}`}>{item.value}</div>
                        <div className={dynamicClassName}>{optionValue}</div>
                      </div>
                      {FIRST_BOX_OPITONS.length !== index + 1 && (
                        <div className={classes.seperator} />
                      )}
                    </div>
                  );
                }
                return null;
              })}
            </Box>
          </div>
        </>
      ) : (
        ''
      )}
      <div className={classes.lensInfo}>
        <div>{QC.LENS_ID}</div>
        <div className="fw-bold">{lens?.order?.product?.productId || '-'}</div>
      </div>
      <div className={classes.lensInfo}>
        <div>{QC.INDEX_ID}</div>
        <div className="fw-bold">{lens?.order?.customOptions?.lensIndex || '-'}</div>
      </div>
      <div className={classes.lensInfo}>
        <div>{QC.COATING}</div>
        <div className="fw-bold">{(lens && coating) || '-'}</div>
      </div>
      <div className={classes.lensInfo}>
        <div>{QC.HAND_EDGER_USED}</div>
        <div className="fw-bold">{orderBarcodeInfo[handEdgingUsedMapping[eye]] ? 'Yes' : 'No'}</div>
      </div>
      {barcodeVal && (
        <div className={classes.lensInfo}>
          <div>{QC.BARCODE}</div>
          <div className="fw-bold">{barcodeVal ?? '-'}</div>
        </div>
      )}
      <QCEMSActions
        showHoldCTA={showHoldCTA}
        qc={qc}
        userDetail={userDetail}
        qcHoldHandler={qcHoldHandler}
        qcUnholdHandler={qcUnholdHandler}
        uwItemId={uwItemId}
      />
    </div>
  );
};
export default LensCard;
