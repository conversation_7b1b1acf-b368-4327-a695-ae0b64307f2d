export default class FormValidator {
  constructor(validations) {
    this.validations = validations;
  }

  // Validates all fields when submit the form
  validateForm = (formData) => {
    // start out assuming valid
    const validation = { isValid: true, errors: {} };
    Object.entries(formData).map(([key, value]) => {
      const validationStatus = this.validateInput(key, value);
      if (!validationStatus.isValid) {
        validation.errors[key] = validationStatus.message;
      }
      return true;
    });
    if (Object.keys(validation.errors).length > 0) {
      validation.isValid = false;
    }

    return validation;
  };

  // Validates a single field
  validateInput = (field, value) => {
    // start out assuming valid
    const validation = { isValid: true, message: '' };
    const rules = this.validations;
    const fieldRules = rules.filter((rule) => rule.field === field);
    fieldRules.some((rule) => {
      const status = rule.method(value);
      const regex = rule?.pattern ? new RegExp(rule.pattern) : null;
      
      if (status || (regex && !regex.test(value))) {
        validation.isValid = false;
        validation.message = rule.message;
        return true;
      }

      return false;
    });
    return validation;
  };
}
