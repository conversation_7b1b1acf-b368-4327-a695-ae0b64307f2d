import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { updateFilterFormat } from './monitor';

export const getBarcodeItemDetailsApi = (payload, totalCountRequired = true) =>
  tokenInstance().post(config.inventory.fetchBarcodeItemDetails, payload.requestPayload, {
    params: {
      page: payload.page,
      size: payload.size,
      sortBy: payload.sortBy,
      sortOrder: payload.sortOrder,
      totalCountRequired
    }
  });

export const getPIDdetailsApi = (pid) => {
  const url = config.inventory.PIDdetails;
  return tokenInstance({ 'x-api-client': 'desktop' }).get(url, { params: { pid } });
};

export const getPidTotalCountApi = (pid) =>
  tokenInstance().get(`${config.inventory.pidTotalCount}/${pid}`);

export const getWarehouseListApi = (searchTerms) =>
  tokenInstance().get(config.inventory.warehouseList, { params: { searchTerms } });

export const getWarehouseDetailsListApi = (payload) =>
  tokenInstance().get(config.inventory.warehouseDetailsList, { params: payload });

export const getInventoryOrderTypeListApi = (payload) =>
  tokenInstance().post(config.inventory.inventoryOrderTypeList, payload);

export const getInventoryBarcodeDetailsListApi = (payload) => {
  const {
    filters: { productDetailsFilter, ...filters },
    ...data
  } = payload;
  const filterPayload = updateFilterFormat(filters, payload.version);
  filterPayload.productDetailsFilter = productDetailsFilter;
  const url = `${config.inventory.wareHouseBarcodeDetailsList}?version=${payload.version}`;
  return tokenInstance().post(url, {
    ...data,
    monitorPanelFilters: filterPayload
  });
};
