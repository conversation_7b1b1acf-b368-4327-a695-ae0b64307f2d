import React from 'react';
import LkModal from '../../Modal/Modal';
import Button from '@mui/material/Button';
import Spinner from 'components/Spinner/Spinner';

const UploadingFile = props => {
  const { open, handleClose, file, cancelFile, uploading, importFile, localUpload, importLoading } = props;
  return (
    <LkModal open={open} title="Uploading File" subTitle="Please wait while we upload your CSV file" handleClose={() => handleClose()}>
      <div className="upload-csv mr-40 text-center">
        <div className={`file-container ${(uploading || localUpload) ? 'display-flex' : 'display-none'} justify-content-space-between border-grey5-radiusbase  pd-12`}>
          <div className="pos-rel">
            <img src={import.meta.env.VITE_STATIC_SERVER + '/images/file.svg'} alt="img" />
            <img className="tick-mark-img pos-abs bg-white" src={import.meta.env.VITE_STATIC_SERVER + '/images/check-circle.svg'} alt="img" />
          </div>
          <div className="fs12 align-self-center mr-l12 mr-r20">
            {file?.name}
          </div>
          <div className="cursor-pointer" onClick={() => cancelFile()}>
            <img data-cy="remove-file-icon" src={import.meta.env.VITE_STATIC_SERVER + '/images/cancel.svg'} alt="img" />
          </div>
          {/*<IconButton onClick={() => cancelFile()}><Close /></IconButton>*/}
        </div>
        <div className={`fs24 fw-bold mr-t40 mr-b5 justify-content-center ${(uploading || localUpload) ? 'display-none' : 'display-flex'}`}>
          <img src={import.meta.env.VITE_STATIC_SERVER + '/images/tickmark.svg'} alt="img" />
        </div>

        <div className="display-flex justify-content-center mr-b40 mr-t40">
          <div className="mr-r5">
            <Button style={{ borderRadius: 8 }} variant="outlined" color="primary"
              onClick={() => handleClose()}>CANCEL</Button>
          </div>
          <div className="mr-l5">
            {(uploading || importLoading) && <Button disabled style={{ borderRadius: 8 }} variant="contained" color="primary"
              onClick={() => importFile()}><Spinner /> </Button>}
            {(!uploading && !importLoading) && <Button data-cy="import-btn" disabled={uploading && !localUpload} style={{ borderRadius: 8 }} variant="contained" color="primary"
              onClick={() => importFile()}>IMPORT</Button>}
          </div>
        </div>
      </div>
    </LkModal>
  )
};

export default UploadingFile;
