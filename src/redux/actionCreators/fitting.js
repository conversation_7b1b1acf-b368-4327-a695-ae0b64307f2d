import * as action from '../actionTypes/fitting';

export const fittingDataRequest = (data) => ({
  type: action.FITTING_DATA_REQUEST,
  payload: data
});

export const fittingDataRequestSuccess = (data) => ({
  type: action.FITTING_DATA_REQUEST_SUCCESS,
  payload: data
});

export const fittingQCDataRequestSuccess = (data) => ({
  type: action.FITTING_QC_DATA_REQUEST_SUCCESS,
  payload: data
});

export const fittingDataRequestError = (data) => ({
  type: action.FITTING_DATA_REQUEST_ERROR,
  payload: data
});

export const fittingDataReset = () => ({
  type: action.FITTING_DATA_RESET
});

export const completeFittingRequest = (data) => ({
  type: action.FITTING_COMPLETE_REQUEST,
  payload: data
});

export const completeFittingRequestSuccess = (data) => ({
  type: action.FITTING_COMPLETE_SUCCESS,
  payload: data
});

export const completeFittingRequestError = (data) => ({
  type: action.FITTING_COMPLETE_ERROR,
  payload: data
});
