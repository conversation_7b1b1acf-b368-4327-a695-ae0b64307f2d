import React, { useCallback, useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { makeStyles } from 'tss-react/mui';
import Checkbox from '@mui/material/Checkbox';

import useWindowResize from 'common/useWindowResize';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import { genericDateFormatted } from 'utils/helpers';
import localStorageHelper from 'utils/localStorageHelper';
import { marKJitTrayDamagedLoad } from 'redux/reducers/jitOrderSearch';
import { LOCALISATION } from 'redux/reducers/localisation';
import MarkDamageModal from './MarkDamageModal';
import { JIT_TRAY_SCAN } from '../../../redux/reducers/jitOrderSearch';
import Spinner from '../../../components/Spinner/Spinner';

const useStyles = makeStyles()(() => ({
  tableContainer: {
    backgroundColor: '#fff',
    paddingBlock: 15,
    paddingInline: 25
  },
  headingContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    paddingLeft: 6,
    marginBottom: 25
  },
  labelContainer: {
    display: 'flex',
    gap: 18
  },
  textContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: 10
  },
  fittingId: {
    marginTop: -4
  },
  shippingPackageId: {
    fontSize: 12,
    color: '#3c3c3c8a'
  }
}));

const JitScanTable = ({ jitOrderStatusDetailsList, jitTrayScanLoading, shippingPackageId }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { barcode } = useParams();

  const [tableHeight] = useWindowResize(window.innerHeight - 600);

  const [selectedItems, setSelectedItems] = useState([]);
  const [openMarkDamageModal, setOpenMarkDamageModal] = useState(false);

  const facility = localStorageHelper.getItem('facility-code');

  const { isLoading } = useSelector((state) => state[JIT_TRAY_SCAN].marKDamaged);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.JIT_ORDER_SEARCH);

  useEffect(() => {
    if (jitTrayScanLoading) {
      setSelectedItems([]);
    }
  }, [jitTrayScanLoading]);

  useEffect(() => () => setOpenMarkDamageModal(false), [barcode]);

  const handleSubmit = (reason) => {
    navigate(`/autojit/ordersearch/${jitOrderStatusDetailsList[0].fittingId}`);
    const sourceRefId = selectedItems.length === 1 ? selectedItems[0].sourceReferenceId : '';
    dispatch(
      marKJitTrayDamagedLoad({
        jobId: String(selectedItems[0].fittingId),
        facility,
        status: 'BREAKAGE',
        reason,
        sourceRefId
      })
    );
  };

  const isMarkDamageButtonDisabled = (lkStatus) =>
    !(lkStatus === 'BLANK_PICKED' || lkStatus === 'IN_COATING');

  const isProductionDoneButtonDisabled = () => {
    let blankPickedBarCodes = 0;
    let differentBarcode = false;
    // eslint-disable-next-line
    for (const orderItem of jitOrderStatusDetailsList) {
      if (orderItem.lkStatus === 'BLANK_PICKED') {
        blankPickedBarCodes += 1;
      } else if (orderItem.lkStatus !== 'PRODUCTION_DONE') {
        differentBarcode = true;
      }
    }
    return differentBarcode || blankPickedBarCodes === 0;
  };

  const selectAll = () => {
    if (selectedItems.length !== 0 && jitOrderStatusDetailsList.length === selectedItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(jitOrderStatusDetailsList);
    }
  };

  const toggleSelection = (item) => {
    if (
      selectedItems.find(({ sourceReferenceId }) => sourceReferenceId === item.sourceReferenceId)
    ) {
      setSelectedItems(
        selectedItems.filter((eachItem) => eachItem.sourceReferenceId !== item.sourceReferenceId)
      );
    } else {
      setSelectedItems([...selectedItems, item]);
    }
  };

  const disableAllSelect = () => {
    if (jitTrayScanLoading || jitOrderStatusDetailsList.length === 0) {
      return true;
    }
    return jitOrderStatusDetailsList.some(({ lkStatus }) => isMarkDamageButtonDisabled(lkStatus));
  };

  const headerConfig = [
    {
      name: '',
      key: 'checkBoxSelect',
      formatHeader: useCallback(
        () => (
          <Checkbox
            data-cy="select-all-checkbox"
            checked={
              selectedItems.length > 0 && jitOrderStatusDetailsList?.length === selectedItems.length
            }
            disabled={disableAllSelect()}
            color="primary"
            inputProps={{ 'aria-label': 'select all manifest Shipments' }}
            onClick={selectAll}
          />
        ),
        [selectedItems, jitTrayScanLoading, jitOrderStatusDetailsList]
      ),
      formatBody: useCallback(
        (item) => {
          const isItemSelected = selectedItems.find(
            ({ sourceReferenceId }) => sourceReferenceId === item.sourceReferenceId
          );

          return (
            <Checkbox
              data-cy="select-checkbox"
              onClick={() => toggleSelection(item)}
              checked={!!isItemSelected}
              inputProps={{ 'aria-labelledby': item.sourceReferenceId }}
              color="primary"
              disabled={jitTrayScanLoading || isMarkDamageButtonDisabled(item.lkStatus)}
            />
          );
        },
        [selectedItems, jitTrayScanLoading]
      ),
      style: { minWidth: 50, maxWidth: 50 }
    },
    {
      name: CONTENT.UW_ITEM_ID,
      key: 'sourceReferenceId',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.PID,
      key: 'actualPid',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.BLANK_PID,
      key: 'blankPid',
      style: { minWidth: 120, maxWidth: 120 }
    },
    {
      name: CONTENT.BARCODE,
      key: 'barcode',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.STATUS,
      key: 'orderStatus',
      style: { minWidth: 140, maxWidth: 140 }
    },
    {
      name: CONTENT.RXU_STATUS,
      key: 'rxString',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.ORDER_TYPE,
      key: 'orderType',
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.LENS_TYPE,
      key: 'lens',
      formatBody: useCallback(
        ({ lens }) => <Typography>{lens === 'L' ? 'Left' : 'Right'}</Typography>,
        []
      ),
      style: { minWidth: 100, maxWidth: 100 }
    },
    {
      name: CONTENT.JIT_STATUS,
      key: 'lkStatus'
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updatedAt',
      formatBody: useCallback(
        ({ updated_at }) => <Box fontSize="bold">{genericDateFormatted(updated_at)}</Box>,
        []
      )
    },
    {
      name: CONTENT.UPDATED_BY,
      key: 'updatedBy',
      style: { minWidth: 100, maxWidth: 100 }
    }
  ];

  return (
    <Box className={classes.tableContainer} data-cy="table-container">
      <Box className={classes.headingContainer} data-cy="heading-container">
        <Box className={classes.labelContainer}>
          <Box className={classes.textContainer}>
            <Typography variant="subtitle2" data-cy="fitting-label">
              {CONTENT.FITTING_ID}
            </Typography>
            <Typography className={classes.fittingId} data-cy="fitting-value">
              {jitOrderStatusDetailsList.length > 0 ? jitOrderStatusDetailsList[0].fittingId : '-'}
            </Typography>
          </Box>
          <Box className={classes.textContainer}>
            <Typography variant="subtitle2" data-cy="shipping-package-label">
              {CONTENT.SHIPPING_PACKAGE_ID}
            </Typography>
            <Link
              target="_blank"
              data-cy="shipping-package-value"
              to={`/order/${shippingPackageId}/overview`}
            >
              {shippingPackageId}
            </Link>
          </Box>
        </Box>
        <Box className={classes.labelContainer}>
          <Button
            data-cy="mark-damage"
            color="error"
            size="small"
            variant="contained"
            disabled={selectedItems.length === 0}
            onClick={() => {
              setOpenMarkDamageModal((prevValue) => ({
                ...prevValue,
                modalOpen: true,
                jobId: '',
                sourceRefId: ''
              }));
            }}
          >
            {CONTENT.MARK_DAMAGED}
          </Button>
          <Button
            data-cy="production-done"
            disabled={isLoading || isProductionDoneButtonDisabled()}
            onClick={() => {
              navigate(`/autojit/ordersearch/${jitOrderStatusDetailsList[0].fittingId}`);
              dispatch(
                marKJitTrayDamagedLoad({
                  jobId: jitOrderStatusDetailsList[0].fittingId,
                  status: 'PRODUCTION_DONE',
                  facility,
                  sourceRefId: '',
                  reason: ''
                })
              );
            }}
            variant="contained"
          >
            {isLoading ? <Spinner /> : CONTENT.PRODUCTION_DONE}
          </Button>
        </Box>
      </Box>
      <LkTable
        tableHeight={tableHeight}
        headerConfig={headerConfig}
        tableData={jitOrderStatusDetailsList}
        isDataFetching={jitTrayScanLoading}
        totalRowsCount={jitOrderStatusDetailsList?.length}
        isNonVertualizedTable
      />
      {openMarkDamageModal && (
        <MarkDamageModal
          selectedItems={selectedItems}
          handleClose={() => setOpenMarkDamageModal(false)}
          primaryBtnFn={handleSubmit}
          setOpenMarkDamageModal={setOpenMarkDamageModal}
        />
      )}
    </Box>
  );
};

export default JitScanTable;
