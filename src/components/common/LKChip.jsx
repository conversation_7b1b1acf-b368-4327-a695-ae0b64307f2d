import React from 'react';

import Box from '@mui/material/Box';
import CircleIcon from '@mui/icons-material/Circle';

const LKChip = ({ color, value }) => (
  <Box
    sx={{
      border: '1px solid #dddddd',
      borderRadius: '16px',
      display: 'flex',
      width: 110,
      justifyContent: 'space-around',
      alignItems: 'center',
      padding: '3px 2px'
    }}
  >
    <CircleIcon fontSize="18px" color={color} />
    {value}
  </Box>
);
export default LKChip;
