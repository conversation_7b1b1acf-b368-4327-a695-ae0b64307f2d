import { all } from 'redux-saga/effects';
import { runPurchaseOrderCalls } from './puchaseOrder';
import { runGrnCalls } from './grn';
import { runInvoiceDashboardCalls } from './invoiceDashboard';
import { runInvoiceViewCalls } from './invoiceView';
import { runGrnListingCalls } from './grnListing';
import { runGrnDetailsCalls } from './grnDetails';
import { runInvoiceReferenceCreationCalls } from './invoiceReferenceCreation';
import { runDebitNoteCalls } from './debitNote';
import { runVendorMasterCalls } from './vendorMaster';
import { settingsCalls } from './settings';
import { runPdfDownloadCalls } from './pdfDownload';
import { runFiltersCalls } from './filters';
import { runLoginCalls } from './login';
import { runPutawayCalls } from './putaway';
import { runConstraintCalls } from './constraint';
import { runLayoutCalls } from './layout';
import { runFacilityCalls } from './facility';
import { runTransferCalls } from './bulkInventory/transferReceive';
import { runPutAwayListCalls } from './putAwayList';
import { runPutawayDetailCalls } from './putawayDetail';
import runBarcodeHistoryCalls from './barcodeHistory';
import runManifestListCalls from './manifest';
import { runFittingCalls } from './fitting';
import { runtrayReassignCalls } from './trayReassign';
import { runSupervisorPickingSkippedCalls } from './supervisorPickingSkipped';
import runStorePackingCalls from './storePacking';
import { runMEIEdgingCalls } from './MEIEdging';
import runMonitorCalls from './monitor';
import { runInventoryCalls } from './inventory';
import runConsolidationCalls from './consolidation';
import { runBulkInventoryCalls } from './bulkInventory/bulkInventory';
import { runOrderCalls } from './order';
import { runPackingCalls } from './packing';
import { runQCCalls } from './qc';
import { runPrintShipment } from './printShipment';
import runBarcodeSeriesCalls from './barcodeSeries';
import runOrderDetailsCalls from './orderDetails';
import runCycleCountWebCalls from './cycleCount';
import runPickingListCalls from './pickingListSaga';
import runRegisterCall from './register';
import runEInvoiceCall from './e-invoice';
import runRefurbtCalls from './refurb';
import runInvoiceCalls from './invoicePosting';
import createPutawayCalls from './createPutaway';
import runSupervisorPickingAssignmentCall from './supervisorPickingAssignment';
import runD365Call from './d365';
import runAsrsDiscrepancyCalls from './asrsDiscrepancySaga';
import runUserManagementCalls from './userManagement';
import runAdhocASRSPickingCall from './adhocASRSPicking';
import runInwardQCCalls from './inwardQC';
import runManifestShippingMethodsCalls from './manifestShippingMethod';
import runShipmentBagsCalls from './shipmentBags';
import runCourierCutoffCall from './courierCutoff';
import runAddInventoryCalls from './addInventory';
import storeConsolidationCalls from './storeConsolidation';
import runLocalisationSaga from './localisation';
import runConsulCall from './consul';
import runStartRetireCalls from './startRetire';
import runPriceControlCall from './priceControl';
import runReactiveTrasnferCalls from './reactiveTransfer';
import runJitTrayCalls from './jitOrderSearch';
import runHandEdgingCalls from './handEdging';
import runMEIBlockingCalls from './meiBlocking';
import runEdgingQCCalls from './edgingQC';
import runDistributorOrdersCalls from './distributorOrders';
import runFinanceDashboardSagaCall from './finance';
import runPickingScanCalls from './pickingScan';
import runDoShipmentListCalls from './doShipmentList';
import runReportingToolCalls from './reportingTool';
import runCustomerCalls from './customer';
import runVendorShipmentCalls from './vendorShipment';
import runStockTakeCalls from './stockTake';
import runStockTakeBarcodeScanCalls from './stockTakeBarcodeScan';
import runBulkOrderPickingCalls from './bulkOrderPicking';
import runRTVListCalls from './RTVlist';
import runRTVScanCalls from './RTVbarcodeScan';
import runAutoGrnCalls from './autoGrn';
import runAsrsManualSyncCalls from './asrsManualSync';

function* index() {
  yield all([
    runPurchaseOrderCalls(),
    runGrnCalls(),
    runInvoiceDashboardCalls(),
    runInvoiceViewCalls(),
    runGrnListingCalls(),
    runGrnDetailsCalls(),
    runInvoiceReferenceCreationCalls(),
    runDebitNoteCalls(),
    runVendorMasterCalls(),
    settingsCalls(),
    runPdfDownloadCalls(),
    runFiltersCalls(),
    runLoginCalls(),
    runPutawayCalls(),
    runConstraintCalls(),
    runLayoutCalls(),
    runFacilityCalls(),
    runTransferCalls(),
    runPutAwayListCalls(),
    runPutawayDetailCalls(),
    runBarcodeHistoryCalls(),
    runManifestListCalls(),
    runFittingCalls(),
    runtrayReassignCalls(),
    runInventoryCalls(),
    runSupervisorPickingSkippedCalls(),
    runMEIEdgingCalls(),
    runMonitorCalls(),
    runConsolidationCalls(),
    runBulkInventoryCalls(),
    runOrderCalls(),
    runPackingCalls(),
    runQCCalls(),
    runPrintShipment(),
    runStorePackingCalls(),
    runBarcodeSeriesCalls(),
    runOrderDetailsCalls(),
    runCycleCountWebCalls(),
    runRegisterCall(),
    runPickingListCalls(),
    runEInvoiceCall(),
    runRefurbtCalls(),
    runInvoiceCalls(),
    createPutawayCalls(),
    runSupervisorPickingAssignmentCall(),
    runD365Call(),
    runAsrsDiscrepancyCalls(),
    runUserManagementCalls(),
    runAdhocASRSPickingCall(),
    runInwardQCCalls(),
    runManifestShippingMethodsCalls(),
    runShipmentBagsCalls(),
    runCourierCutoffCall(),
    runAddInventoryCalls(),
    storeConsolidationCalls(),
    runLocalisationSaga(),
    runConsulCall(),
    runStartRetireCalls(),
    runPriceControlCall(),
    runReactiveTrasnferCalls(),
    runJitTrayCalls(),
    runHandEdgingCalls(),
    runMEIBlockingCalls(),
    runEdgingQCCalls(),
    runDistributorOrdersCalls(),
    runFinanceDashboardSagaCall(),
    runPickingScanCalls(),
    runDoShipmentListCalls(),
    runRefurbtCalls(),
    runReportingToolCalls(),
    runCustomerCalls(),
    runVendorShipmentCalls(),
    runStockTakeCalls(),
    runStockTakeBarcodeScanCalls(),
    runBulkOrderPickingCalls(),
    runRTVListCalls(),
    runRTVScanCalls(),
    runAutoGrnCalls(),
    runAsrsManualSyncCalls()
  ]);
}

export default index;
