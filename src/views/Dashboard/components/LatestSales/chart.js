import palette from 'theme/palette';

export const data = {
  labels: ['1 Aug', '2 Aug', '3 Aug', '4 Aug', '5 Aug', '6 Aug'],
  datasets: [
    {
      label: 'This year',
      backgroundColor: palette.primary.main,
      data: [18, 5, 19, 27, 29, 19, 20],
      barThickness: 12,
      maxBarThickness: 10,
      barPercentage: 0.5,
      categoryPercentage: 0.5,
      borderRadius: 100
    },
    {
      label: 'Last year',
      backgroundColor: palette.neutral,
      data: [11, 20, 12, 29, 30, 25, 13],
      barThickness: 12,
      maxBarThickness: 10,
      barPercentage: 0.5,
      categoryPercentage: 0.5,
      borderRadius: 100
    }
  ]
};

export const options = {
  responsive: true,
  maintainAspectRatio: false,
  animation: false,
  plugins: {
    legend: { display: false },
    title: { display: false },
    tooltip: {
      enabled: true,
      mode: 'index',
      intersect: false,
      borderWidth: 1,
      borderColor: palette.divider,
      backgroundColor: palette.white,
      titleColor: palette.text.primary,
      bodyColor: palette.text.secondary,
      footerColor: palette.text.secondary
    }
  },
  layout: { padding: 0 },
  scales: {
    x: {
      ticks: {
        color: palette.text.secondary
      },
      grid: {
        display: false,
        drawBorder: false
      }
    },
    y: {
      ticks: {
        color: palette.text.secondary,
        beginAtZero: true,
        min: 0
      },
      grid: {
        borderDash: [2],
        color: palette.divider,
        drawBorder: false,
        zeroLineColor: palette.divider
      }
    }
  }
};
