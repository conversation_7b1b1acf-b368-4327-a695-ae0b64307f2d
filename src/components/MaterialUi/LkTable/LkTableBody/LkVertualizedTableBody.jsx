import React, { useCallback, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import TableBody from '@mui/material/TableBody';
import { makeStyles } from 'tss-react/mui';
import AutoSizer from 'react-virtualized-auto-sizer';
import InfiniteLoader from 'react-window-infinite-loader';
import { VariableSizeList } from 'react-window';
import { get } from 'utils/lodash';
import LkTableBodyRow from './LkTableBodyRow';
import LKTableNoDataRow from '../LKTableNoDataRow';

const hasMoreItemsToload = (totalRowsCount, currentDataCount, PageSize) => {
  const totalPage = Math.ceil(totalRowsCount / PageSize);
  const currentPage = Math.ceil(currentDataCount / PageSize);
  return totalPage !== currentPage;
};

const useStyles = makeStyles()(() => ({
  tableBody: {
    height: '100%',
    width: '100%'
  },
  variableSizeList: {
    overflowX: 'hidden !important'
  }
}));

const LkVertualizedTableBody = ({
  headerConfig,
  isDataFetching,
  tableData,
  pageSize,
  totalRowsCount,
  currentPage,
  setNextPage,
  selectedRows,
  warnedRows,
  tableBodyRef,
  rowSize,
  rowExtendedSize,
  renderExtenedComponent,
  columnWidth,
  plainTheme,
  noDataText,
  border,
  rowKey = null,
  onRowClick = null
}) => {
  const { classes } = useStyles();
  const listRef = useRef();
  const [rowsConfig, setRowsConfig] = useState({});
  const isItemLoaded = (index) => index < tableData.length;
  const hasMoreItemsToLoad = hasMoreItemsToload(totalRowsCount, tableData.length, pageSize);

  const toggleRowSize = useCallback((index) => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(index);
    }
    setRowsConfig((prevState) => {
      let newRowConfig = {};
      const targerRow = prevState[index];
      if (targerRow && targerRow.expanded) {
        newRowConfig = { ...prevState[index], expanded: false, size: rowSize };
      } else {
        newRowConfig = { ...prevState[index], expanded: true, size: rowExtendedSize };
      }
      return {
        ...prevState,
        [index]: newRowConfig
      };
    });
  }, []);

  const renderFixedSizeRow = useCallback(
    ({ index, style }) => {
      const isLoading = !isItemLoaded(index);
      const rowData = tableData[index];
      return (
        <LkTableBodyRow
          rowIndex={index}
          id={get(rowData, rowKey, null)}
          selectedRows={selectedRows}
          warnedRows={warnedRows}
          isLoading={isLoading}
          style={style}
          headerConfig={headerConfig}
          rowData={rowData}
          isExpanded={style.height === rowExtendedSize}
          rowSize={rowSize}
          toggleRowSize={() => toggleRowSize(index)}
          renderExtenedComponent={renderExtenedComponent}
          columnWidth={columnWidth}
          onRowClick={onRowClick}
          plainTheme={plainTheme}
          border={border}
        />
      );
    },
    [tableData, toggleRowSize, headerConfig]
  );

  const getRowSize = (index) => {
    if (rowsConfig[index]) {
      return rowsConfig[index].size;
    }
    return rowSize;
  };

  const renderAutoSizer = ({ height, width }) => (
    <InfiniteLoader
      itemCount={totalRowsCount}
      isItemLoaded={isItemLoaded}
      threshold={10}
      loadMoreItems={(_, b) => {
        if (hasMoreItemsToLoad && Math.floor(b / pageSize) > currentPage) {
          setNextPage();
        }
      }}
    >
      {({ onItemsRendered, ref }) => {
        const setRef = (cusRef) => {
          listRef.current = cusRef;
          ref(cusRef);
        };
        return (
          <VariableSizeList
            height={height}
            width={width}
            itemSize={getRowSize}
            itemCount={
              totalRowsCount > tableData.length
                ? tableData.length + pageSize
                : totalRowsCount || pageSize
            }
            onItemsRendered={onItemsRendered}
            ref={setRef}
            outerRef={tableBodyRef}
            className={classes.variableSizeList}
          >
            {renderFixedSizeRow}
          </VariableSizeList>
        );
      }}
    </InfiniteLoader>
  );

  const renderTableBody = () => {
    if (tableData.length === 0 && !isDataFetching) {
      return <LKTableNoDataRow noDataText={noDataText} />;
    }
    return <AutoSizer>{renderAutoSizer}</AutoSizer>;
  };

  return (
    <TableBody data-cy="table-body-container" component="div" className={classes.tableBody}>
      {renderTableBody()}
    </TableBody>
  );
};

export default React.memo(LkVertualizedTableBody);

LkVertualizedTableBody.propTypes = {
  headerConfig: PropTypes.arrayOf(PropTypes.instanceOf(Object).isRequired).isRequired,
  isDataFetching: PropTypes.bool.isRequired,
  tableData: PropTypes.instanceOf(Array).isRequired,
  pageSize: PropTypes.number.isRequired,
  totalRowsCount: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  setNextPage: PropTypes.func.isRequired,
  rowKey: PropTypes.string,
  selectedRows: PropTypes.instanceOf(Array).isRequired,
  warnedRows: PropTypes.instanceOf(Array).isRequired,
  rowSize: PropTypes.number.isRequired,
  rowExtendedSize: PropTypes.number.isRequired,
  columnWidth: PropTypes.number.isRequired,
  noDataText: PropTypes.string.isRequired,
  onRowClick: PropTypes.func
};
