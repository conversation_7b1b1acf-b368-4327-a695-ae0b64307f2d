import React, { useEffect, useRef } from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import './carousel.scss';

const Carousel = (props) => {
  const { currentSlide, slides, customSettings, isLeftRightPadding, carousalRef, setCarousalRef } =
    props;
  const slider = useRef();

  useEffect(() => {
    if (!carousalRef?.current?.slickGoTo && setCarousalRef && slider?.current?.slickGoTo) {
      setCarousalRef(slider);
    }
  }, [carousalRef]);

  return (
    <div
      className={`slider-main carousel-comp-slider carouse-container ${
        isLeftRightPadding ? 'pd-l40 pd-r40' : ''
      }`}
    >
      <Slider ref={slider} {...customSettings} initialSlide={currentSlide}>
        {slides?.map((slide) => slide)}
      </Slider>
    </div>
  );
};
export default Carousel;
