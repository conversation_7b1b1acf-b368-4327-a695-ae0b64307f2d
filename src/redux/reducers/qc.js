/* eslint-disable no-case-declarations */
import * as action from 'redux/actionTypes/qc';

const initialState = {
  qcHoldReasons: {},
  qcData: [],
  qcDataFitting: [],
  qcCompleteData: undefined,
  qcCompleteFail: null,
  qcCompleteLoading: false,
  printInvoiceData: undefined,
  printInvoiceLoading: false,
  isDamaged: {
    isLoading: true,
    status: null
  },
  qcLensoMeterData: {
    isLoading: false
  }
};

const qc = (state = initialState, { type, payload }) => {
  switch (type) {
    case action.QC_HOLD_REASONS_SUCCESS:
      const reasons = { ...state.qcHoldReasons };
      reasons[payload.id] = payload.data.data;
      return { ...state, qcHoldReasons: reasons };
    case action.QC_HOLD_REASONS_FAIL:
      return { ...state };
    case action.CREATE_QC_DATA:
      return { ...state, qcCompleteData: undefined, qcCompleteFail: null, qcData: payload };
    case action.UPDATE_QC_DATA:
      // eslint-disable-next-line no-shadow
      state.qcData.forEach((qc, i) => {
        if (qc.uwItemId === payload.uwItemId) {
          // eslint-disable-next-line no-param-reassign
          state.qcData[i] = { ...qc, ...payload.data };
        }
      });
      // Object.assign(state.qcData.find(qc => qc.uwItemId === payload.uwItemId), payload.data);
      return { ...state, qcData: [...state.qcData] };
    case action.MARK_QC_DONE_LOAD:
      return { ...state, qcCompleteData: undefined, qcCompleteFail: null, qcCompleteLoading: true };
    case action.MARK_QC_DONE_SUCCESS:
      return {
        ...state,
        qcCompleteData: payload.data,
        qcCompleteFail: null,
        qcData: payload.qcData,
        qcCompleteLoading: false
      };
    case action.MARK_QC_DONE_FAIL:
      return { ...state, qcCompleteFail: payload, qcCompleteLoading: false };
    case action.MARK_QC_DONE_RESET:
      return {
        ...state,
        qcCompleteData: undefined,
        qcCompleteFail: null,
        qcCompleteLoading: false
      };

    case action.MARK_PRODUCT_DAMAGED_REQUEST:
      return { ...state, isDamaged: { ...state.isDamaged, isLoading: true } };
    case action.MARK_PRODUCT_DAMAGED_SUCCESS:
      return { ...state, isDamaged: { ...state.isDamaged, status: payload, isLoading: false } };
    case action.MARK_PRODUCT_DAMAGED_FAIL:
      return { ...state, isDamaged: { ...state.isDamaged, isLoading: false } };
    case action.MARK_PRODUCT_DAMAGED_RESET:
      return { ...state, isDamaged: { isLoading: true, status: null } };
    case action.FITTING_BY_STATION_CODE_LOAD:
      return { ...state, qcLensoMeterData: { isLoading: true } };
    case action.FITTING_BY_STATION_CODE_SUCCESS:
      return { ...state, qcLensoMeterData: { isLoading: false } };
    case action.FITTING_BY_STATION_CODE_FAIL:
      return { ...state, qcLensoMeterData: initialState.qcLensoMeterData };
    default:
      return state;
  }
};

export default qc;
