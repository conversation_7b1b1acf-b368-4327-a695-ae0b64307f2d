import * as action from 'redux/actionTypes/printShipment';

// print shipment detail
export function getPrintShipmentDetailLoad(data) {
  return { type: action.GET_PRINT_SHIPMENT_DETAIL_LOAD, payload: data };
}
export function getPrintShipmentDetailSuccess(data) {
  return { type: action.GET_PRINT_SHIPMENT_DETAIL_SUCCESS, payload: data };
}
export function getPrintShipmentDetailReset(data) {
  return { type: action.GET_PRINT_SHIPMENT_DETAIL_RESET, payload: data };
}

// print Shipment detail for Market place order
export function getPrintShipmentMPDTCLoad(data) {
  return { type: action.GET_PRINT_SHIPMENT_MPDTC_LOAD, payload: data };
}
export function getPrintShipmentMPDTCSuccess(data) {
  return { type: action.GET_PRINT_SHIPMENT_MPDTC_SUCCESS, payload: data };
}
export function getPrintShipmentMPDTCReset(data) {
  return { type: action.GET_PRINT_SHIPMENT_MPDTC_RESET, payload: data };
}

// print packslip template
export function getPrintPackslipLoad(data) {
  return { type: action.GET_PRINT_PACKSLIP_LOAD, payload: data };
}

export function getPrintPackslipSuccess(data) {
  return { type: action.GET_PRINT_PACKSLIP_SUCCESS, payload: data };
}

export function getPrintPackslipFail(data) {
  return { type: action.GET_PRINT_PACKSLIP_FAIL, payload: data };
}

// print Lens Lab Sticker
export const printLensLabStickerLoad = (data) => ({
  type: action.PRINT_LENS_LAB_STICKER_LOAD,
  payload: data
});
export const printLensLabStickerSuccess = (data) => ({
  type: action.PRINT_LENS_LAB_STICKER_SUCCESS,
  payload: data
});
export const printLensLabStickerFail = () => ({ type: action.PRINT_LENS_LAB_STICKER_FAIL });
export const printLensLabStickerReset = () => ({ type: action.PRINT_LENS_LAB_STICKER_RESET });

// print Lens Lab Sticker
export const printJobCard = (data) => ({ type: action.PRINT_JOB_CARD, payload: data });

export function isPrintShipmentDone(data) {
  return { type: action.IS_PRINT_SHIPMENT_DONE, payload: data };
}

export const getPrintMPShipmentDummyLoad = (data) => ({
  type: action.PRINT_DUMMY_MP_SHIPMENT_DATA_LOAD,
  payload: data
});
export const getPrintMPShipmentDummySuccess = (data) => ({
  type: action.PRINT_DUMMY_MP_SHIPMENT_DATA_SUCCESS,
  payload: data
});
export const getPrintMPShipmentDummyFail = () => ({
  type: action.PRINT_DUMMY_MP_SHIPMENT_DATA_FAIL
});
export const getPrintMPShipmentDummyReset = () => ({
  type: action.PRINT_DUMMY_MP_SHIPMENT_DATA_RESET
});

export const isDoShipmentFailed = (data) => ({
  type: action.IS_DO_SHIPMENT_FAILED,
  payload: data
});

export const isDoShipmentFailedReset = () => ({
  type: action.IS_DO_SHIPMENT_FAILED_RESET
});
