import React, { useEffect, useState } from 'react';
import { makeStyles } from 'tss-react/mui';

import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';

import DeleteIcon from '@mui/icons-material/Delete';
import TimerIcon from '@mui/icons-material/Timer';
import ErrorIcon from '@mui/icons-material/Error';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

import ProductBarcode from './ProductBarcode';

const useStyles = makeStyles()(() => ({
  BoxContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: 8
  },

  BoxBarcodeHeader: {
    display: 'flex',
    alignItems: 'center',
    gap: 8
  },
  boxBarcode: {
    fontWeight: 700,
    fontSize: 15,
    lineHeight: '20px',
    color: '#333333'
  },
  boxBarcodeBadge: {
    width: 27,
    height: 16,
    background: '#00BAC6',
    borderRadius: 20,
    color: '#FFFFFF',
    fontWeight: 700,
    fontSize: 9,
    lineHeight: '8px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  activeIndicator: {
    color: '#4CAF50',
    fontSize: 12,
    lineHeight: '20px',
    fontWeight: 600
  },
  statusIcon: {
    fontSize: 14
  },
  errorStatusIcon: {
    color: '#F44336'
  }
}));

const BoxBarcodes = ({
  activeBoxBarcode,
  deleteActiveBox,
  boxBarcode,
  items,
  deleteBarcode,
  disableItemDelete,
  disableBoxDelete,
  inProgress,
  inValidBox,
  showDeleteItemForFailed
}) => {
  const { classes, cx } = useStyles();
  const [toggle, setToggle] = useState(activeBoxBarcode === boxBarcode);

  useEffect(() => {
    if (activeBoxBarcode === boxBarcode) {
      setToggle(true);
    }
  }, [activeBoxBarcode]);

  return (
    <Box>
      <Box className={classes.BoxContainer}>
        <Box className={classes.BoxBarcodeHeader}>
          <span className={classes.boxBarcode}>{boxBarcode}</span>
          <Box className={classes.boxBarcodeBadge}>{items.length}</Box>
        </Box>
        <Box className={classes.BoxBarcodeHeader}>
          {activeBoxBarcode === boxBarcode && (
            <span className={classes.activeIndicator}>Active</span>
          )}
          {(items.length === 0 || !disableBoxDelete) && (
            <IconButton
              data-cy="delete-active-box"
              size="small"
              onClick={() => deleteActiveBox(boxBarcode)}
            >
              <DeleteIcon fontSize="inherit" />
            </IconButton>
          )}
          {inProgress && <TimerIcon className={classes.statusIcon} />}
          {inValidBox && <ErrorIcon className={cx(classes.statusIcon, classes.errorStatusIcon)} />}
          <IconButton size="small" onClick={() => setToggle(!toggle)}>
            {toggle ? <ExpandLessIcon fontSize="inherit" /> : <ExpandMoreIcon fontSize="inherit" />}
          </IconButton>
        </Box>
      </Box>
      {toggle &&
        items.map(({ barcode, status }) => (
          <ProductBarcode
            key={barcode}
            barcode={barcode}
            status={status}
            deleteBarcode={deleteBarcode}
            disableDelete={disableItemDelete}
            inValidBox={inValidBox}
            inProgressBox={inProgress}
            showDeleteItemForFailed={showDeleteItemForFailed}
          />
        ))}
    </Box>
  );
};

export default BoxBarcodes;
