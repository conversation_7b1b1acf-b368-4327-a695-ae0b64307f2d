import React, { useState } from 'react'
import { Box, Paper, ClickAwayListener, Fade, Popper } from '@mui/material';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()(() => ({
  root: {
    background: ' #FFFFFF',
    border: ' 1px solid #DDDDDD',
    boxSizing: 'border-box',
    boxShadow:
      // eslint-disable-next-line max-len
      '0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 5px 8px rgba(0, 0, 0, 0.14), 0px 1px 14px rgba(0, 0, 0, 0.12);',
    borderRadius: '6px'
  },
  submitBtn: {
    borderRadius: 8,
    padding: 4
  }
}));
const PopOver = ({
  children,
  width = '180px',
  marginTop = '0px',
  marginLeft = '0px',
  open,
  setOpen,
  icon,
  paddingTop = 1.5,
  placement = 'bottom',
  mouseEvent = 'onClick'
}) => {
  const { classes } = useStyles();

  const [anchorEl, setAnchorEl] = useState(null);
  // const [placement, setPlacement] = useState();

  const handleClick = () => (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setOpen(!open);
  };

  return (
    <ClickAwayListener mouseEvent={mouseEvent} onClickAway={() => setOpen(false)}>
      <Box pt={0.4}>
        <Popper open={open} anchorEl={anchorEl} placement={placement} transition>
          {({ TransitionProps }) => (
            <Fade {...TransitionProps} timeout={350}>
              <Paper className={classes.root} style={{ marginLeft, marginTop }}>
                <Box width={width} pt={paddingTop}>
                  {children}
                </Box>
              </Paper>
            </Fade>
          )}
        </Popper>
        <div onClick={handleClick()} className="cursor-pointer">
          {icon}
        </div>
      </Box>
    </ClickAwayListener>
  );
};

export default PopOver;
