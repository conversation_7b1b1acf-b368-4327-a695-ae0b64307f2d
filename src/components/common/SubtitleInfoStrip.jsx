import React from 'react';
import Box from '@mui/material/Box';
import uuid from 'uuid/v4';

const SubtitleInfoStrip = React.forwardRef((props, ref) => {
  const { data = [] } = props;
  return (
    <div {...props} ref={ref}>
      <Box
        display="flex"
        justifyContent="space-between"
        p={1}
        pl={3}
        pr={2}
        minWidth={430}
        height="fit-content"
        border={1}
        borderRadius="8px"
        borderColor="grey.200"
      >
        {data.map((d) => (
          <Box key={uuid()}>
            <div className={`text-33 ${d.titleClassName}`}>{d.title}</div>
            <div className={`text-33 ${d.subTitleClassName}`}>{d.subtitle}</div>
          </Box>
        ))}
      </Box>
    </div>
  );
});

export default SubtitleInfoStrip;
