/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import { Button } from '@mui/material';
import Spinner from 'components/Spinner/Spinner';
import React, { useState, useCallback, useEffect } from 'react';
import { convertDateFormat } from '../../utils/helpers';
import './layOutCreate.scss';
import LayoutCreateModal from './CreateLayoutModal';

const LayoutCreateHeader = ({
  layoutData,
  layoutSaveProccessing,
  saveLayout,
  navigate,
  layoutDraftProccessing,
  openCreateModal,
  isClone,
  viewMode,
  CONTENT
}) => {
  const [showCreateModal, setShowCreateModal] = useState(openCreateModal);

  useEffect(() => {
    setShowCreateModal(openCreateModal);
  }, [openCreateModal]);

  const closeLayoutModal = useCallback(() => {
    setShowCreateModal(false);
  }, []);

  const changeStatus = (status) => {
    saveLayout(status);
  };

  const goToLayoutList = () => {
    navigate('/layoutpage/list');
  };

  return (
    <>
      <div
        className="layout-create-header pd-20 display-flex bg-white 
      justify-content-space-between align-items-center"
      >
        <div className="display-flex align-items-center">
          <div className="fs20 fw-bold display-flex align-items-center">
            <div className="mr-15">{layoutData?.name}</div>
            {!viewMode && (
              <div>
                <img
                  onClick={() => setShowCreateModal(true)}
                  onKeyDown={() => setShowCreateModal(true)}
                  className="img-action cursor-pointer"
                  src={`${import.meta.env.VITE_STATIC_SERVER}/images/edit-Black.svg`}
                  alt="edit-Black"
                />
              </div>
            )}
          </div>
          <div className="date">
            <div className="text fs10">{CONTENT.LAST_MODIFIED_UPPERCASE}</div>
            <div className="d fs16 mr-t10">
              {(layoutData?.updatedDate && convertDateFormat(layoutData?.updatedDate, 'abbDate')) ||
                convertDateFormat(new Date(), 'abbDate')}
            </div>
          </div>
        </div>
        <div className="display-flex">
          {viewMode ? (
            <Button
              onClick={() => goToLayoutList()}
              color="primary"
              style={{ marginRight: 10, borderRadius: 8, height: 36 }}
              variant="outlined"
            >
              {CONTENT.CLOSE_TAB}
            </Button>
          ) : (
            <div>
              {' '}
              <Button
                onClick={() => goToLayoutList()}
                color="primary"
                style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                variant="outlined"
              >
                {CONTENT.CANCEL}
              </Button>
              {layoutData?.status !== 'LINKED' && !layoutDraftProccessing && (
                <Button
                  onClick={() => changeStatus('DRAFT')}
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="outlined"
                >
                  {CONTENT.SAVE_AS_DRAFT}
                </Button>
              )}
              {layoutData?.status !== 'LINKED' && layoutDraftProccessing && (
                <Button
                  disabled
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="outlined"
                >
                  <Spinner />
                </Button>
              )}
              {!layoutSaveProccessing && (
                <Button
                  onClick={() =>
                    changeStatus(layoutData?.status === 'LINKED' ? 'LINKED' : 'ACTIVE')
                  }
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="contained"
                >
                  {CONTENT.SUBMIT}
                </Button>
              )}
              {layoutSaveProccessing && (
                <Button
                  disabled
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="contained"
                >
                  <Spinner />
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
      {showCreateModal && (
        <LayoutCreateModal
          isClone={isClone}
          open={showCreateModal}
          close={() => closeLayoutModal()}
        />
      )}
    </>
  );
};

export default LayoutCreateHeader;
