/* eslint-disable no-param-reassign */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Checkbox from '@mui/material/Checkbox';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelRoundedIcon from '@mui/icons-material/CancelRounded';

import { StyledTabs } from 'components/common/StyledTabs';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import {
  APPROVE_REFECT_CONFIG,
  CLASSFICATION_TYPE_CONST,
  STATUS_LIST_DATA
} from 'config/VendorMaster';
import LkToolTip from 'components/ToolTip/ToolTip';
import useFilterHook from 'components/common/useFilterHook';
import { genericDateFormatted, getCurrencySumbol, roundUptoFixedDigits } from 'utils/helpers';
import { POWER_DATA } from 'config/PowerConfig';
import LkChip from 'components/MaterialUi/LkChip';
import ShortCut from 'components/shortCut/shortCut';
import useWindowResize from 'common/useWindowResize';
import Spinner from 'components/Spinner/Spinner';
import { LOCALISATION } from 'redux/reducers/localisation';
import { getPidAuditHistoryListLoad } from 'redux/actionCreators/vendorMaster';
import PidToolTip from './PidToolTip';
import SingleApproveRejectModal from './SingleApproveRejectModal';

let tempCurrencySymbol = '';

const useStyles = makeStyles()((_theme, _params, classes) => ({
  outlinedPrimary: {
    marginLeft: '16px',
    height: 35
  },
  chip: {
    color: '#000',
    padding: '6px 8px',
    height: 'inherit',
    borderRadius: '360px'
  },
  CONFIRMED: {
    border: '1px solid #16CAD3',
    backgroundColor: '#EDFAFB'
  },
  PENDING_APPROVAL: {
    border: '1px solid #FF9800',
    backgroundColor: '#fff5e5'
  },
  APPROVED: {
    border: '1px solid #3B873E',
    backgroundColor: '#edf7ed'
  },
  REJECTED: {
    border: '1px solid #F44336',
    backgroundColor: '#feeceb'
  },
  positionRelative: {
    position: 'relative'
  },
  tab1Absolute: {
    position: 'absolute',
    background: 'white',
    top: 48,
    left: 1
  },
  tab2Absolute: {
    position: 'absolute',
    background: 'white',
    top: 48,
    left: 161
  },
  vendorStatus: {
    [`&:hover .${classes.superVisorAction}`]: {
      display: 'inline'
    },
    [`&:hover .${classes.PENDING_APPROVAL}`]: {
      display: 'none'
    },
    [`&:hover .${classes.CONFIRMED}`]: {
      display: 'none'
    }
  },
  superVisorAction: {
    display: 'none'
  }
}));

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

let approavlModalData = {};

const VendorMasterBody = ({
  filterInput,
  selectedPIDs,
  handleSelectAllClick,
  selectPidFun,
  setRequestBody,
  isInitialReq,
  sortingData,
  requestBody,
  PAGE_SIZE,
  exportVendorMaster,
  isApproveSuccess
}) => {
  const { classes, cx } = useStyles();
  const [tabValue, setTabValue] = useState(0);
  const [tableHeight] = useWindowResize(window.innerHeight - 300);
  const [openaApproveModal, setOpenApproveModal] = useState(false);
  const dispatch = useDispatch();

  const {
    vendorMasterList,
    vendorMasterListLoading,
    totalRecordsCount,
    exportVendorLoading,
    pidAuditHistory
  } = useSelector((state) => state.vendorMaster);
  const {
    autoSuggestionListPackage,
    autoSuggestionListBRAND,
    autoSuggestionListVendor_Code,
    autoSuggestionListCurrency
  } = useSelector((state) => state.filters);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.VENDOR_ITEM_MASTER);
  const { isShowShortCuts } = useSelector((state) => state.shortCuts);

  const {
    selectedFilterList,
    onChangeFilterList,
    deleteSelectedFilter,
    resetFilters,
    addFilterList
  } = useFilterHook();

  const handleChange = () => {
    setTabValue(0);
  };

  const getSortKeyOnChange = (value) =>
    value === CLASSFICATION_TYPE_CONST.PRESCRITION_LENS ? 'package_name' : 'brand';

  const chip = (value, bgColor) =>
    value ? (
      <span
        style={{ borderRadius: '12px', fontSize: '8px' }}
        className={`pd-5 mr-r10 text-white fw-bold ${bgColor}`}
      >
        {value}
      </span>
    ) : null;

  const toogleModal = useCallback(() => setOpenApproveModal((oldState) => !oldState), []);

  const onClickStatusIcon = (status, updates_id) => {
    approavlModalData = {
      status,
      ids: [updates_id]
    };
    toogleModal();
  };

  const extendedHeaderConfig = [
    {
      name: CONTENT.PRODUCT_ID,
      key: 'productId',
      style: { minWidth: '80px', maxWidth: '80px' }
    },
    {
      name: CONTENT.UNIT_PRICE,
      key: 'currentApprovedPrice',
      style: { minWidth: '80px', maxWidth: '80px' },
      formatBody: useCallback(
        ({ currentApprovedPrice }) => (
          <Box>{`${tempCurrencySymbol}${roundUptoFixedDigits(currentApprovedPrice)}`}</Box>
        ),
        []
      )
    },
    {
      name: CONTENT.PROPOSED_PRICE,
      key: 'newProposedPrice',
      style: { minWidth: '80px', maxWidth: '80px' },
      formatBody: useCallback(
        ({ newProposedPrice }) => (
          <Box>{`${tempCurrencySymbol}${roundUptoFixedDigits(newProposedPrice)}`}</Box>
        ),
        []
      )
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updatedAt',
      align: 'left',
      style: { minWidth: '120px', maxWidth: '120px' },
      formatBody: useCallback(
        ({ updatedAt }) => <div className="fw-bold">{genericDateFormatted(updatedAt)}</div>,
        []
      )
    },
    {
      name: CONTENT.UPDATED_BY,
      key: 'updatedBy',
      align: 'left',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: CONTENT.STATUS,
      key: 'status',
      align: 'left',
      style: { minWidth: '400px', maxWidth: '400px' },
      formatBody: useCallback(
        ({ status }) => <LkChip className={cx(classes.chip, classes[status])} label={status} />,
        []
      )
    }
  ];

  const headerConfig = [
    {
      name: '',
      key: 'id',
      formatHeader: useCallback(
        () => (
          <Checkbox
            style={{ padding: 2 }}
            indeterminate={
              selectedPIDs.length > 0 && vendorMasterList?.length > selectedPIDs.length
            }
            checked={false}
            onClick={handleSelectAllClick}
            color="primary"
            inputProps={{ 'aria-label': 'select all po' }}
          />
        ),
        [selectedPIDs]
      ),
      formatBody: useCallback(
        (pidInfo) => {
          const { id, action } = pidInfo;
          const isItemSelected = selectedPIDs.some((t) => t.id === id);
          const disabled =
            !(action === 'PENDING_APPROVAL' || action === 'CONFIRMED') ||
            (selectedPIDs.length ? selectedPIDs[0].action !== action : false);
          return (
            <Checkbox
              disabled={disabled}
              style={{ padding: 2 }}
              onClick={(event) => selectPidFun(event, pidInfo)}
              checked={isItemSelected}
              inputProps={{ 'aria-labelledby': id }}
              color="primary"
            />
          );
        },
        [selectedPIDs]
      ),
      style: { minWidth: '50px', maxWidth: '50px' }
    },
    {
      enableExpander: true,
      name:
        filterInput === CLASSFICATION_TYPE_CONST.PRESCRITION_LENS
          ? CONTENT.PACKAGE_NAME
          : CONTENT.ITEM_NAME,
      key: getSortKeyOnChange(filterInput),
      formatBody: useCallback(
        ({ product_description, package_name, brand }) => (
          <LkToolTip placement="bottom-end" title={<Box p={2}>{product_description} </Box>}>
            <div>
              <div className="fw-bold">
                {filterInput === CLASSFICATION_TYPE_CONST.PRESCRITION_LENS ? package_name : brand}
              </div>
              <div className="ellipsis-vertical">{product_description}</div>
            </div>
          </LkToolTip>
        ),
        []
      ),
      style: { minWidth: '180px', maxWidth: '180px' },
      filterData: {
        type: 'autoSelect',
        listData:
          filterInput === CLASSFICATION_TYPE_CONST.PRESCRITION_LENS
            ? autoSuggestionListPackage
            : autoSuggestionListBRAND,
        selectedFilterList,
        submitFilter: onChangeFilterList,
        columnName: filterInput === CLASSFICATION_TYPE_CONST.PRESCRITION_LENS ? 'Package' : 'BRAND',
        apiName: 'vendorItemMaster'
      }
    },
    {
      name: CONTENT.VENDOR_NAME_CODE,
      key: 'vendor_code',
      formatBody: useCallback(
        ({ vendor_name, vendor_code }) => (
          <LkToolTip placement="bottom-end" title={<Box p={2}>{vendor_name} </Box>}>
            <div>
              <div className="fw-bold">{vendor_code}</div>
              <div className="ellipsis-vertical">{vendor_name}</div>
            </div>
          </LkToolTip>
        ),
        []
      ),
      style: { minWidth: '180px', maxWidth: '180px' },
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListVendor_Code,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'Vendor_Code',
        apiName: 'vendorItemMaster'
      }
    },
    {
      name: CONTENT.PID_INFO,
      key: 'product_id',
      formatBody: useCallback(
        ({ vendor_sku, gtin, upc, product_id }) => (
          <LkToolTip
            placement="bottom-end"
            title={
              <PidToolTip
                input={[
                  { key: 'Vendor SKU', value: vendor_sku },
                  { key: 'GTIN', value: gtin },
                  { key: 'UPC', value: upc }
                ]}
              />
            }
          >
            <div>
              <Link target="_blank" to={`/inventory/${product_id}`} className="fw-bold">
                {product_id}
              </Link>
              <div>
                {chip(vendor_sku, 'bg-yellow')}
                {chip(gtin, 'bg-blue')}
                {chip(upc, 'bg-paris_green')}
              </div>
            </div>
          </LkToolTip>
        ),
        []
      ),
      style: { minWidth: '180px', maxWidth: '180px' },
      filterData: {
        type: 'input',
        submitFilter: addFilterList,
        selectedFilterList,
        columnName: 'Product_Id',
        apiName: 'vendorItemMaster'
      }
    },
    {
      name: CONTENT.LEGAL_OWNER,
      key: 'legal_owner',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: CONTENT.LENS_ID,
      key: 'lens_id',
      formatBody: useCallback(({ lens_id }) => <div className="fw-bold">{lens_id}</div>, []),
      style: { minWidth: '100px', maxWidth: '100px' }
    },

    {
      name: CONTENT.PARENT_SKU,
      key: 'parent_sku',
      formatBody: useCallback(
        ({ parent_sku }) => <div className="text-uppercase">{parent_sku}</div>,
        []
      ),
      align: 'left',
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: CONTENT.BRAND,
      key: 'brand_value',
      supportSort: true,
      columnName: 'brand',
      align: 'left',
      style: { minWidth: '120px', maxWidth: '120px' },
      formatBody: useCallback(({ brand }) => brand, []),
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListBRAND,
        selectedFilterList,
        submitFilter: onChangeFilterList,
        columnName: 'BRAND',
        apiName: 'vendorItemMaster'
      }
    },
    {
      name: CONTENT.CURRENCY,
      key: 'currency',
      formatBody: useCallback(
        ({ currency }) => <div className="text-uppercase">{currency}</div>,
        []
      ),
      style: { minWidth: '120px', maxWidth: '120px' },
      filterData: {
        type: 'status',
        listData: autoSuggestionListCurrency,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'Currency',
        apiName: 'vendorItemMaster'
      }
    },
    {
      name: CONTENT.CURRENT_PRICE,
      key: 'unit_price',
      formatBody: useCallback(
        ({ unit_price, currency }) => (
          <Box>
            {getCurrencySumbol(currency)}
            {roundUptoFixedDigits(unit_price)}
          </Box>
        ),
        []
      ),
      paddingRight: 2,
      style: { minWidth: '120px', maxWidth: '120px' }
    },
    {
      name: CONTENT.PROPOSED_PRICE,
      key: 'new_proposed_price',
      formatBody: useCallback(
        ({ new_proposed_price, currency, action }) =>
          action === 'PENDING_APPROVAL' || action === 'CONFIRMED' ? (
            <Box>
              {getCurrencySumbol(currency)}
              {roundUptoFixedDigits(new_proposed_price)}
            </Box>
          ) : (
            '--'
          ),
        []
      ),
      paddingRight: 2,
      style: { minWidth: '150px', maxWidth: '150px' }
    },

    {
      name: CONTENT.COLOR,
      key: 'color',
      align: 'center',
      formatBody: useCallback(({ color }) => <div className="text-capitalize">{color}</div>, []),
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: CONTENT.SPH_CYL_AXIS_AP_BC,
      key: 'sph',
      formatBody: useCallback(
        ({ sph, cyl, axis, ap, base_curve }) => (
          <div className="display-flex">
            <div className="text-a2 fw-bold">{sph || sph === 0 ? sph : '-'} / </div>
            <div className="text-94 fw-bold">{cyl || cyl === 0 ? cyl : '-'} / </div>
            <div className="text-22 fw-bold">{axis || axis === 0 ? axis : '-'} / </div>
            <div className="text-28 fw-bold">{ap || ap === 0 ? ap : '-'} / </div>
            <div className="text-red fw-bold">
              {base_curve || base_curve === 0 ? base_curve : '-'}
            </div>
          </div>
        ),
        []
      ),
      style: { minWidth: '220px', maxWidth: '220px' },
      filterData: {
        type: 'custom',
        listData: POWER_DATA,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'Last_Updated'
      }
    },
    {
      name: CONTENT.IS_ENABLE,
      key: 'status',
      align: 'center',
      formatBody: useCallback(
        ({ status }) => (
          <Box>
            {status ? (
              <CheckCircleIcon className="image-container" color="primary" />
            ) : (
              <CancelRoundedIcon className="image-container" color="error" />
            )}
          </Box>
        ),
        []
      ),
      style: { minWidth: '120px', maxWidth: '120px' },
      filterData: {
        type: 'status',
        listData: ['true', 'false'],
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'Status',
        apiName: 'vendorItemMaster'
      }
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updated_at',
      formatBody: useCallback(({ updated_at }) => {
        const [date, time] = genericDateFormatted(updated_at).split(' ');
        return (
          <>
            <div className="fw-bold">{date}</div>
            <div className="fs10 text-66">{time}</div>
          </>
        );
      }, []),
      style: { minWidth: '180px', maxWidth: '180px' },
      supportSort: true,
      columnName: 'updated_at',
      filterData: {
        type: 'dateRange',
        submitFilter: onChangeFilterList,
        selectedFilterList,
        columnName: 'Last_Updated'
      }
    },
    {
      name: CONTENT.UPDATED_BY,
      key: 'updated_by',
      formatBody: useCallback(
        ({ updated_by }) => <div className="fw-bold">{updated_by || '--'}</div>,
        []
      ),
      style: { minWidth: '180px', maxWidth: '180px' }
    },
    {
      name: CONTENT.STATUS,
      key: 'action',
      align: 'left',
      supportSort: true,
      style: { minWidth: '200px', maxWidth: '200px' },
      formatBody: useCallback(
        ({ updates_id, action }) =>
          action ? (
            <Box className={classes.vendorStatus}>
              <LkChip className={cx(classes.chip, classes[action])} label={action} />
              {(action === 'PENDING_APPROVAL' || action === 'CONFIRMED') && (
                <Box className={classes.superVisorAction}>
                  <IconButton
                    onClick={() => onClickStatusIcon(APPROVE_REFECT_CONFIG[action], updates_id)}
                  >
                    <CheckCircleIcon color="primary" />
                  </IconButton>
                  <IconButton onClick={() => onClickStatusIcon('REJECTED', updates_id)}>
                    <CancelRoundedIcon color="error" />
                  </IconButton>
                </Box>
              )}
            </Box>
          ) : (
            '-'
          ),
        []
      ),
      filterData: {
        type: 'singleSelect',
        columnName: 'action',
        listData: STATUS_LIST_DATA,
        selectedFilterList
      }
    }
  ];

  const filterHeader = () =>
    headerConfig.filter(({ key }) => {
      if (
        filterInput === CLASSFICATION_TYPE_CONST.EYEFRAME &&
        (key === 'color' || key === 'sph' || key === 'lens_id')
      ) {
        return false;
      }
      if (
        filterInput === CLASSFICATION_TYPE_CONST.PRESCRITION_LENS &&
        (key === 'color' || key === 'product_id')
      ) {
        return false;
      }
      if (
        (filterInput === CLASSFICATION_TYPE_CONST.CONTACT_LENS ||
          filterInput === CLASSFICATION_TYPE_CONST.CONTACT_LENS_SOLUTIONS ||
          filterInput === CLASSFICATION_TYPE_CONST.SUNGLASSES) &&
        key === 'lens_id'
      ) {
        return false;
      }
      return true;
    });

  const isDisableResetButton = useMemo(() => {
    const temp = selectedFilterList.length === 1;
    return temp;
  }, [selectedFilterList]);

  const filteredSelectedFilterList = () =>
    selectedFilterList.filter(({ key }) => {
      if (key === 'globalSelector') {
        return false;
      }
      return true;
    });

  const updateRequestBody = (sortType, sortKey, page) => {
    if (!isInitialReq.current) {
      setRequestBody((prevReqBody) => ({
        ...prevReqBody,
        isReset:
          prevReqBody.payload.pageRequest.sortKey !== sortKey ||
          prevReqBody.payload.pageRequest.sortOrder !== sortType,
        payload: {
          ...prevReqBody.payload,
          pageRequest: {
            ...prevReqBody.payload.pageRequest,
            pageNumber: page,
            sortKey,
            sortOrder: sortType
          }
        }
      }));
    }
  };

  useEffect(() => {
    if (isApproveSuccess) {
      setOpenApproveModal(false);
    }
  }, [isApproveSuccess]);

  const getHistoryList = ({
    id,
    currency,
    product_id: productId,
    vendor_code: vendorCode,
    legal_owner
  }) => {
    if (id) {
      tempCurrencySymbol = getCurrencySumbol(currency);
      dispatch(
        getPidAuditHistoryListLoad({
          id,
          payload: {
            productId,
            vendorCode,
            legalOwner: legal_owner
          }
        })
      );
    }
  };

  const renderExtenedComponent = (extendedData) => (
    <LkTable
      tableHeight={235}
      tableData={pidAuditHistory[extendedData.id]?.data}
      isDataFetching={pidAuditHistory[extendedData.id]?.isLoading}
      showTableCount={false}
      totalRowsCount={pidAuditHistory[extendedData.id]?.data?.length}
      headerConfig={extendedHeaderConfig}
      isNonVertualizedTable
      plainTheme
      dataRequestFunction={() => getHistoryList(extendedData)}
    />
  );

  return (
    <div className={`overflow-hidden ${!filterInput ? 'display-none' : 'display-block'}`}>
      <Box width="100%" overflow="hidden" margin="0 auto" className={classes.positionRelative}>
        <StyledTabs value={tabValue} onChange={handleChange}>
          <Tab label={CONTENT.LISTING} className={cx('tabs', { tabselected: tabValue === 0 })} />
          {/* <Tab label="Price History"
           className={clsx('tabs', { tabselected: tabValue === 1 })} /> */}
        </StyledTabs>
        <Box
          className={cx('border-grey5-radiusbase overflow-hidden', {
            'border-left-no-radius': tabValue === 0
          })}
        >
          <Box
            width={158}
            component="div"
            height={2}
            className={cx(tabValue === 0 ? classes.tab1Absolute : classes.tab2Absolute)}
          />

          <TabPanel value={tabValue} index={0}>
            <div>
              <div
                className="invoice-dashboard-action
                 pd-16 display-flex justify-content-space-between"
              >
                <Box display="flex" style={{ flexWrap: 'wrap' }} flex={1}>
                  {filteredSelectedFilterList().map(({ key, value }) => (
                    <Box key={`${key}:${value}`} mr={2} mb={1}>
                      <LkChip
                        label={`${key}: ${value}`}
                        type="filter"
                        deleteIcon={<CloseIcon style={{ color: '#666666' }} />}
                        size="small"
                        onDelete={() => deleteSelectedFilter(key, value)}
                      />
                    </Box>
                  ))}
                </Box>
                <div className="display-flex justify-content-space-between">
                  <Button
                    style={{ borderRadius: 8 }}
                    disabled={isDisableResetButton}
                    className={classes.outlinedPrimary}
                    onClick={() => {
                      resetFilters([{ key: 'globalSelector', value: filterInput }]);

                      sortingData.id = 'updated_at';
                      sortingData.order = 'DESC';
                    }}
                    variant="outlined"
                    color="primary"
                  >
                    {CONTENT.RESET}
                  </Button>

                  {filterInput && (
                    <LkToolTip
                      placement="bottom"
                      title={<ShortCut name="Alt+E" />}
                      open={isShowShortCuts}
                    >
                      {exportVendorLoading ? (
                        <Button
                          style={{ borderRadius: 8, width: '105px' }}
                          disabled
                          className={classes.outlinedPrimary}
                          variant="contained"
                          color="primary"
                        >
                          <Spinner />
                        </Button>
                      ) : (
                        <Button
                          style={{ borderRadius: 8 }}
                          onClick={() => exportVendorMaster()}
                          className={classes.outlinedPrimary}
                          variant="outlined"
                          color="primary"
                        >
                          {CONTENT.EXPORT}
                        </Button>
                      )}
                    </LkToolTip>
                  )}
                </div>
              </div>
              <LkTable
                headerConfig={filterHeader()}
                tableHeight={tableHeight}
                tableData={vendorMasterList}
                isDataFetching={vendorMasterListLoading}
                setFilters={onChangeFilterList}
                dataRequestFunction={updateRequestBody}
                totalRowsCount={totalRecordsCount}
                pageLimit={PAGE_SIZE}
                pageNumber={requestBody.payload.pageRequest.pageNumber}
                initialSortBy={sortingData}
                rowSize={90}
                renderExtenedComponent={renderExtenedComponent}
                isNonVertualizedTable
              />
            </div>
          </TabPanel>
        </Box>
        <SingleApproveRejectModal
          data={approavlModalData}
          open={openaApproveModal}
          handleClose={toogleModal}
        />
      </Box>
    </div>
  );
};

export default VendorMasterBody;
