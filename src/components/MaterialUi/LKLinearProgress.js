import { withStyles } from 'tss-react/mui';
import LinearProgress from '@mui/material/LinearProgress';

export const LKLinearProgress = withStyles(LinearProgress, (theme, { colorbg }) => ({
  root: {
    height: 8,
    borderRadius: 5
  },
  colorPrimary: {
    backgroundColor: theme.palette.grey[200]
  },
  bar: {
    borderRadius: 5,
    backgroundColor: colorbg
  }
}));

export const linearProgressColorMapping = {
  current: '#45C476',
  critical: '#FAA61A',
  severe: '#DF3747'
};
