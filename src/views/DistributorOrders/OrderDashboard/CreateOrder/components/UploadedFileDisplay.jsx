import React from 'react';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import ClearSharpIcon from '@mui/icons-material/ClearSharp';
import clsx from 'clsx';

const UploadedFileDisplay = ({ fileName, pidListing, csvErrors, removeFile, classes, CONTENT }) => (
  <Box
    className={clsx(classes.fileUploadButton, {
      [classes.fileName]: pidListing?.length > 0,
      [classes.errorFile]: csvErrors?.length
    })}
    sx={{ position: 'relative', paddingBlock: '25px', paddingInline: '15px' }}
  >
    {!pidListing?.length > 0 && (
      <IconButton
        sx={{
          position: 'absolute',
          top: '5px',
          right: '5px',
          padding: '4px'
        }}
        onClick={removeFile}
        data-cy="clear-file-icon"
      >
        <ClearSharpIcon sx={{ fontSize: 16 }} />
      </IconButton>
    )}
    <Box component="p" textAlign="center" marginTop={3}>
      {fileName}
    </Box>
    {!pidListing?.length > 0 && (
      <Box className="pd-t20">
        <Box
          sx={{ cursor: 'pointer', color: '#00acc1' }}
          component="label"
          variant="text"
          htmlFor="fileInput"
          data-cy="change-btn"
        >
          {CONTENT.CHANGE}
        </Box>
      </Box>
    )}
  </Box>
);

export default UploadedFileDisplay;
