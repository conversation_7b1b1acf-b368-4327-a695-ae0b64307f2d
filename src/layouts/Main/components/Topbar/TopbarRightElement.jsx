import React, { useEffect, useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import Box from '@mui/material/Box';
import Avatar from '@mui/material/Avatar';
import Backdrop from '@mui/material/Backdrop';
import VisibilityIcon from '@mui/icons-material/Visibility';
import NotificationsIcon from '@mui/icons-material/Notifications';

import { makeStyles } from 'tss-react/mui';
import ExitToAppOutlinedIcon from '@mui/icons-material/ExitToAppOutlined';
import LockIcon from '@mui/icons-material/Lock';

import PopOver from 'components/PopOver';
import { LOGIN, logoutUserLoad } from 'redux/reducers/login';
import { toastOpen } from 'redux/actionCreators/toast';
import { BARCODE_HISTORY, getBarcodeHistoryReset } from 'redux/reducers/barcodeHistory';
import { showShortCuts } from 'redux/actionCreators/shortCuts';

import TopbarSearch from './TopbarSearch';

const useStyles = makeStyles()(() => ({
  backdrop: {
    zIndex: 1201,
    color: '#fff'
  }
}));

const TopbarRightElement = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const [openToolTip, setOpenToolTip] = useState(false);
  const [searchedValue, setSearchedValue] = useState('');

  const { isShowShortCuts } = useSelector((state) => state.shortCuts);
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const { barcodeHistoryFail } = useSelector((state) => state[BARCODE_HISTORY].barcodeHistory);

  const handleClose = () => {
    dispatch(showShortCuts(false));
  };

  const handleToggle = () => {
    dispatch(showShortCuts(!isShowShortCuts));
  };

  const toogleToolTip = useCallback((value) => setOpenToolTip(value), [setOpenToolTip]);

  const setSearchValueFun = useCallback((value) => setSearchedValue(value), [setSearchedValue]);

  const logoutFun = () => {
    dispatch(logoutUserLoad());
  };

  useEffect(() => {
    if (location.state?.fromTopBarSearch) {
      setSearchValueFun('');
    }
  }, [location.state]);

  useEffect(() => {
    if (barcodeHistoryFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: barcodeHistoryFail?.meta?.displayMessage,
          severity: 'error'
        })
      );
      const tempElement = document.getElementById('barcodeSearchId');
      if (tempElement) {
        tempElement.focus();
      }
      dispatch(getBarcodeHistoryReset());
    }
  }, [barcodeHistoryFail]);

  const tooltipActions = () => (
    <div className="grnActions">
      <div
        className="display-flex  align-items-center pd-8 pd-l12 pd-r12 cursor-pointer hover-grey"
        onClick={() => navigate('/changePassword')}
      >
        <div className="fs14">
          {' '}
          <LockIcon fontSize="small" />
        </div>
        <div className="fs14 mr-l4">Change Password</div>
      </div>
      <div
        data-cy="logout"
        className="display-flex align-items-center pd-8 pd-l12 pd-r12 cursor-pointer hover-grey"
        onClick={logoutFun}
      >
        <div className="fs14">
          <ExitToAppOutlinedIcon fontSize="small" />{' '}
        </div>
        <div className="fs14 mr-l4">Logout</div>
      </div>
    </div>
  );

  return (
    <>
      <Backdrop className={classes.backdrop} open={isShowShortCuts} onClick={handleClose} />
      <Box display="flex" alignItems="center">
        <TopbarSearch onChangeFun={setSearchValueFun} value={searchedValue} />
        <Box pl={2} onClick={() => handleToggle()}>
          <VisibilityIcon className="cursor-pointer" />
        </Box>
        <Box pl={2}>
          <NotificationsIcon color="disabled" />
        </Box>
        <Box className="boxBorderLeft" pl={2.5} ml={3}>
          <Box mb={0.5} className="fs14 fw-bold">
            {userDetail?.userName || userDetail?.email || userDetail?.empCode}
          </Box>
          <Box textAlign="right" className="fs12 text-66">
            {userDetail?.empCode}
          </Box>
        </Box>
        <Box pl={1.5}>
          <PopOver
            open={openToolTip}
            setOpen={toogleToolTip}
            icon={
              <Avatar
                alt="Remy Sharp"
                src={`${import.meta.env.VITE_STATIC_SERVER}/images/avatar_11.png`}
              />
            }
          >
            {tooltipActions()}
          </PopOver>
        </Box>
      </Box>
    </>
  );
};

export default TopbarRightElement;
