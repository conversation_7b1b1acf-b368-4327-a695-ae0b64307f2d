import Tax from './Tax';

export default function PurchaseOrderDetailItems({
  cgst_per = 0,
  sgst_per = 0,
  igst_per = 0,
  ugst_per = 0,
  product_id = null,
  desc,
  quantity = 0,
  vendor_unit_cost_price = 0,
  overheads = {},
  duty_per = 0,
  cess_per = 0,
  pending_quantity = 0,
  rejected_quantity = 0,
  accepted_quantity = 0,
  tax_type,
  hsn_classification=''
}) {
  const vendorCost = Math.round(quantity * vendor_unit_cost_price * 100) / 100;
  this.duty_per = duty_per;
  this.item_customs_cost = Math.round(vendorCost * duty_per) / 100;
  Tax.call(this, {
    cgst_per,
    sgst_per,
    igst_per,
    ugst_per,
    taxable_value: vendorCost + this.item_customs_cost
  });
  this.product_id = product_id;
  this.desc = desc;
  this.tax_type = tax_type;
  this.quantity = isNaN(quantity) ? 0 : parseInt(quantity);
  this.pending_quantity = pending_quantity;
  this.rejected_quantity = rejected_quantity;
  this.accepted_quantity = accepted_quantity;
  this.vendor_unit_cost_price = vendor_unit_cost_price;
  this.total_vendor_cost_price = vendorCost;
  this.overhead_per = overheads;
  this.overheads = overheads;
  this.overhead_rate = {};
  this.overheadsPrice = 0;
  this.hsn_classification = hsn_classification;
  Object.keys(overheads).forEach((o) => {
    this.overhead_rate[o] = Math.round(overheads[o] * vendorCost) / 100;
    this.overheadsPrice += this.overhead_rate[o];
  });
  this.cess = {
    percent: cess_per,
    rate: Math.round(cess_per * this.taxable_value) / 100
  };
  this.total = this.taxable_value + this.total_tax + this.cess.rate;
  this.total_with_overhead = this.total + this.overheadsPrice;
}

PurchaseOrderDetailItems.prototype = Object.create(Tax.prototype);

Object.defineProperty(PurchaseOrderDetailItems.prototype, 'constructor', {
  value: PurchaseOrderDetailItems,
  enumerable: false, // so that it does not appear in 'for in' loop
  writable: true
});

PurchaseOrderDetailItems.prototype.setQuantity = function (quantity) {
  this.quantity = quantity;
  this.total_vendor_cost_price =
    Math.round(this.vendor_unit_cost_price * this.quantity * 100) / 100;
  this.overheads = 0;
  Object.keys(this.overhead_per).forEach((o) => {
    this.overhead_rate[o] = Math.round(this.overhead_per[o] * this.total_vendor_cost_price) / 100;
    this.overheads += this.overhead_rate[o];
  });
  this.item_customs_cost = Math.round(this.total_vendor_cost_price * this.duty_per) / 100;
  this.setTaxableValue(this.total_vendor_cost_price + this.item_customs_cost);
  this.cess.rate = Math.round(this.cess.percent * this.taxable_value) / 100;
  this.total = this.taxable_value + this.total_tax + this.cess.rate;
  this.total_with_overhead = this.total + this.overheads;
};
