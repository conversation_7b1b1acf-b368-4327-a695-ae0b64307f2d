import { useEffect, useState } from 'react';

const useInfiniteScroll = (callback, tableRef, showRecord, setShowRecord) => {
  const [isFetching, setIsFetching] = useState(false);

  function handleScroll(event) {
    const tBody = event.target;
    if (!showRecord) {
      setShowRecord(true);
      setTimeout(() => {
        setShowRecord(false);
      }, 2000);
    }
    if (tBody.scrollTop < tBody.scrollHeight - 750 || isFetching) {
      return
    };
    setIsFetching(true);
  }

  useEffect(() => {
    tableRef.current.addEventListener('scroll', handleScroll);
    return () => tableRef.current.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (isFetching) {
      callback();
    }
  }, [isFetching]);

  return [isFetching, setIsFetching];
};

export default useInfiniteScroll;
