import * as action from '../actionTypes/vendorMaster';

export const getVendorMasterListLoad = (data) => ({
  type: action.GET_VENDOR_MASTER_LIST_LOAD,
  payload: data
});

export const getVendorMasterListSuccess = (data) => ({
  type: action.GET_VENDOR_MASTER_LIST_SUCCESS,
  payload: data
});

export const getVendorMasterListFail = (data) => ({
  type: action.GET_VENDOR_MASTER_LIST_FAIL,
  payload: data
});

export const getVendorMasterListReset = (data) => ({
  type: action.GET_VENDOR_MASTER_LIST_RESET,
  payload: data
});

// export vendor master
export const exportVendorMasterLoad = (data) => ({
  type: action.EXPORT_VENDOR_MASTER_LOAD,
  payload: data
});

export const exportVendorMasterSuccess = (data) => ({
  type: action.EXPORT_VENDOR_MASTER_SUCCESS,
  payload: data
});

export const exportVendorMasterFail = (data) => ({
  type: action.EXPORT_VENDOR_MASTER_FAIL,
  payload: data
});

export const exportVendorMasterReset = (data) => ({
  type: action.EXPORT_VENDOR_MASTER_RESET,
  payload: data
});

// upload vendor master
export const uploadVendorMasterLoad = (data) => ({
  type: action.UPLOAD_VENDOR_MASTER_LOAD,
  payload: data
});

export const uploadVendorMasterSuccess = (data) => ({
  type: action.UPLOAD_VENDOR_MASTER_SUCCESS,
  payload: data
});

export const uploadVendorMasterFail = (data) => ({
  type: action.UPLOAD_VENDOR_MASTER_FAIL,
  payload: data
});

export const uploadVendorMasterReset = (data) => ({
  type: action.UPLOAD_VENDOR_MASTER_RESET,
  payload: data
});

// download vendor master
export const downloadVendorMasterLoad = (data) => ({
  type: action.DOWNLOAD_VENDOR_MASTER_LOAD,
  payload: data
});

export const downloadVendorMasterSuccess = (data) => ({
  type: action.DOWNLOAD_VENDOR_MASTER_SUCCESS,
  payload: data
});

export const downloadVendorMasterFail = (data) => ({
  type: action.DOWNLOAD_VENDOR_MASTER_FAIL,
  payload: data
});

export const downloadVendorMasterReset = (data) => ({
  type: action.DOWNLOAD_VENDOR_MASTER_RESET,
  payload: data
});

//  vendor proccess status
export const vendorProccessStatusLoad = (data) => ({
  type: action.VENDOR_PROCCESS_STATUS_LOAD,
  payload: data
});

export const vendorProccessStatusSuccess = (data) => ({
  type: action.VENDOR_PROCCESS_STATUS_SUCCESS,
  payload: data
});

export const vendorProccessStatusFail = (data) => ({
  type: action.VENDOR_PROCCESS_STATUS_FAIL,
  payload: data
});

export const vendorProccessStatusReset = (data) => ({
  type: action.VENDOR_PROCCESS_STATUS_RESET,
  payload: data
});

// file download vendor
export const fileDownloadVendorLoad = (data) => ({
  type: action.FILE_DOWNLOAD_VENDOR_LOAD,
  payload: data
});

export const fileDownloadVendorSuccess = (data) => ({
  type: action.FILE_DOWNLOAD_VENDOR_SUCCESS,
  payload: data
});

export const fileDownloadVendorFail = (data) => ({
  type: action.FILE_DOWNLOAD_VENDOR_FAIL,
  payload: data
});

export const fileDownloadVendorReset = (data) => ({
  type: action.FILE_DOWNLOAD_VENDOR_RESET,
  payload: data
});

// price update
export const vendorMasterStatusApproveLoad = (data) => ({
  type: action.VENDOR_MASTER_APPROVE_LOAD,
  payload: data
});

export const vendorMasterStatusApproveSuccess = (data) => ({
  type: action.VENDOR_MASTER_APPROVE_SUCCESS,
  payload: data
});

export const vendorMasterStatusApproveFail = () => ({
  type: action.VENDOR_MASTER_APPROVE_FAIL
});

export const vendorMasterStatusApproveReset = () => ({
  type: action.VENDOR_MASTER_APPROVE_RESET
});

// History List

export const getPidAuditHistoryListLoad = (data) => ({
  type: action.GET_PID_AUDIT_HISTORY_LIST_LOAD,
  payload: data
});

export const getPidAuditHistoryListSuccess = (data) => ({
  type: action.GET_PID_AUDIT_HISTORY_LIST_SUCCESS,
  payload: data
});

export const getPidAuditHistoryListFail = () => ({
  type: action.GET_PID_AUDIT_HISTORY_LIST_FAIL
});

export const getPidAuditHistoryListReset = () => ({
  type: action.GET_PID_AUDIT_HISTORY_LIST_RESET
});

// EXPORT pid audit History List

export const exportPidAuditHistoryListLoad = (data) => ({
  type: action.EXPORT_PID_AUDIT_HISTORY_LIST_LOAD,
  payload: data
});

export const exportPidAuditHistoryListSuccess = (data) => ({
  type: action.EXPORT_PID_AUDIT_HISTORY_LIST_SUCCESS,
  payload: data
});

export const exportPidAuditHistoryListFail = () => ({
  type: action.EXPORT_PID_AUDIT_HISTORY_LIST_FAIL
});

export const exportPidAuditHistoryListReset = () => ({
  type: action.EXPORT_PID_AUDIT_HISTORY_LIST_RESET
});
