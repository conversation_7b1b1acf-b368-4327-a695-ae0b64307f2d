import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { makeStyles } from 'tss-react/mui';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import ReportingTool from './ReportingTool';
import ReportHistoryList from './ReportingList/ReportHistoryList';
import { INVENTORY_REPORT, SALE_ORDER_REPORT } from './Common/constant';

const useStyles = makeStyles()(() => ({
  container: {
    backgroundColor: '#f0f0f0',
    height: 'calc(100vh - 64px)',
    padding: '0px 20px 20px 20px'
  },
  dataContainer: {
    borderRadius: 4,
    backgroundColor: '#ffffff',
    height: 'calc(100% - 45px)',
    border: '1px solid rgba(0,0,0,0.17)'
  },
  customTabRoot: {
    color: '#00B9C6'
  },
  customTabIndicator: {
    backgroundColor: '#00B9C6'
  }
}));

const TabPanel = ({ children, value, index }) => value === index && children;

const ReportingTab = () => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const [value, setValue] = useState(
    location.pathname.includes('/list') ? SALE_ORDER_REPORT : INVENTORY_REPORT
  );
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.EXPORT_REPORT);

  const handleChange = (_event, report) => {
    if (report === SALE_ORDER_REPORT) {
      navigate('/reporting/list');
    } else {
      navigate('/reporting');
    }
    setValue(report);
  };

  return (
    <Box className={classes.container}>
      <Box>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="secondary tabs example"
          classes={{
            root: classes.customTabRoot,
            indicator: classes.customTabIndicator
          }}
        >
          <Tab value={INVENTORY_REPORT} label={CONTENT.DOWNLOAD_REPORT} />
          <Tab value={SALE_ORDER_REPORT} label={CONTENT.REPORT_HISTORY} />
        </Tabs>
      </Box>
      <Box className={classes.dataContainer}>
        <TabPanel value={value} index={INVENTORY_REPORT}>
          <ReportingTool />
        </TabPanel>
        <TabPanel value={value} index={SALE_ORDER_REPORT}>
          <ReportHistoryList />
        </TabPanel>
      </Box>
    </Box>
  );
};

export default ReportingTab;
