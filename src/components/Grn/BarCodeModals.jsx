import React, { useEffect, useState, useCallback } from 'react';
import LkModal from 'components/Modal/Modal';
import LkInput from 'components/MaterialUi/LkInput';
import { Box, Typography } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { productMismatchLoad, getQcDataLoad } from '../../redux/actionCreators/grn';
import { toastOpen } from '../../redux/actionCreators/toast';

const BarCodeModels = ({
  handleModal,
  modalOpen,
  toggleAddPidModal,
  pidDetail,
  primaryBtn,
  barCode
}) => {
  const dispatch = useDispatch();
  const [pidMismatch, setPidMisMatch] = useState('');
  const { ASNApiSuccess, meta, productMismatchSuccess, productMismatchFailure, qcDetailsSuccess } =
    useSelector((state) => state.grn);
  const [gradientShift, setGradientShift] = useState(false);
  const [minScanQuantity, setMinScanQuantity] = useState(0);

  useEffect(() => {
    if (modalOpen.pidMisMatch) {
      setPidMisMatch('');
    }
  }, [modalOpen]);
  useEffect(() => {
    if (ASNApiSuccess?.data && pidDetail?.result?.data?.pid !== ASNApiSuccess?.data?.product) {
      handleModal('pidMisMatch', true);
    }
  }, [ASNApiSuccess, pidDetail]);

  useEffect(() => {
    if (qcDetailsSuccess) {
      const { gradient_shift, min_scan_quantity } = qcDetailsSuccess.result.data;
      if (gradient_shift === 1) {
        setMinScanQuantity(min_scan_quantity);
        setGradientShift(true);
      }
    }
  }, [qcDetailsSuccess]);

  useEffect(() => {
    if (productMismatchSuccess) {
      const { data } = pidDetail.result;
      if (productMismatchSuccess.data.result) {
        dispatch(getQcDataLoad(data));
      }
      handleModal('pidMisMatch', false);
    }
    if (productMismatchFailure) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: productMismatchFailure.display_message,
          severity: 'error'
        })
      );
      handleModal('pidMisMatch', false);
    }
  }, [productMismatchSuccess, productMismatchFailure, dispatch]);

  // useEffect(() => {
  //     if (modalOpen.greenChannel) {
  //         const { data } = pidDetail.result
  //         dispatch(getQcDataLoad(data))
  //     }

  // }, [modalOpen,dispatch])

  const confirmPidMismatch = () => {
    // action and reason depends on asn api response

    const { grn_code, pid } = pidDetail.result.data;
    if (pid !== pidMismatch) {
      const payload = {
        action: 'pid mismatch',
        barcode: barCode,
        grn_code,
        meta,
        pid,
        reason: 'pid mismatch',
        scanned_pid: pidMismatch
      };
      dispatch(productMismatchLoad(payload));
    } else {
      handleModal('pidMisMatch', false);
    }
  };
  const closeGradientShiftModal = useCallback(() => setGradientShift(false), [setGradientShift]);
  const disabled = pidMismatch === '';
  return (
    <>
      {modalOpen.pidMisMatch && (
        <LkModal
          disablePrimaryButton={disabled}
          open={modalOpen.pidMisMatch}
          title="PID Mismatch"
          subTitle="This barcode belongs to another PID. Please scan PID to verify"
          disableBackdrop
          handleClose={() => handleModal('pidMisMatch', false)}
          primaryBtn={confirmPidMismatch}
          primaryBtnText="Confirm"
        >
          <Box margin="0 auto" pl={3} pt={2} pb={1} width="80%">
            <LkInput
              fullWidth
              placeholder="Scan PID to confirm"
              value={pidMismatch}
              onChange={(e) => setPidMisMatch(e.target.value)}
            />
          </Box>
        </LkModal>
      )}

      {modalOpen.greenChannel && (
        <LkModal
          open={modalOpen.greenChannel}
          title="Proceed to Green Channel"
          subTitle="You will not be able to edit barcodes that are already scanned"
          disableBackdrop
          handleClose={() => handleModal('greenChannel', false)}
          primaryBtn={primaryBtn}
          primaryBtnText="Proceed"
        />
      )}

      {modalOpen.redChannel && (
        <LkModal
          open={modalOpen.redChannel}
          title="Failure Threshold Breached"
          subTitle="Following PID has been Blocked, your admin has been notified
      and will take appropriate action"
          disableBackdrop
          handleClose={() => handleModal('redChannel', false)}
          primaryBtn={() => {
            handleModal('redChannel', false);
            toggleAddPidModal(true);
          }}
          primaryBtnText="Add new pid"
        >
          <Box textAlign="center" pt={2}>
            <Box
              color="text.secondary"
              p={2}
              width="25%"
              style={{ background: '#F5F5F5', margin: 'auto' }}
            >
              <Typography color="textSecondary">PID</Typography>
              <Typography variant="button">{pidDetail?.result?.data?.pid}</Typography>
            </Box>
          </Box>
        </LkModal>
      )}
      {gradientShift && (
        <LkModal
          open={gradientShift}
          title="Too Many QC Fail, Qty Increased"
          subTitle={`Please QC ${minScanQuantity} items more before moving to Green Channel`}
          handleClose={closeGradientShiftModal}
          primaryBtn={closeGradientShiftModal}
          primaryBtnText="Ok"
        />
      )}
    </>
  );
};

export default BarCodeModels;
