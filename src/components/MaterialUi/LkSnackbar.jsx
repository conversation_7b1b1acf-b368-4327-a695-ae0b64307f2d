import React, { useEffect, useState } from 'react';
import { Snackbar } from '@mui/material';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import { withStyles } from 'tss-react/mui';
import './LkSnackbar.scss';

const StyledAlert = withStyles(Alert, () => ({
  root: {
    fontSize: '14px'
  },
}));

const LkSnackbar = ({ alertDetails, resetNotification }) => {
  const [open, setOpen] = useState(true);
  const { message, severity, secondaryMessage = null } = alertDetails;

  useEffect(() => {
    if (!open) {
      resetNotification();
    }
  }, [open]);

  return (
    <Snackbar
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      open={open}
      autoHideDuration={2000}
      onClose={() => setOpen(false)}
    >
      <StyledAlert severity={severity || 'info'} variant="filled" >
        <div className="lkSnackbarAlert">
          <AlertTitle>{message}</AlertTitle>
          {secondaryMessage}
        </div>
      </StyledAlert>
    </Snackbar>
  )
};

export default React.memo(LkSnackbar);
