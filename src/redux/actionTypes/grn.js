
// SET META
export const SET_GRN_META = 'SET_GRN_META';
// Grn Update PID

export const PID_SELECT_LOAD = 'PID_SELECT_LOAD';
export const PID_SELECT_SUCCESS = 'PID_SELECT_SUCCESS';
export const PID_SELECT_FAIL = 'PID_SELECT_FAIL';

// Grn ADD PID
export const PID_ADD_LOAD = 'PID_ADD_LOAD';
export const PID_ADD_SUCCESS = 'PID_ADD_SUCCESS';
export const PID_ADD_FAIL = 'PID_ADD_FAIL';


// Grn Box Code
export const BOX_CODE_LOAD = 'BOX_CODE_LOAD';
export const BOX_CODE_SUCCESS = 'BOX_CODE_SUCCESS';
export const BOX_CODE_FAIL = 'BOX_CODE_FAIL';


// Grn QC Details
export const QC_DETAIL_LOAD = 'QC_DETAIL_LOAD';
export const QC_DETAIL_SUCCESS = 'QC_DETAIL_SUCCESS';
export const QC_DETAIL_FAIL = 'QC_DETAIL_FAIL';
export const QC_DETAIL_RESET = 'QC_DETAIL_RESET';


// Grn List
export const GRN_LIST_LOAD = 'GRN_LIST_LOAD';
export const GRN_LIST_SUCCESS = 'GRN_LIST_SUCCESS';
export const GRN_LIST_FAIL = 'GRN_LIST_FAIL';

// Grn Create
export const GRN_CREATE_LOAD = 'GRN_CREATE_LOAD';
export const GRN_CREATE_SUCCESS = 'GRN_CREATE_SUCCESS';
export const GRN_CREATE_FAIL = 'GRN_CREATE_FAIL';
export const GRN_CREATE_RESET = 'GRN_CREATE_RESET';

// GET invoices by vendor id
export const GET_INVOICES_BY_VENDOR_ID_LOAD = 'GET_INVOICES_BY_VENDOR_ID_LOAD';
export const GET_INVOICES_BY_VENDOR_ID_SUCCESS = 'GET_INVOICES_BY_VENDOR_ID_SUCCESS';
export const GET_INVOICES_BY_VENDOR_ID_FAIL = 'GET_INVOICES_BY_VENDOR_ID_FAIL';
export const GET_INVOICES_BY_VENDOR_ID_RESET = 'GET_INVOICES_BY_VENDOR_ID_RESET';

export const GET_INVOICE_DETAIL_LOAD = 'GET_INVOICE_DETAIL_LOAD';
export const GET_INVOICE_DETAIL_SUCCESS = 'GET_INVOICE_DETAIL_SUCCESS';
export const GET_INVOICE_DETAIL_FAIL = 'GET_INVOICE_DETAIL_FAIL';

export const GET_PID_LIST_LOAD = 'GET_PID_LIST_LOAD';
export const GET_PID_LIST_SUCCESS = 'GET_PID_LIST_SUCCESS';
export const GET_PID_LIST_FAIL = 'GET_PID_LIST_FAIL';

export const GET_PID_DETAIL_LOAD = 'GET_PID_DETAIL_LOAD';
export const GET_PID_DETAIL_SUCCESS = 'GET_PID_DETAIL_SUCCESS';
export const GET_PID_DETAIL_FAIL = 'GET_PID_DETAIL_FAIL';

// Add Pid

export const ADD_PID_RESET = 'ADD_PID_RESET';
export const ADD_PID_LOAD = 'ADD_PID_LOAD';
export const ADD_PID_SUCCESS = 'ADD_PID_SUCCESS';
export const ADD_PID_FAIL = 'ADD_PID_FAIL';

// Convert Pid
export const CONVERT_PID_LOAD = 'CONVERT_PID_LOAD';
export const CONVERT_PID_SUCCESS = 'CONVERT_PID_SUCCESS';
export const CONVERT_PID_FAIL = 'CONVERT_PID_FAIL';

// get sampling quantity
export const GET_SAMPLING_QTY_LOAD = 'GET_SAMPLING_QTY_LOAD';
export const GET_SAMPLING_QTY_SUCCESS = 'GET_SAMPLING_QTY_SUCCESS';
export const GET_SAMPLING_QTY_FAIL = 'GET_SAMPLING_QTY_FAIL';


export const RESET_PID_LIST = 'RESET_PID_LIST';

// PID Sampling
export const GET_PID_QC_SAMPLING_LOAD = 'GET_PID_QC_SAMPLING_LOAD';
export const GET_PID_QC_SAMPLING_SUCCESS = 'GET_PID_QC_SAMPLING_SUCCESS';
export const GET_PID_QC_SAMPLING_FAIL = 'GET_PID_QC_SAMPLING_FAIL';

// Edit Item
export const GET_ITEM_EDIT_LOAD = 'GET_ITEM_EDIT_LOAD';
export const GET_ITEM_EDIT_SUCCESS = 'GET_ITEM_EDIT_SUCCESS';
export const GET_ITEM_EDIT_FAIL = 'GET_ITEM_EDIT_FAIL';

/// Delete Item
export const GET_ITEM_DELETE_LOAD = 'GET_ITEM_DELETE_LOAD';
export const GET_ITEM_DELETE_SUCCESS = 'GET_ITEM_DELETE_SUCCESS';
export const GET_ITEM_DELETE_FAIL = 'GET_ITEM_DELETE_FAIL';

// QCData update
export const QC_DATA_UPDATE = 'QC_DATA_UPDATE';

// Get QC Data
export const GET_QC_DATA_LOAD = 'GET_QC_DATA_LOAD';
export const GET_QC_DATA_SUCCESS = 'GET_QC_DATA_SUCCESS';
export const GET_QC_DATA_FAIL = 'GET_QC_DATA_FAIL';

// Update Qty
export const UPDATE_QTY_RESET = 'UPDATE_QTY_RESET';
export const UPDATE_QTY_LOAD = 'UPDATE_QTY_LOAD';
export const UPDATE_QTY_SUCCESS = 'UPDATE_QTY_SUCCESS';
export const UPDATE_QTY_FAIL = 'UPDATE_QTY_FAIL';

// grn summary
export const GET_GRN_SUMMARY_LOAD = 'GET_GRN_SUMMARY_LOAD';
export const GET_GRN_SUMMARY_SUCCESS = 'GET_GRN_SUMMARY_SUCCESS';
export const GET_GRN_SUMMARY_FAIL = 'GET_GRN_SUMMARY_FAIL';

export const RESET_ADD_PID_DATA = 'RESET_ADD_PID_DATA';
export const SET_GRN_DATA = 'SET_GRN_DATA';

// get Box items

export const GET_BOX_ITEMS_LOAD = 'GET_BOX_ITEMS_LOAD';

// get Expiry Details

export const GET_EXPIRY_DETAILS_LOAD = 'GET_EXPIRY_DETAILS_LOAD';
export const GET_EXPIRY_DETAILS_SUCCESS = 'GET_EXPIRY_DETAILS_SUCCESS';
export const GET_EXPIRY_DETAILS_FAIL = 'GET_EXPIRY_DETAILS_FAIL';

// Proceed to Green Channel

export const PROCEED_TO_GREEN_CHANNEL = 'PROCEED_TO_GREEN_CHANNEL';

// Product mismatch

export const PRODUCT_MISMATCH_LOAD = 'PRODUCT_MISMATCH_LOAD';
export const PRODUCT_MISMATCH_SUCCESS = 'PRODUCT_MISMATCH_SUCCESS';
export const PRODUCT_MISMATCH_FAIL = 'PRODUCT_MISMATCH_FAIL';

// Update Grn Item
export const UPDATE_GRN_ITEM_LOAD = 'UPDATE_GRN_ITEM_LOAD';
export const UPDATE_GRN_ITEM_SUCCESS = 'UPDATE_GRN_ITEM_SUCCESS';
export const UPDATE_GRN_ITEM_FAIL = 'UPDATE_GRN_ITEM_FAIL';
export const UPDATE_GRN_ITEM_RESET = 'UPDATE_GRN_ITEM_RESET';

// GRN Close
export const GRN_CLOSE_RESET = 'GRN_CLOSE_RESET';
export const GRN_CLOSE_LOAD = 'GRN_CLOSE_LOAD';
export const GRN_CLOSE_SUCCESS = 'GRN_CLOSE_SUCCESS';
export const GRN_CLOSE_FAIL = 'GRN_CLOSE_FAIL';

// Get all grn with its details for an Invoice
export const GET_GRN_DETAILS_FOR_AN_INVOICE_LOAD = 'GET_GRN_DETAILS_FOR_AN_INVOICE_LOAD';
export const GET_GRN_DETAILS_FOR_AN_INVOICE_SUCCESS = 'GET_GRN_DETAILS_FOR_AN_INVOICE_SUCCESS';
export const GET_GRN_DETAILS_FOR_AN_INVOICE_FAIL = 'GET_GRN_DETAILS_FOR_AN_INVOICE_FAIL';
export const GET_GRN_DETAILS_FOR_AN_INVOICE_RESET = 'GET_GRN_DETAILS_FOR_AN_INVOICE_RESET';


// Search By description
export const SEARCH_BY_DESC_LOAD = 'SEARCH_BY_DESC_LOAD';
export const SEARCH_BY_DESC_SUCCESS = 'SEARCH_BY_DESC_SUCCESS';
export const SEARCH_BY_DESC_FAIL = 'SEARCH_BY_DESC_FAIL';
export const SEARCH_BY_DESC_RESET = 'SEARCH_BY_DESC_RESET';

// Search By description Products
export const SEARCH_BY_DESC_PRODUCTS_LOAD = 'SEARCH_BY_DESC_PRODUCTS_LOAD';
export const SEARCH_BY_DESC_PRODUCTS_SUCCESS = 'SEARCH_BY_DESC_PRODUCTS_SUCCESS';
export const SEARCH_BY_DESC_PRODUCTS_FAIL = 'SEARCH_BY_DESC_PRODUCTS_FAIL';


// Reset Grn Page

export const RESET_GRN_CREATE = 'RESET_GRN_CREATE'

// is grn open
export const IS_GRN_OPEN_LOAD = 'IS_GRN_OPEN_LOAD';
export const IS_GRN_OPEN_SUCCESS = 'IS_GRN_OPEN_SUCCESS';
export const IS_GRN_OPEN_FAIL = 'IS_GRN_OPEN_FAIL';
export const IS_GRN_OPEN_RESET = 'IS_GRN_OPEN_RESET';


// Close all open GRN
export const CLOSE_ALL_OPEN_GRN_LOAD = 'CLOSE_ALL_OPEN_GRN_LOAD';
export const CLOSE_ALL_OPEN_GRN_RESET = 'CLOSE_ALL_OPEN_GRN_RESET';
