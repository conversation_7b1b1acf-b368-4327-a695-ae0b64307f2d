import { convertToQueryString } from 'utils/helpers';
import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';

export const getOrderDetails = (searchParam) => {
  // 344565672, abcgdgd, Abjhbhjjj, 227782855, BAR12346
  let URL;
  if (window.location.pathname.includes('/packing')) {
    URL = `${config.apiPath.packingDetails}?id=${searchParam}`;
  } else {
    URL = `${config.apiPath.orderDetails}?scannedEntity=${searchParam}`;
  }
  return tokenInstance().get(URL);
};

export const getOrderByBarcode = (barcode) => {
  let URL;
  if (window.location.pathname.includes('/packing')) {
    URL = `${config.apiPath.packingByBarcode}?id=${barcode}`;
  } else {
    URL = `${config.apiPath.orderByBarcode}/${barcode}`;
  }
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const getUnicomComments = (payload) => {
  const f = convertToQueryString(payload);
  const URL = `${config.apiPath.unicomComments}?${f}`;
  return tokenInstance()
    .get(URL)
    .then((response) => response.data)
    .catch((error) => {
      throw error;
    });
};

export const qcEnableApi = (payload) =>
  tokenInstance().get(`${config.apiPath.qcEnable}/${payload}`);
