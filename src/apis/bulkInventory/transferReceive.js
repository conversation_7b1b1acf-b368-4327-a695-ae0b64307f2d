import { tokenInstance } from '../../utils/axiosInstance';
import config from '../../config';

export const transferReceiveListApi = ({ pageNo, size, searchKeyword='' }) => {
    const url = `${config.transfer.transferList}?keyword=${searchKeyword}&pageNo=${pageNo}&size=${size}`
    return tokenInstance().get(url)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

export const gatePassScanApi = ({ boxCode, facility, transferType }) => {
    const url = `${config.transfer.gatePassScan}/${boxCode}?transfer_type=${transferType}&facility=${facility}`
    return tokenInstance().get(url)
        .then(response => ({ response }))
        .catch(error => { throw error });
}

