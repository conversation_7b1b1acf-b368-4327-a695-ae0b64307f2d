import React from 'react';
import { getOrderListLoad } from 'redux/reducers/distributorOrders';
import { DO_ORDER_MANAGEMENT as CONTENT } from '../../../../../public/localisation/lang_en.json';
import config from '../../../../config';
import OrderDashboard from '../OrderDashboard';
import {
  tableData,
  Shipment_Detail,
  tableSearchData,
  cancelOrderSuccessData
} from './data/tableList.json';

const defaultPayload = {
  page: 0,
  size: 35,
  sortBy: 'createdAt',
  sortOrder: 'DESC',
  searchTerms: { poNumber: [], incrementId: [], createdAt: '', status: ['CREATED'], facility: '' }
};

describe('header component render correctly', () => {
  beforeEach(() => {
    cy.intercept('GET', `${config.distributorOrders.orderList}**`, {
      statusCode: 200,
      body: { data: tableSearchData.data }
    }).as('doOrdersList');

    cy.intercept(
      'POST',
      `${config.distributorOrders.doNumberDetails}/${tableData.data.content[0].incrementId}**`,
      {
        statusCode: 200,
        body: { data: Shipment_Detail.data }
      }
    ).as('ShipmentDetail');

    cy.mount(<OrderDashboard />);
  });

  it('should render correctly', () => {
    cy.getByCy('do-order-header-container').should('exist');
    cy.get('#search-by-do').should('have.attr', 'placeholder', CONTENT.SEARCH_BY_DO_NUMBER);
    cy.getByCy('create-new-order-btn').should('have.text', CONTENT.CREATE_NEW_ORDER);
    cy.getByCy('download-csv-btn').should('have.text', CONTENT.DOWNLOAD_CSV);
    cy.getByCy('orders-container').should('exist');
  });

  it('header container styles are applied correctly', () => {
    cy.getByCy('do-order-header-container')
      .should('have.css', 'display', 'flex')
      .and('have.css', 'justify-content', 'space-between')
      .and('have.css', 'margin-bottom', '27px')
      .and('have.css', 'margin-top', '4px');
  });

  it('Header button styles are applied correctly', () => {
    cy.getByCy('create-new-order-btn')
      .should('have.css', 'border-radius', '4px')
      .and('have.css', 'margin-right', '20px')
      .and('have.css', 'width', '180px');
  });
  it('should download CSV work correctly', () => {
    cy.getByCy('download-csv-btn').should('exist');
    cy.getByCy('download-csv-btn').should('not.be.disabled').click();
  });

  it('Search functionality works correctly', () => {
    cy.get('#search-by-do').type('{enter}');
    cy.get('#search-by-do').type('1930608292');
    cy.get('#search-by-do').type('{enter}');
  });

  it('filter chip functionality works correctly', () => {
    cy.get('#search-by-do').type('1930608292{enter}');
    cy.getByCy('DO Number-filter-delete-icon').click();
    cy.getByCy('Status-filter-delete-icon').click();
  });

  it('Should redirect to Create new Order page', () => {
    cy.getByCy('create-new-order-btn').click();
  });
});

describe('body component render correctly', () => {
  beforeEach(() => {
    cy.viewport('macbook-15');

    cy.intercept('GET', `${config.distributorOrders.orderList}**`, {
      statusCode: 200,
      body: { data: tableData.data }
    }).as('doOrdersListHeader');

    cy.intercept(
      'POST',
      `${config.distributorOrders.cancelOrder}/${tableData.data.content[0].id}**`,
      {
        statusCode: 200,
        body: { data: cancelOrderSuccessData.data }
      }
    ).as('cancelOrderData');

    cy.intercept(
      'POST',
      `${config.distributorOrders.doNumberDetails}/${tableData.data.content[0].incrementId}**`,
      {
        statusCode: 200,
        body: { data: Shipment_Detail.data }
      }
    ).as('ShipmentDetail');

    cy.mount(<OrderDashboard />);
  });

  it('should render correctly', () => {
    cy.getByCy('table-body-container').should('exist');
    cy.getByCy('table-container').should('exist');
    cy.getByCy('table-header-cell-0').find('span').should('have.text', CONTENT.DO_NUMBER);
    cy.getByCy('table-header-cell-1').find('span').should('have.text', CONTENT.PO_NUMBER);
    cy.getByCy('table-header-cell-2').find('span').should('have.text', CONTENT.ENTITY);
    cy.getByCy('table-header-cell-3').find('span').should('have.text', CONTENT.SO_VALUE);
    cy.getByCy('table-header-cell-4').find('span').should('have.text', CONTENT.DO_TYPE);
    cy.getByCy('table-header-cell-5').find('span').should('have.text', CONTENT.PLACED_BY);
    cy.getByCy('table-header-cell-6').find('span').should('have.text', CONTENT.STATUS);
    cy.getByCy('table-header-cell-7').find('span').should('have.text', CONTENT.CREATED_AT);
    cy.getByCy('table-header-cell-8').find('span').should('have.text', CONTENT.FACILITY);
    cy.getByCy('table-header-cell-9').find('span').should('have.text', CONTENT.ACTION);
    cy.getByCy('table-cell-0-0').find('button').should('exist');
  });

  it('should throw error message after clicking download CSV', () => {
    cy.intercept('GET', `${config.distributorOrders.orderList}**`, {
      statusCode: 200,
      body: { data: { ...tableData.data, totalCount: tableData.data.content.length } }
    }).as('doOrdersListDownload');
    cy.getByCy('download-csv-btn').click();
    cy.dispatch(getOrderListLoad(defaultPayload));
    cy.intercept('GET', `${config.distributorOrders.orderList}**`, {
      statusCode: 500,
      body: { data: 'Internal Server Error' }
    }).as('doOrdersListErrorDownload');
    cy.getByCy('download-csv-btn').click();
    cy.getByCy('toaster-message').should('exist');
  });

  it('should work "do number" column filters correctly', () => {
    cy.getByCy('Status-filter-delete-icon').click();
    cy.getByCy('table-header-cell-0').find('svg').click();
    cy.getByCy('checkbox-list-0').find('input').click();
    cy.getByCy('checkbox-list-2').find('input').click();
    cy.getByCy('incrementId-filter-apply').should('exist').click();
    cy.getByCy('table-header-cell-0').find('svg').click();
    cy.getByCy('checkbox-list-0').find('input').click();
    cy.getByCy('checkbox-list-2').find('input').click();
    cy.getByCy('incrementId-filter-apply').should('exist').click();
    cy.getByCy('table-header-cell-0').find('svg').click();
    cy.getByCy('checkbox-list-0').find('input').click();
    cy.getByCy('incrementId-filter-apply').click();
    cy.getByCy('DO Number-filter-delete-icon').click();
  });

  it('should work "po number" column filters correctly', () => {
    cy.getByCy('table-header-cell-1').find('svg').click();
    cy.getByCy('checkbox-list-0').find('input').click();
    cy.getByCy('poNumber-filter-apply').should('exist').click();
  });

  it('should work "Created At" column filters correctly', () => {
    cy.getByCy('table-header-cell-7').find('svg').click({ multiple: true });
    cy.findByRole('tooltip').should('exist');
    cy.getByCy('date-filter-TODAY').click();
    cy.getByCy('Created At-filter-delete-icon').click();
  });

  it('should work extended component correctly', () => {
    cy.getByCy('table-cell-0-0').find('button').should('exist');
    cy.getByCy('KeyboardArrowRight').should('exist');
    cy.getByCy('table-cell-0-0').find('button').click();
    cy.getByCy('KeyboardArrowDown').should('exist');
    cy.getByCy('table-cell-0-0').find('button').click();
  });

  it('cancels an order correctly', () => {
    cy.getByCy('table-cell-0-9').find('button').click();
    cy.getByCy('cancel-order-button').should('have.text', CONTENT.CANCEL_ORDER_SMALL);
    cy.getByCy('cancel-order-button').click();
    cy.getByCy('modal-container').should('exist');
    cy.getByCy('modal-heading').should('have.text', `${CONTENT.CANCEL_ORDER_SMALL}?`);
    cy.getByCy('modal-sub-heading').should('have.text', CONTENT.MODAL_INFO_MESSAGE);
    cy.getByCy('modal-so-number').should(
      'have.text',
      `${CONTENT.SO_NUMBER} ${tableData.data.content[0].incrementId}`
    );
    cy.getByCy('raised-against').should('have.text', `${CONTENT.RAISED_AGAINST} `);
    cy.getByCy('po-number').should('have.text', `PO: ${tableData.data.content[0].poNumber} `);
    cy.getByCy('customer-name').should('have.text', ` ${tableData.data.content[0].customer.name}`);
    cy.getByCy('modal-cancel-order-button').should('have.text', CONTENT.CANCEL_ORDER);
    cy.getByCy('go-back-button').should('have.text', CONTENT.GO_BACK);
    cy.getByCy('modal-cancel-order-button').click();
    cy.getByCy('success-image').should(
      'have.attr',
      'src',
      `${import.meta.env.VITE_STATIC_SERVER}/images/success-icon.png`
    );
    cy.getByCy('success-message-heading').should('exist');
    cy.getByCy('success-message-heading').should('have.text', `${CONTENT.ORDER_CANCELLED}!`);
    cy.getByCy('success-message-subHeading').should('exist');
    cy.getByCy('success-message-subHeading').should(
      'have.text',
      // eslint-disable-next-line max-len
      `${CONTENT.ORDER_CANCELLED_FOR_SO_NO} ${tableData.data.content[0].incrementId}`
    );
    cy.getByCy('close-modal').should('exist');
    cy.getByCy('close-modal').click();
  });

  it('should work correctly after getting error for cancel order', () => {
    cy.intercept(
      'POST',
      `${config.distributorOrders.cancelOrder}/${tableData.data.content[0].id}**`,
      {
        statusCode: 500,
        body: { data: 'internal server error' }
      }
    ).as('cancelOrderDataError');

    cy.intercept(
      'POST',
      `${config.distributorOrders.doNumberDetails}/${tableData.data.content[0].incrementId}**`,
      {
        statusCode: 500,
        body: { data: 'Internal Server Error' }
      }
    ).as('ShipmentDetailError');
    cy.getByCy('table-cell-0-9').find('button').click();
    cy.getByCy('cancel-order-button').click();
    cy.getByCy('modal-cancel-order-button').click();
    cy.getByCy('go-back-button').click();
    cy.get('body').click();
  });
});
