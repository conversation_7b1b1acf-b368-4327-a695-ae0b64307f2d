import React, { useEffect, useState, useCallback } from 'react'
import { Box } from '@mui/material';
import BlockedTabActions from './BlockedTabActions';
import BlockedProductsTable from './BlockedProductsTable'
import { useDispatch, useSelector } from 'react-redux';



const BlockedTab = ({  mainGrnSearch, sortKey, setSortkey, selectedFilterList, onChangeFilterList, deleteSelectedFilter, resetFilters, sortingData, objPayload }) => {
    const [actionValues, setActionValues] = useState( {
        manual_override_allowed: [],
        manual_override_rejected: []
    })
    const dispatch = useDispatch();
    const { manualOverrideSuccess } = useSelector(state => state.grnListing);

    useEffect(() => {
        if (manualOverrideSuccess) {
            setActionValues({
                manual_override_allowed: [],
                manual_override_rejected: []
            })
        }
    }, [manualOverrideSuccess])

    const overrideFun = (isAllowed, grnCode, pid) => {
        let tempState = { ...actionValues }
        const objName = isAllowed ? "manual_override_allowed" : "manual_override_rejected"
        let alreadyPresent = false
        if (isAllowed) {
            alreadyPresent = tempState.manual_override_rejected.some((t) => t.grn_code === grnCode && t.pid === pid)
        }
        else alreadyPresent = tempState.manual_override_allowed.some((t) => t.grn_code === grnCode && t.pid === pid)
        if (!alreadyPresent) {
            let findIndex = actionValues[objName].findIndex((data) => data.grn_code === grnCode && data.pid === pid)
            if (findIndex === -1) {
                let newData = { "grn_code": grnCode, "pid": pid }
                tempState[objName].push(newData)
            }
            else tempState[objName].splice(findIndex, 1)
        }
        setActionValues(tempState)
    }

    const clearSelection = useCallback(() => setActionValues({
        manual_override_allowed: [],
        manual_override_rejected: []
    }), [setActionValues])


    const resetFun = () => {

        setSortkey('product_created_by')
        sortingData.key = 'product_created_by';
        sortingData.order = 'DESC';
        let tempData = selectedFilterList.filter(({ key }) => !key.startsWith("BLOCKED"))
        resetFilters(tempData)
    }
    return <div >
        <Box>
            <BlockedTabActions
                dispatch={dispatch}
                clearSelection={clearSelection}
                actionValues={actionValues}
                selectedFilterList={selectedFilterList}
                deleteSelectedFilter={deleteSelectedFilter}
                resetFilters={resetFun}
            />
        </Box>
        <BlockedProductsTable
            actionValues={actionValues}
            overrideFun={overrideFun}
            mainGrnSearch={mainGrnSearch}
            selectedFilterList={selectedFilterList}
            onChangeFilterList={onChangeFilterList}
            objPayload={objPayload}
            sortKey={sortKey}
            setSortkey={setSortkey}
            sortingData={sortingData}

        />
    </div>
}

export default BlockedTab