import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Form, Field } from 'react-final-form';

import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';

import { PurchaseOrderStatus } from 'config/PurchaseOrderStatus';

import useKeyboardShortcut from 'common/useKeyboardShortcut';
import useProccessStatusHook from 'common/useProccessStatusHook';

import {
  convertDateFormat,
  getQueryParam,
  roundUptoFixedDigits,
  fileDownload,
  formatDateDashedSeparated,
  getCurrencySumbol
} from 'utils/helpers';
import { required } from 'utils/validation';

import LkModal from 'components/Modal/Modal';
import FlexBox from 'components/core/FlexBox';
import Spinner from 'components/Spinner/Spinner';
import MoreItem from 'components/common/MoreItem';
import LkToolTip from 'components/ToolTip/ToolTip';
import LkInput from 'components/MaterialUi/LkInput';
import { InfoStrip, InfoTable } from 'components/common';
import ChipableInput from 'components/common/ChipableInput';
import SubtitleInfoStrip from 'components/common/SubtitleInfoStrip';
import PrintConfirmModal from 'components/PrintConfirmModal/PrintConfirmModal';

import {
  purchaseOrderDetailLoad,
  submitPurchaseOrderLoad,
  submitApprovalLoad,
  submitPurchaseOrderReset,
  submitApprovalReset,
  editPo,
  amendDraftReset,
  amendPoLoad,
  amnedPoReset,
  generatePoNumLoad,
  exportPoLoad,
  exportPoReset,
  processingStatusLoad,
  processingStatusReset,
  purchaseOrderDetailReset
} from 'redux/actionCreators/purchaseOrder';
import { toastOpen } from 'redux/actionCreators/toast';
import { LOCALISATION } from 'redux/reducers/localisation';
import { pdfDownloadLoad, pdfDownloadReset } from 'redux/actionCreators/pdfDownload';

import PoActions from './PoActions';
import ReasonForHoldAmend from './ReasonForHoldAmend';
import AddressBox from '../components/AddressBox';
import ClosePo from './ClosePo';
import ClonePo from './ClonePo';
import TabsContainer from './TabsContainer';
import './purchaseOrderDetail.scss';

const useStyles = makeStyles()((theme) => ({
  root: {
    backgroundColor: theme.palette.white,
    height: '90vh'
  },
  backdrop: {
    zIndex: theme.zIndex.drawer + 1,
    color: '#fff'
  },
  bottomBox: {
    width: 282,
    height: 122
  }
}));

const PurchaseOrderDetail = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [openPoDetailModal, setOpenPoDetailModal] = useState();
  const [isReasonForHoldAmendShow, setIsReasonForHoldAmendShow] = useState(false);
  const [isHoldOrAmend, setIsHoldOrAmend] = useState();
  const [isShowClosePo, setIsShowClosePo] = useState();
  const [amending, setAmending] = useState(null);
  const [isShowCloneModal, setIsShowCloneModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);

  const PO = useSelector((state) => state[LOCALISATION].localeData.PO);

  const {
    purchaseOrderDetail,
    purchaseOrderDetailProccessing,
    submitPoData,
    submitPoProccessing,
    submitApprovalData,
    submitApprovalProccessing,
    amendPoData,
    amendPoDraft,
    amendPoProccessing,
    generatePoNumber,
    generatePoNumberLoading,
    exportPoData,
    processingStatus,
    processingStatusFail
  } = useSelector((state) => state.purchaseOrder);
  const { pdfDownloadSuccess, pdfDownloadError } = useSelector((state) => state.pdfDownload);
  const [poStatus, statusSuccessFul, setPoStatus] = useProccessStatusHook({
    processingStatus,
    processingStatusFail,
    processingStatusLoad,
    processingStatusReset
  });
  const { classes } = useStyles();
  const poNumTemp = getQueryParam(location.search.replace('?', ''), 'poNum');
  const getPo = (overheads = false) => {
    dispatch(purchaseOrderDetailLoad({ ponum: poNumTemp, items: true, overheads }));
  };

  const procurementTypeValues = [
    { key: 'DISPLAY', value: PO.DISPLAY_ORDER },
    { key: 'BULK', value: PO.BULK_ORDER },
    { key: 'FOC', value: PO.FOC }
  ];

  const setAmendingFun = useCallback(
    (type, reason) => {
      setAmending({ type, reason });
      getPo(true);
    },
    [setAmending]
  );

  const getPoStausObject = (refNo) => ({ refNo });

  const goToCreatePo = (type = 'Edit', reason) => {
    dispatch(editPo({ data: purchaseOrderDetail.data, type, reason }));
    navigate('/po/create');
  };

  const generatePoLoad = () => {
    dispatch(generatePoNumLoad());
  };

  useEffect(() => {
    if (pdfDownloadSuccess) {
      fileDownload(pdfDownloadSuccess, `PO-NEXS-${poNumTemp}`, 'pdf');
      dispatch(pdfDownloadReset());
    }
    if (pdfDownloadError) {
      dispatch(pdfDownloadReset());
    }
  }, [pdfDownloadError, pdfDownloadSuccess]);

  useEffect(() => {
    getPo();
  }, []);

  useEffect(() => {
    if (statusSuccessFul) {
      getPo();
    }
  }, [statusSuccessFul]);

  useEffect(() => {
    if (submitPoData?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: submitPoData?.meta?.displayMessage,
          severity: 'success'
        })
      );
      setOpenPoDetailModal(false);
      getPo();
      setPoStatus(getPoStausObject(submitPoData?.data));
    }
  }, [submitPoData, dispatch]);

  useEffect(() => {
    if (submitPoData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: submitPoData.response.data.meta.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [submitPoData, dispatch]);

  useEffect(() => {
    if (submitApprovalData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: submitApprovalData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    } else if (submitApprovalData?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: submitApprovalData?.meta?.displayMessage,
          severity: 'success'
        })
      );
      setPoStatus(getPoStausObject(submitApprovalData?.data));
    }
  }, [submitApprovalData, dispatch]);

  useEffect(() => {
    if (amendPoData?.response?.data?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: amendPoData?.response?.data?.meta?.displayMessage,
          severity: 'error'
        })
      );
    } else if (amendPoData?.meta?.displayMessage) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: amendPoData?.meta?.displayMessage,
          severity: 'success'
        })
      );
      dispatch(amendDraftReset());
      setPoStatus(getPoStausObject(amendPoData?.data));
    }
  }, [amendPoData, dispatch]);

  useEffect(() => {
    if (generatePoNumber && isShowCloneModal) {
      getPo(true);
    }
  }, [generatePoNumber, dispatch]);

  useEffect(() => {
    if (purchaseOrderDetail?.data?.items[0]?.overheads) {
      if (generatePoNumber) {
        purchaseOrderDetail.data.po_num = generatePoNumber;
        purchaseOrderDetail.data.parent_po_num = null;
        purchaseOrderDetail.data.version = 0;
        purchaseOrderDetail.data.status = 'DRAFT';
      }
      if (amending) {
        goToCreatePo(amending.type, amending.reason);
      } else {
        goToCreatePo();
      }
    }
  }, [purchaseOrderDetail, generatePoNumber, amending]);

  useEffect(() => {
    if (amendPoDraft) {
      generatePoLoad();
    }
  }, [amendPoDraft, dispatch]);

  useEffect(
    () => () => {
      dispatch(purchaseOrderDetailReset());
      dispatch(submitPurchaseOrderReset());
      dispatch(submitApprovalReset());
      dispatch(amendDraftReset());
      dispatch(amnedPoReset());
      dispatch(exportPoReset());
    },
    []
  );

  useEffect(() => {
    if (exportPoData) {
      if (exportPoData.error?.response?.data) {
        dispatch(
          toastOpen({
            isToastOpen: true,
            heading: exportPoData.error.response.data.meta?.displayMessage,
            severity: 'error'
          })
        );
      } else {
        const po_num = purchaseOrderDetail?.data.po_num;
        fileDownload(exportPoData?.response?.data, `PO-NEXS-${po_num}`);
      }
      dispatch(exportPoReset());
    }
  }, [exportPoData]);

  const exportPo = () => {
    const po_num = purchaseOrderDetail?.data.po_num;
    const vendor_name = purchaseOrderDetail?.data.vendor_name;
    dispatch(exportPoLoad({ po_num, vendor_name, fromPodetails: true }));
  };

  const row = (key, value, className = '') => (
    <div className={`display-flex justify-content-space-between mr-b15 fs12 ${className}`}>
      <div>{key}</div>
      <div>{value}</div>
    </div>
  );

  const convertAddressString = (addressArray) => {
    let add;
    addressArray.forEach((address, index) => {
      const con = index === addressArray.length - 1 ? '.' : ', ';
      if (address) {
        add = add ? add + address + con : address + con;
      }
    });
    return add;
  };

  const errorContainer = () => {
    const isError = purchaseOrderDetail?.meta?.displayMessage;
    return isError ? (
      <div className="display-grid-center pd-t15">{isError}</div>
    ) : (
      <Spinner className="display-grid-center pd-15" />
    );
  };

  const openPoDModal = () => {
    setOpenPoDetailModal(true);
  };

  const submitPoReq = (data) => {
    const {
      parent_po_num,
      po_num,
      vendor_id,
      shipping_date,
      expiry_date,
      version,
      items,
      reference_number,
      po_type,
      season,
      procurement_type,
      shipment_type
    } = data;
    const reqItemFields = [
      'id',
      'product_id',
      'quantity',
      'vendor_unit_cost_price',
      'cgst_rate',
      'igst_rate',
      'sgst_rate',
      'ugst_rate',
      'price_with_taxes',
      'enabled'
    ];
    const newItems = [...items];
    newItems.forEach((item, index) => {
      const p = { ...item };
      Object.keys(p).forEach((key) => {
        if (reqItemFields.indexOf(key) === -1) {
          delete p[key];
        }
      });
      newItems[index] = p;
    });
    return {
      parent_po_num,
      po_num,
      po_type,
      vendor_id,
      shipping_date,
      expiry_date,
      version,
      items: newItems,
      reference_number,
      season,
      procurement_type,
      shipment_type,
      current_timestamp: new Date().getTime()
    };
  };

  const submitRefenceNo = (values) => {
    const { reference_number, procurement_type, season, expiry_date, shipping_date } = values;
    let refConvert;
    if (reference_number) {
      reference_number.forEach((ref) => {
        refConvert = refConvert ? refConvert.concat(`,${ref}`) : ref;
      });
    }
    let req = submitPoReq(purchaseOrderDetail?.data);
    req = {
      ...req,
      reference_number: refConvert,
      procurement_type,
      season,
      expiry_date,
      shipping_date,
      update_property: true
    };
    dispatch(submitPurchaseOrderLoad(req));
  };

  const goToGrn = () => {
    const vendor_name = purchaseOrderDetail?.data?.vendor_name;
    const po_num = purchaseOrderDetail?.data?.po_num;
    const vendor_id = purchaseOrderDetail?.data?.vendor_id;
    navigate(`/grn/create?vendorName=${vendor_name}&poNumber=${po_num}&vendorId=${vendor_id}`);
  };

  const goToCreateInvoice = () => {
    const po_num = purchaseOrderDetail.data?.po_num;
    const vendor_id = purchaseOrderDetail?.data?.vendor_id;
    navigate(`/invoice/create?poNum=${po_num}&vendorName=${vendor_id}`);
  };

  const changeHoldAmendShow = useCallback(
    (type) => {
      if (!isReasonForHoldAmendShow) {
        setIsHoldOrAmend(type);
        setIsReasonForHoldAmendShow(true);
      }
    },
    [isHoldOrAmend, isReasonForHoldAmendShow]
  );

  useKeyboardShortcut([17, 72], () => {
    const status = purchaseOrderDetail?.data?.status;
    if (status === 'APPROVED' || status === 'HOLD') {
      changeHoldAmendShow(status === 'APPROVED' ? 'hold' : 'unhold');
    }
  });

  useKeyboardShortcut([17, 77], () => {
    const status = purchaseOrderDetail?.data?.status;
    if (status === 'APPROVED') {
      changeHoldAmendShow('Amend');
    }
  });

  useKeyboardShortcut([17, 81], () => {
    const status = purchaseOrderDetail?.data?.status;
    if (status === 'APPROVED') {
      setIsShowClosePo(true);
    }
  });

  useKeyboardShortcut([17, 16, 67], () => {
    setIsShowCloneModal(true);
  });

  useKeyboardShortcut([17, 69], () => {
    exportPo();
  });

  useKeyboardShortcut([17, 80], () => {
    setShowPrintModal(true);
  });

  const callPoStatus = useCallback(
    (value) => {
      if (!poStatus) {
        setPoStatus(value);
      }
    },
    [poStatus]
  );

  useKeyboardShortcut([68, 18], () => {
    openPoDModal();
  });

  const clonePo = () => {
    generatePoLoad();
  };

  const submitApproval = () => {
    dispatch(submitApprovalLoad(submitPoReq(purchaseOrderDetail?.data)));
  };

  const submitAmend = () => {
    const { data, reason } = amendPoDraft;
    if (data?.items?.items) {
      data.items = data.items.items;
    }
    const obj = submitPoReq(data);
    obj.parent_po_num = obj.po_num;
    obj.po_num = generatePoNumber;
    obj.amend_comments = reason;
    dispatch(amendPoLoad(obj));
  };

  const cancelAmending = () => {
    dispatch(amendDraftReset());
    getPo();
  };

  const view = () => {
    const {
      po_num,
      created_at,
      vendor_name,
      currency,
      order_qty,
      order_accepted_quantity,
      order_rejected_quantity,
      order_pending_quantity,
      total_taxes,
      total_customs_cost,
      total_additional_cost,
      total_logistics_cost,
      total_cha_cost,
      total_po_cost,
      vendor_id,
      status,
      po_type,
      facility_name,
      approved_by,
      approved_at,
      created_by,
      expiry_date,
      total_vendor_cost,
      season,
      shipping_address_line1,
      shipping_address_line2,
      shipping_address_city,
      shipping_address_state,
      shipping_address_pincode,
      billing_address_line1,
      billing_address_line2,
      billing_address_city,
      billing_address_state,
      billing_address_pincode,
      total_flexi1_cost,
      total_flexi2_cost,
      reference_number,
      canReceive,
      po_hold_reason,
      parent_po_num,
      child_po_num,
      procurement_type,
      primary_contact_name,
      shipping_date,
      shipment_type
    } = purchaseOrderDetail?.data || {};

    const convertedCurrency = getCurrencySumbol(currency);

    const infoTableData = [
      { key: PO.PO, value: po_num },
      { key: PO.VENDOR_CODE, value: vendor_id, header: vendor_name },
      { key: PO.PO_STATUS, value: PurchaseOrderStatus[status].text },
      {
        key: PO.AMENDED_TO,
        value: child_po_num,
        link: `${window.location.origin}/po/detail?poNum=${child_po_num}&fromPo=true`
      },
      { key: PO.SHIPMENT_MODE, value: shipment_type },
      { key: PO.TYPE, value: po_type },
      { key: PO.FROM_PARTY, value: facility_name },
      { key: PO.APPROVED_BY, value: approved_by },
      {
        key: PO.APPROVED_ON,
        value: approved_at && convertDateFormat(approved_at, 'shortDateTime')
      },
      { key: PO.CREATED_BY, value: created_by },
      { key: PO.REASON_FOR_HOLD, value: po_hold_reason },
      {
        key: PO.CREATED_ON,
        value: created_at ? convertDateFormat(created_at, 'shortDateTime') : null
      },
      {
        key: PO.AMENDED_FROM,
        value: parent_po_num,
        link: `${window.location.origin}/po/detail?poNum=${parent_po_num}&fromPo=true`
      }
    ];

    const infoStripData = [
      {
        key: PO.PO,
        value: po_num,
        status: amendPoDraft ? 'Amending' : PurchaseOrderStatus[status]?.text,
        type: PurchaseOrderStatus[status]?.type,
        className: 'fs16 fw-bold text-turquioise_surf cursor-pointer',
        onClick: () => openPoDModal(),
        parent_po_num,
        parent_po_num_url: `${window.location.origin}/po/detail?poNum=${parent_po_num}&fromPo=true`
      },
      { key: PO.DATE, value: convertDateFormat(created_at, 'shortDate') },
      { key: PO.VENDOR, value: vendor_name }
    ];
    const addressBoxData = [
      {
        superTitle: PO.BILLING_ADDRESS,
        title: primary_contact_name,
        text: convertAddressString([
          billing_address_line1,
          billing_address_line2,
          billing_address_city,
          billing_address_state,
          billing_address_pincode
        ])
      },
      {
        superTitle: PO.SHIPPING_ADDRESS,
        title: primary_contact_name,
        text: convertAddressString([
          shipping_address_line1,
          shipping_address_line2,
          shipping_address_city,
          shipping_address_state,
          shipping_address_pincode
        ])
      }
    ];
    const subtitleInfoStripData = [
      {
        title: order_qty || 0,
        subtitle: PO.TOTAL_ORDER,
        titleClassName: 'fs20',
        subTitleClassName: 'fs14 text-99 mr-t5'
      },
      {
        title: order_accepted_quantity || 0,
        subtitle: PO.ACCEPTED,
        titleClassName: 'fs20',
        subTitleClassName: 'fs14 text-99 mr-t5'
      },
      {
        title: order_rejected_quantity || 0,
        subtitle: PO.REJECTED,
        titleClassName: 'fs20 text-red',
        subTitleClassName: 'fs14 text-99 mr-t5'
      },
      {
        title: order_pending_quantity || 0,
        subtitle: PO.PENDING,
        titleClassName: 'fs20 text-bright-yellow',
        subTitleClassName: 'fs14 text-99 mr-t5'
      }
    ];
    const priceStripData = [
      {
        title: PO.SUBTOTAL,
        subtitle: `${convertedCurrency}${roundUptoFixedDigits(total_vendor_cost, 2)}`,
        titleClassName: 'fs12 mr-r40',
        subTitleClassName: 'fs16 mr-t8 mr-r40'
      },
      {
        title: PO.TAXES_DUTY,
        subtitle: `${convertedCurrency}${roundUptoFixedDigits(
          total_taxes + total_customs_cost,
          2
        )}`,
        titleClassName: 'fs12 mr-r0',
        subTitleClassName: 'fs16 mr-t8 mr-r40'
      },
      {
        title: PO.OVERHEADS,
        subtitle: `${convertedCurrency}${roundUptoFixedDigits(total_additional_cost, 2)} `,
        titleClassName: 'fs12 mr-r40',
        subTitleClassName: 'fs16 mr-t8 mr-r40'
      },
      {
        title: PO.TOTAL,
        subtitle: `${convertedCurrency}${roundUptoFixedDigits(total_po_cost, 2)} `,
        titleClassName: 'fs12',
        subTitleClassName: 'fs16 mr-t8 fw-bold'
      }
    ];
    const priceTooltip = (tooltipCurrency) => (
      // width='400px'

      <div className="pd-20">
        {row(PO.SUBTOTAL, `${tooltipCurrency} ${roundUptoFixedDigits(total_vendor_cost, 2)} `)}
        {row(PO.DUTY, `${tooltipCurrency} ${roundUptoFixedDigits(total_customs_cost, 2)} `)}
        {row(
          PO.TOTAL_TAX_ON_SALES,
          `${tooltipCurrency} ${roundUptoFixedDigits(total_taxes, 2)} `,
          'pd-b15 border-bottom-dd-dashed'
        )}
        {row(
          PO.LOGISTICS_CHARGES,
          `+ ${tooltipCurrency} ${roundUptoFixedDigits(total_logistics_cost, 2)} `
        )}
        {row(
          PO.INSURANCE_CHARGES,
          `+ ${tooltipCurrency} ${roundUptoFixedDigits(total_flexi1_cost, 2)} `
        )}
        {row(
          PO.FORWADER_CHARGES,
          `+ ${tooltipCurrency} ${roundUptoFixedDigits(total_flexi2_cost, 2)} `
        )}
        {row(
          PO.CHA_CHARGES,
          `+ ${tooltipCurrency} ${roundUptoFixedDigits(total_cha_cost, 2)} `,
          'pd-b15 border-bottom-dd-dashed'
        )}
        {row(
          PO.TOTAL_AMOUNT,
          `${tooltipCurrency} ${roundUptoFixedDigits(total_po_cost, 2)} `,
          'fw-bold fs18'
        )}
      </div>
    );
    const poDetailDisbaled = status === 'SHORT_CLOSED' || status === 'CLOSED';

    const printFunCall = () => {
      setShowPrintModal(false);
      dispatch(pdfDownloadLoad({ type: 'po', id: poNumTemp }));
    };

    return (
      <div className="po-detail">
        <div>
          <InfoStrip data={infoStripData}>
            <div className="display-flex align-items-center">
              {amendPoDraft && (
                <Button
                  onClick={() => cancelAmending()}
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="outlined"
                >
                  {PO.CANCEL}
                </Button>
              )}
              {(status === 'DRAFT' || status === 'REJECTED' || amendPoDraft) && (
                <Button
                  onClick={() => getPo(true)}
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, width: 115, height: 36 }}
                  variant="outlined"
                  disabled={poStatus}
                >
                  {PO.EDIT}
                </Button>
              )}
              {amendPoDraft && !amendPoProccessing && generatePoNumber && (
                <Button
                  onClick={() => submitAmend()}
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  disabled={poStatus}
                  variant="contained"
                >
                  {PO.SUBMIT_AMEND}
                </Button>
              )}
              {amendPoDraft && amendPoProccessing && generatePoNumber && (
                <Button
                  disabled
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36, width: '170px' }}
                  variant="contained"
                >
                  <Spinner className="display-grid-center" />
                </Button>
              )}
              {status === 'DRAFT' && !submitApprovalProccessing && (
                <Button
                  onClick={() => submitApproval()}
                  color="primary"
                  disabled={poStatus}
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="contained"
                >
                  {PO.SUBMIT_FOR_APPROVAL}
                </Button>
              )}
              {status === 'DRAFT' && submitApprovalProccessing && (
                <Button
                  disabled
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36, width: '170px' }}
                  variant="contained"
                >
                  <Spinner className="display-grid-center" />
                </Button>
              )}
              {!amendPoDraft && status === 'APPROVED' && (
                <Button
                  onClick={() => goToGrn()}
                  disabled={!canReceive}
                  color="primary"
                  style={{ marginRight: 10, borderRadius: 8, height: 36 }}
                  variant="outlined"
                >
                  {PO.START_RECEIVING}
                </Button>
              )}
              {(status === 'APPROVED' || status === 'HOLD') && !amendPoDraft && (
                <Button
                  onClick={() => goToCreateInvoice()}
                  disabled={status === 'HOLD' || order_pending_quantity === 0}
                  color="primary"
                  style={{ borderRadius: 8, height: 36 }}
                  variant="outlined"
                >
                  {PO.CREATE_INVOICE}
                </Button>
              )}
              <div className="mr-l20">
                <MoreItem
                  itemsToShow={
                    <PoActions
                      PO={PO}
                      status={status}
                      changeHoldAmendShow={(type) => changeHoldAmendShow(type)}
                      showClosePo={() => setIsShowClosePo(true)}
                      showCloneModal={() => setIsShowCloneModal(true)}
                      exportPo={() => exportPo()}
                      showPrintModal={() => setShowPrintModal(true)}
                    />
                  }
                />
              </div>
            </div>
          </InfoStrip>
          <Box p={3} pt={1.5} pb={2}>
            <div className="display-flex justify-content-space-between">
              <SubtitleInfoStrip data={subtitleInfoStripData} />
              <LkToolTip title={priceTooltip(convertedCurrency)} width="280px">
                <SubtitleInfoStrip data={priceStripData} />
              </LkToolTip>
            </div>
          </Box>
        </div>

        <Box pl={3} pr={3}>
          <TabsContainer
            purchaseOrderDetail={purchaseOrderDetail}
            purchaseOrderDetailProccessing={purchaseOrderDetailProccessing}
            currency={convertedCurrency}
          />
        </Box>
        {openPoDetailModal && (
          <LkModal
            open={openPoDetailModal}
            upperHeading={PO.PO_DETAILS}
            handleClose={() => setOpenPoDetailModal(false)}
          >
            <div className="pos-rel">
              <Form
                onSubmit={submitRefenceNo}
                initialValues={{
                  reference_number:
                    !submitPoProccessing && reference_number && reference_number.split(','),
                  procurement_type: procurement_type && procurement_type.toUpperCase(),
                  season,
                  expiry_date,
                  shipping_date
                }}
                render={({ handleSubmit, values }) => (
                  <form
                    onSubmit={handleSubmit}
                    name="refrence-detail-form"
                    className="refrence-detail-form"
                    noValidate
                  >
                    <FlexBox pt={2.5} justifyContent="space-between" width="750px">
                      <InfoTable data={infoTableData} />
                      <Box>
                        <SubtitleInfoStrip data={subtitleInfoStripData} />
                        <div className="display-flex mr-t25 mr-b25 justify-content-space-between">
                          <Field name="season">
                            {({ input }) => (
                              <div>
                                <LkInput
                                  disabled={status !== 'DRAFT'}
                                  {...input}
                                  fullWidth
                                  label={PO.SEASON}
                                />
                              </div>
                            )}
                          </Field>
                          <Field name="procurement_type">
                            {({ input }) => (
                              <div style={{ width: '50%' }}>
                                <LkInput
                                  disabled={status !== 'DRAFT'}
                                  {...input}
                                  select
                                  options={[]}
                                  fullWidth
                                  label={PO.PROCUREMENT_TYPE}
                                >
                                  {procurementTypeValues.map(({ key, value }) => (
                                    <MenuItem key={value} value={key}>
                                      {value}
                                    </MenuItem>
                                  ))}
                                </LkInput>
                              </div>
                            )}
                          </Field>
                        </div>
                        <div className="display-flex mr-t25 mr-b25 justify-content-space-between">
                          <Field name="expiry_date" validate={required}>
                            {({ input, meta }) => (
                              <LkInput
                                label={PO.EXPIRY_DATE}
                                variant="outlined"
                                disabled={status !== 'DRAFT'}
                                type="date"
                                {...input}
                                error={meta.error && meta.touched}
                                InputLabelProps={{
                                  shrink: true
                                }}
                                inputProps={{
                                  min: values.shipping_date
                                    ? values.shipping_date
                                    : formatDateDashedSeparated(Date.now())
                                }}
                                // mr={3}
                              />
                            )}
                          </Field>
                          <div style={{ width: '50%' }}>
                            <Field name="shipping_date" validate={required}>
                              {({ input, meta }) => (
                                <LkInput
                                  label={PO.SHIPPING_DATE}
                                  variant="outlined"
                                  type="date"
                                  disabled={status !== 'DRAFT'}
                                  {...input}
                                  error={meta.error && meta.touched}
                                  InputLabelProps={{
                                    shrink: true
                                  }}
                                  inputProps={{
                                    min: formatDateDashedSeparated(Date.now()),
                                    max: values.expiry_date ? values.expiry_date : null
                                    // min: "2020-07-15"
                                  }}
                                  // mr={3}
                                />
                              )}
                            </Field>
                          </div>
                        </div>
                        <div className="mr-t25">
                          <Field name="reference_number">
                            {({ input }) => (
                              <div className="mr-t25 mr-b20 refno-container">
                                <ChipableInput
                                  disableInput={poDetailDisbaled}
                                  label={PO.REFRENCE_DETAILS}
                                  {...input}
                                />
                              </div>
                            )}
                          </Field>
                        </div>
                        <AddressBox data={addressBoxData} />
                      </Box>
                    </FlexBox>
                    <div className="text-center mr-b40 mr-t40">
                      <Button
                        onClick={() => setOpenPoDetailModal(false)}
                        style={{ borderRadius: '8px', marginRight: '10px' }}
                        variant="outlined"
                        color="primary"
                      >
                        {PO.CANCEL_UPPERCASE}
                      </Button>
                      {!submitPoProccessing ? (
                        <Button
                          disabled={poDetailDisbaled}
                          type="submit"
                          style={{ borderRadius: '8px', marginLeft: '10px' }}
                          className="mr-l10"
                          variant="contained"
                          color="primary"
                        >
                          {PO.SAVE_AND_CLOSE}
                        </Button>
                      ) : (
                        <Button
                          disabled
                          style={{ borderRadius: '8px', marginLeft: '10px' }}
                          className="mr-l10"
                          variant="contained"
                          color="primary"
                        >
                          <Spinner />
                        </Button>
                      )}
                    </div>
                  </form>
                )}
              />
            </div>
          </LkModal>
        )}
        {isReasonForHoldAmendShow && (
          <ReasonForHoldAmend
            amendingFun={setAmendingFun}
            open={isReasonForHoldAmendShow}
            close={() => setIsReasonForHoldAmendShow(false)}
            type={isHoldOrAmend}
            po_num={po_num}
            poData={purchaseOrderDetail?.data}
            callPoStatus={callPoStatus}
          />
        )}
        {isShowClosePo && (
          <ClosePo
            open={isShowClosePo}
            close={() => setIsShowClosePo(false)}
            poData={purchaseOrderDetail?.data}
            callPoStatus={callPoStatus}
          />
        )}
        {isShowCloneModal && (
          <ClonePo
            open={isShowCloneModal}
            close={() => setIsShowCloneModal(false)}
            clonePo={() => clonePo()}
            cloneProcessing={generatePoNumberLoading}
          />
        )}
        {showPrintModal && (
          <PrintConfirmModal
            open={showPrintModal}
            printFun={printFunCall}
            handleClose={() => setShowPrintModal(false)}
            printOptions={[{ key: 'PO', value: 'PO' }]}
          />
        )}
      </div>
    );
  };

  return (
    <div className={`${classes.root} `}>
      {!purchaseOrderDetailProccessing && purchaseOrderDetail?.data ? view() : errorContainer()}
    </div>
  );
};

export default PurchaseOrderDetail;
