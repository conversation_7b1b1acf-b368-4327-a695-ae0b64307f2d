import * as action from '../actionTypes/manifest';

// Manifest list
export const manifestListLoad = (data) => ({ type: action.GET_MANIFEST_LIST_LOAD, payload: data });
export const manifestListSuccess = (data) => ({
  type: action.GET_MANIFEST_LIST_SUCCESS,
  payload: data
});
export const manifestListFail = () => ({ type: action.GET_MANIFEST_LIST_FAIL });

// Remove list
export const removeManifestLoad = (id) => ({ type: action.REMOVE_MANIFEST_LOAD, payload: id });
export const removeManifestSuccess = (id) => ({
  type: action.REMOVE_MANIFEST_SUCCESS,
  payload: id
});
export const removeManifestFail = () => ({ type: action.REMOVE_MANIFEST_FAIL });
export const resetRemoveManifest = () => ({ type: action.REMOVE_MANIFEST_RESET });
// Fetch Manifest channel (Order Type) list
export const fetchChannelLoad = () => ({ type: action.FETCH_CHANNEL_LOAD });
export const fetchChannelSuccess = (data) => ({
  type: action.FETCH_CHANNEL_SUCCESS,
  payload: data
});
export const fetchChannelFail = () => ({ type: action.FETCH_CHANNEL_FAIL });
// GET Manifest Details
export const manifestDetailsLoad = (payload) => ({ type: action.MANIFEST_DETAILS_LOAD, payload });
export const manifestDetailsSuccess = (data) => ({
  type: action.MANIFEST_DETAILS_SUCCESS,
  payload: data
});
export const manifestDetailsFail = () => ({ type: action.MANIFEST_DETAILS_FAIL });

// Delete Shipment From Manifest
export const manifestShippingDeleteRequest = (manifestId, shippingId) => ({
  type: action.MANIFEST_DELETE_SHIPPING_REQUEST,
  payload: { manifestId, shippingId }
});

export const manifestShippingDeleteSuccess = ({ manifestId, shippingId, canBeRemoved }) => ({
  type: action.MANIFEST_DELETE_SHIPPING_SUCCESS,
  payload: { manifestId, shippingId, canBeRemoved }
});

export const manifestShippingDeleteFail = ({ manifestId, shippingId }) => ({
  type: action.MANIFEST_DELETE_SHIPPING_FAIL,
  payload: { manifestId, shippingId }
});

// GET Manifest Details
export const searchAWBLoad = (data) => ({ type: action.SEARCH_AWB_LOAD, payload: data });
export const searchAWBSuccess = (data) => ({ type: action.SEARCH_AWB_SUCCESS, payload: data });
export const searchAWBFail = () => ({ type: action.SEARCH_AWB_FAIL });
// Close Manifest
export const closeManifestLoad = (id) => ({ type: action.CLOSE_MANIFEST_LOAD, payload: id });
export const closeManifestSuccess = (data) => ({
  type: action.CLOSE_MANIFEST_SUCCESS,
  payload: data
});
export const closeManifestFail = (shipmentIds) => ({
  type: action.CLOSE_MANIFEST_FAIL,
  payload: shipmentIds
});
export const closeManifestReset = () => ({
  type: action.CLOSE_MANIFEST_RESET
});

// Get Shipping Provider List
export const getProviderLoad = (data) => ({ type: action.GET_PROVIDER_LOAD, payload: data });
export const getProviderSuccess = (data) => ({ type: action.GET_PROVIDER_SUCCESS, payload: data });
export const getProviderFail = () => ({ type: action.GET_PROVIDER_FAIL });
// Save Manifest
export const saveManifestLoad = (data) => ({ type: action.SAVE_MANIFEST_LOAD, payload: data });
export const saveManifestSuccess = (data) => ({
  type: action.SAVE_MANIFEST_SUCCESS,
  payload: data
});
export const saveManifestFail = () => ({ type: action.SAVE_MANIFEST_FAIL });

// Print Manifest
export const printManifestLoad = (data) => ({ type: action.PRINT_MANIFEST_LOAD, payload: data });
export const printManifestSuccess = (data) => ({
  type: action.PRINT_MANIFEST_SUCCESS,
  payload: data
});
export const printManifestFail = () => ({ type: action.PRINT_MANIFEST_FAIL });
export const printManifestReset = (data) => ({ type: action.PRINT_MANIFEST_RESET, payload: data });

// RETRY Manifest
export const retryManifestLoad = (data) => ({ type: action.RETRY_MANIFEST_LOAD, payload: data });
export const retryManifestSuccess = (data) => ({
  type: action.RETRY_MANIFEST_SUCCESS,
  payload: data
});
export const retryManifestFail = () => ({ type: action.RETRY_MANIFEST_FAIL });
export const retryManifestReset = () => ({ type: action.RETRY_MANIFEST_RESET });

// Print SG Invoices
export const printSGInvoicesLoad = (data) => ({
  type: action.PRINT_SG_INVOICES_LOAD,
  payload: data
});
