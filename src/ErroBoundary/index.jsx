import React from 'react';
import ErrorBoundary from './ErrorBoundary';

class ErrorBoundaryContainer extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.log(error, errorInfo);
  }

  render() {
    const { hasError } = this.state;
    const { children } = this.props;

    if (hasError) {
      return <ErrorBoundary />;
    }

    return children;
  }
}

export default ErrorBoundaryContainer;
