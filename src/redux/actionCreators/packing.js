import * as action from 'redux/actionTypes/packing';

export function holdModalOperations(payload) {
  return {
    type: action.PACKING_HOLD_MODAL,
    payload
  };
}

export function unHoldModal(payload) {
  return {
    type: action.PACKING_UNHOLD_MODAL,
    payload
  };
}

/** Set selected index action to set selected index in reducer for Enable scanning On mode */
export function setSelectedIndex(payload) {
  return {
    type: action.SET_SELECTED_INDEX,
    payload
  };
}

/** Mark packing done : last call for submission */
export function markPackingDone(payload) {
  return {
    type: action.MARK_PACKING_DONE_LOAD,
    payload
  };
}
export function markPackingDoneSuccess(payload) {
  return {
    type: action.MARK_PACKING_DONE_SUCCESS,
    payload
  };
}
export function markPackingDoneFail(payload) {
  return {
    type: action.MARK_PACKING_DONE_FAIL,
    payload
  };
}
export function markPackingDoneReset(payload) {
  return {
    type: action.MARK_PACKING_DONE_RESET,
    payload
  };
}
/** End */

/** API call to get AWB number and Shipment label */
export function lsmCourierAwb(payload) {
  return {
    type: action.LSM_COURIER_AWB_LOAD,
    payload
  };
}
export function lsmCourierAwbSuccess(payload) {
  return {
    type: action.LSM_COURIER_AWB_SUCCESS,
    payload
  };
}
export function lsmCourierAwbFail(payload) {
  return {
    type: action.LSM_COURIER_AWB_FAIL,
    payload
  };
}
/** End */



/**  print packing Invoice */
export function printPackingInvoiceLoad(payload) {
  return {
    type: action.PRINT_PACKING_INVOICE_LOAD,
    payload
  };
}
export function printPackingInvoiceSuccess(payload) {
  return {
    type: action.PRINT_PACKING_INVOICE_SUCCESS,
    payload
  };
}

export function printPackingInvoiceReset(payload) {
  return {
    type: action.PRINT_PACKING_INVOICE_RESET,
    payload
  };
}
/** End */

/** Scan Valid barcode if isOwnDays true */
export const isBarcodeValidLoad = (payload) => ({
  type: action.IS_BARCODE_VALID_LOAD,
  payload
});

export const isBarcodeValidSuccess = (payload) => ({
  type: action.IS_BARCODE_VALID_SUCCESS,
  payload
});

export const isBarcodeValidFail = (payload) => ({
  type: action.IS_BARCODE_VALID_FAIL,
  payload
});

export const isBarcodeValidReset = (payload) => ({
  type: action.IS_BARCODE_VALID_RESET,
  payload
});
/** End */
