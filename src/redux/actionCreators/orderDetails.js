import { createAction } from '@reduxjs/toolkit';
import * as action from '../actionTypes/orderDetails';
// API CALL FOR DROP DOWN CATEGORY OPTIONS

// HEADERS
export const orderDetailsHeaderDataRequest = (payload) => ({
  type: action.ORDER_DETAILS_HEADER_LOAD,
  payload
});

export const orderDetailsHeaderDataSuccess = (payload) => ({
  type: action.ORDER_DETAILS_HEADER_SUCCESS,
  payload
});

export const orderDetailsHeaderDataFail = (payload) => ({
  type: action.ORDER_DETAILS_HEADER_FAIL,
  payload
});

// OVERVIEW
export const orderDetailsOverviewDataRequest = (payload) => ({
  type: action.ORDER_DETAILS_OVERVIEW_LOAD,
  payload
});

export const orderDetailsOverviewDataSuccess = (payload) => ({
  type: action.ORDER_DETAILS_OVERVIEW_SUCCESS,
  payload
});

export const orderDetailsOverviewDataFail = () => ({
  type: action.ORDER_DETAILS_OVERVIEW_FAIL
});

// Courier List

export const courierListLoad = (payload) => ({
  type: action.ORDER_DETAILS_COURIER_LIST_LOAD,
  payload
});

export const courierListSuccess = (payload) => ({
  type: action.ORDER_DETAILS_COURIER_LIST_SUCCESS,
  payload
});

export const courierListfail = () => ({
  type: action.ORDER_DETAILS_COURIER_LIST_FAIL
});

// ITEMS

export const orderDetailsItemsDataRequest = (payload) => ({
  type: action.ORDER_DETAILS_ITEMS_LOAD,
  payload
});

export const orderDetailsItemsDataSuccess = (payload) => ({
  type: action.ORDER_DETAILS_ITEMS_SUCCESS,
  payload
});

export const orderDetailsItemsDataFail = () => ({
  type: action.ORDER_DETAILS_ITEMS_FAIL
});

// SHIPMEMTS

export const orderDetailsShipmentsDataRequest = (payload) => ({
  type: action.ORDER_DETAILS_SHIPMENTS_LOAD,
  payload
});

export const orderDetailsShipmentsDataSuccess = (payload) => ({
  type: action.ORDER_DETAILS_SHIPMENTS_SUCCESS,
  payload
});

export const orderDetailsShipmentsDataFail = () => ({
  type: action.ORDER_DETAILS_SHIPMENTS_FAIL
});

// shipment reassign

export const orderDetailsShipmentReassignRequest = (payload) => ({
  type: action.ORDER_DETAILS_SHIPMENT_REASSIGN_LOAD,
  payload
});

export const orderDetailsShipmentReassignSuccess = (payload) => ({
  type: action.ORDER_DETAILS_SHIPMENT_REASSIGN_SUCCESS,
  payload
});

export const orderDetailsShipmentReassignFail = () => ({
  type: action.ORDER_DETAILS_SHIPMENT_REASSIGN_FAIL
});

// ORDER STOCK OUT

export const orderDetailStockOutRequest = (payload) => ({
  type: action.ORDER_STOCK_OUT_LOAD,
  payload
});

export const orderDetailStockOutSuccess = (payload) => ({
  type: action.ORDER_STOCK_OUT_SUCCESS,
  payload
});

export const orderDetailStockOutFail = () => ({
  type: action.ORDER_STOCK_OUT_FAIL
});

export const orderDetailStockOutReset = () => ({
  type: action.ORDER_STOCK_OUT_RESET
});

// ORDER SHIPMENT LIST
export const orderDetailsShipmentItemsRequest = (payload) => ({
  type: action.ORDER_DETAILS_SHIPMENT_ITEMS_LOAD,
  payload
});

export const orderDetailsShipmentItemsSuccess = (data) => ({
  type: action.ORDER_DETAILS_SHIPMENTS_ITEMS_SUCCESS,
  payload: data
});

export const orderDetailsShipmentItemsFail = (payload) => ({
  type: action.ORDER_DETAILS_SHIPMENTS_ITEMS_FAIL,
  payload
});

// PRINT INVOICE
export const orderDetailsPrintInvoiceDataRequest = (payload) => ({
  type: action.ORDER_DETAILS_PRINT_INVOICE_LOAD,
  payload
});

export const orderDetailsPrintInvoiceDataSuccess = (payload) => ({
  type: action.ORDER_DETAILS_PRINT_INVOICE_SUCCESS,
  payload
});

export const orderDetailsPrintInvoiceDataFail = (payload) => ({
  type: action.ORDER_DETAILS_PRINT_INVOICE_FAIL,
  payload
});

export const orderDetailsPrintInvoiceDataReset = () => ({
  type: action.ORDER_DETAILS_PRINT_INVOICE_RESET
});

// INVOICE TAB
export const orderDetailsInvoiceDataRequest = (payload) => ({
  type: action.ORDER_DETAILS_INVOICE_DATA_LOAD,
  payload
});

export const orderDetailsInvoiceDataSuccess = (payload) => ({
  type: action.ORDER_DETAILS_INVOICE_DATA_SUCCESS,
  payload
});

export const orderDetailsInvoiceDataFail = () => ({
  type: action.ORDER_DETAILS_INVOICE_DATA_FAIL
});

// RESET

export const orderDetailsResetData = () => ({
  type: action.ORDER_DETAILS_RESET_DATA
});

// ORDER DETAILS HISOTRY

export const orderDetailsHistoryLoad = (payload) => ({
  type: action.ORDER_DETAILS_HISTORY_LOAD,
  payload
});

export const orderDetailsHistorySuccess = (payload) => ({
  type: action.ORDER_DETAILS_HISTORY_SUCCESS,
  payload
});

export const orderDetailsHistoryFail = (payload) => ({
  type: action.ORDER_DETAILS_HISTORY_FAIL,
  payload
});

export const orderDetailsHistoryReset = () => ({
  type: action.ORDER_DETAILS_HISTORY_FAIL
});

export const autoToManualLoad = (payload) => ({
  type: action.ORDER_DETAILS_AUTO_TO_MANUAL_LOAD,
  payload
});

export const autoToManualSuccess = (payload) => ({
  type: action.ORDER_DETAILS_AUTO_TO_MANUAL_SUCCESS,
  payload
});

export const autoToManualFail = () => ({
  type: action.ORDER_DETAILS_AUTO_TO_MANUAL_FAIL
});

export const autoToManualReset = () => ({
  type: action.ORDER_DETAILS_AUTO_TO_MANUAL_RESET
});

export const autoReassignLoad = (payload) => ({
  type: action.ORDER_DETAILS_AUTO_REASSIGN_LOAD,
  payload
});

export const getNotFoundStatusLoad = createAction('ORDER_DETAIL_GET_NF_STATUS_LOAD');
export const getNotFoundStatusSuccess = createAction('ORDER_DETAIL_GET_NF_STATUS_SUCCESS');
export const getNotFoundStatusFail = createAction('ORDER_DETAIL_GET_NF_STATUS_FAIL');
export const getNotFoundStatusReset = createAction('ORDER_DETAIL_GET_NF_STATUS_RESET');

export const regularToJitLoad = createAction('REGULAR_TO_JIT_LOAD');
