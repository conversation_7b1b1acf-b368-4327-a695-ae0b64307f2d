import * as action from '../actionTypes/MEIEdging';

export const getLocation = () => ({ type: action.LOCATION_DATA_REQUEST });

export const getLocationSuccess = (data) => ({ type: action.LOCATION_DATA_SUCCESS, payload: data });

export const postBarcodeDetailsRequest = (data) => ({
  type: action.POST_BARCODE_DATA_REQUEST,
  payload: data
});

export const postBarcodeDetailsSuccess = (data) => ({
  type: action.POST_BARCODE_DATA_SUCCESS,
  payload: data
});

export const postBarcodeDetailsFailed = (data) => ({
  type: action.POST_BARCODE_DATA_FAILED,
  payload: data
});

export const resetMeiEdging = () => ({ type: action.MEI_EDGING_RESET });
