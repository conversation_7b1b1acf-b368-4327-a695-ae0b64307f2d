import { tokenInstance } from '../utils/axiosInstance';
import config from '../config';
import { convertToQueryString } from '../utils/helpers';

export const searchVendorListApi = (query) =>
  tokenInstance()
    .get(`${config.apiPath.vendor}${query}`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const getApprovedPoListApi = (vendorCode) =>
  tokenInstance()
    .get(`${config.purchaseOrder.approvedPo}/${vendorCode}?status=approved`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const searchVendorInvoiceNumberApi = (query) =>
  tokenInstance()
    .get(`${config.apiPath.invoice}vendorinvoice/search?q=${query}`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const getItemsVendorInvoiceCreationApi = (payload) => {
  const f = convertToQueryString(payload, true);
  return tokenInstance()
    .get(`${config.apiPath.invoice}items/creation?${f}`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));
};

export const createVendorInvoiceApi = (obj) =>
  tokenInstance()
    .post(config.invoice.save, obj)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const createVendorCLInvoiceApi = ({
  vendor_invoice_number,
  invoice_date,
  po_num,
  total_invoice_qty,
  total_invoice_amount,
  editMode
}) => {
  const params = {
    vendor_invoice_num: vendor_invoice_number,
    invoice_date,
    po_num,
    total_amount: total_invoice_amount,
    total_quantity: total_invoice_qty,
    is_update: editMode
  };
  return tokenInstance()
    .post(config.invoice.saveCL, null, { params })
    .then((response) => ({ response }))
    .catch((error) => ({ error }));
};

export const vendorInvoiceCreationStatusApi = (reqid) =>
  tokenInstance()
    .get(config.invoice.processingStatus, { params: { reqid } })
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const closeInvoiceApi = (payload = {}) =>
  tokenInstance()
    .put(config.invoice.close, payload)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const importInvoiceApi = (payload = {}) =>
  tokenInstance()
    .post(config.invoice.import, payload)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const exportInvoiceApi = (payload = {}) => {
  const f = convertToQueryString(payload);
  return tokenInstance()
    .get(`${config.invoice.download}?${f}`)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));
};

export const getUploadErrorCsvInvoiceApi = (fileName) =>
  tokenInstance()
    .get(`${config.invoice.import}?file=${fileName} `)
    .then((response) => ({ response }))
    .catch((error) => ({ error }));

export const exportInvoiceDetailsApi = ({ invoiceRefNum }) =>
  tokenInstance()
    .get(config.invoice.exportInvoiceDetails, { params: { invoice_ref_no: invoiceRefNum } })
    .then((response) => ({ response }))
    .catch((error) => {
      throw error;
    });

export const getPidInfoApi = (payload) =>
  tokenInstance().get(config.invoice.getPidInfo, { params: payload });
