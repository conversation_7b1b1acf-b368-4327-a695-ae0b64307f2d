import React, { useCallback } from 'react';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import useWindowResize from 'common/useWindowResize';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import Checkbox from '@mui/material/Checkbox';

import LkInput from 'components/MaterialUi/LkInput';
import LkChip from 'components/MaterialUi/LkChip';
import { debounce, genericDateFormatted } from 'utils/helpers';
import { useSelector } from 'react-redux';
import { LOCALISATION } from 'redux/reducers/localisation';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';

const initialSortingData = {
  id: 'updated_at',
  order: 'DESC'
};

const styles = makeStyles()(() => ({
  chip: {
    color: '#000',
    padding: '6px 8px',
    height: 'inherit',
    borderRadius: '360px'
  },
  input: {
    width: '382px'
  }
}));

const statusMapping = {
  TRANSFER_RECOMMENDED: 'Transfer Recommended'
};

const ReactiveTransferTabsContainer = ({
  fetchMoreData,
  onChangeFilterList,
  onGlobalSearch,
  value,
  setValue,
  selectedFilterList,
  deleteSelectedFilter,
  resetFilters,
  selectedTransfers,
  setSelectedTransfers,
  exportFun,
  data,
  isLoading,
  totalCount,
  pageLimit,
  disableCheckBox = false
}) => {
  const [tableHeight] = useWindowResize(window.innerHeight - 230);
  const { classes } = styles();
  const isExportLoading = useSelector((state) => state.reactiveTransfer.exportLoading);
  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.REACTIVE_TRANSFER);

  const handleSelectAllClick = () => {
    if (selectedTransfers.length > 0) {
      setSelectedTransfers([]);
    }
  };

  const selectTransfer = (event, rowData) => {
    const isCheck = event.target.checked;
    const updatedSelectedTransfers = isCheck
      ? [...selectedTransfers, rowData]
      : selectedTransfers.filter((eachRow) => eachRow.orderItemId !== rowData.orderItemId);
    setSelectedTransfers(updatedSelectedTransfers);
  };

  const headerConfig = [
    {
      name: '',
      key: 'checkBoxSelect',
      formatHeader: useCallback(
        () => (
          <Checkbox
            className="pd-0"
            indeterminate={selectedTransfers.length > 0}
            checked={false}
            onClick={handleSelectAllClick}
            color="primary"
            inputProps={{ 'aria-label': 'select all po' }}
            disabled={disableCheckBox}
          />
        ),
        [selectedTransfers]
      ),
      formatBody: useCallback(
        (rowData) => {
          const { orderItemId } = rowData;
          const isItemSelected = selectedTransfers.some((t) => t.orderItemId === orderItemId);
          return (
            <Checkbox
              className="pd-0"
              onClick={(event) => selectTransfer(event, rowData)}
              checked={isItemSelected}
              inputProps={{ 'aria-labelledby': orderItemId }}
              color="primary"
              disabled={disableCheckBox}
            />
          );
        },
        [selectedTransfers]
      ),
      style: { minWidth: '50px', maxWidth: '50px' }
    },
    {
      name: CONTENT.INCREMENT_ID,
      key: 'incrementId'
    },
    {
      name: CONTENT.SHIPPING_PACKAGE_ID,
      key: 'shippingPackageId',
      filterData: {
        type: 'input',
        columnName: 'Shipping Package'
      },
      style: { minWidth: '180px', maxWidth: '180px' }
    },
    {
      name: CONTENT.ORDER_DATE,
      key: 'Order Date',
      formatBody: useCallback(
        ({ orderDate }) => (
          <Box className="fw-bold">{genericDateFormatted(orderDate, 'utcTOlocalDate')}</Box>
        ),
        []
      ),
      filterData: {
        type: 'dateRange',
        columnName: 'Order Date',
        selectedFilterList
      }
    },
    {
      name: CONTENT.UPDATED_AT,
      key: 'updated_at',
      supportSort: true,
      formatBody: useCallback(
        ({ updatedAt }) => (
          <Box className="fw-bold">{genericDateFormatted(updatedAt, 'utcTOlocalDate')}</Box>
        ),
        []
      )
    },
    {
      name: CONTENT.PRODUCT_ID,
      key: 'pid'
    },
    {
      name: CONTENT.UW_ITEM_ID,
      key: 'orderItemId'
    },
    {
      name: CONTENT.SOURCE_WAREHOUSE,
      key: 'source'
    },

    {
      name: CONTENT.AVL_QTY,
      key: 'quantity',
      style: { minWidth: 120, maxWidth: 120 }
    },
    {
      name: CONTENT.CATEGORY,
      key: 'productType'
    },
    {
      name: CONTENT.TRANSFER_ITEM_ID,
      key: 'transferId'
    },
    {
      name: CONTENT.STATUS,
      key: 'Status',
      style: { minWidth: 180, maxWidth: 180 },

      formatBody: useCallback(
        ({ status }) =>
          status ? (
            <LkChip
              className={classes.chip}
              type={status}
              label={statusMapping[status] || status}
            />
          ) : (
            '-'
          ),
        []
      )
    }
  ];
  const delayedQuery = useCallback(
    debounce((newValue) => onGlobalSearch(newValue), 500),
    []
  );

  const onKeyPressFun = ({ target: { value: newValue } }) => {
    delayedQuery(newValue);
  };

  return (
    <Box>
      <Box p={2} className="display-flex justify-content-space-between">
        <Box className="display-flex justify-content-space-between gap10 align-items-center">
          <Box className={classes.input}>
            <LkInput
              id="searchOrderId"
              label={CONTENT.SEARCH_ORDER_ID}
              fullWidth
              onKeyUp={onKeyPressFun}
              onChange={({ target }) => setValue(target.value)}
              value={value}
            />
          </Box>

          <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />
        </Box>

        <Box>
          <Button
            variant="outlined"
            color="primary"
            size="medium"
            className="mr-r10 border-radius-8 "
            disabled={!selectedFilterList.length}
            onClick={() => resetFilters()}
            data-cy="reset-btn"
          >
            {CONTENT.RESET}
          </Button>
          <Button
            variant="outlined"
            color="primary"
            size="medium"
            onClick={exportFun}
            data-cy="export-btn"
            disabled={!data.length || isExportLoading}
            className="border-radius-8"
          >
            {CONTENT.EXPORT}
          </Button>
        </Box>
      </Box>
      <LkTable
        rowSize={60}
        tableData={data}
        tableHeight={tableHeight}
        isDataFetching={isLoading}
        totalRowsCount={totalCount}
        dataRequestFunction={fetchMoreData}
        headerConfig={headerConfig}
        initialSortBy={initialSortingData}
        setFilters={onChangeFilterList}
        pageLimit={pageLimit}
      />
    </Box>
  );
};

export default ReactiveTransferTabsContainer;
