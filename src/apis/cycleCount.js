import config from 'config';
import { tokenInstance } from 'utils/axiosInstance';

// LOCATION LIST API
export const cycleCountLocationListAPI = (payload) => {
  const URL = config.cycleCountWeb.locationListAPI;
  return tokenInstance()
    .post(URL, payload)
    .then((response) => response.data);
};

// LOCATION DETAILS API
export const cycleCountLocationDetailsAPI = (payload) => {
  const URL = config.cycleCountWeb.locationDetailAPI;
  return tokenInstance().get(`${URL}?id=${payload}`).then((response) => response.data)
};

// SUMMARY VIEW API
export const cycleCountSummaryViewAPI = (payload) => {
  const filteredDate = {
    date: {
      ...payload
    }
  };
  const URL = config.cycleCountWeb.SummaryViewAPI;
  return tokenInstance()
    .post(URL, filteredDate)
    .then((response) => response.data);
};
