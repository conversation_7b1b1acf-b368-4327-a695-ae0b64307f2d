// eslint-disable-next-line import/no-extraneous-dependencies
import 'cypress-file-upload';
import config from '../../src/config';

describe('Courier Cutoff', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/dashboard');
    cy.navigate(['main-navigation', 'DC Sorting', 'Courier Cut-Off']);
  });

  afterEach(() => {
    cy.logout();
  });

  it('Visits the Courieru Cutoff Page from app bar and table is populated', () => {
    const url = `${config.courierCutoff.list}?page=0&size=25&sort=updatedAt,desc`;

    cy.fixture('courierCutoff/courierCutoffHeader').as('headers');

    cy.intercept('GET', url).as('getCourierCutoffList');

    cy.get('@headers').then((headers) => {
      headers.forEach((header, index) => {
        cy.getByCy(`table-header-cell-${index}`).should('contain.text', header);
      });
    });

    cy.wait('@getCourierCutoffList').then(({ response }) => {
      const apiData = response.body.data?.courierCutOffs; // Capturing the API response body
      if (apiData.length) {
        cy.getByCy(`table-cell-${0}-0`).should('exist');

        // Your assertions for this item's data
        cy.getByCy(`table-cell-${0}-0`).should('contain.text', apiData[0].manifestShippingMethod);
      }
    });
  });

  it('File Upload', () => {
    const fileName = 'courierCutoff/sampleFileCourierCutoff.csv';

    cy.intercept('POST', config.courierCutoff.upload).as('uploadCourierCutoff');

    cy.getByCy('upload cutoff').click();
    cy.get('#upload-items-file').attachFile(fileName);

    cy.wait('@uploadCourierCutoff').then(({ response }) => {
      const apiData = response.body; // Capturing the API response body
      if (apiData?.meta?.displayMessage) {
        // uploading failed error is thrown from API
        cy.getByCy('cancel-fileupload-modal').click();
      } else if (apiData) {
        // Downloading error file
        cy.findByText(/Problems with CSV File/i).should('exist');
        cy.getByCy('ok_button').click();
      } else {
        cy.findByText(/Import Processed Successfully/i).should('exist');
        cy.getByCy('upload-success-ok-button').click();
      }
    });
  });
});
