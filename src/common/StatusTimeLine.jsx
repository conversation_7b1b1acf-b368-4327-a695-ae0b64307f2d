import React from 'react';
import { makeStyles } from 'tss-react/mui';

import LkToolTip from 'components/ToolTip/ToolTip';
import Box from '@mui/material/Box';

const useStyles = makeStyles()(() => ({
  displayFlex: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  titleStatus: {
    fontSize: 14,
    letterSpacing: 1,
    color: 'rgba(60, 60, 60, 0.54)',
    textAlign: 'left',
    paddingBottom: 5,
    paddingLeft: 5
  },
  status: {
    padding: 5,
    paddingRight: 6,
    borderRadius: 20,
    textTransform: 'capitalize'
  },
  infoIcon: {
    minWidth: 20,
    minHeight: 20,
    cursor: 'pointer'
  },
  highZIndex: {
    zIndex: 1001
  }
}));

// statusStyle is applied on status value only
const StatusTimeLine = ({
  value,
  status = '-',
  child,
  statusStyle = '',
  hideInfoIcon = false,
  sx = {}
}) => {
  const { classes, cx } = useStyles();
  return (
    <div className={classes.highZIndex} data-cy="status-time-line">
      <div className={classes.titleStatus}>{status.toUpperCase()}</div>
      <div className={classes.displayFlex}>
        {value ? (
          <Box component="span" className={cx(classes.status, statusStyle)} sx={{ ...sx }}>
            {value}
          </Box>
        ) : (
          <div className="text-center flex1">-</div>
        )}
        {!hideInfoIcon && (
          <LkToolTip title={child}>
            <img
              data-cy="status-time-line-image"
              className={classes.infoIcon}
              src={`${import.meta.env.VITE_STATIC_SERVER}/images/infoGray.svg`}
              alt="info"
            />
          </LkToolTip>
        )}
      </div>
    </div>
  );
};

export default StatusTimeLine;
