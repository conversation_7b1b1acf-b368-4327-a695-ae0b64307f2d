import React, { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { LOGIN } from 'redux/reducers/login';
import QCEMSActions from 'views/QualityCheck/QCEMSActions';
import QCHoldModal from 'views/QualityCheck/QCHoldModal';
import QCUnholdModal from 'views/QualityCheck/QCUnholdModal';

const defaultModalState = {
  openHoldModal: false,
  openUnHoldModal: false,
  isFail: false
};

const QCActions = ({ status, barcode, onSubmitFun, reason, dataCy = '' }) => {
  const userDetail = useSelector((state) => state[LOGIN].user.userDetail);
  const [modal, setModal] = useState(defaultModalState);

  const qcUnholdHandler = useCallback((show, uwItemId, isFail = false) => {
    setModal({ openHoldModal: false, openUnHoldModal: true, isFail });
  }, []);

  return (
    <>
      {modal.openHoldModal && (
        <QCHoldModal
          open={modal.openHoldModal}
          uwItemId={barcode}
          closedHandler={() => setModal(defaultModalState)}
          customSubmitFun={onSubmitFun}
        />
      )}
      {modal.openUnHoldModal && (
        <QCUnholdModal
          open={modal.openUnHoldModal}
          isFail={modal.isFail}
          uwItemId={barcode}
          customSubmitFun={onSubmitFun}
          closedHandler={() => setModal(defaultModalState)}
        />
      )}
      <QCEMSActions
        showHoldCTA
        qc={{ status, reason }}
        userDetail={userDetail}
        qcHoldHandler={() => setModal({ openHoldModal: true, openUnHoldModal: false })}
        qcUnholdHandler={qcUnholdHandler}
        uwItemId="uwItemId"
        dataCy={dataCy}
      />
    </>
  );
};

export default QCActions;
