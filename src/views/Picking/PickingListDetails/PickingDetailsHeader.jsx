import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import Autocomplete from '@mui/material/Autocomplete';
import IconButton from '@mui/material/IconButton';
import ArrowBack from '@mui/icons-material/ArrowBack';
import { LOCALISATION } from 'redux/reducers/localisation';
import Phases from 'common/Phases';
import LkInput from 'components/MaterialUi/LkInput';
import StatusTimeLine from 'common/StatusTimeLine';
import { debounce } from 'utils/helpers';
import { getPickingListRequest } from 'redux/reducers/pickingListReducer';
import HeaderItem from 'components/Header/HeaderItem';
import PickingListInfoBar from './PickingListInfoBar';

const useStyles = makeStyles()(() => ({
  container: {
    width: '100%',
    display: 'flex',
    background: '#FFFFFF',
    borderBottom: '1px solid rgba(100,100,100,0.10)'
  },
  dropDownContainer: {
    display: 'flex',
    marginLeft: 20
  },
  backIcon: {
    margin: 'auto',
    marginRight: 10,
    marginLeft: 20
  },
  field: {
    fontSize: 16,
    minWidth: 200,
    color: '#3C3C3C',
    letterSpacing: 0.15
  },
  statusTimeLineContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 50,
    marginRight: 60
  },
  headerQtyContainer: {
    display: 'flex',
    marginRight: 60
  },
  headerQty: {
    width: 'calc(100vw - 90vw)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 20
  },
  headerQtyLabel: {
    fontSize: 14,
    textAlign: 'right',
    letterSpacing: 1,
    color: 'rgba(60, 60, 60, 0.54)'
  },
  headerQtyValue: {
    fontSize: 14,
    textAlign: 'right',
    color: '#3C3C3C',
    lineHeight: '199%',
    letterSpacing: 0.46
  },
  statusStyle: {
    fontSize: 14,
    fontWeight: 400,
    color: '#3C3C3C',
    letterSpacing: 0.46,
    paddingLeft: 4
  }
}));

const PickingDetailsHeader = ({ data, dropDownValue }) => {
  const { classes, cx } = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [autoCompleteValue, setAutoCompleteValue] = useState(dropDownValue);
  const PICKING = useSelector((state) => state[LOCALISATION].localeData.PICKING);

  const { data: pickingList, isLoading } = useSelector(
    (state) => state.pickingList.pickingListData
  );

  const headerQty = [
    { label: PICKING.PICKER_ID, key: 'pickerId' },
    { label: PICKING.TOTAL_QTY_UPPERCASE, key: 'totalQuantity', style: { textAlign: 'right' } },
    { label: PICKING.PICKED_ITEMS_UPPERCASE, key: 'pickedItems', style: { textAlign: 'right' } },
    { label: PICKING.SKIPPED, key: 'skippedQuantity', style: { textAlign: 'right' } },
    { label: PICKING.PENDING_ITEMS, key: 'pendingQuantity', style: { textAlign: 'right' } }
  ];

  const searchPicklistCode = (pickingid) => {
    dispatch(
      getPickingListRequest({
        pageRequest: {
          pageSize: 30,
          searchFields: pickingid ? { id: [pickingid] } : {}
        }
      })
    );
  };

  useEffect(() => {
    if (!pickingList.length) {
      searchPicklistCode();
    }
  }, []);

  const selectedPicklistCode = useCallback(
    debounce((q) => q.length >= 2 && searchPicklistCode(q), 500),
    []
  );

  const selectedOption = pickingList.map((item) => Number(item.id));

  return (
    <Box className={classes.container} data-cy="picking-details-header">
      <Box className={classes.dropDownContainer}>
        <IconButton
          disableRipple
          className={classes.backIcon}
          onClick={() => navigate('/picking')}
          data-cy="back-icon"
          size="large"
        >
          <ArrowBack />
        </IconButton>
        <Box className="pd-10 mr-l10">
          <Phases labels={[PICKING.PICK_LIST, dropDownValue]} />
          <form
            onSubmit={(e) => {
              e.preventDefault();
              navigate(`/picking/details/${autoCompleteValue}`);
            }}
          >
            <Autocomplete
              onKeyUp={(e) => selectedPicklistCode(e.target.value)}
              value={dropDownValue}
              loading={isLoading}
              options={selectedOption}
              id="picking-details-autocomplete"
              className={classes.field}
              getOptionLabel={(option) => String(option)}
              renderOption={(renderProps, option) => (
                <li {...renderProps} key={option}>
                  <Box
                    className="wd-100"
                    onClick={(e) => navigate(`/picking/details/${e.target.innerText}`)}
                    data-cy={`list-item-${option}`}
                  >
                    {option}
                  </Box>
                </li>
              )}
              onInputChange={(_event, newInputValue) => {
                setAutoCompleteValue(newInputValue);
              }}
              renderInput={(params) => <LkInput {...params} fullWidth />}
            />
          </form>
        </Box>
      </Box>
      <Box className={classes.headerQtyContainer}>
        <Box className={cx(classes.statusTimeLineContainer)}>
          <StatusTimeLine
            child={
              <PickingListInfoBar
                isCreated={data.createdAt}
                isAssigned={data.assignedAt}
                isCompleted={data.closedAt}
              />
            }
            status="status"
            value={data.status}
            statusStyle={classes.statusStyle}
          />
        </Box>
        {headerQty.map((item) => (
          <Box key={item.label} className={classes.headerQty}>
            <HeaderItem title={item.label} value={data[item.key]} />
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default PickingDetailsHeader;
