import * as action from '../actionTypes/putawayDetail';

export function getPutawayDetailLoad(data){
    return { type : action.GET_PUTAWAY_DETAIL_LOAD, payload : data }
}

export function getPutawayDetailSuccess(data){
    return { type : action.GET_PUTAWAY_DETAIL_SUCCESS, payload : data }
}

export function getPutawayDetailFail(data){
    return { type : action.GET_PUTAWAY_DETAIL_FAIL, payload : data }
}

export function getPutawayDetailReset(data){
    return {type : action.GET_PUTAWAY_DETAIL_RESET, payload : data }
}


export function exportPutawayDetailLoad(data){
    return { type : action.EXPORT_PUTAWAY_DETAIL_LOAD, payload : data }
}

export function exportPutawayDetailSuccess(data){
    return { type : action.EXPORT_PUTAWAY_DETAIL_SUCCESS, payload : data }
}

export function exportPutawayDetailFail(data){
    return { type : action.EXPORT_PUTAWAY_DETAIL_FAIL, payload : data }
}

export function exportPutawayDetailReset(data){
    return { type : action.EXPORT_PUTAWAY_DETAIL_RESET, payload : data }
}