{"name": "nexs", "author": "NexS", "licence": "MIT", "type": "module", "version": "2.0.10", "private": false, "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview", "eject": "react-scripts eject", "server": "node-env-run server --exec nodemon | pino-colada", "dev": "run-p server start", "lint": "eslint .", "lint:fix": "eslint --fix .", "prepare": "husky install", "cypress:open": "cypress open", "cypress:e2e": "cypress run --e2e -b chrome", "cypress:e2e:headless": "cypress run --e2e -b chrome --headless", "cypress:component": "cypress open --component  -b  chrome", "cypress:component:headless": "rm -rf coverage && cypress run --headless --component  -b  chrome", "code-report": "nyc report --reporter=text-summary", "merge-reports": "jrm combined.xml \"coverage/junit/results-*.xml\"", "check-coverage": "nyc check-coverage --lines 30 --statements 30 --functions 30 --branches 30", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "proxy": "https://ui.nexs.preprod.lenskart.com", "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.126", "@mui/material": "^5.12.0", "@mui/styles": "^5.12.0", "@mui/x-date-pickers": "^6.19.4", "@mui/x-tree-view": "^6.17.0", "@newrelic/browser-agent": "^1.265.0", "@reduxjs/toolkit": "^1.8.3", "@storybook/preview-api": "^8.0.8", "@storybook/test": "^8.0.8", "axios": "^1.10.0", "chart.js": "^4.5.0", "clsx": "^1.0.4", "dayjs": "^1.11.9", "final-form-arrays": "^3.0.2", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "react": "^17.0.2", "react-barcode": "^1.4.1", "react-chartjs-2": "^5.3.0", "react-date-range": "^2.0.1", "react-dom": "^17.0.2", "react-final-form": "^6.5.0", "react-final-form-arrays": "^3.1.1", "react-perfect-scrollbar": "^1.5.3", "react-redux": "^7.2.8", "react-router-dom": "^6.27.0", "react-slick": "^0.27.0", "react-to-print": "^3.0.5", "react-virtualized-auto-sizer": "^1.0.6", "react-window": "^1.8.7", "react-window-infinite-loader": "^1.0.7", "reactflow": "^11.9.4", "redux-saga": "^1.3.0", "reselect": "^4.0.0", "slick-carousel": "^1.8.1", "styled-components": "^5.1.1", "tss-react": "^4.9.14", "uuid": "^3.3.2", "validate.js": "^0.13.1"}, "devDependencies": {"@cypress/code-coverage": "^3.12.41", "@cypress/react": "^8.0.2", "@cypress/vite-dev-server": "^5.1.1", "@storybook/addon-actions": "^8.0.8", "@storybook/addon-docs": "^8.0.8", "@storybook/addon-essentials": "^8.0.8", "@storybook/addon-interactions": "^8.0.8", "@storybook/addon-links": "^8.0.8", "@storybook/blocks": "^8.0.8", "@storybook/builder-vite": "^8.0.8", "@storybook/react": "^8.0.8", "@storybook/react-vite": "^8.6.4", "@storybook/react-webpack5": "^8.0.8", "@testing-library/cypress": "^10.0.2", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-react": "^4.0.3", "babel-plugin-named-exports-order": "^0.0.2", "cypress": "^13.17.0", "cypress-file-upload": "^5.0.8", "cypress-multi-reporters": "^1.6.4", "cypress-real-events": "^1.14.0", "cypress-sonarqube-reporter": "^1.13.4", "eslint": "^8.15.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-cypress": "^2.13.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.5.0", "eslint-plugin-storybook": "^0.8.0", "husky": "^8.0.0", "junit-report-merger": "^7.0.0", "lint-staged": "^12.4.1", "mocha-junit-reporter": "^2.2.1", "node-env-run": "^4.0.2", "nodemon": "^3.1.4", "npm-run-all": "^4.1.5", "pino-colada": "^1.5.1", "prettier": "^2.6.2", "sass": "^1.64.1", "storybook": "^8.0.8", "vite": "^6.2.5", "vite-plugin-istanbul": "^7.0.0"}, "lint-staged": {"**/*.{js,jsx}": ["npx eslint"]}}