import React, { useEffect } from 'react';
import { MenuItem, Box } from '@mui/material';
import { makeStyles } from 'tss-react/mui';
import LkInput from 'components/MaterialUi/LkInput';
import { useParams, useNavigate } from 'react-router-dom';
import Spinner from 'components/Spinner/Spinner';
import { useDispatch, useSelector } from 'react-redux';
import { consolidationDataRequest } from 'redux/actionCreators/consolidation';
import { LOCALISATION } from 'redux/reducers/localisation';

const useStyles = makeStyles()({
  dropDownContainer: {
    padding: '20px 24px 20px 38px',
    background: '#FFFFFF'
  },
  dropDownInputField: {
    width: '300px',
    color: '3C3C3C'
  }
});

const ConsolidationHeader = () => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { type } = useParams();

  const CONTENT = useSelector(
    (state) => state[LOCALISATION].localeData.CONSOLIDATIONS_SUPERVISOR_PANEL
  );

  const { isLoading, data: options } = useSelector(
    (state) => state.consolidation.consolidationTypes
  );

  useEffect(() => {
    dispatch(consolidationDataRequest());
  }, []);

  return (
    <Box className={classes.dropDownContainer}>
      {isLoading ? (
        <Spinner />
      ) : (
        <LkInput
          select
          className={classes.dropDownInputField}
          label={CONTENT.SELECT_TYPE}
          onChange={(event) => {
            navigate(`/supervisor/consolidation/${event.target.value.id}`);
          }}
          value={options.find((item) => item.id === parseInt(type, 10)) || ''}
        >
          {options?.map((item) => (
            <MenuItem key={item.id} value={item}>
              {item.label}
            </MenuItem>
          ))}
        </LkInput>
      )}
    </Box>
  );
};

export default ConsolidationHeader;
