import React, { useCallback, useMemo } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import LkInput from 'components/MaterialUi/LkInput';
import { debounce } from 'utils/helpers';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    background: '#FFFFFF',
    padding: 16
  }
}));

const DistributorOrderShipmentListHeader = ({
  onSearch,
  CONTENT,
  disableDownloadFun,
  onDownloadFun,
  deleteSelectedFilter,
  selectedFilterList = []
}) => {
  const { classes } = useStyles();

  const delayedQuery = useCallback(
    debounce(({ target: { value } }) => onSearch(value), 500),
    []
  );

  const filtersApplied = useMemo(
    () =>
      selectedFilterList.map((filter) => {
        if (filter.key === 'CREATED') {
          return { ...filter, hideDeleteOption: true };
        }
        return filter;
      }),
    [selectedFilterList]
  );

  return (
    <Box className={classes.container}>
      <Box className="display-flex">
        <Box width={380} className="mr-r10">
          <LkInput
            fullWidth
            onChange={delayedQuery}
            variant="outlined"
            id="DoShipmentglobalSearchId"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="disabled" style={{ fontSize: '1.1rem' }} />
                </InputAdornment>
              )
            }}
            placeholder={CONTENT.SEARCH_BY_SHIPPING_PACKAGE_ID}
          />
        </Box>
        {selectedFilterList.length ? (
          <Box data-cy="DoShipmentListChips">
            <FilterChipsV2 deleteData={deleteSelectedFilter} filters={filtersApplied} />
          </Box>
        ) : (
          ''
        )}
      </Box>
      <Box>
        <Button
          color="primary"
          type="submit"
          variant="outlined"
          data-cy="doShipmentListDownload"
          disabled={disableDownloadFun}
          onClick={onDownloadFun}
        >
          {CONTENT.DOWNLOAD_CSV}
        </Button>
      </Box>
    </Box>
  );
};

export default DistributorOrderShipmentListHeader;
