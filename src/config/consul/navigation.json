[{"name": "Monitor Panel", "path": "/monitor", "id": "monitor"}, {"name": "Procurement", "id": "procurement", "children": [{"name": "Purchase Order", "path": "/po/list", "id": "ProcurementPurchaseOrder"}, {"name": "Vendor Invoice", "path": "/invoice/dashboard", "id": "ProcurementVendorInvoiceDashboard"}, {"name": "GRN", "path": "/grn/listing", "id": "ProcurementGRNListing"}, {"name": "Debit Note", "path": "/debitnote/list", "id": "ProcurementDebitNoteList"}, {"name": "PO Shipment Tracking", "path": "/vendorShipment/list", "id": "vendorShipmentList"}, {"name": "Return to vendor", "path": "/RTVgatepass/list", "id": "RTVgatepassList"}, {"name": "<PERSON><PERSON><PERSON>", "id": "vendorItemMaster", "children": [{"name": "<PERSON><PERSON>", "path": "/vendor-master/dashboard", "id": "ProcurementVendorItemMasterDashboard"}, {"name": "Category Approvals", "path": "/priceControl", "id": "priceControl"}]}]}, {"name": "JIT", "id": "JITPanel", "children": [{"name": "Order Search", "path": "/autojit/ordersearch", "id": "JitOrderSearch"}]}, {"name": "Facility Management", "id": "facilityManagement", "children": [{"name": "Layout Templates", "path": "/layoutpage/list", "id": "FacilityLayoutPageList"}, {"name": "Facilities", "path": "/facilitys/list", "id": "FacilityFacilities"}, {"name": "Constraint Management", "path": "/constraint/list", "id": "FacilityConstraintManagement"}]}, {"name": "Order Processing", "id": "orderProcessing", "children": [{"name": "Picking", "path": "/picking", "id": "OrderPicking"}, {"name": "Adhoc ASRS Picking", "path": "/adhocASRSPicking", "id": "OrderASRSPicking"}, {"name": "Bulk Order Picking", "path": "/bulk-order-picking", "id": "bulkOrderPicking"}, {"name": "Tray Reassignment", "path": "/trayReassignment", "id": "OrderTrayReassignment"}, {"name": "MEI", "path": "/MEIEdging", "id": "OrderMEIEdging"}, {"name": "MEI Blocking", "path": "/MEIHandEdging/blocker", "id": "MEIBlocking"}, {"name": "MEI Hand Edging", "path": "/MEIHandEdging", "id": "MEIHandEdging"}, {"name": "Fitting", "path": "/fitting", "id": "OrderFitting"}, {"name": "Order QC", "path": "/qc", "id": "OrderOrderQC"}, {"name": "Packing", "path": "/packing", "id": "OrderPacking"}, {"name": "Carton Shipment Mapping", "path": "/cartonShipmentMapping", "id": "OrderCartonShipmentMapping"}, {"name": "Autobag Printing", "path": "/shipment-printing/autobag", "id": "OrderAutobagPrinting"}, {"name": "Mono Carton Printing", "path": "/shipment-printing/carton", "id": "OrderMonoCartonPrinting"}, {"name": "Packslip", "path": "/shipment-printing/packslip", "id": "PackSlipPrinting"}, {"name": "Store Packing", "path": "/storepacking", "id": "OrderStorePacking"}, {"name": "Manifest", "path": "/manifest", "id": "OrderManifest"}, {"name": "Lab Lens Sticker Printing", "path": "/lensLabSticker", "id": "OrderLabLensStickerPrinting"}, {"name": "Job Card Printing", "path": "/jobCard", "id": "OrderJobCardPrinting"}, {"name": "Unfulfillable Orders", "path": "/reactiveTransfer", "id": "reactiveTransfer"}, {"name": "Edging QC", "path": "/edgingQC", "id": "edgingQC"}]}, {"name": "Automation", "id": "automation", "children": [{"name": "Tray Release", "path": "/trayRelease", "id": "AutomationTrayRelease"}, {"name": "Bag Shipments", "path": "/bagShipments", "id": "DCBagShipments"}]}, {"name": "Inventory", "id": "inventory", "children": [{"name": "Putaway", "path": "/putawayPage/list", "id": "inventoryPutawayList"}, {"name": "Transfers", "path": "/bulkInventory", "id": "inventoryTransfers"}, {"name": "Cycle Count", "path": "/cycleCount", "id": "inventoryCycleCount"}, {"name": "Add Inventory", "path": "/addInventory", "id": "AddInventory", "facility": ["NXS2"]}, {"name": "Start Retire", "path": "/startRetire", "id": "startRetire"}]}, {"name": "Exception Management", "id": "exceptionManagement", "children": [{"name": "EMS Putaway", "path": "/putawaycreation", "id": "emsPutawayCreation"}]}, {"name": "Distributor Orders", "id": "marketPlace", "children": [{"name": "Customer Onboard", "path": "/customer/list", "id": "customerList"}, {"name": "Upload", "path": "/marketPlace/distributorOrders/create", "id": "marketPlaceDOOrderCreate"}, {"name": "Listing", "path": "/marketPlace/distributorOrders/list", "id": "marketPlaceDOOrderList"}, {"name": "Approval", "path": "/finance/distributorOrders", "id": "financeDOOrdersDashboard"}, {"name": "Picking", "path": "/distributorOrder/Shipment/List", "id": "doShipmentList"}]}, {"name": "Supervisor", "id": "supervisor", "children": [{"name": "Picking", "path": "/supervisor/picking/skipped/skipped", "id": "supervisorPicking"}, {"name": "Consolidation", "path": "/supervisor/consolidation", "id": "supervisorConsolidation"}]}, {"name": "Reports", "id": "reports", "children": [{"name": "D365", "path": "/D365/List", "id": "ReportsD365List"}]}, {"name": "Auto GRN", "id": "autoGRN", "children": [{"name": "Lens Lab", "path": "/autogrn/startReceiving", "id": "lenslab"}, {"name": "Warehouse Receiving", "path": "/autogrn/warehouseReceiving", "id": "warehouseRecieving"}]}, {"name": "E-Invoice", "path": "/E-Invoice/list", "id": "eInvoice"}, {"name": "Invoice Posting", "path": "/invoicePosting", "id": "invoicePosting"}, {"name": "Export Report", "path": "/reporting", "id": "exportReport"}, {"name": "Stock Take Cycle", "path": "/stockTake/list", "id": "stockTakeBarcodeScan"}]