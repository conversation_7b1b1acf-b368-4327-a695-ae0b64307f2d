import * as action from '../actionTypes/putaway';

const putawayBarcodeStatusInitailData = () => ({
  Scanned: [],
  Passed: [],
  Failed: []
});

const initialState = {
  userPutawayList: [],
  userPutawayListLoading: false,
  userPutawayListFail: undefined,
  markPutawayPending: undefined,
  markPutawayPendingLoad: false,
  markPutawayPendingFail: undefined,
  putawayCreationResponse: null,
  putawayCreationLoading: false,
  putawayBarcodeStatus: putawayBarcodeStatusInitailData(),
  isPutawayComplete: false,
  isPutawayCompleteLoading: false,
  multiplePutaway: {
    isLoading: false,
    isSuccess: false,
    isError: false
  }
};

const putaway = (state = initialState, { type, payload }) => {
  switch (type) {
    case action.USER_PUTAWAY_LIST_LOAD:
      return {
        ...state,
        userPutawayList: [],
        userPutawayListLoading: true,
        userPutawayListFail: undefined
      };
    case action.USER_PUTAWAY_LIST_SUCCESS:
      return {
        ...state,
        userPutawayList: payload?.putawayIds || [],
        userPutawayListLoading: false,
        userPutawayListFail: undefined
      };
    case action.USER_PUTAWAY_LIST_FAIL:
      return {
        ...state,
        userPutawayList: [],
        userPutawayListLoading: false,
        userPutawayListFail: payload
      };
    case action.USER_PUTAWAY_LIST_RESET:
      return {
        ...state,
        userPutawayList: [],
        userPutawayListLoading: false,
        userPutawayListFail: undefined
      };
    case action.UPDATE_PUTAWAY_TO_PENDING_LOAD:
      return {
        ...state,
        markPutawayPending: undefined,
        markPutawayPendingLoad: true,
        markPutawayPendingFail: undefined
      };
    case action.UPDATE_PUTAWAY_TO_PENDING_SUCCESS:
      return {
        ...state,
        markPutawayPending: payload,
        markPutawayPendingLoad: false,
        markPutawayPendingFail: undefined
      };
    case action.UPDATE_PUTAWAY_TO_PENDING_FAIL:
      return {
        ...state,
        markPutawayPending: undefined,
        markPutawayPendingLoad: false,
        markPutawayPendingFail: payload
      };
    case action.UPDATE_PUTAWAY_TO_PENDING_RESET:
      return {
        ...state,
        markPutawayPending: undefined,
        markPutawayPendingLoad: false,
        markPutawayPendingFail: undefined
      };
    case action.PRINT_PUTAWAY_LOAD:
      return {
        ...state,
        printPutaway: undefined,
        printPutawayLoading: true,
        printPutawayFail: undefined
      };
    case action.PRINT_PUTAWAY_SUCCESS:
      return {
        ...state,
        printPutaway: payload,
        printPutawayLoading: false,
        printPutawayFail: undefined
      };
    case action.PRINT_PUTAWAY_FAIL:
      return {
        ...state,
        printPutaway: undefined,
        printPutawayLoading: false,
        printPutawayFail: payload
      };
    case action.PRINT_PUTAWAY_RESET:
      return {
        ...state,
        printPutaway: undefined,
        printPutawayLoading: false,
        printPutawayFail: undefined
      };

    case action.CREATE_PUTAWAY_LOAD:
      return { ...state, putawayCreationResponse: null, putawayCreationLoading: true };
    case action.CREATE_PUTAWAY_SUCCESS:
      return { ...state, putawayCreationResponse: payload, putawayCreationLoading: false };
    case action.CREATE_PUTAWAY_FAIL:
      return { ...state, putawayCreationResponse: payload, putawayCreationLoading: false };

    case action.UPDATE_PUTAWAY_BARCODE_LIST:
      return { ...state, putawayBarcodeStatus: { ...payload } };
    case action.RESET_PUTAWAY_BARCODE_LIST:
      return { ...state, putawayBarcodeStatus: putawayBarcodeStatusInitailData() };
    case action.COMPLETE_PUTAWAY_SG_LOAD:
      return { ...state, isPutawayComplete: false, isPutawayCompleteLoading: true };
    case action.COMPLETE_PUTAWAY_SG_SUCCESS:
      return { ...state, isPutawayComplete: payload, isPutawayCompleteLoading: false };
    case action.COMPLETE_PUTAWAY_SG_FAIL:
      return { ...state, isPutawayComplete: false, isPutawayCompleteLoading: false };
    case action.COMPLETE_PUTAWAY_SG_RESET:
      return { ...state, isPutawayComplete: false, isPutawayCompleteLoading: false };
    case action.PRINT_MULTIPLE_PUTAWAY_LOAD:
      return { ...state, multiplePutaway: { ...state.multiplePutaway, isLoading: true } };
    case action.PRINT_MULTIPLE_PUTAWAY_SUCCESS:
      return {
        ...state,
        multiplePutaway: {
          isLoading: false,
          isSuccess: true,
          isError: false
        }
      };
    case action.PRINT_MULTIPLE_PUTAWAY_FAIL:
      return {
        ...state,
        multiplePutaway: {
          isLoading: false,
          isSuccess: false,
          isError: true
        }
      };
    case action.PRINT_MULTIPLE_PUTAWAY_RESET:
      return {
        ...state,
        multiplePutaway: {
          isLoading: false,
          isSuccess: false,
          isError: false
        }
      };

    default:
      return state;
  }
};

export default putaway;
