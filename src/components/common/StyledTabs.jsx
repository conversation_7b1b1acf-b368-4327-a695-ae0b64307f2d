import React from 'react';
import Tabs from '@mui/material/Tabs';
import { withStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';

export const StyledTabs = withStyles(
  (props) => <Tabs {...props} TabIndicatorProps={{ children: <span /> }} />,
  {
    indicator: {
      backgroundColor: 'transparent'
    }
  }
);

export const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

export const StyledTab = withStyles((props) => <Tab {...props} />, {
  root: {
    fontSize: '12px'
  }
});
