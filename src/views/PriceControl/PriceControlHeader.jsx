import React, { useEffect, useMemo, useState } from 'react';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { makeStyles } from 'tss-react/mui';

import LkModal from 'components/Modal/Modal';
import { Autocomplete } from '@mui/material';
import LkInput from 'components/MaterialUi/LkInput';
import { useDispatch, useSelector } from 'react-redux';
import { validateEmail } from 'utils/helpers';
import {
  confirmApproverLoad,
  confirmApproverReset,
  getUserDetailsLoad,
  updateSelectedRowLoad
} from 'redux/reducers/priceControl';
import { toastOpen } from 'redux/actionCreators/toast';
import { isEmpty } from 'utils/lodash';
import LkChip from 'components/MaterialUi/LkChip';
import CancelIcon from '@mui/icons-material/Cancel';
import { LOCALISATION } from 'redux/reducers/localisation';
import FilterChipsV2 from 'components/FilterChip/FilterChipsV2';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  classificationContainer: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    gap: 30,
    padding: 30,
    borderBottom: '1px solid #EEEEEE',
    margin: '0 40px'
  }
}));

const defaultValues = {
  classificationType: '',
  brand: '',
  legalOwner: ''
};

const PriceControlHeader = ({ selectedFilterList, deleteSelectedFilter, dispatchListLoadFun }) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.PRICE_CONTROL);

  const [headerValues, setHeaderValues] = useState(defaultValues);
  const [openModal, setOpenModal] = useState(false);
  const [confirmersEmail, setConfirmersEmail] = useState([]);
  const [approversEmail, setApproversEmail] = useState([]);

  const {
    legalEntityList,
    classificationList,
    brandList,
    l1Confirmer: { list: l1ConfirmerList },
    approver: { list: approverList },
    confirmApprover: { isLoading, isSuccess },
    updateSelectedRow,
    priceControlList: { superAdmin }
  } = useSelector((state) => state.priceControl);

  const toogleModal = () => {
    dispatch(updateSelectedRowLoad({}));
    setOpenModal((oldState) => !oldState);
  };

  const onChangeFun = (name, newValue) => {
    setHeaderValues((preValues) => ({ ...preValues, [name]: newValue }));
  };

  useEffect(() => {
    if (isSuccess) {
      dispatch(confirmApproverReset());
      setOpenModal(false);
      setHeaderValues(defaultValues);
      dispatch(updateSelectedRowLoad({}));
      dispatchListLoadFun();
    }
  }, [isSuccess]);

  useEffect(() => {
    if (!isEmpty(updateSelectedRow)) {
      const { classificationType, brand, legalOwner, confirmers, approvers } = updateSelectedRow;
      setHeaderValues({ classificationType, brand, legalOwner });

      setConfirmersEmail(
        confirmers.map((item) => ({
          name: item.empName,
          email: item.email,
          empCode: item.empCode,
          id: item.id
        }))
      );

      setApproversEmail(
        approvers.map((item) => ({
          name: item.empName,
          email: item.email,
          empCode: item.empCode,
          id: item.id
        }))
      );
      setOpenModal(true);
    } else {
      setHeaderValues(defaultValues);
    }
  }, [updateSelectedRow]);

  const isEditMode = useMemo(() => !isEmpty(updateSelectedRow), [updateSelectedRow]);

  const renderForm = () => (
    <Box className={classes.classificationContainer}>
      <Box width={270}>
        <Autocomplete
          options={legalEntityList}
          value={headerValues?.legalOwner}
          disabled={isEditMode}
          getOptionLabel={(option) => option ?? ''}
          onInputChange={(event, newValue) => onChangeFun('legalOwner', newValue)}
          renderInput={(params) => (
            <LkInput {...params} className="input-box" fullWidth label={CONTENT.LEGAL_ENTITY} />
          )}
        />
      </Box>
      <Box width={270}>
        <Autocomplete
          options={classificationList}
          value={headerValues?.classificationType}
          disabled={isEditMode}
          getOptionLabel={(option) => option ?? ''}
          onInputChange={(event, newValue) => onChangeFun('classificationType', newValue)}
          renderInput={(params) => (
            <LkInput {...params} className="input-box" fullWidth label={CONTENT.CLASSIFICATION} />
          )}
        />
      </Box>
      <Box width={270}>
        <Autocomplete
          options={brandList}
          disabled={isEditMode}
          value={headerValues?.brand}
          getOptionLabel={(option) => option ?? ''}
          onInputChange={(event, newValue) => onChangeFun('brand', newValue)}
          renderInput={(params) => (
            <LkInput {...params} className="input-box" fullWidth label={CONTENT.BRAND} />
          )}
        />
      </Box>
    </Box>
  );

  const loadUserDetailsFun = ({ keyCode, target: { name, value } }) => {
    if (keyCode === 13) {
      if (!validateEmail(value.trim())) {
        dispatch(getUserDetailsLoad({ name, emailIds: value }));
      } else {
        dispatch(
          toastOpen({
            isToastOpen: true,
            subHeading: CONTENT.PLEASE_ENTER_VALID_EMAIL,
            severity: 'error'
          })
        );
      }
    }
  };

  const disableApprovers = useMemo(
    () => Object.keys(headerValues).some((value) => !headerValues[value]),
    [headerValues]
  );

  const disableSubmit = useMemo(
    () => !(confirmersEmail.length !== 0 && confirmersEmail.filter((item) => item.email)[0]),
    [confirmersEmail]
  );

  const disableApplyBtn = useMemo(
    () => !(approversEmail.length !== 0 && approversEmail.filter((item) => item.email)[0]),
    [approversEmail]
  );

  const onChangeApproverFun = (type, data, roleState, setterMethod) => {
    const filterEmails = roleState.filter((item) => !!item.email);
    if (filterEmails.length < 2) {
      if (data) {
        const obj = {
          empCode: data?.empCode,
          name: data?.userName,
          email: data?.userEmail
        };
        setterMethod((prev) => [...prev, obj]);
      }
    } else {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: 'Error',
          subHeading: `The maximum number of ${type} can be 2.`,
          severity: 'error'
        })
      );
    }
  };

  const renderOptionsFun = (renderProps, option) => (
    <li {...renderProps} key={option.userName}>
      <Box>
        <Box className="fw-bold fs12">{option.userName}</Box>
        <Box className="text-99 fs10">{option.userEmail}</Box>
      </Box>
    </li>
  );

  const onSubmitFun = () => {
    let payload = {
      ...headerValues,
      confirmers: confirmersEmail,
      approvers: approversEmail,
      edit: isEditMode
    };
    if (!isEmpty(updateSelectedRow)) {
      payload = {
        ...payload,
        id: updateSelectedRow.id
      };
    }
    dispatch(confirmApproverLoad(payload));
  };

  const deleteConfirmer = (rowDetail, roleState, setterMethod, id) => {
    if (isEditMode && id) {
      const deleteFilter = roleState.filter((item) => item.id !== rowDetail.id);
      setterMethod([...deleteFilter, { id }]);
    } else {
      setterMethod((prev) => prev.filter((item) => item.email !== rowDetail.email));
    }
  };

  return (
    <>
      <Box className={classes.container}>
        <FilterChipsV2 deleteData={deleteSelectedFilter} filters={selectedFilterList} />

        <Box>
          <Button
            color="primary"
            className={classes.button}
            type="submit"
            name="bulk"
            variant="outlined"
            onClick={toogleModal}
            disabled={!superAdmin}
          >
            {CONTENT.ADD_LEVELS}
          </Button>
        </Box>
      </Box>

      <LkModal
        title={CONTENT.MANAGE_ROLES_AND_PERMISSIONS}
        open={openModal}
        handleClose={toogleModal}
        disablePrimaryButton={disableApprovers || disableSubmit || disableApplyBtn || isLoading}
        primaryBtnText={CONTENT.APPLY}
        primaryBtn={onSubmitFun}
        primaryBtnWidth="122px"
      >
        {renderForm()}
        <Box className="text-center fw-bold fs16 mr-20">{CONTENT.ROLES}</Box>
        <Box className="display-flex gap20">
          <Box width={200}>
            <Autocomplete
              options={l1ConfirmerList}
              value={confirmersEmail[confirmersEmail.length]?.name || ''}
              onKeyUp={loadUserDetailsFun}
              getOptionLabel={(option) =>
                typeof option === 'string' ? option : `${option.empCode} - ${option.userName}`
              }
              onChange={(event, newValue) =>
                onChangeApproverFun('confirmers', newValue, confirmersEmail, setConfirmersEmail)
              }
              disabled={disableApprovers}
              filterOptions={(options) => options}
              renderOption={renderOptionsFun}
              renderInput={(params) => (
                <LkInput
                  {...params}
                  name="l1Confirmer"
                  className="input-box"
                  fullWidth
                  label={CONTENT.CONFIRMER}
                />
              )}
            />
            <Box>
              {confirmersEmail.map(
                (item) =>
                  item.email && (
                    <Box key={item.email} style={{ paddingTop: '5px' }}>
                      <LkChip
                        label={item.email}
                        type="filter"
                        deleteIcon={
                          <CancelIcon
                            style={{ color: 'rgba(60, 60, 60, 0.23)', fontSize: '16px' }}
                          />
                        }
                        onDelete={() =>
                          deleteConfirmer(item, confirmersEmail, setConfirmersEmail, item.id)
                        }
                      />
                    </Box>
                  )
              )}
            </Box>
          </Box>
          <Box width={200}>
            <Autocomplete
              options={approverList}
              disabled={disableApprovers || !superAdmin}
              value={approversEmail[approversEmail.length]?.name || ''}
              onChange={(event, newValue) =>
                onChangeApproverFun('approvers', newValue, approversEmail, setApproversEmail)
              }
              getOptionLabel={(option) =>
                typeof option === 'string' ? option : `${option.empCode} - ${option.userName}`
              }
              filterOptions={(options) => options}
              onKeyUp={loadUserDetailsFun}
              renderOption={renderOptionsFun}
              renderInput={(params) => (
                <LkInput
                  {...params}
                  name="approver"
                  className="input-box"
                  fullWidth
                  label={CONTENT.APPROVER}
                />
              )}
            />
            <Box>
              {approversEmail.map(
                (item) =>
                  item.email && (
                    <Box key={item.email} style={{ paddingTop: '5px' }}>
                      <LkChip
                        label={item.email}
                        type="filter"
                        deleteIcon={
                          <CancelIcon
                            style={{ color: 'rgba(60, 60, 60, 0.23)', fontSize: '16px' }}
                          />
                        }
                        onDelete={() =>
                          deleteConfirmer(item, approversEmail, setApproversEmail, item.id)
                        }
                      />
                    </Box>
                  )
              )}
            </Box>
          </Box>
        </Box>
      </LkModal>
    </>
  );
};

export default PriceControlHeader;
