// @TODO refactor this component make it generic.
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';

import { monitorFiltersUpdate } from 'redux/actionCreators/monitor';
import { MONITOR_TYPES } from 'config/monitor';

const useStyles = makeStyles()((theme) => ({
  root: {
    background: '#FFFFFF',
    padding: '10px 16px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderBottom: `1px solid ${theme.palette.grey[300]}`
  },
  filters: {
    gap: 10,
    alignItems: 'center'
  },
  iconWrapper: {
    borderRadius: 8,
    background: '#F0F0F0',
    width: 40,
    height: 40,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '&:hover': {
      cursor: 'pointer'
    }
  },
  flowChartIcon: {
    transform: 'rotate(332deg)'
  },
  activeIcon: {
    background: '#E5F8F9'
  },
  row: {
    display: 'flex',
    fontWeight: 500,
    fontSize: 14,
    borderBottom: '1px solid #DDDDDD',
    '&:last-child': {
      borderBottom: 0
    }
  },
  boldHeader: {
    fontWeight: 600
  },

  rowCell: {
    width: 95,
    justifyContent: 'right',
    borderRight: '1px solid #DDDDDD',
    padding: '8px 12px 8px 0px',
    cursor: 'pointer',
    display: 'flex',
    color: '#333333',
    background: '#F3FFFB',
    '&:last-child': {
      borderRight: 0
    }
  },
  secondaryBackgoundCell: {
    background: '#FFF5E5'
  },
  heading: {
    backgroundColor: '#eeeeee',
    fontSize: 14,
    fontWeight: 600,
    color: '#666666',
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    position: 'sticky',
    top: 0,
    display: 'flex',
    borderRadius: 8,
    justifyContent: 'space-between',
    padding: '8px 10px'
  },
  colorPrimary: {
    color: '#0A8367'
  },
  colorSecondary: {
    color: '#8D2D20'
  },
  borderTopLeftRadius: {
    borderTopLeftRadius: 8
  },
  borderTopRightRadius: {
    borderTopRightRadius: 8
  },
  borderBottomLeftRadius: {
    borderBottomLeftRadius: 8
  },
  borderBottomRightRadius: {
    borderBottomRightRadius: 8
  }
}));

const Rowkeys = [
  {
    name: 'Category',
    rowKey: 'Category'
  },
  {
    name: 'Total',
    rowKey: 'standard',
    filterkey: null
  },
  {
    name: '1 Day',
    rowKey: 'current',
    filterkey: 'current'
  },
  {
    name: '2 Day',
    rowKey: 'critical',
    filterkey: 'critical'
  },
  {
    name: '>2 day',
    rowKey: 'severe',
    filterkey: 'severe'
  }
];

const headersTemp = [
  {
    title: 'Category'
  },
  {
    title: 'Total',
    bgSecondary: true,
    filterPayload: {}
  },
  {
    title: 'JIT',
    filterPayload: { JIT: true }
  },
  {
    title: 'Non JIT',
    filterPayload: { JIT: false }
  },
  {
    title: 'ASRS',
    bgSecondary: true,
    filterPayload: { ASRS: true }
  },
  {
    title: 'Non ASRS',
    bgSecondary: true,
    filterPayload: { ASRS: false }
  },
  {
    title: 'STS',
    filterPayload: { 'Ship to Customer': false }
  },
  {
    title: 'STC',
    filterPayload: { 'Ship to Customer': true }
  }
];

const JITheaders = [
  {
    title: 'Category'
  },
  {
    title: 'Total',
    headerKey: 'total',
    bgSecondary: true,
    filterPayload: {}
  },
  {
    title: 'Lens Lab',
    headerKey: 'LENS LAB',
    filterPayload: {}
  },
  {
    title: 'Ext Vendor',
    headerKey: 'EXTERNAL VENDOR',
    filterPayload: {}
  },
  {
    title: 'DO Lens Lab',
    headerKey: 'DO LENS LAB',
    filterPayload: {}
  }
];

const MonitorBodyInfoTable = ({ activeCategory, selectedTab, showTable, setShowTable }) => {
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();

  const isJitOrders = selectedTab === MONITOR_TYPES.JIT_ORDERS;

  const [Headers, setHeaders] = useState(headersTemp);
  const monitorInfoTableList = useSelector((state) => state.monitor.monitorInfoTableList);
  const { isLoading, data } = useSelector((state) => state.monitor.monitorJitInfoTableList);

  const [rows, setRows] = useState([]);

  useEffect(() => {
    if (isJitOrders) {
      setHeaders(JITheaders);
    } else {
      setHeaders(headersTemp);
    }
  }, [selectedTab]);

  useEffect(() => {
    if (showTable) {
      setRows([...Rowkeys]);
    } else {
      setRows(Rowkeys.slice(0, 2));
    }
  }, [showTable]);

  const onClickFun = (filterPayload, orderType) => {
    dispatch(
      monitorFiltersUpdate({
        filter: 'BINARY',
        updatedFilters: filterPayload
      })
    );
    dispatch(
      monitorFiltersUpdate({
        filter: 'CATEGORY',
        updatedFilters: orderType
      })
    );
  };

  const calculateTotals = useCallback(
    (dataTemp) => {
      const totals = {
        critical: 0,
        severe: 0,
        current: 0,
        standard: 0
      };

      // Iterate over the dataTemp for each Fr tag
      dataTemp?.FR_TAG?.forEach((frTag) => {
        // Iterate over the different order types (critical, severe, current, standard)
        ['critical', 'severe', 'current', 'standard'].forEach((orderType) => {
          // Sum the TOTAL for each order type and Fr tag
          const total = dataTemp[orderType].JIT_ORDERS.reduce((acc, order) => {
            let tempAcc = acc;
            if (order['Fr Tag'] === frTag) {
              tempAcc += order.TOTAL || 0;
            }
            return tempAcc;
          }, 0);

          // Update the appropriate totals based on the Fr tag
          totals[orderType] += total;
        });
      });

      return totals;
    },
    [data]
  );

  const returnRows = ({ rowKey, filterkey }, rowIndex) => (
    <Box
      className={cx({
        [classes.row]: true,
        [classes.boldHeader]: rowIndex === 0
      })}
      key={rowKey}
    >
      {Headers.map(({ title, headerKey, bgSecondary, filterPayload }, rowCellIndex) => {
        const keyIndex = `${rowCellIndex}${rowKey}`;
        let value = 0;
        if (rowCellIndex === 0) {
          value = Rowkeys[rowIndex].name;
        } else if (rowIndex === 0) {
          value = title;
        } else {
          let temp;
          if (isJitOrders) {
            temp = data?.[rowKey]?.[selectedTab]?.find(
              (element) => element['Fr Tag'] === headerKey
            );
          } else {
            temp = monitorInfoTableList[title]?.[rowKey]?.[selectedTab]?.find(
              (element) => element['Fr Tag'] === activeCategory
            );
          }
          if (temp) {
            value = temp.TOTAL;
          }
          if (rowCellIndex === 1 && isJitOrders) {
            value = calculateTotals(data)[rowKey];
          }
        }

        return (
          <Box
            p={1}
            key={keyIndex}
            onClick={() =>
              rowIndex === 1 && rowCellIndex === 0 && setShowTable((tempState) => !tempState)
            }
            className={cx({
              [classes.rowCell]: true,
              [classes.secondaryBackgoundCell]: bgSecondary,
              [classes.colorPrimary]: rowIndex === 0,
              [classes.colorSecondary]: rowIndex === 0 && bgSecondary,
              [classes.borderTopLeftRadius]: rowIndex === 0 && rowCellIndex === 0,
              [classes.borderTopRightRadius]:
                rowIndex === 0 && rowCellIndex === headersTemp.length - 1,
              [classes.borderBottomLeftRadius]: rows.length - 1 === rowIndex && rowCellIndex === 0,
              [classes.borderBottomRightRadius]:
                rows.length - 1 === rowIndex && rowCellIndex === headersTemp.length - 1
            })}
          >
            {monitorInfoTableList.isLoading && isLoading && rowIndex && rowCellIndex ? (
              <Box width={65} height={12}>
                <Skeleton variant="rectangular" animation="wave" />
              </Box>
            ) : (
              <Box className="display-flex" alignItems="center">
                {rowIndex === 1 && rowCellIndex === 0 && (
                  <Box display="flex">
                    {showTable ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
                  </Box>
                )}
                <span
                  onClick={() =>
                    value &&
                    rowIndex &&
                    rowCellIndex &&
                    filterPayload &&
                    !isJitOrders &&
                    onClickFun(filterPayload, filterkey)
                  }
                >
                  {value}
                </span>
              </Box>
            )}
          </Box>
        );
      })}
    </Box>
  );

  const renderTable = () => (
    <Box className="border-radius-8 border-ee ">{rows.map(returnRows)}</Box>
  );

  return (
    <Box className={classes.root}>
      <Box className={classes.filters}>{renderTable()}</Box>
    </Box>
  );
};

export default MonitorBodyInfoTable;
