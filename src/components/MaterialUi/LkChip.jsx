import React from 'react';
import { makeStyles } from 'tss-react/mui';
import { Chip } from '@mui/material';

const useStyles = makeStyles()((theme) => ({
  root: {
    borderRadius: 8,
    height: 24,
    fontSize: '12px'
  },
  secondaryDark: {
    color: theme.palette.primary.dark,
    borderColor: theme.palette.primary.dark
  },
  primary: {
    color: theme.palette.primary.main,
    borderColor: theme.palette.primary.main
  },
  success: {
    color: theme.palette.success.main,
    borderColor: theme.palette.success.main
  },
  error: {
    color: theme.palette.error.main,
    borderColor: theme.palette.error.main
  },
  warning: {
    color: theme.palette.warning.main,
    borderColor: theme.palette.warning.main
  },
  info: {
    color: theme.palette.info.main,
    borderColor: theme.palette.info.main
  },
  filter: {
    color: '#666666',
    border: 'none',
    fontSize: '10px',
    fontWeight: 'bold',
    background: '#F5F5F5',
    borderRadius: 9999
  },
  grey2: {
    color: '#666666',
    border: '1px solid #666666'
  },
  brightYellow: {
    color: '#FAA61A',
    borderColor: '#FAA61A'
  },
  parisGreen: {
    color: '#45C476',
    borderColor: '#45C476'
  },
  red: {
    color: '#DF3747',
    borderColor: '#DF3747'
  },
  active: {
    color: '#FFFFFF',
    background: '#45C476',
    borderRadius: 20,
    fontSize: 10
  },
  draft: {
    color: '#FFFFFF',
    background: '#FFC20E',
    borderRadius: 20,
    fontSize: 10
  },
  inactive: {
    color: '#FFFFFF',
    background: 'rgba(60, 60, 60, 0.38)',
    borderRadius: 20,
    fontSize: 10
  },
  failure: {
    background: 'rgba(244, 67, 54, .1)',
    borderRadius: 20,
    color: '#DF3747',
    border: 'none',
    textTransform: 'Capitalize'
  },
  processing: {
    background: 'rgba(76, 175, 80, .1)',
    borderRadius: 20,
    color: '#45C476',
    border: 'none',
    textTransform: 'Capitalize'
  },
  CONFIRMED: {
    border: '1px solid #16CAD3',
    backgroundColor: '#EDFAFB'
  },
  PENDING_APPROVAL: {
    border: '1px solid #FF9800',
    backgroundColor: '#fff5e5'
  },
  APPROVED: {
    border: '1px solid #3B873E',
    backgroundColor: '#edf7ed'
  },
  REJECTED: {
    border: '1px solid #F44336',
    backgroundColor: '#feeceb'
  }
}));

const LkChip = (props) => {
  const { className, variant = 'outlined', label, type, ...rest } = props;
  const { classes, cx } = useStyles();
  switch (type) {
    case 'success':
      return (
        <Chip
          className={cx(classes.root, classes.success, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'error':
    case 'CLOSED':
      return (
        <Chip
          className={cx(classes.root, classes.error, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'warning':
      return (
        <Chip
          className={cx(classes.root, classes.warning, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'info':
      return (
        <Chip
          className={cx(classes.root, classes.info, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'grey2':
      return (
        <Chip
          className={cx(classes.root, classes.grey2, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'brightYellow':
      return (
        <Chip
          className={cx(classes.root, classes.brightYellow, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'parisGreen':
    case 'RETURN_AWAITED':
      return (
        <Chip
          className={cx(classes.root, classes.parisGreen, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'red':
      return (
        <Chip
          className={cx(classes.root, classes.red, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'filter':
      return (
        <Chip
          data-cy="filter-chip"
          className={cx(classes.root, classes.filter, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'primary':
      return (
        <Chip
          className={cx(classes.root, classes.primary, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'CREATED':
    case 'DRAFT':
    case 'PENDING':
      return (
        <Chip
          className={cx(classes.root, classes.draft, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'ACTIVE':
      return (
        <Chip
          className={cx(classes.root, classes.active, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'INACTIVE':
    case 'DISCARDED':
      return (
        <Chip
          className={cx(classes.root, classes.inactive, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'secondarDark':
      return (
        <Chip
          className={cx(classes.root, classes.secondaryDark, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'failure':
      return (
        <Chip
          className={cx(classes.root, classes.failure, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
    case 'processing':
      return (
        <Chip
          className={cx(classes.root, classes.processing, className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );

    default:
      return (
        <Chip
          className={cx(classes.root, classes[type], className)}
          label={label}
          variant={variant}
          {...rest}
        />
      );
  }
};

export default LkChip;
