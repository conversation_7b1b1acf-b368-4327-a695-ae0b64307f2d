import React, { useState, useCallback, useEffect } from 'react';
import NavigationDrawer from 'layouts/Main/components/Drawer/NavigationDrawer';
import './layOutCreate.scss';
import { Field, Form } from 'react-final-form';
import { composeValidators } from 'utils/helpers';
import { customIdValidation, required } from 'utils/validation';
import { Button, Checkbox, FormControlLabel } from '@mui/material';
import LkInput from 'components/MaterialUi/LkInput';
import { FieldArray } from 'react-final-form-arrays';
import arrayMutators from 'final-form-arrays';
import RegexModal from './RegexModal';
import './regexModal.scss';

let data = {
  type: '',
  format: '',
  fields: [
    {
      field: '',
      regex: '',
      mandatory: true,
      new: true
    }
  ]
};
const PhyicalEntityCreate = (props) => {
  const { openDrawer, closeDrawer, editEntityData, entityList, CONTENT } = props;
  const [showRegexModal, setShowRegexModal] = useState(false);
  const openModalFun = useCallback(() => closeDrawer(), []);

  const heading = (title) => (
    <div className="display-flex align-items-center">
      <img
        className="img-action mr-r10"
        src={`${import.meta.env.VITE_STATIC_SERVER}/images/entity.svg`}
        alt="entity"
      />
      <div className="physical-heading fs10">{title}</div>
    </div>
  );

  useEffect(
    () => () => {
      data = {
        type: '',
        format: '',
        fields: [
          {
            field: '',
            regex: '',
            mandatory: true,
            new: true
          }
        ]
      };
    },
    []
  );

  const pushCustomFields = (push, errors, type) => {
    if (errors?.fields?.length > 0) {
      return;
    }
    const payload = data.fields[0];
    if (entityList?.physical_entity[type]) {
      const { count, inventory } = entityList.physical_entity[type];
      if (count > 0 || inventory > 0) {
        payload.mandatory = false;
      }
    }
    push('fields', payload);
  };

  const submitReason = (values) => {
    const { addEntity } = props;
    addEntity(values);
  };

  const disableStorageEntity = (values) => {
    const p = values?.id && entityList?.physical_entity[values?.type];
    return !!(p && (p.count > 0 || p.invertory > 0));
  };

  const setShowRegexModalFun = useCallback(() => {
    setShowRegexModal(!showRegexModal);
  }, [showRegexModal]);

  const disableCheckBoxIfithasEntity = (type) => {
    if (entityList?.physical_entity[type]) {
      const { count, inventory } = entityList.physical_entity[type];
      if (count > 0 || inventory > 0) {
        return true;
      }
    }
    return false;
  };
  return (
    <div className="display-flex">
      <NavigationDrawer
        openDrawer={openDrawer}
        handleClose={openModalFun}
        htmlHeading={heading(editEntityData?.type || CONTENT.NEW_PHSICAL_ENTITY)}
        modalwidth="540px"
      >
        <div className="display-flex">
          <div className="phsyical-entity">
            <div className="entity-form pd-40">
              <div
                className="fs14 link-container pd-10 display-flex 
              align-items-center justify-content-space-between"
              >
                <div className="display-flex">
                  <div>
                    <img
                      className="img-action mr-r10"
                      src={`${import.meta.env.VITE_STATIC_SERVER}/images/Info.svg`}
                      alt="info"
                    />
                  </div>
                  <div>{CONTENT.DISCOVER_TYPES_OF_REGEX}</div>
                </div>
                <div
                  onClick={setShowRegexModalFun}
                  onKeyDown={setShowRegexModalFun}
                  style={{ color: '#4ECED4' }}
                  className="mr-l10 cursor-pointer"
                >
                  {CONTENT.READ_MORE}
                </div>
              </div>
              <div className="fw-bold fs20 mr-t20">{CONTENT.DEFINE_PHYSICAL_LAYER_ENTITY}</div>
              <Form
                mutators={{
                  ...arrayMutators
                }}
                onSubmit={submitReason}
                initialValues={editEntityData || data}
                render={({
                  handleSubmit,
                  form: {
                    mutators: { push }
                  },
                  values,
                  errors,
                  valid
                }) => (
                  <form
                    onSubmit={handleSubmit}
                    name="reason-po-form"
                    className="refrence-detail-form"
                    noValidate
                  >
                    <div className="display-flex pd-b30 border-bf0">
                      <Field name="type" validate={composeValidators(required)}>
                        {({ input, meta }) => (
                          <div className="material-group mr-t40">
                            <LkInput
                              disabled={disableStorageEntity(values)}
                              {...input}
                              fullWidth
                              label={CONTENT.LAYER_NAME}
                            />
                            <span className="input-error">
                              {meta.error && meta.touched ? meta.error : ''}
                            </span>
                          </div>
                        )}
                      </Field>
                      <div className="width-241 mr-l30">
                        <Field name="format" validate={composeValidators(required)}>
                          {({ input, meta }) => (
                            <div className="material-group mr-t40">
                              <LkInput
                                disabled={disableStorageEntity(values)}
                                {...input}
                                fullWidth
                                label={CONTENT.ID_FORMAT}
                              />
                              <span className="input-error">
                                {meta.error && meta.touched ? meta.error : ''}
                              </span>
                            </div>
                          )}
                        </Field>
                      </div>
                    </div>
                    <div className="mr-t25 mr-b15">
                      <div className="color-81 fs14 fw-bold">{CONTENT.CUSTOM_FIELDS}</div>
                      <FieldArray name="fields">
                        {({ fields }) =>
                          fields.map((name, index) => (
                            <>
                              <div key={name} className="display-flex align-items-center">
                                <Field
                                  name={`${name}.field`}
                                  validate={composeValidators(required, customIdValidation)}
                                >
                                  {({ input, meta }) => (
                                    <div className="material-group mr-t30">
                                      <LkInput
                                        disabled={
                                          disableStorageEntity(values) && !values.fields[index]?.new
                                        }
                                        {...input}
                                        fullWidth
                                        label={CONTENT.CUSTOM_FIELD_NAME}
                                      />
                                      <span className="input-error">
                                        {meta.error && meta.touched ? meta.error : ''}
                                      </span>
                                    </div>
                                  )}
                                </Field>
                                <div className="width-241 mr-l30">
                                  <Field
                                    name={`${name}.regex`}
                                    validate={composeValidators(required)}
                                  >
                                    {({ input, meta }) => (
                                      <div className="material-group mr-t30">
                                        <LkInput
                                          disabled={
                                            disableStorageEntity(values) &&
                                            !values.fields[index]?.new
                                          }
                                          {...input}
                                          fullWidth
                                          label={CONTENT.CUSTOM_FIELD_ID_FORMAT}
                                        />
                                        <span className="input-error">
                                          {meta.error && meta.touched ? meta.error : ''}
                                        </span>
                                      </div>
                                    )}
                                  </Field>
                                </div>
                                {!(disableStorageEntity(values) && !values.fields[index]?.new) && (
                                  <div 
                                    onClick={() => fields.remove(index)}
                                    onKeyDown={() => fields.remove(index)}
                                  >
                                    <img
                                      className="img-action mr-l20 mr-t30 cursor-pointer"
                                      src={`${
                                        import.meta.env.VITE_STATIC_SERVER
                                      }/images/delete-grey.svg`}
                                      alt="delete"
                                    />
                                  </div>
                                )}
                              </div>
                              <div className="mr-t20">
                                <Field
                                  component={Checkbox}
                                  type="checkbox"
                                  name={`${name}.mandatory`}
                                >
                                  {({
                                    input: { checked, onChange, ...restInput },
                                    meta,
                                    ...rest
                                  }) => (
                                    <FormControlLabel
                                      control={
                                        <Checkbox
                                          disabled={
                                            (disableStorageEntity(values) &&
                                              !values.fields[index]?.new) ||
                                            disableCheckBoxIfithasEntity(values.type)
                                          }
                                          inputProps={restInput}
                                          onChange={onChange}
                                          checked={Boolean(checked)}
                                          color="primary"
                                          {...rest}
                                        />
                                      }
                                      label={CONTENT.MARK_AS_MANDATORY}
                                    />
                                  )}
                                </Field>
                              </div>
                            </>
                          ))
                        }
                      </FieldArray>
                      <div
                        onClick={() => pushCustomFields(push, errors, values.type)}
                        onKeyDown={() => pushCustomFields(push, errors, values.type)}
                        className="text-primary mr-t25 fw-bold fs14 cursor-pointer"
                      >
                        + {CONTENT.ADD_NEW_CUSTOM_FIELD}
                      </div>
                    </div>
                    <div className="display-flex mr-b40 mr-t40">
                      <div className="mr-r5">
                        <Button
                          disabled={!valid}
                          onClick={() => submitReason(values)}
                          style={{ borderRadius: 8, paddingLeft: '40px', paddingRight: '40px' }}
                          variant="contained"
                          color="primary"
                        >
                          {editEntityData?.id ? CONTENT.DONE : CONTENT.ADD}
                        </Button>
                      </div>
                    </div>
                  </form>
                )}
              />
            </div>
          </div>
        </div>
        <div>
          {/* <ModalCustom /> */}
          <RegexModal
            showModal={showRegexModal}
            setShowModal={setShowRegexModalFun}
            CONTENT={CONTENT}
          />
        </div>
      </NavigationDrawer>
    </div>
  );
};

export default PhyicalEntityCreate;
