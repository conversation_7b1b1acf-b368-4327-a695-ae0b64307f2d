import React, { useCallback, useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import { makeStyles } from 'tss-react/mui';
import { useDispatch, useSelector } from 'react-redux';
import {
  DISTRIBUTOR_ORDERS,
  getCreateOrderDataReset,
  getCustomerListLoad,
  getValidateCSVReset
} from 'redux/reducers/distributorOrders';
import { getConsulKeyValue } from 'redux/reducers/consul';
import { LOCALISATION } from 'redux/reducers/localisation';
import OrderDetails from './OrderDetails';
import ValidateCSV from './ValidateCSV';
import { PlaceOrder } from './PlaceOrder';

const useStyles = makeStyles()(() => ({
  root: {
    color: '#939393'
  },
  steppers: {
    width: '33.33%',
    height: '81vh',
    paddingTop: '30px',
    borderRight: '1px solid #dcdcdc',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column'
  }
}));

const requestPayload = {
  page: 0,
  size: 50,
  sortBy: 'id',
  sortOrder: 'DESC',
  searchTerms: 'customerEnabled.eq:true'
};

const CreateOrder = () => {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [requestBody, setRequestBody] = useState(requestPayload);
  const [activeStep, setActiveStep] = useState(0);
  const [orderDetails, setOrderDetails] = useState({
    customer: null,
    poNumber: '',
    doType: ''
  });

  const CONTENT = useSelector((state) => state[LOCALISATION].localeData.DO_ORDER_MANAGEMENT);
  const { isSuccess } = useSelector((state) => state[DISTRIBUTOR_ORDERS].createOrder);

  const steps = [
    CONTENT.ENTER_ORDER_DETAILS,
    CONTENT.SELECT_AND_VALIDATE_CSV,
    CONTENT.PLACE_NEW_ORDER
  ];

  const resetOrderDetails = () => {
    setActiveStep(0);
    setOrderDetails({
      customer: '',
      poNumber: ''
    });
    dispatch(getValidateCSVReset());
    dispatch(getCreateOrderDataReset());
    dispatch(getCustomerListLoad(requestPayload));
  };

  useEffect(() => {
    dispatch(getCustomerListLoad(requestBody));
  }, [requestBody]);

  useEffect(() => {
    dispatch(getConsulKeyValue(['currency']));
  }, []);

  const searchSuggestion = useCallback((value) => {
    setRequestBody((prev) => ({ ...prev, searchTerms: `name.like:${value}` }));
  }, []);

  return (
    <Box data-cy="create-order-container" className={classes.root}>
      <Box
        sx={{
          width: '100%',
          paddingTop: '30px',
          paddingBottom: '30px',
          backgroundColor: '#F5F5F5'
        }}
      >
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step data-cy={`stepper-${label}`} key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>
      <Box className="display-flex">
        <Box className={classes.steppers}>
          <OrderDetails
            isSuccess={isSuccess}
            orderDetails={orderDetails}
            setOrderDetails={setOrderDetails}
            setActiveStep={setActiveStep}
            activeStep={activeStep}
            searchSuggestion={searchSuggestion}
            CONTENT={CONTENT}
          />
        </Box>
        <Box className={classes.steppers}>
          {activeStep > 0 && (
            <ValidateCSV
              isSuccess={isSuccess}
              orderDetails={orderDetails}
              setOrderDetails={setOrderDetails}
              setActiveStep={setActiveStep}
              CONTENT={CONTENT}
            />
          )}
        </Box>
        {activeStep > 1 && (
          <PlaceOrder
            resetOrderDetails={resetOrderDetails}
            classes={classes}
            CONTENT={CONTENT}
            orderDetails={orderDetails}
          />
        )}
      </Box>
    </Box>
  );
};

export default CreateOrder;
