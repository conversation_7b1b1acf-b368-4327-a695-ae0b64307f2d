// layout define
export const LAYOUT_DEFINE_LOAD = 'LAYOUT_DEFINE_LOAD';
export const LAYOUT_DEFINE_SUCCESS = 'LAYOUT_DEFINE_SUCCESS';
export const LAYOUT_DEFINE_FAIL = 'LAYOUT_DEFINE_FAIL';
export const LAYOUT_DEFINE_RESET = 'LAYOUT_DEFINE_RESET';

// layout save
export const LAYOUT_SAVE_LOAD = 'LAYOUT_SAVE_LOAD';
export const LAYOUT_SAVE_SUCCESS = 'LAYOUT_SAVE_SUCCESS';
export const LAYOUT_SAVE_FAIL = 'LAYOUT_SAVE_FAIL';
export const LAYOUT_SAVE_RESET = 'LAYOUT_SAVE_RESET';

// set layout create data
export const SET_LAYOUT_CREATE_DATA = 'SET_LAYOUT_CREATE_DATA';

// get layout detail
export const GET_LAYOUT_DEATIL_LOAD = 'GET_LAYOUT_DEATIL_LOAD';
export const GET_LAYOUT_DEATIL_SUCCESS = 'GET_LAYOUT_DEATIL_SUCCESS';
export const GET_LAYOUT_DEATIL_FAIL = 'GET_LAYOUT_DEATIL_FAIL';
export const GET_LAYOUT_DEATIL_RESET = 'GET_LAYOUT_DEATIL_RESET';

// get layout list
export const GET_LAYOUT_LIST_LOAD = 'GET_LAYOUT_LIST_LOAD';
export const GET_LAYOUT_LIST_SUCCESS = 'GET_LAYOUT_LIST_SUCCESS';
export const GET_LAYOUT_LIST_FAIL = 'GET_LAYOUT_LIST_FAIL';
export const GET_LAYOUT_LIST_RESET = 'GET_LAYOUT_LIST_RESET';