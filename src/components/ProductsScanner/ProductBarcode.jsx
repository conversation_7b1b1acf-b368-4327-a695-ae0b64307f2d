import React, { useMemo } from 'react';
import { makeStyles } from 'tss-react/mui';

import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Grow from '@mui/material/Grow';

import DeleteIcon from '@mui/icons-material/Delete';
import ErrorIcon from '@mui/icons-material/Error';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import TimerIcon from '@mui/icons-material/Timer';

const useStyles = makeStyles()(() => ({
  container: {
    display: 'flex',
    justifyContent: 'space-between',
    height: 34,
    alignItems: 'center'
  },
  barcodeContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: 7,
    flex: 3
  },
  barcode: {
    fontWeight: 400,
    fontSize: 15,
    lineHeight: '20px',
    color: '#666666'
  },
  statusIcon: {
    fontSize: 14
  },
  successStatusIcons: {
    color: '#4CAF50'
  },
  errorStatusIcon: {
    color: '#F44336'
  },
  productDelete: {
    flex: 1
  }
}));

const ProductBarcode = ({
  barcode,
  status,
  deleteBarcode,
  disableDelete,
  inValidBox = false,
  inProgressBox = false,
  showDeleteItemForFailed = false
}) => {
  const { classes, cx } = useStyles();
  const statusIcon = useMemo(() => {
    if (status === 'pending' || inProgressBox) {
      return <TimerIcon className={classes.statusIcon} />;
    }
    if (status === 'failed' || inValidBox) {
      return <ErrorIcon className={cx(classes.statusIcon, classes.errorStatusIcon)} />;
    }
    return <CheckCircleIcon className={cx(classes.statusIcon, classes.successStatusIcons)} />;
  }, [status]);

  return (
    <Grow in>
      <Box className={classes.container}>
        <Box className={classes.barcodeContainer}>
          <span className={classes.barcode}>{barcode}</span>
          {statusIcon}
        </Box>
        {((status === 'success' && !disableDelete) ||
          (status === 'failed' && showDeleteItemForFailed)) && (
          <Box className={classes.productDelete}>
            <IconButton size="small" onClick={() => deleteBarcode(barcode)}>
              <DeleteIcon fontSize="inherit" />
            </IconButton>
          </Box>
        )}
      </Box>
    </Grow>
  );
};

export default ProductBarcode;
