import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import './constraintList.scss';

import useWindowResize from 'common/useWindowResize';
import { useDispatch, useSelector } from 'react-redux';
import LkToolTip from 'components/ToolTip/ToolTip';
import {
  getConstraintListLoad,
  saveConstraintLoad,
  getConstraintListReset,
  saveConstraintReset
} from 'redux/actionCreators/constraint';
import { convertDateFormat, getAllQueryParam, generatePayloadForSearchAPI } from 'utils/helpers';
import { toastOpen } from 'redux/actionCreators/toast';
import { mapSearchFilterKey } from 'config/filtersKeyMapping';
import LkTable from 'components/MaterialUi/LkTable/LkTable';
import ChangePriorityModal from './ChangePriorityModal';
import config from '../../config';
import PopperConstraint from './PopperConstraint';

const useRowStyles = makeStyles()({
  root: {
    '& > *': {
      borderBottom: 'unset'
    }
  },
  childTable: {
    boxShadow: '0px 2px 1px -1px rgba(0, 0, 0, 0.2)'
  },
  menuItem: {
    maxWidth: '200px',
    minWidth: '200px',
    color: '#00B9C6',
    fontWeight: 500,
    borderBottom: '1px solid #f0f0f0'
  },
  menuItemLastChild: {
    borderBottom: 'none'
  }
});

const sortKeyNames = {
  ruleStatus: 'RULE_STATUS',
  priority: 'PRIORITY',
  ruleId: 'RULE_ID',
  title: 'TITLE',
  createdAt: 'CREATED_AT',
  updatedAt: 'UPDATED_AT',
  UPDATED_AT: 'UPDATED_AT'
};

let payload = {
  enabled: true,
  ruleType: 'PUTAWAY',
  sortKeysOrder: {
    UPDATED_AT: 'DESC'
  }
};

let rowData = {};

const initialSortData = { id: 'UPDATED_AT', order: 'DESC' };

const ConstraintList = ({
  globalSearch,
  isRulesEnabled,
  ruleStatus,
  selectedFilterList,
  onChangeFilterList,
  ruleType,
  CONTENT
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [openChangePriorityModal, setOpenChangePriorityModal] = useState(false);
  const { classes } = useRowStyles();
  const { constraintListData, constraintListLoad, constraintSaveSuccess, constraintSaveFail } =
    useSelector((state) => state.constraint);
  const { selectedFacility } = useSelector((state) => state.settings);
  const dispatch = useDispatch();
  const [sortType, setSortType] = useState({ UPDATED_AT: 'DESC' });
  const [tableHeight] = useWindowResize(window.innerHeight - 250);

  const { autoSuggestionListRULE_STATUS, autoSuggestionListFACILITY } = useSelector(
    (state) => state.filters
  );
  const changePriorityFun = () => {
    setOpenChangePriorityModal(true);
  };

  const fetchMoreData = (sortOrder, sortKey) => {
    if (sortKey) {
      const key = sortKeyNames[sortKey];
      const st = sortType[key] === 'ASC' ? 'DESC' : 'ASC';
      setSortType({ [key]: st });
      payload.sortKeysOrder = {
        [key]: st
      };
      dispatch(getConstraintListLoad(payload));
    }
  };

  const returnIcon = (status) => {
    const statusIcons = {
      ENABLED: {
        icon: 'enabledStatus.svg',
        text: CONTENT.ENABLED
      },
      DISABLED: {
        icon: 'disabledStatus.svg',
        text: CONTENT.DISABLE
      },
      PENDING_REVIEW: {
        icon: 'draftStatus.svg',
        text: CONTENT.PENDING
      },
      default: {
        icon: 'draftStatus.svg',
        text: CONTENT.DRAFT
      }
    };

    const { icon, text } = statusIcons[status] || statusIcons.default;

    return (
      <Box display="flex" alignItems="center">
        <img
          src={`${import.meta.env.VITE_STATIC_SERVER}/images/${icon}`}
          alt="info"
          className="mr-r8"
        />
        <Box>{text}</Box>
        <div className="mr-l8">
          <img
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/arrow-down-filled.svg`}
            alt="info"
            className="mr-r8 mr-l4"
          />
        </div>
      </Box>
    );
  };

  const updateFun = (ev, resRowData, status) => {
    const data = resRowData;
    ev.stopPropagation();
    data.ruleStatus = status;
    data.consequenceRequestList = data.consequences;
    if (data.consequenceRequestList) {
      data.consequenceRequestList.forEach((element, index) => {
        data.consequenceRequestList[index].approvalNeeded = element.approvalRequired;
      });
    }
    if (status === 'ENABLED') {
      rowData = data;
      rowData.isEnable = true;
      changePriorityFun();
    } else {
      dispatch(saveConstraintLoad(data));
    }
  };

  const statusActions = (status, data) => (
    <Box className="statusActions">
      {status !== 'ENABLED' && (
        <Box style={{ backgroundColor: '#ffff' }} onClick={(ev) => updateFun(ev, data, 'ENABLED')}>
          {' '}
          <img
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/enabledStatus.svg`}
            alt="info"
            className="mr-r12"
          />
          {CONTENT.ENABLE}{' '}
        </Box>
      )}
      {status !== 'DRAFT' && (
        <Box onClick={(ev) => updateFun(ev, data, 'DRAFT')}>
          {' '}
          <img
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/draftStatus.svg`}
            alt="info"
            className="mr-r16 "
          />
          {CONTENT.DRAFT}{' '}
        </Box>
      )}
      {status !== 'DISABLED' && (
        <Box onClick={(ev) => updateFun(ev, data, 'DISABLED')}>
          {' '}
          <img
            src={`${import.meta.env.VITE_STATIC_SERVER}/images/disabledStatus.svg`}
            alt="info"
            className="mr-r8 "
          />
          {CONTENT.DISABLE}{' '}
        </Box>
      )}
    </Box>
  );

  const actionIcon = () => (
    <Box className="constraint-actions">
      <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/create-icon-filled.svg`} alt="info" />
      <img src={`${import.meta.env.VITE_STATIC_SERVER}/images/arrow-down-filled.svg`} alt="info" />
    </Box>
  );

  const actionOptions = (data) => (
    <Box className="statusActions">
      <Box
        onClick={(ev) => {
          ev.stopPropagation();
          rowData = JSON.parse(JSON.stringify(data));
          changePriorityFun();
        }}
      >
        {CONTENT.CHANGE_PREIORITY}
      </Box>
      <Box onClick={() => navigate(`/constraint/create?ruleId=${data.ruleId}`)}>
        {CONTENT.EDIT_RULE}</Box>
    </Box>
  );

  const extendedHeaderConfig = [
    { name: CONTENT.DESTINATION_TYPE, key: 'destinationType', align: 'center' },
    { name: CONTENT.DESTINATION, key: 'destination', align: 'center',
      formatBody: useCallback(
        ({ destination }) => (
          <Box>
            {destination.length > 30 ? (
              <LkToolTip
                placement="bottom-end"
                title={<Box p={1}>{destination} </Box>}
                className="dtoolTip"
              >
                <div className="ellipsis-vertical">{destination}</div>
              </LkToolTip>
            ) : (
              destination || '-'
            )}
          </Box>
        ),
        []
      ),
    },
    { name: CONTENT.SCAN_METHOD, key: 'scanType', align: 'center' },
    { name: CONTENT.LOCATION_SCAN, key: 'locationScanType', align: 'center' },
    { name: CONTENT.ITEM_SCAN_TYPE, key: 'scanMethod', align: 'center' },
    { name: `${CONTENT.APPROVAL_NEEDED}?`, key: 'approvalRequired', align: 'center' }
  ];

  const renderExtenedComponent = (extendedData) => (
    extendedData && <LkTable
      tableHeight={190}
      tableData={extendedData.consequences}
      headerConfig={extendedHeaderConfig}
      pageNumber={0}
      className={classes.extendedData}
      showTableCount={false}
      totalRowsCount={extendedData.consequences.length}
      isNonVertualizedTable
    />
  );

  const headerConfig = [
    {
      name: '',
      key: '',
      align: 'center',
      enableExpander: true,
      formatBody: () => null,
      isExpandable: useCallback(({ ruleId }) => !!ruleId, []),
      style: { minWidth: '30px', maxWidth: '30px' }
    },
    {
      name: CONTENT.STATUS,
      key: 'ruleStatus',
      align: 'left',
      style: { minWidth: '100px', maxWidth: '100px', fontSize: '14px' },
      supportFilter: true,
      marginLeft: '20px',
      formatBody: useCallback(
        (data) => (
          <PopperConstraint
            key={data.ruleId}
            ruleId={data.ruleId}
            classes={classes}
            icon={returnIcon(data.ruleStatus)}
            popperShow={statusActions(data.ruleStatus, JSON.parse(JSON.stringify(data)))}
          />
        ),
        []
      ),
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListRULE_STATUS,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        postData: { suggestionFields: ['RULE_STATUS'], name: 'RULE_STATUS' },
        apiName: 'facility-constraints',
        columnName: 'RULE_STATUS',
        methodType: 'POST',
        url: config.constraint.suggestion
      }
    },
    {
      name: CONTENT.PREIORITY,
      key: 'priority',
      align: 'left',
      supportSort: true,
      style: { minWidth: '70px', maxWidth: '70px', paddingRight: '20px' },
      formatBody: useCallback(
        (data) => (
          <Box className="fw-bold" pr={1.5}>
            {data.priority ?? '--'}
          </Box>
        ),
        []
      )
    },
    {
      name: CONTENT.RULE_ID,
      key: 'ruleId',
      align: 'left',
      supportSort: true,
      style: { minWidth: '80px', maxWidth: '80px', paddingLeft: '20px' }
    },
    {
      name: CONTENT.RULE_NAME,
      key: 'title',
      align: 'left',
      supportSort: true,
      style: { minWidth: '100px', maxWidth: '100px' }
    },
    {
      name: CONTENT.CONDITION,
      key: 'condition',
      align: 'left',
      style: { minWidth: '250px', maxWidth: '250px' },
      formatBody: useCallback(
        ({ condition }) => (
          <Box>
            {condition.length > 30 ? (
              <LkToolTip
                placement="bottom-end"
                title={<Box p={1}>{condition} </Box>}
                className="dtoolTip"
              >
                <div className="ellipsis-vertical">{condition}</div>
              </LkToolTip>
            ) : (
              condition || '-'
            )}
          </Box>
        ),
        []
      ),
    },
    {
      name: CONTENT.FACILITY_CODE,
      key: 'facility',
      align: 'left',
      style: { minWidth: '100px', maxWidth: '100px' },
      supportFilter: true,
      marginLeft: '20px',
      filterData: {
        type: 'autoSelect',
        listData: autoSuggestionListFACILITY,
        submitFilter: onChangeFilterList,
        selectedFilterList,
        postData: { suggestionFields: ['FACILITY'], name: 'FACILITY' },
        apiName: 'facility-constraints',
        columnName: 'FACILITY',
        methodType: 'POST',
        url: config.constraint.suggestion
      }
    },
    {
      name: CONTENT.CREATED_ON,
      key: 'createdAt',
      align: 'left',
      style: { minWidth: '130px', maxWidth: '130px' },
      supportSort: true,
      marginLeft: '20px',
      formatBody: useCallback(
        (data) => (
          <>
            <div className="fw-bold">
              {data.createdAt && convertDateFormat(data.createdAt, 'utcTOlocalDate')}
            </div>
            <div className="fs10 text-66">
              {data.createdAt && convertDateFormat(data.createdAt, 'utcTOlocalTime')}
            </div>
          </>
        ),
        [convertDateFormat]
      )
    },
    {
      name: CONTENT.UPDATED_ON,
      key: 'updatedAt',
      align: 'left',
      style: { minWidth: '130px', maxWidth: '130px' },
      supportSort: true,
      marginLeft: '20px',
      formatBody: useCallback(
        (data) => (
          <>
            <div className="fw-bold">
              {data.updatedAt && convertDateFormat(data.updatedAt, 'utcTOlocalDate')}
            </div>
            <div className="fs10 text-66">
              {data.updatedAt && convertDateFormat(data.updatedAt, 'utcTOlocalTime')}
            </div>
          </>
        ),
        [convertDateFormat]
      )
    },
    {
      name: '',
      key: 'ACTION',
      align: 'left',
      formatBody: useCallback(
        (data) => (
          <PopperConstraint
            key={data.ruleId}
            ruleId={data.ruleId}
            classes={classes}
            icon={actionIcon()}
            popperShow={actionOptions(data)}
          />
        ),
        []
      ),
      style: { minWidth: '60px', maxWidth: '60px' }
    }
  ];

  useEffect(() => {
    payload.ruleType = [ruleType];
    return () => {
      payload.sortKeysOrder = {
        UPDATED_AT: 'DESC'
      };
      dispatch(getConstraintListReset());
      dispatch(saveConstraintReset());
    };
  }, []);
  useEffect(() => {
    if (globalSearch) {
      payload.globalSearchKey = globalSearch;
      payload.ruleStatus = ruleStatus;
    } else {
      delete payload.globalSearchKey;
    }
    payload.enabled = isRulesEnabled;
    if (isRulesEnabled) {
      payload.ruleStatus = [];
    }
    // payload.facility = selectedFacility
    dispatch(getConstraintListLoad(payload));
  }, [dispatch, globalSearch, isRulesEnabled, selectedFacility]);

  useEffect(() => {
    if (constraintSaveSuccess) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: constraintSaveSuccess?.meta?.displayMessage,
          severity: 'success'
        })
      );
      payload.sortKeysOrder = {
        UPDATED_AT: 'DESC'
      };
      setSortType({ UPDATED_AT: 'DESC' });
      dispatch(getConstraintListLoad(payload));
    }
    if (constraintSaveFail) {
      dispatch(
        toastOpen({
          isToastOpen: true,
          heading: constraintSaveFail?.meta?.displayMessage,
          severity: 'error'
        })
      );
    }
  }, [constraintSaveSuccess, constraintSaveFail, dispatch]);

  useEffect(() => {
    if (ruleStatus && !isRulesEnabled) {
      payload.ruleStatus = ruleStatus;
      dispatch(getConstraintListLoad(payload));
    }
  }, [ruleStatus]);

  const setOpenChangePriorityModalFun = useCallback(
    (v) => {
      setOpenChangePriorityModal(v);
    },
    [setOpenChangePriorityModal]
  );

  useEffect(() => {
    payload.ruleStatus = [];
    delete payload.created_at_from;
    delete payload.created_at_to;
    delete payload.updated_at_from;
    delete payload.updated_at_to;
    delete payload.facility;
    const result = getAllQueryParam(window.location.search);
    payload = generatePayloadForSearchAPI(result, payload, mapSearchFilterKey);
    dispatch(getConstraintListLoad(payload));
  }, [location.search, dispatch]);

  return (
    <Box
      component={Paper}
      className="constraintTableContainer"
      style={{ maxHeight: tableHeight, overflowX: 'scroll' }}
    >
      {openChangePriorityModal && (
        <ChangePriorityModal
          openChangePriorityModal={openChangePriorityModal}
          seOpenChangePriorityModal={setOpenChangePriorityModalFun}
          data={rowData}
        />
      )}
      <LkTable
        tableHeight={tableHeight}
        isDataFetching={constraintListLoad}
        headerConfig={headerConfig}
        tableData={constraintListData}
        pageNumber={0}
        dataRequestFunction={fetchMoreData}
        totalRowsCount={constraintListData?.length}
        showTableCount={false}
        rowSize={100}
        rowExtendedSize={300}
        renderExtenedComponent={renderExtenedComponent}
        initialSortBy={initialSortData}
      />
    </Box>
  );
};
export default ConstraintList;
