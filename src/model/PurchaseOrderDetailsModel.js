import PurchaseOrderDetailItems from './PurchaseOrderDetailItems';

export default function PurchaseOrderDetailsModel({ items = [], procurement_type, reference_details, season, expiryDate, deliveryDate, shippingDate }) {
  this.items = items.length > 0 ? items.map(it => new PurchaseOrderDetailItems(it)) : [new PurchaseOrderDetailItems({})];
  this.procurement_type = procurement_type;
  this.reference_number = reference_details;
  this.season = season;
  this.expiryDate = expiryDate;
  this.shippingDate = shippingDate;
  this.deliveryDate = deliveryDate;
  calculateValues(this, items);
}


const calculateValues = (that, items) => {
  that.subtotal = getSum(that.items, 'total_vendor_cost_price');
  that.overheads = getSum(that.items, 'overheadsPrice');
  that.total = getSum(that.items, 'total_with_overhead');
  that.taxes = getSum(that.items, 'total_tax');
  that.duty = getSum(that.items, 'item_customs_cost');
  that.taxes_duty = that.taxes + that.duty;
  that.pending_quantity = getSum(that.items, 'pending_quantity');
  that.accepted_quantity = getSum(that.items, 'accepted_quantity');
  that.rejected_quantity = getSum(that.items, 'rejected_quantity');
  that.quantity = getSum(that.items, 'quantity');
  that.total_quantity = that.pending_quantity + that.accepted_quantity + that.rejected_quantity + that.quantity;
  that.total_overheads_rate = {};
  if (items.length > 0) {
    Object.keys(that.items[0].overhead_rate).forEach(o => {
      that.total_overheads_rate[o] = 0;
    });
    that.items.map(it => {
      Object.keys(it.overhead_rate).forEach(o => {
        that.total_overheads_rate[o] += it.overhead_rate[o];
      });
    })
  }
};

PurchaseOrderDetailsModel.prototype.setQuantity = function (quantity, index) {
  this.items[index].setQuantity(quantity);
  calculateValues(this, this.items);
};

PurchaseOrderDetailsModel.prototype.newItem = function (item = {}) {
  return new PurchaseOrderDetailItems(item);
};

PurchaseOrderDetailsModel.prototype.calculateVal = function (obj, items) {
  calculateValues(obj, items);
};

const getSum = (items = [], field) => {
  let sum = 0;
  items.forEach(it => sum += it[`${field}`]);
  return sum;
};
